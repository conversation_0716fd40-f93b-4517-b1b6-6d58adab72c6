# dependencies
/node_modules

# testing
/coverage

# build
/dist

# etc
.DS_Store
*.env*
.idea

# debug
yarn-error.log

# compiled
utils/reload/*.js
utils/reload/injections/*.js
scripts/reload/*.js
scripts/reload/injections/*.js
public/manifest.json
*.zip

# modern yarn
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Sentry Config File
.env.sentry-build-plugin

.vscode*
