<div align="center">
  <img src="public/icon-128.png" alt="logo"/>
  <h1> Drumkit Chrome Extension built with<br/>React + Vite + TypeScript</h1>
</div>

<details>
  <summary>Table of Contents</summary>
  <ol>
    <li><a href="#getting-started">Getting Started</a>
      <ul>
        <li><a href="#prerequisites">Prerequisites</a></li>
        <li><a href="#setup">Setup</a></li>
        <li><a href="#development">Development</a></li>
      </ul>
    </li>
    <li><a href="#contributing">Contributing</a></li>
    <li><a href="#release-process">Release Process</a></li>
    <li><a href="#design">Design</a></li>
  </ol>
</details>

# Getting Started

## Prerequisites

- [Google Chrome](https://www.google.com/chrome/)
- [Node](https://nodejs.org/en/download)
- [Yarn](https://yarnpkg.com/getting-started/install)

## Setup

1. Install the dependencies: `yarn install`
2. Add an `.env.dev` file to the root folder with the following variables:

- `VITE_DRUMKIT_API_URL=http://localhost:5000`
- `VITE_DRUMKIT_AUTH_URL=http://localhost:5173`

3. If you want to enable Sentry, then add [`VITE_SENTRY_DSN`](https://axle-technologies.sentry.io/settings/projects/vulcan/keys/), which is the vulcan dsn.
4. We'll also use `SENTRY_AUTH_TOKEN` eventually, which is the account auth token to upload sourcemaps (WIP).
5. Add an `.env.production` file to the root folder with the following variables:

- `VITE_DRUMKIT_API_URL=https://api.drumkit.ai`
- `VITE_DRUMKIT_AUTH_URL=https://app.drumkit.ai`

6. Add an `.env.staging` file to the root folder with the following variables:

- `VITE_DRUMKIT_API_URL=https://api.drumkit.ai`
- `VITE_DRUMKIT_AUTH_URL=https://app.drumkit.ai`

7. Populate alexandria (submodule): `git submodule update –init –recursive`. If this fails, go to vulcan .git folder and make sure the submodule url is correct!
8. In order to do step 9, you need a bearer token. Get this from the network tab in the chrome dev tools after signing into the Drumkit sidebar.
9. You need a TMS integration to populate sidebar. Run this Postman call:

- POST http://localhost:5000/integrations/tms/onboard
- Header: Authorization: Bearer <token from step 8>
- Body:

```json
{
  "TODO": "Ask the team for this JSON"
}
```

10. Enable hot reloading for running on dev: `yarn run dev`

- Note that when you update `manifest.ts`, you must restart Node for the manifest changes to be reflected in the built `/dist` folder.

## Running the Extension

### Local

Load the extension:

1. Open the Chrome browser and navigate to `chrome://extensions`.
2. Enable "Developer mode" by clicking the toggle switch.
3. Click on "Load unpacked" and select the Vulcan dist's directory in
   the root folder. Drumkit should now be loaded and visible in the
   extensions list.
4. Subsequent code updates require you to click the extension's
   refresh toggle on `chrome://extensions` after the rebuild of the
   dist folder.

If you see a blank "Loading..." screen, head over to the Drumkit Portal, sign in, and then try the sidebar again.

- local: http://localhost:3000/login (make sure the Drumkit API is running on http://localhost:5000)
- prod: https://app.drumkit.ai/

### Staging

Run `yarn run staging` to generate a staging-ready `dist` folder locally, then upload to `chrome://extensions`. This version of Drumkit is hooked up to the Drumkit staging backend

### Production

Run `yarn run build` to generate a production-ready `dist` folder.

# Contributing

1. We use [Linear](https://linear.app/axleapi) for issue tracking /
   product development.
2. We use [Notion](https://www.notion.so/axleapi) for documentation or
   lack thereof.
3. We use [Slack](https://axleapi.slack.com/ssb/redirect) for
   communication.

# Release Process

1. PRs are merged into `main` with changesets
2. A new "Release" PR is created
3. "Release" PR is tested and approved
   - Tip: Given Github rules, you may need to locally download `changeset-release/main` to your computer locally and then run `git commit --allow-empty -m "trigger checks"` and `git push` in order to trigger the linter and testing CI/CD process on the "Release" PR
   - Tip: In order to test the PR, please download it locally and build the production-ready version by running `yarn run build`
4. Once "Release" PR is approved and merged, go download the latest `dist.zip` from Releases page on Github at https://github.com/drumkitai/vulcan/releases
5. Log into "<EMAIL>" and go to the Chrome Extension Store account at https://chrome.google.com/webstore/devconsole/5d51f8fc-a86b-4ef9-afea-af544c69323b/ilafmdbednnamihpnofibohjpfidgjjc/edit/package and upload the `dist.zip` under "Package" and click "Submit"

# Design

## Error Handling

There are 2 slightly different approaches to Sentry initialization and error capture depending on the different contexts of the extension:

1. **Content Scripts:** Uses the [recommended explicit `BrowserClient` initialization for Chrome extensions](https://docs.sentry.io/platforms/javascript/best-practices/shared-environments/). Must use `initializeSentry` and `captureException` utility functions
2. **Sidepanel Context:** Same as above.
3. **Background worker**: Because the background worker operates in a completely separate context from the webpage, it requires its own Sentry initialization. This uses the standard `Sentry.init` and `Sentry.captureException` SDK funcs

> ⚠️
> Thrown exceptions are not automatically captured for background worker scripts. You must use explicit captures, e.g. `try { ... } catch (e) { Sentry.captureException }`
> See examples in src/pages/background/modules/e2open.ts and src/pages/background/modules/freightview.ts

```tsx
// src/pages/background/index.ts
function initializeSentryForBackground() {
  Sentry.init({
    dsn: SENTRY_DSN,
    environment: ENVIRONMENT,
    release: `${APP_VERSION}-${ENVIRONMENT}`,
    integrations: [
      captureConsoleIntegration({
        levels: ['error', 'warn'],
        ...Sentry.getDefaultIntegrations({}),
      }),
    ],
    tracesSampleRate: 0.5,
  });
}
```

# Development Tips

### Debugging Sidepanel

"The Side Panel API allows extensions to display their own UI in the side panel, enabling persistent experiences that complement the user's browsing journey."--[Docs](https://developer.chrome.com/docs/extensions/reference/api/sidePanel)

Once the sidepanel is open, you can right-click and open the inspector to see the network requests and console logs like any other webpage. However, in order to see the initial startup requests/logs, do the following:

1. Open Drumkit sidepanel
1. Right-click on the panel then Inspect
1. Then hit Cmd/Ctrl + R inside the sidepanel and _without clicking/focusing anywhere else on the browser_, you can view the sidepanel startup network/console logs.

Any other sequence will refresh the page and close the panel-specific inspector, and the context menu when you right click on the side panel disables the Reload option. It may also take a couple tries for this to work.

### Debugging Background Script

"An optional manifest key used to specify a javascript file as the extension service worker. A service worker is a background script that acts as the extension's main event handler."[Docs](https://developer.chrome.com/docs/extensions/reference/manifest/background)

1. Open chrome://extensions
1. Click the hyperlinked "service worker" under the unpacked, local extension to open inspector.
