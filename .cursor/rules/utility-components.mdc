---
description: Utility components
globs:
alwaysApply: true
---

Always use utility components like `<Flex>` and `<Grid>` instead of `<div className="flex">` or `<div className="grid">`. You should use those components' attributes as well for standardization and readability, such as `<Flex wrap="none" direction="none" ...>` or `<Grid cols="2" gap="sm" ...>`.


Always use `<Typography>` when adding text to components instead of `<p>`, `<h1>`, `<h2>` ... or non-semantic tags like wrapping text on `<div>`. You can format your text with props on the Typography component according to your needs, like `<Typography variant="h2" size="sm">`.


@layout/index.tsx
@typography/index.tsx
