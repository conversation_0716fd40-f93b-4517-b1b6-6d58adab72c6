---
description: Proper way to handle opening external URLs
globs:
alwaysApply: true
---

When handling components with URLs that need to opened in new tabs, please implement the `openExternalUrl` method from `SidebarStateContext` instead of simply using `window.open`. We have this solution in place so Drumkit wrapper that have SDKs can use their appropriate function.

Example:
```
const {
  currentState: { openExternalUrl },
} = useContext(SidebarStateContext);

const openUrl = async () => {
  openExternalUrl('https://www.my-url.com');
};
```

@src/alexandria/contexts/sidebarStateContext.tsx
