---
description: Tailwind color palette
globs:
alwaysApply: true
---

Always use colors named in `tailwind.config.js` as CSS variables in order to automatically support our Dark Theme feature. When using colors for text, background, borders or similar classes you should:
- Use semantic colors like `success`, `error`, `info` or `warning` respectively in place of `green`, `red`, `blue` or `yellow`.
- Use `brand` colors for our main brand color rather than orange.
  - e.g. `text-orange-200` becomes `text-brand-200`
- Use `accent` colors for blue tones associated to our AI features.
  - e.g. `text-blue-200` becomes `text-accent-200`
- Use `neutral` colors for any color in the black-white range.
  - e.g. `text-white` becomes `text-neutral-50`, `text-black` becomes `text-neutral-900` and `text-gray-200` becomes `text-neutral-200`.
- Use `input` color for inputs rather than mixing them with other colors
  - e.g. `<input className="text-gray-600 bg-gray-200">` becomes `<input className="text-input-text bg-input-bg">`


@Global.css
