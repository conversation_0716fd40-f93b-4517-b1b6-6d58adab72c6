const { TextEncoder, TextDecoder } = require('util');

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

Object.assign(global, { TextDecoder, TextEncoder });
Object.assign(global, require('jest-chrome'));

// Mock window.matchMedia to prevent TypeError in tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
  })),
});

jest.mock('@constants/DrumkitApiUrl', () => ({
  DRUMKIT_API_URL: 'mock_url',
}));

jest.mock('@constants/DrumkitAuthUrl', () => ({
  DRUMKIT_AUTH_URL: 'mock_url',
}));

jest.mock('@constants/SentryDsn', () => ({
  SENTRY_DSN: 'mock_sentry_dsn',
}));

jest.mock('@constants/SkipIngestEmail', () => ({
  SKIP_INGEST_EMAIL: 'mock_skip_ingest_email',
}));

jest.mock('@constants/PosthogApiKey', () => ({
  POSTHOG_API_KEY: 'mock_posthog_api_key',
}));

jest.mock('@constants/Environment', () => ({
  ENVIRONMENT: 'development',
}));

jest.mock('@constants/AppVersion', () => ({
  APP_VERSION: '0.0.1',
}));

jest.mock('@auth/AuthService', () => ({
  getCurrentUser: jest.fn().mockResolvedValue({ email: '<EMAIL>' }),
  hasValidUserAuth: jest.fn().mockResolvedValue(true),
}));

// Mock PostHog to prevent initialization errors in tests
jest.mock('posthog-js/react', () => ({
  PostHogProvider: ({ children }) => children,
  usePostHog: () => ({
    capture: jest.fn(),
    identify: jest.fn(),
  }),
}));

// Mock posthog-js recorder import
jest.mock('posthog-js/dist/recorder', () => ({}));

// Do what you need to set up your test
console.log('setup test: jest.setup.js');
