{"printWidth": 80, "trailingComma": "es5", "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "jsxSingleQuote": true, "arrowParens": "always", "bracketSpacing": true, "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^react(.*)", "<THIRD_PARTY_MODULES>", "^@(alexandria|auth|constants|contexts|components|css|hooks|lib|pages|utils|src)/(.*)$", "^(auth|constants|contexts|components|css|hooks|icons|lib|pages|types|utils|src)/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}