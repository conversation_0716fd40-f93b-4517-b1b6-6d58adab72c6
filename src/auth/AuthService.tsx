import { AuthUser } from 'types/AuthUser';
import { Maybe } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

import { ChromeEvents } from '../pages/content/src/types';

export default class AuthService {
  // TODO: what is the exact return type here from the JSON?
  static async logOut(): Promise<Maybe<any>> {
    try {
      await chrome.runtime.sendMessage({
        command: ChromeEvents.RemoveAuthCookie,
      });
    } catch (e) {
      if ((e as Error).message === 'Extension context invalidated.') {
        throw e;
      }

      captureException(e, { functionName: 'logOut' });

      return null;
    }
  }

  static async getCurrentUser(): Promise<Maybe<AuthUser>> {
    try {
      const response = await chrome.runtime.sendMessage({
        command: ChromeEvents.GetAuthCookie,
      });

      return JSON.parse(response);
    } catch (e) {
      if ((e as Error).message === 'Extension context invalidated.') {
        throw e;
      }

      captureException(e, { functionName: 'getCurrentUser' });
      return null;
    }
  }

  static async hasValidUserAuth(): Promise<boolean> {
    const response = await chrome.runtime.sendMessage({
      command: ChromeEvents.GetAuthCookie,
    });

    // if there's no cookie, it can't be valid
    if (!response) return false;

    // try parsing the cookie to check if it's valid
    try {
      JSON.parse(response);
      return true;
    } catch {
      return false;
    }
  }

  static async getAuthHeader(): Promise<{
    Authorization: string;
  }> {
    const user = await AuthService.getCurrentUser();

    if (!user || !user.access_token) {
      AuthService.logOut();

      return { Authorization: 'Bearer ' };
    }

    return { Authorization: 'Bearer ' + user.access_token };
  }
}
