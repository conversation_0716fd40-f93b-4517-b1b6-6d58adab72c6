import { useContext } from 'react';

import DRUMKIT_AUTH_URL from '@constants/DrumkitAuthUrl';

import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { SidebarStateContext } from 'contexts/sidebarStateContext';

export default function Login(): React.JSX.Element {
  const {
    currentState: { isChromeSidePanel },
  } = useContext(SidebarStateContext);

  return (
    <div id='drumkit-content-view-root'>
      <Flex
        direction='col'
        justify='center'
        align='center'
        gap='2xl'
        className='h-screen px-4'
      >
        <a
          type='button'
          href={`${DRUMKIT_AUTH_URL}/login`}
          target='_blank'
          className='border border-neutral-900 text-neutral-900 rounded-full shadow-md cursor-pointer py-2 px-8 hover:bg-neutral-200'
        >
          Sign into Drumkit
        </a>
        <Typography align='center'>
          {isChromeSidePanel
            ? "Close and reopen Drumkit when you're done."
            : "Refresh the page when you're done."}
        </Typography>
      </Flex>
    </div>
  );
}
