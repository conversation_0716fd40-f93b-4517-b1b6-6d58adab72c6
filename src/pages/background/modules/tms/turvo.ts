import * as Sentry from '@sentry/browser';

import {
  ChromeEvents,
  Identifier,
  IdentifierType,
} from '@pages/content/src/types';

import { Maybe } from 'types/UtilityTypes';

import { isSuppressedSidepanelError } from '../util';

export async function handleParseForTurvo(tabId: number) {
  chrome.tabs
    .sendMessage(tabId, {
      command: ChromeEvents.ParseTurvoPro,
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        Sentry.captureException(
          new Error('error sending parse turvo pro message: ' + errorMessage)
        );
      }
    });
}

export function findAndExtractTurvoProNumber(): Maybe<Identifier> {
  const spans = document.querySelectorAll(
    'span.tu-breadcrumbs-caption.fleet-body-bold'
  );

  for (const span of spans) {
    const textContent = span.textContent?.trim() || '';
    const match = textContent.match(/^#(.+)$/);
    if (match) {
      return { type: IdentifierType.LoadId, value: match[1] };
    }
  }
  return null;
}
