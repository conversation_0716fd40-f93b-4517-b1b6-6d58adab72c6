import * as Sentry from '@sentry/browser';

import { applyParsedIdentifier } from '@pages/background';
import {
  ChromeEvents,
  Identifier,
  IdentifierType,
} from '@pages/content/src/types';

import { Maybe } from 'types/UtilityTypes';

import { isSuppressedSidepanelError } from '../util';

export function findAndExtractStarkShipmentId(
  tabUrl: string
): Maybe<Identifier> {
  if (!tabUrl) {
    return null;
  }

  // Parse shipment ID from URL pattern: https://apps-staging.transfix.io/#/shipment-details/1734986
  // or https://apps.transfix.io/#/shipment-details/1734986
  const match = tabUrl.match(/\/shipment-details\/(\d+)/);
  if (match) {
    return {
      type: IdentifierType.LoadId,
      value: match[1],
    };
  }

  return null;
}

export async function handleParseForStark(tabId: number, tabUrl: string) {
  const urlMatch = tabUrl.match(/\/shipment-details\/(\d+)/);

  let newQeIdentifier: Maybe<Identifier> = null;
  if (urlMatch) {
    newQeIdentifier = {
      type: IdentifierType.LoadId,
      value: urlMatch[1],
    };
  }

  applyParsedIdentifier(tabId, newQeIdentifier);

  chrome.tabs
    .sendMessage(tabId, {
      command: ChromeEvents.ParseStarkShipment,
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        Sentry.captureException(
          new Error('error sending parse stark shipment message: ' + errorMessage)
        );
      }
    });
}

export function registerStarkLoadChangeListener() {
  let lastSentLoadId: string | null = null;

  function extractStarkLoadId(): string | null {
    // Find the side panel root — only exists when OPEN
    const sidePanel = document.querySelector(
      'section.card.sidebar.active, section[data-test="sidebar"]'
    );

    if (!sidePanel) {
      // Panel closed → reset state
      return null;
    }

    // Extract ID ONLY inside the side panel
    const linkInsidePanel = sidePanel.querySelector<HTMLAnchorElement>(
      'a[href*="shipment-details"]'
    );

    if (linkInsidePanel) {
      const text = linkInsidePanel.textContent?.trim() || "";
      const match = text.match(/#?(\d{5,10})/);
      if (match) return match[1];
    }

    return null;
  }

  const callback: MutationCallback = function () {
    try {
      const loadId = extractStarkLoadId();

      if (loadId) {
        if (loadId === lastSentLoadId) return;

        chrome.runtime.sendMessage({
          command: "IdentifierUpdatedFromContentScript",
          parsedIdentifier: {
            type: "loadId",
            value: loadId,
          },
          source: "StarkTablePageObserver",
        });

        lastSentLoadId = loadId;
      } else {
        lastSentLoadId = null;
      }
    } catch (err) {
      console.error("Stark MutationObserver error:", err);
    }
  };

  const observer = new MutationObserver(callback);

  // Observe changes in the container that wraps the side panel
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  // run once on load
  setTimeout(() => callback([], observer), 500);
}
