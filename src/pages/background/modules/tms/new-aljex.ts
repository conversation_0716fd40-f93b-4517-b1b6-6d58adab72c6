import * as Sentry from '@sentry/browser';

import {
  ChromeEvents,
  Identifier,
  IdentifierType,
} from '@pages/content/src/types';

import { Maybe } from 'types/UtilityTypes';

import { isSuppressedSidepanelError } from '../util';

export function findAndExtractNewAljexProNumber(): Maybe<Identifier> {
  if (document.querySelector('div[data-v-02743b7e]')) {
    const allDivs = document.querySelectorAll('div[data-v-02743b7e]');
    for (const div of allDivs) {
      if (div.textContent?.includes('Pro#')) {
        const textContent = div.textContent || '';
        const match = textContent.match(/Pro#\s*(\d+)/);
        return match ? { type: IdentifierType.LoadId, value: match[1] } : null;
      }
    }
  }
  return null;
}

export async function handleParseForNewAljex(tabId: number) {
  chrome.tabs
    .sendMessage(tabId, {
      command: ChromeEvents.ParseNewAljexPro,
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        Sentry.captureException(
          new Error(
            'error sending parse new aljex pro message: ' + errorMessage
          )
        );
      }
    });
}
