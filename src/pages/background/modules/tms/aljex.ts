import { applyParsedIdentifier } from '@pages/background';
import { Identifier, IdentifierType } from '@pages/content/src/types';

import { Maybe } from 'types/UtilityTypes';

export function parseAljexLoadIdentifier() {
  return document.querySelector('#headpro')?.textContent?.trim();
}

// Parse the PRO ID from the passed in Aljex tab (should work globally for all Aljex boards)
export async function handleParseForAljex(tabId: number) {
  const aljexLoadId = await chrome.scripting.executeScript({
    target: { tabId },
    func: parseAljexLoadIdentifier,
  });

  let newAljexIdentifier: Maybe<Identifier> = null;

  if (aljexLoadId?.[0]?.result) {
    newAljexIdentifier = {
      value: aljexLoadId?.[0]?.result ?? '',
      type: IdentifierType.LoadId,
    };
  }

  applyParsedIdentifier(tabId, newAljexIdentifier);
}
