import * as Sentry from '@sentry/browser';

import {
  ChromeEvents,
  Identifier,
  IdentifierType,
} from '@pages/content/src/types';

import { Undef } from 'types/UtilityTypes';

import { isSuppressedSidepanelError } from '../util';

export function findAndExtractRelayProNumber(
  tabUrl: string
): Undef<Identifier> {
  if (!tabUrl) {
    return undefined;
  }

  // Try parsing both IDs because their availability depends on the TMS webpage we're on
  const relayLoadIdentifier =
    tryParsingRelayExternalTMSID(tabUrl) ??
    tryParsingRelayFreightTrackingID(tabUrl);

  if (!relayLoadIdentifier) {
    return undefined;
  }

  return {
    type: IdentifierType.LoadId,
    value: relayLoadIdentifier,
  };
}

// ExternalTMSID = Load # for Relay
function tryParsingRelayExternalTMSID(tabUrl: string) {
  // URLs are in the form of:
  //  * https://{training.}relaytms.com/sourcing/load_board_load_detail/{(2|3)\d{6,7}}
  //  * https://{training.}relaytms.com/planning_board/stop_management/{(2|3)\d{6,7}}
  if (
    tabUrl.includes('load_board_load_detail') ||
    tabUrl.includes('planning_board/stop_management')
  ) {
    const match = tabUrl.match(/((2|3)\d{5,7})/);
    if (match) {
      return match[1];
    }
  }

  if (tabUrl.includes('planning_board')) {
    const elements = document.getElementsByClassName('col load-number');
    if (elements.length === 0) {
      return '';
    }
    const element = elements[0] as HTMLElement;
    return element?.innerHTML?.trim() ?? '';
  }

  if (tabUrl.includes('tracking_load_detail')) {
    return (
      document
        .querySelector('.relay-reference-number.d-inline')
        ?.textContent?.trim() ?? ''
    );
  }

  if (tabUrl.includes('hub')) {
    const match = tabUrl.match(/((2|3)\d{5,7})/);
    if (match) {
      return match[1];
    }
  }

  return null;
}

// FreightTrackingID = Booking ID for Relay
function tryParsingRelayFreightTrackingID(tabUrl: string) {
  if (tabUrl.includes('load_board_load_detail')) {
    return (
      document.querySelector('.text-info.pl-3.d-inline')?.textContent?.trim() ??
      ''
    );
  }

  if (tabUrl.includes('tracking_load_detail')) {
    const match = tabUrl.match(/(\d{7})/);
    if (match) {
      return match[1];
    }
  }

  if (tabUrl.includes('hub')) {
    const header = document.querySelector(
      'tr[data-phx-id="c1-load-signature"][data-phx-component="1"]'
    );
    const numElement =
      header?.querySelector('span.tw-text-neutral-500.tw-text-xs') ??
      header?.querySelector('span:first-of-type');

    return numElement?.textContent?.trim() ?? '';
  }

  return null;
}

export async function handleParseForRelay(tabId: number) {
  chrome.tabs
    .sendMessage(tabId, {
      command: ChromeEvents.ParseRelayPro,
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        Sentry.captureException(
          new Error('error sending parse relay pro message: ' + errorMessage)
        );
      }
    });
}

/**
 * @description
 * Observe when Relay adds or removes 'phxModal' to DOM to trigger a refresh of OpenDock form.
 */
export function registerRelayModalListener(url: string, tabId: number) {
  // Initially assume it's planning board's modal
  let elementID = 'phxModal';

  if (url.includes('relaytms.com/hub')) {
    elementID = 'slide-over';
  }

  const isNodeValid = (node: Node) =>
    node.nodeType === Node.ELEMENT_NODE &&
    (node as HTMLElement).id === elementID;

  // Function to execute when mutations are observed
  const callback: MutationCallback = function (mutationsList) {
    mutationsList.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (isNodeValid(node)) {
            chrome.runtime
              .sendMessage({
                command: 'ParseRelayPro', // Not using enum to prevent Uncaught ReferenceError
                tabId,
              })
              .catch((err) => {
                console.error('Error sending parse relay message:', err);
              });
          }
        });
      }
    });
  };

  const observer = new MutationObserver(callback);

  const config: MutationObserverInit = {
    childList: true,
    subtree: true,
  };

  const targetNode = document.querySelector('#planning-board') || document.body;

  observer.observe(targetNode, config);
}
