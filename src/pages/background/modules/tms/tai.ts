import { applyParsedIdentifier } from '@pages/background';
import { Identifier, IdentifierType } from '@pages/content/src/types';

import { Maybe } from 'types/UtilityTypes';

export function handleParseForTai(tabId: number, tabUrl: string) {
  const match = tabUrl.match(/\/shipment-details\/(\d+)/);
  const taiLoadId = match?.[1] ?? null;

  let newTaiIdentifier: Maybe<Identifier> = null;
  if (taiLoadId) {
    newTaiIdentifier = {
      value: taiLoadId,
      type: IdentifierType.LoadId,
    };
  }

  applyParsedIdentifier(tabId, newTaiIdentifier);
}
