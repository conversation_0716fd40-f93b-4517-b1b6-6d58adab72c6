# Quoting Portal Integration Playbook

Please view [Notion](https://www.notion.so/drumkitai/Quoting-Bidding-Portal-Integration-Playbook-20e2b16b087a80759840f6a361459873) for latest updates.

## Overview

This playbook explains how to integrate new quoting portals into our system. A quoting portal is a website where users can submit quotes for freight shipments. Examples include FreightView and E2Open.

## User Flow

1. User visits portal
2. Background script detects visit
3. <PERSON>ript parses quote data
4. Data sent to sidepanel
5. User enters quote amount
6. Quote submitted back to portal

## Testing Checklist

1. [ ] Portal detection works
2. [ ] Quote data parsing is accurate
3. [ ] Quote submission works
4. [ ] Error handling works
5. [ ] Change detection works (if needed)
6. [ ] Sidepanel updates correctly
7. [ ] Success/error messages are clear

## Structure

### 1. Core Components

#### 1.1 Background Script (`src/pages/background/modules/[portal].ts`)

- Main integration file for each portal
- Handles:
  - Detecting when user visits portal
  - Parsing quote request data
  - Submitting quotes
  - Sending data to sidepanel

#### 1.2 Portal Class (`src/alexandria/lib/hosts/quoting/[portal].ts`)

- Defines portal-specific behavior
- Implements `QuotingPortal` interface
- Handles:
  - Checking if quote can be submitted
  - Defining submit action name
  - Portal-specific validation

#### 1.3 Interface (`src/alexandria/lib/hosts/quoting/interface.ts`)

- Defines common interface for all portals
- Provides factory function to create portal instances

## Implementation Steps

### 1. Create Portal Class

```typescript
// src/alexandria/lib/hosts/quoting/[portal].ts
export class NewPortal implements QuotingPortal {
  submitAction = 'new-portal-submit-quote';

  canSubmit(tab: Undef<chrome.tabs.Tab>, html: string): boolean {
    // 1. Check if URL matches portal
    // 2. Check if page has required elements
    // 3. Return true if quote can be submitted
  }
}
```

### 2. Add Portal to Factory

```typescript
// src/alexandria/lib/hosts/quoting/interface.ts
export function newQuotingPortal(url: Undef<string>): Undef<QuotingPortal> {
  if (parsedUrl.origin?.includes('your-portal.com')) {
    return new NewPortal();
  }
}
```

### 3. Create Background Script

```typescript
// src/pages/background/modules/[portal].ts

// 1. Create quote request parser
function parseQuoteRequestData() {
  // Parse quote data from page
  // Return CreateQuoteRequestSuggestionRequest
}

// 2. (Optional, varies by portal)

// 2. Create quote submitter
async function submitQuoteToNewPortal(data: SubmitQuoteToPortalData) {
  // Fill and submit quote form
  // Return PortalActionResult
}

// 3. Add message listener
chrome.runtime.onMessage.addListener((message, _, sendResponse) => {
  if (message.action === NewPortalSubmitAction) {
    // Handle quote submission
  }
});
```

## Important Considerations

### 1. Page Structure Types

#### Single Quote Page (Like E2Open)

- One quote per page
- No need for change detection
- Simpler implementation
- Example: E2Open's detailed quote page

#### Multi-Quote Page (Like FreightView)

- Multiple quotes on one page
- Requires change detection
- More complex implementation
- Example: FreightView's quote list

### 2. Change Detection

Some portals list multiple quote requests on one page (e.g. FreightView) so we need to detect when the user changes their selected quote request.

#### For Single Quote Pages

- No change detection needed
- Parse data once when page loads
- Simpler implementation

#### For Multi-Quote Pages

```typescript
// Required for pages with multiple quotes
function registerQuoteRequestChangeListener(tabId: number) {
  const observer = new MutationObserver(() => {
    // 1. Check if quote selection changed
    // 2. Parse new quote data
    // 3. Send to sidepanel
  });

  observer.observe(document.body, {
    attributes: true,
    subtree: true,
  });
}
```

### 2.1 Understanding MutationObserver for Quote Selection

#### Why We Need MutationObserver

- Some portals (like FreightView) show multiple quotes on one page
- Users can click different quotes to select them
- We need to detect when user changes their selection
- The tab doesn't reload when selection changes so `chrome.tabs.onUpdated` listener is insufficient

#### How MutationObserver Works

```typescript
function registerQuoteRequestChangeListener(tabId: number) {
  // 1. Create observer
  const observer = new MutationObserver(() => {
    // 2. Check if any quote is selected
    const hasSelectedQuote =
      document.querySelector('.quote-item--selected') !== null;

    // 3. If quote is selected, parse its data
    if (hasSelectedQuote) {
      const quoteData = parseQuoteRequestData();

      // 4. Send data to sidepanel
      chrome.runtime.sendMessage({
        action: 'updateQuoteRequestData',
        data: quoteData,
        targetTabId: tabId,
      });
    }
  });

  // 5. Start observing
  observer.observe(document.body, {
    attributes: true, // Watch for attribute changes
    subtree: true, // Watch all child elements
  });
}
```

#### Real Example from FreightView

```typescript
// From freightview.ts
function registerQuoteRequestChangeListener(tabId: number) {
  const observer = new MutationObserver(() => {
    // Check if any shipment is selected
    const hasSelectedShipment =
      document.querySelector('.shipment-item--selected') !== null;

    // Parse data if selection exists
    const parseResult = hasSelectedShipment
      ? window.parseQuoteRequestData?.()
      : { data: null };

    // Send to sidepanel
    chrome.runtime.sendMessage({
      action: 'updateQuoteRequestData',
      data: parseResult?.data ?? null,
      targetTabId: window.__tabId,
    });
  });

  // Watch for class changes (selection changes)
  observer.observe(document.body, {
    attributes: true,
    attributeFilter: ['class'], // Only watch class changes
    subtree: true,
  });
}
```

#### Best Practices for MutationObserver

1. **Performance**
   - Use specific `attributeFilter` when possible
   - Only watch necessary elements
   - Debounce callback if needed

2. **Memory Management**
   - Store observer reference
   - Disconnect when not needed
   - Clean up on page unload

3. **Error Handling**
   - Wrap callback in try-catch
   - Handle missing elements
   - Log errors for debugging

4. **Testing**
   - Test selection changes
   - Test multiple rapid changes
   - Test error cases

### 4. Error Handling

```typescript
try {
  // Portal operations
} catch (error) {
  return {
    success: false,
    error: error.message,
    partialSuccess: true // If inputting the values succeeded but not the form submission
  };
}
```

## Common Issues

1. **ReferenceError**
   - Because content scripts are injected into tabs, Chrome requires them to be entirely self-contained. They cannot directly reference external types or functions.

   ❌ **Bad:**

   ```typescript
   // This will fail because it references external types and functions
   chrome.scripting.executeScript({
     target: { tabId },
     func: (data: SubmitQuoteToPortalData) => {
       // ❌ Error: SubmitQuoteToPortalData is not defined
       // ❌ Error: parseQuoteRequestData is not defined
       const quoteData = parseQuoteRequestData();
       return quoteData;
     },
   });

   function parseQuoteRequestData() {
     // Implement
   }
   ```

   ✅ **Good**: Pass function to injected script

   ```typescript
   chrome.scripting.executeScript({
     target: { tabId },
     func: handleQuote,
     // args: [arg1, arg2] // Arguments if applicable
   });

   // Main function that uses local definitions
   function handleQuote() {
     function helper() {
       return 42;
     }

     return helper();
   }
   ```

   ✅ **Good:** Function defined _and_ called in injected script

   ```typescript
   chrome.scripting.executeScript({
     target: { tabId },
     func: () => {
       // Define types & helpers locally
       function helper() {
         return 42;
       }

       // Main function that uses local definitions
       function handleQuote() {
         return helper();
       }

       return handleQuote;
     },
   });
   ```

   ✅ **Good:** Define function & attach to window so it can be called by multiple injected scripts.
   Relevant for multi-quote pages where MutationObserver script also needs access to function
   (see Change Detection -> For Multi-Quote Pages)

   ```typescript
   // 1. First inject the types and functions
   await chrome.scripting.executeScript({
     target: { tabId },
     func: () => {
       window.parseQuoteRequestData = function () {
         // Implementation here
       };
     },
   });

   // 2. Then use them in subsequent injections
   await chrome.scripting.executeScript({
     target: { tabId },
     func: () => {
       // Now we can use the function we attached to window
       const result = window.parseQuoteRequestData();
       return result;
     },
   });
   ```

2. Serialization of Script Inputs & Outputs

   ❌ **Bad:** Passing Date objects or complex errors directly

   ```typescript
   chrome.scripting.executeScript({
     target: { tabId },
     func: () => {
       try {
         // ❌ Error: Date objects don't serialize properly
         const date = new Date();

         // ❌ Error: Custom errors don't serialize properly
         throw new Error('Something went wrong');

         return {
           date, // Will be serialized as string, losing Date methods
           error: new Error('Failed'), // Will lose error properties
         };
       } catch (error: any) {
         // ❌ ReferenceError from calling captureException inside injected script
         captureException(error);
       }
     },
   });
   ```

   ✅ **Good:** Serialize dates and errors properly

   ```typescript
   chrome.scripting.executeScript({
     target: { tabId },
     func: () => {
       try {
         // 1. For dates, use ISO string
         const date = new Date();
         const serializedDate = date.toISOString();

         // 2. Return serializable data
         return {
           date: serializedDate,
         };
       } catch (error) {
         // 3. Explicitly serialize any caught errors
         return {
           error: error?.message
         };
       }
     }
   });

   // 4. In the background script, reconstruct if needed
   const result = await chrome.scripting.executeScript({...});
   if (result[0].result.error) {
        captureException(error)
    }

   // Reconstruct date if needed
   const date = new Date(result[0].result.date);
   ```

3. **Timing Issues**
   - Wait for tab to load completely by using [`changeInfo.status == "completed"`](./freightview.ts:46)
   - Use appropriate timeouts
   - Handle race conditions

4. **Element Selection**
   - Use robust selectors
   - Handle missing elements
   - Consider dynamic content

5. **Data Parsing**
   - Handle different date formats
   - Validate required fields
   - Handle missing data

6. **Form Submission**
   - Trigger proper events
   - Handle disabled buttons
   - Verify submission success

## Best Practices

1. **Code Organization**
   - Keep portal-specific code separate
   - Use clear naming conventions
   - Document complex logic

2. **Error Handling**
   - Use try-catch blocks
   - Capture errors to Sentry with context
   - Provide user feedback with toasts

3. **Performance**
   - Minimize DOM operations
   - Use efficient selectors
   - Debounce change detection

## Example Implementation

See `freightview.ts` and `e2open.ts` for complete examples of:

- Multi-quote page implementation (FreightView)
- Single-quote page implementation (E2Open)
- Error handling
- Data parsing
- Form submission

## Opportunities for Improvement

- Move parsing to backend for quicker turnaround on bug fixes & updating obsolete code
- Move more of the logic in `modules/{portal}.ts` to `QuotingPortals` class to standardize future portal integrations
  - Originally we were limited by chrome script's `ReferenceError` requiring scripts to be self sufficient, but FreightView's `window.parseQuoteRequestData` shows workarounds are possible
- Add webpage to `SidebarStateContext` and/or add mutation observer to reset submit disabled status
  - Example: E2Open Make Detailed Offer flow has is a 2-page process. Drumkit parses quote info from first page. If user clicks `Get Quick Quote` on that first page, submit buttons are disabled (as they should be). When user navigates to second page, submit buttons should be reset to enabled.
