import { Identifier, IdentifierType } from '@pages/content/src/types';

import { Maybe } from 'types/UtilityTypes';

export function findAndExtractGmailThreadID(): Maybe<Identifier> {
  const emailTitle = document.querySelector('h2[data-legacy-thread-id]');
  if (emailTitle) {
    return {
      type: IdentifierType.ThreadId,
      value: emailTitle.getAttribute('data-legacy-thread-id') ?? '',
    };
  }
  return null;
}
