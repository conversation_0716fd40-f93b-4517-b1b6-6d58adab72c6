import { PortalParseQuoteRequestResult } from 'types/ChromeScript';

// Extend the Window interface to include the custom flags to prevent duplicate registrations of event listeners
declare global {
  interface Window {
    __tabId: number;
    __quoteRequestChangeListenerAdded?: boolean;
    parseQuoteRequestData?: () => PortalParseQuoteRequestResult;
  }
}

const SUPPRESSED_SIDEPANEL_ERRORS = [
  // Errors expected if tab is no longer valid (e.g. closed, crashed)
  'no tab with id',
  'frame with id 0 was removed',
  'frame with id 0 is showing error page',
  // Errors expected if trying to access urls not allowed by manifest
  'extension manifest must request permission to access',
  // Errors expected if trying to access chrome:// urls
  'cannot access a chrome:// url',
  'cannot access a chrome-extension:// url',
  'the extensions gallery cannot be scripted',
  // Errors expected if side panel isn't open yet
  'receiving end does not exist',
  // Errors expected if tab is not ready (e.g. not finished loading)
  'frame with id 0 is not ready',
];

export const isSuppressedSidepanelError = (error: string): boolean =>
  SUPPRESSED_SIDEPANEL_ERRORS.some((err) => error.includes(err));
