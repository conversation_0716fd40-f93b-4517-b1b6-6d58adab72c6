import type { PortalParseQuoteRequestResult } from 'types/ChromeScript';
import { TransportType } from 'types/QuoteRequest';
import { QuotingPortals } from 'types/enums/Integrations';
import { SuggestionSourceCategories } from 'types/suggestions/QuoteSuggestions';

import { parseQuoteRequestData } from '../quoting-portals/e2open';

// Note: Time values are parsed in the TZ of the local machine, so strings are set to 00:00:00.000Z
// to pass CI/CD, but will fail on machines with different local timezones.
describe('parseQuoteRequestData - Version2Detailed', () => {
  beforeEach(() => {
    document.body.innerHTML = '';
  });

  it('parses a standard QR', () => {
    document.body.innerHTML = `
      <table>
        <tr class="resultrow2">
          <td id="test_loadid_company_offerdate">
            *********<br>
            ACME CORP<br>
            01/01/2025 09:00 CDT
          </td>
          <td id="test_stop_weight_dist_temp">
            2 <br>
            8,172&nbsp;lb<br>
            595&nbsp;mi<br>
            --
          </td>
          <td id="test_servicecategory_servicerequest">
            TL&nbsp;:&nbsp;TL STANDARD,<br>
            TL&nbsp;:&nbsp;GF TL STANDARD,<br>
            IM&nbsp;:&nbsp;IM<br><br>--
          </td>
          <td id="test_equipment_pallet">
            53VAN, RAIL
            <br>No<br>
          </td>
          <td id="test_pickdate">12/31</td>
          <td id="test_origin">
            ACME WAREHOUSE - KINGS MOUNTAIN, NC<br>KINGS MOUNTAIN, NC&nbsp;28086&nbsp;US
          </td>
          <td id="test_dropdate">12/31</td>
          <td id="test_destination">
            SAMS CLUB - HATTIESBURG - IF<br>HATTIESBURG, MS&nbsp;39401&nbsp;US
          </td>
          <td id="test_offer_details">
            16<br>--<br>--0&nbsp;USD<br><br><span id="reservePrice">0.00&nbsp;USD</span>
          </td>
          <td>&nbsp;</td>
        </tr>
      </table>
    `;

    const result = parseQuoteRequestData();
    const expected: PortalParseQuoteRequestResult = {
      data: {
        customer: { name: 'ACME CORP' },
        customerExternalTMSID: '',
        transportType: TransportType.VAN,
        pickupCity: 'KINGS MOUNTAIN',
        pickupState: 'NC',
        pickupZip: '28086',
        pickupDate: '2025-12-31T00:00:00.000Z',
        deliveryCity: 'HATTIESBURG',
        deliveryState: 'MS',
        deliveryZip: '39401',
        deliveryDate: '2025-12-31T00:00:00.000Z',
        sourceCategory: SuggestionSourceCategories.QuotingPortal,
        source: QuotingPortals.E2Open,
        sourceExternalID: 'ACME CORP-*********',
        sourceURL: 'https://na-app.tms.e2open.com/detailedspotmarketoffer.do',
        htmlSnippet: result.data?.htmlSnippet || '',
        distanceMiles: 595,
        fuelSurchargePerMile: null,
        fuelSurchargeTotal: null,
      },
      error: undefined,
    };
    expect(result).toEqual(expected);
  });

  it('parses a row with different equipment type (FLATBED)', () => {
    document.body.innerHTML = `
      <table>
        <tr class="resultrow2">
          <td id="test_loadid_company_offerdate">
            *********<br>
            ACME CORP<br>
            01/01/2025 09:00 CDT
          </td>
          <td id="test_stop_weight_dist_temp">
            2 <br>
            8,172&nbsp;lb<br>
            595&nbsp;mi<br>
            --
          </td>
          <td id="test_servicecategory_servicerequest">
            TL&nbsp;:&nbsp;TL STANDARD,<br>
            TL&nbsp;:&nbsp;GF TL STANDARD,<br>
            IM&nbsp;:&nbsp;IM<br><br>--
          </td>
          <td id="test_equipment_pallet">
            FLATBED<br>No<br>
          </td>
          <td id="test_pickdate">12/31</td>
          <td id="test_origin">
            ACME WAREHOUSE - KINGS MOUNTAIN, NC<br>KINGS MOUNTAIN, NC&nbsp;28086&nbsp;US
          </td>
          <td id="test_dropdate">12/31</td>
          <td id="test_destination">
            SAMS CLUB - HATTIESBURG - IF<br>HATTIESBURG, MS&nbsp;39401&nbsp;US
          </td>
          <td id="test_offer_details">
            16<br>--<br>--0&nbsp;USD<br><br><span id="reservePrice">0.00&nbsp;USD</span>
          </td>
          <td>&nbsp;</td>
        </tr>
      </table>
    `;

    const result = parseQuoteRequestData();
    const expected: PortalParseQuoteRequestResult = {
      data: {
        customer: { name: 'ACME CORP' },
        customerExternalTMSID: '',
        transportType: TransportType.FLATBED,
        pickupCity: 'KINGS MOUNTAIN',
        pickupState: 'NC',
        pickupZip: '28086',
        pickupDate: '2025-12-31T00:00:00.000Z',
        deliveryCity: 'HATTIESBURG',
        deliveryState: 'MS',
        deliveryZip: '39401',
        deliveryDate: '2025-12-31T00:00:00.000Z',
        sourceCategory: SuggestionSourceCategories.QuotingPortal,
        source: QuotingPortals.E2Open,
        sourceExternalID: 'ACME CORP-*********',
        sourceURL: 'https://na-app.tms.e2open.com/detailedspotmarketoffer.do',
        htmlSnippet: result.data?.htmlSnippet || '',
        distanceMiles: 595,
        fuelSurchargePerMile: null,
        fuelSurchargeTotal: null,
      },
      error: undefined,
    };
    expect(result).toEqual(expected);
  });

  it('parses a row with comma-separated distance value', () => {
    document.body.innerHTML = `
      <table>
        <tr class="resultrow2">
          <td id="test_loadid_company_offerdate">
            *********<br>
            BETA INC<br>
            02/02/2025 10:00 CDT
          </td>
          <td id="test_stop_weight_dist_temp">
            1 <br>
            5,000&nbsp;lb<br>
            2,345&nbsp;mi<br>
            --
          </td>
          <td id="test_servicecategory_servicerequest">
            TL&nbsp;:&nbsp;TL STANDARD,<br>
            TL&nbsp;:&nbsp;GF TL STANDARD,<br>
            IM&nbsp;:&nbsp;IM<br><br>--
          </td>
          <td id="test_equipment_pallet">
            FLATBED<br>No<br>
          </td>
          <td id="test_pickdate">12/31</td>
          <td id="test_origin">
            BETA HQ - ALPHA, ZZ<br>ALPHA, ZZ&nbsp;11111&nbsp;US
          </td>
          <td id="test_dropdate">12/31</td>
          <td id="test_destination">
            GAMMA - OMEGA, YY<br>OMEGA, YY&nbsp;22222&nbsp;US
          </td>
          <td id="test_offer_details">
            10<br>--<br>--0&nbsp;USD<br><br><span id="reservePrice">0.00&nbsp;USD</span>
          </td>
          <td>&nbsp;</td>
        </tr>
      </table>
    `;

    const result = parseQuoteRequestData();
    const expected: PortalParseQuoteRequestResult = {
      data: {
        customer: { name: 'BETA INC' },
        customerExternalTMSID: '',
        transportType: TransportType.FLATBED,
        pickupCity: 'ALPHA',
        pickupState: 'ZZ',
        pickupZip: '11111',
        pickupDate: '2025-12-31T00:00:00.000Z',
        deliveryCity: 'OMEGA',
        deliveryState: 'YY',
        deliveryZip: '22222',
        deliveryDate: '2025-12-31T00:00:00.000Z',
        sourceCategory: SuggestionSourceCategories.QuotingPortal,
        source: QuotingPortals.E2Open,
        sourceExternalID: 'BETA INC-*********',
        sourceURL: 'https://na-app.tms.e2open.com/detailedspotmarketoffer.do',
        htmlSnippet: result.data?.htmlSnippet || '',
        distanceMiles: 2345,
        fuelSurchargePerMile: null,
        fuelSurchargeTotal: null,
      },
      error: undefined,
    };
    expect(result).toEqual(expected);
  });

  it('parses a row with inferred REEFER equipment type', () => {
    document.body.innerHTML = `
      <table>
        <tr class="resultrow2">
          <td id="test_loadid_company_offerdate">
            *********<br>
            ACME CORP<br>
            01/01/2025 09:00 CDT
          </td>
          <td id="test_stop_weight_dist_temp">
            2 <br>
            8,172&nbsp;lb<br>
            595&nbsp;mi<br>
            FROZEN
          </td>
          <td id="test_servicecategory_servicerequest">
            TL&nbsp;:&nbsp;TL STANDARD,<br>
            TL&nbsp;:&nbsp;GF TL STANDARD,<br>
            IM&nbsp;:&nbsp;IM<br><br>--
          </td>
          <td id="test_equipment_pallet">
            FLATBED<br>No<br>
          </td>
          <td id="test_pickdate">12/31</td>
          <td id="test_origin">
            ACME WAREHOUSE - KINGS MOUNTAIN, NC<br>KINGS MOUNTAIN, NC&nbsp;28086&nbsp;US
          </td>
          <td id="test_dropdate">12/31</td>
          <td id="test_destination">
            SAMS CLUB - HATTIESBURG - IF<br>HATTIESBURG, MS&nbsp;39401&nbsp;US
          </td>
          <td id="test_offer_details">
            16<br>--<br>--0&nbsp;USD<br><br><span id="reservePrice">0.00&nbsp;USD</span>
          </td>
          <td>&nbsp;</td>
        </tr>
      </table>
    `;

    const result = parseQuoteRequestData();
    const expected: PortalParseQuoteRequestResult = {
      data: {
        customer: { name: 'ACME CORP' },
        customerExternalTMSID: '',
        transportType: TransportType.REEFER,
        pickupCity: 'KINGS MOUNTAIN',
        pickupState: 'NC',
        pickupZip: '28086',
        pickupDate: '2025-12-31T00:00:00.000Z',
        deliveryCity: 'HATTIESBURG',
        deliveryState: 'MS',
        deliveryZip: '39401',
        deliveryDate: '2025-12-31T00:00:00.000Z',
        sourceCategory: SuggestionSourceCategories.QuotingPortal,
        source: QuotingPortals.E2Open,
        sourceExternalID: 'ACME CORP-*********',
        sourceURL: 'https://na-app.tms.e2open.com/detailedspotmarketoffer.do',
        htmlSnippet: result.data?.htmlSnippet || '',
        distanceMiles: 595,
        fuelSurchargePerMile: null,
        fuelSurchargeTotal: null,
      },
      error: undefined,
    };
    expect(result).toEqual(expected);
  });
});
