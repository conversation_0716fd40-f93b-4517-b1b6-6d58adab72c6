import * as Sentry from '@sentry/browser';

import {
  QuoteSidebarPortName,
  UpdateQuoteRequestDataAction,
} from 'constants/BackgroundScript';
import { CreateQuoteRequestSuggestionRequest } from 'lib/api/createQuoteRequestSuggestion';
import { FreightViewSubmitAction } from 'lib/hosts/quoting/freightview';
import { PortalParseQuoteRequestResult } from 'types/ChromeScript';
import { Address, QuoteRequest, TransportType } from 'types/QuoteRequest';
import { Maybe, Undef } from 'types/UtilityTypes';
import {
  PortalActionResult,
  SubmitQuoteToPortalData,
} from 'types/chromescript/QuotingPortal';
import { SidepanelMessage } from 'types/chromescript/util';
import { QuotingPortals } from 'types/enums/Integrations';
import { SuggestionSourceCategories } from 'types/suggestions/QuoteSuggestions';

import { isSuppressedSidepanelError } from '../util';

/*
 * If user selects a shipment before opening side panel, we store initially selected quote request
 * and send it when QuoteSidebarPortName port connects (see chrome.runtime.onConnect below)
 */
let freightViewParsedQuoteRequest: Maybe<Partial<QuoteRequest>> = null;

// When the sidepanel first connects, send the initial data to QuoteSidebar
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name.startsWith(QuoteSidebarPortName)) {
    // Extract tab ID from port name (format: "QuoteSidebarPortName-{tabId}")
    const tabIdMatch = port.name.match(/-(\d+)$/);
    const targetTabId = tabIdMatch ? parseInt(tabIdMatch[1], 10) : undefined;

    if (freightViewParsedQuoteRequest && targetTabId) {
      await chrome.runtime.sendMessage({
        action: UpdateQuoteRequestDataAction,
        data: freightViewParsedQuoteRequest,
        targetTabId: targetTabId,
      } as SidepanelMessage);
    }
  }
});

// When user navigates to FreightView tab, inject script to listen for quote request changes
// Technically could add to manifest.json, but defined here for keeping all logic in one file
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // IMPORTANT: Only process when the page is fully loaded
  if (changeInfo.status === 'complete') {
    if (tab.url?.includes('carrier.freightview')) {
      // NOTE: Because this is a background script, this log will appear in the service worker's console
      // console.log('injecting FreightView quote request change listener ');

      chrome.scripting.executeScript({
        target: { tabId: tabId },
        args: [tabId],
        func: registerQuoteRequestChangeListener,
      });
    }
  }
});

/**
 * When user first navigates to FreightView tab:
 * 1. Attach parseQuoteRequestData and tabId function to window
 * 2. Parse the initially selected quote request's data
 */
chrome.tabs.onUpdated.addListener(async (tabId, _, tab) => {
  try {
    if (!tab.url) return;
    const url = new URL(tab.url);

    const isFreightView = url.origin?.includes('carrier.freightview');
    if (!isFreightView) {
      return;
    }
    // Both the MutationObserver callback and this injected script need to reference parseQuoteRequestData() but
    // content scripts must be self-contained and not reference external types/functions.
    // Instead, we attach it to the window context which is specific to the tab so it's available to both scripts
    await chrome.scripting.executeScript({
      target: { tabId },
      args: [tabId],
      func: (tabId: number) => {
        window.__tabId = tabId;
        window.parseQuoteRequestData =
          /**
           * Parses the quote request data from the selected shipment on the FreightView page.
           * @returns A PortalParseQuoteRequestResult containing either the parsed data or an error.
           */
          (): PortalParseQuoteRequestResult => {
            // NOTE: Because this is an injected content script, this log will appear in the tab's console
            // console.log('Parsing quote request data');

            /**
             * Parses a shipment date from a month and day string. FreightView doesn't include the year, so this function
             * assumes the current year and rolls over to the next year if the date has already passed.
             * @param month - The month of the shipment date.
             * @param day - The day of the shipment date.
             * @returns A Date object representing the shipment date, or undefined if the date is not valid.
             */
            function parseShipmentDate(
              month: Undef<string>,
              day: Undef<string>
            ): Undef<Date> {
              if (!month || !day || /no date/i.test(month)) return undefined;

              const year = new Date().getFullYear();
              const dateThisYear = new Date(`${month} ${day}, ${year}`);

              const now = new Date();
              // If the date has already passed this year, roll over to next year
              if (dateThisYear.getTime() < now.setHours(0, 0, 0, 0)) {
                const nextYearString = `${month} ${day}, ${year + 1}`;
                const nextYearDate = new Date(nextYearString);

                return nextYearDate;
              }
              return dateThisYear;
            }

            /**
             * Parses a location string into an Address object.
             * @param locationText - The location string to parse, e.g. "San Francisco, CA 94101"
             * @returns An object containing the parsed location information.
             */
            function parseLocationString(
              locationText: string | undefined
            ): Partial<Address> {
              if (!locationText) return {};

              // Remove leading phrases and date if present
              // Handles: "Pick up (on May 16 )?at Boston, MA 02116" and "Deliver (on May 19 )?at San Antonio, TX 78227"
              const cleaned = locationText
                .replace(/Pick up (?:on [A-Za-z]{3,9} \d{1,2} )?at /i, '')
                .replace(/Deliver (?:on [A-Za-z]{3,9} \d{1,2} )?at /i, '')
                .trim();

              const match = cleaned.match(
                /(.+),\s*([A-Z]{2})\s*(\d{5}|[a-zA-Z0-9]{3}\s*[a-zA-Z0-9]{3})?/
              );
              if (match) {
                return {
                  city: match[1].trim(),
                  state: match[2],
                  zip: match[3] || '',
                };
              }
              return { addressLine1: cleaned };
            }

            function parseStopData(stopLi: Element | null): {
              month: string | undefined;
              day: string | undefined;
              date: Date | undefined;
              location: Partial<Address>;
            } {
              if (!stopLi) {
                return {
                  month: undefined,
                  day: undefined,
                  date: undefined,
                  location: {},
                };
              }

              const month = stopLi
                .querySelector('div.bg-fv-beer')
                ?.textContent?.trim();
              const day = stopLi
                .querySelector('div.bg-fv-orange-50')
                ?.textContent?.trim();
              const date = parseShipmentDate(month, day);

              const locationText = stopLi
                .querySelector('p span')
                ?.textContent?.trim();
              const location = parseLocationString(locationText);

              return { month, day, date, location };
            }

            try {
              const selected = document.querySelector(
                '.shipment-item--selected'
              );

              if (!selected) {
                return { data: null };
              }

              // Customer
              const customerSpan = selected.querySelector(
                '.shipment-item__information .general-list__item span'
              );
              const customerName =
                customerSpan?.textContent?.replace('From:', '').trim() ?? '';

              // Pickup
              const pickupLi = selected.querySelectorAll(
                '.general-list__item.general-list__date-icon'
              )?.[0];
              const pickup = parseStopData(pickupLi);

              // Dropoff
              const dropoffLi = selected.querySelectorAll(
                '.general-list__item.general-list__date-icon'
              )[1];
              const dropoff = parseStopData(dropoffLi);

              // Transport type
              const transportLi = selected
                .querySelector('.fa-truck')
                ?.closest('li');
              const transportText = transportLi?.textContent ?? '';

              let transportType: TransportType | undefined = undefined;
              if (/Flatbed/i.test(transportText)) {
                transportType = 'FLATBED' as TransportType;
              } else if (/Van/i.test(transportText)) {
                transportType = 'VAN' as TransportType;
              } else if (/Reefer/i.test(transportText)) {
                transportType = 'REEFER' as TransportType;
              } else if (/hot[-_\s]*shot/i.test(transportText)) {
                transportType = 'HOTSHOT' as TransportType;
              } else if (/box/i.test(transportText)) {
                transportType = 'BOX TRUCK' as TransportType;
              }

              const res: CreateQuoteRequestSuggestionRequest = {
                customer: { name: customerName },
                customerExternalTMSID: '',
                transportType: transportType || ('VAN' as TransportType),
                pickupCity: pickup.location.city || '',
                pickupState: pickup.location.state || '',
                pickupZip: pickup.location.zip || '',
                pickupDate: pickup.date ? pickup.date.toISOString() : null,
                deliveryCity: dropoff.location.city || '',
                deliveryState: dropoff.location.state || '',
                deliveryZip: dropoff.location.zip || '',
                deliveryDate: dropoff.date ? dropoff.date.toISOString() : null,
                sourceCategory:
                  'quoting-portal' as SuggestionSourceCategories.QuotingPortal,
                source: 'freightview' as QuotingPortals,
                sourceExternalID: '',
                sourceURL: window.location.href,
                htmlSnippet: selected.innerHTML,
                fuelSurchargePerMile: null,
                fuelSurchargeTotal: null,
                distanceMiles: null,
              };

              return { data: res };
            } catch (error: any) {
              return {
                data: null,
                error: error?.message || new Error(error).message,
              };
            }
          };
      },
    });

    // Call parseQuoteRequestData() to get the initially selected quote request data
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        try {
          return (window as any).parseQuoteRequestData();
        } catch (error: any) {
          console.error(
            'Error calling injected parseQuoteRequestData: ',
            error?.message
          );
          return {
            data: null,
            error: error?.message || new Error(error).message,
          };
        }
      },
    });

    const parseResult = result?.[0]
      ?.result as Undef<PortalParseQuoteRequestResult>;
    if (parseResult?.error) {
      console.error(
        'Error parsing FreightView quote request data: ',
        parseResult.error
      );
      Sentry.captureException(
        'Error parsing FreightView quote request data: ' + parseResult.error
      );
      return;
    }

    // We rely on onConnect only for the first parsed quote request before the sidePanel is opened.
    // When tab changes to another id, we sendMessage to the sidepanel with the new QR data here.
    if (parseResult?.data) {
      try {
        await chrome.runtime.sendMessage({
          action: UpdateQuoteRequestDataAction,
          data: parseResult.data,
          targetTabId: tabId,
        });
      } catch (error) {
        // "Receiving end does not exist error" expected if side panel isn't open yet
        if (
          error instanceof Error &&
          !error.message.includes('Receiving end does not exist')
        ) {
          Sentry.captureException('Error sending message to sidepanel' + error);
        }
      }
    }

    // Store the initially parsed quote request data for if the sidepanel is opened after the tab changes
    freightViewParsedQuoteRequest = parseResult?.data ?? null;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Known chrome/transient errors that are expected to happen - don't send to Sentry
    if (isSuppressedSidepanelError(errorMessage.toLowerCase())) {
      return;
    }

    Sentry.captureException(
      'Error parsing quote request data: ' + errorMessage
    );
  }
});

/**
 * @description
 * Observe when user switches quote request on FreightView to trigger a refresh of Quick Quote suggestion & form.
 */
export function registerQuoteRequestChangeListener(tabId: number) {
  if (window.__quoteRequestChangeListenerAdded) return;
  window.__quoteRequestChangeListenerAdded = true;
  window.__tabId = tabId;

  const callback: MutationCallback = function () {
    try {
      // After all mutations are processed, check if any shipment is selected
      const hasSelectedShipment =
        document.querySelector('.shipment-item--selected') !== null;

      const parseResult =
        hasSelectedShipment && window.parseQuoteRequestData
          ? (window.parseQuoteRequestData?.() as PortalParseQuoteRequestResult)
          : { data: null };

      if (parseResult?.error) {
        Sentry.captureException(
          'Error parsing FreightView quote request data: ' + parseResult.error
        );
        return;
      }

      chrome.runtime.sendMessage({
        action: 'updateQuoteRequestData',
        data: parseResult?.data ?? null,
        targetTabId: window.__tabId,
      } as SidepanelMessage);
    } catch (error) {
      Sentry.captureException(
        'freightview MutationObserver callback error: ' + error
      );
    }
  };

  const observer = new MutationObserver(callback);

  const config: MutationObserverInit = {
    attributes: true,
    attributeFilter: ['class'],
    subtree: true,
  };

  const targetNode = document.querySelector('#root') || document.body;

  observer.observe(targetNode, config);
}

/**
 * @description
 * Listen for messages to submit a quote to FreightView.
 */
chrome.runtime.onMessage.addListener(
  (message: SidepanelMessage, _, sendResponse) => {
    if (message.action === FreightViewSubmitAction) {
      handleFreightViewSubmit(message, sendResponse);
      return true; // Indicates async response
    }
  }
);

async function handleFreightViewSubmit(
  message: SidepanelMessage,
  sendResponse: (response: any) => void
) {
  try {
    const targetTabId = message.targetTabId;
    if (targetTabId === undefined) {
      sendResponse({ success: false, error: 'No target tab ID provided.' });
      return;
    }

    const tab = await chrome.tabs.get(targetTabId as number);
    if (!tab?.id || !tab.url?.includes('carrier.freightview')) {
      sendResponse({ success: false, error: 'FreightView tab not found.' });
      return;
    }

    // Inject script to fill and submit the quote form
    const result = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      args: [message.data as SubmitQuoteToPortalData],
      func: submitQuoteToFreightView,
    });

    const scriptResult = result?.[0]?.result;
    if (scriptResult) {
      sendResponse(scriptResult);
    } else {
      sendResponse({
        success: false,
        error: 'Script executed but returned no result.',
      });
    }
  } catch (err: any) {
    sendResponse({ success: false, error: err?.message ?? String(err) });
  }
}

async function submitQuoteToFreightView({
  flatRate,
  isSubmitOnPortalEnabled,
}: SubmitQuoteToPortalData): Promise<PortalActionResult> {
  try {
    // 1. Find the selected shipment
    const selected = document.querySelector('.shipment-item--selected');
    if (!selected) {
      throw new Error('No selected shipment found.');
    }

    // 2. Open the quote form if not open
    const form = document.querySelector('form.form-currently-quoting');
    if (!form) {
      // Find and click the "Quote" button within the currently selected shipment
      const quoteBtn = Array.from(selected.querySelectorAll('button, a')).find(
        (el) => el.textContent && el.textContent.trim() === 'Quote'
      );
      if (!quoteBtn) throw new Error('Quote button not found.');
      (quoteBtn as HTMLElement).click();
    }

    // 3. Wait for the form to appear (polling)
    function waitForForm(retries = 20) {
      return new Promise((resolve, reject) => {
        let count = 0;
        const interval = setInterval(() => {
          const form = document.querySelector('form.form-currently-quoting');
          if (form) {
            clearInterval(interval);
            resolve(form);
          } else if (++count > retries) {
            clearInterval(interval);
            reject(new Error('Quote form did not appear.'));
          }
        }, 150);
      });
    }

    // 4. Fill in the price and submit
    const formEl: Maybe<HTMLElement> =
      (await waitForForm()) as Maybe<HTMLElement>;
    if (!formEl) throw new Error('Quote form not found after waiting.');

    // Find the dollar input (by id or name containing 'amount')
    const priceInput: Maybe<HTMLInputElement> = formEl.querySelector(
      'input[name*="amount"], input[id*="amount"]'
    );
    if (!priceInput) throw new Error('Price input not found.');

    priceInput.value = flatRate.toString();
    priceInput.dispatchEvent(new Event('input', { bubbles: true }));
    priceInput.dispatchEvent(new Event('change', { bubbles: true }));
    priceInput.dispatchEvent(new Event('blur', { bubbles: true }));

    // DEV: Fill in the Quote # input with 'TEST--DO NOT BOOK'
    // const quoteNumInput: Maybe<HTMLInputElement> = formEl.querySelector(
    //   'input[name*="num"], input[id*="num"]'
    // );
    // if (quoteNumInput) {
    //   quoteNumInput.value = 'TEST--DO NOT BOOK';
    //   quoteNumInput.dispatchEvent(new Event('input', { bubbles: true }));
    //   quoteNumInput.dispatchEvent(new Event('change', { bubbles: true }));
    //   quoteNumInput.dispatchEvent(new Event('blur', { bubbles: true }));
    // }

    if (!isSubmitOnPortalEnabled) {
      return { success: true };
    }

    // Find the submit button (by text 'Send bid')
    const submitBtn: Undef<Element> = Array.from(
      formEl.querySelectorAll('button[type="submit"]')
    ).find((el) => el.textContent && /send|update bid/i.test(el.textContent));
    if (!submitBtn) {
      return {
        success: false,
        error: 'Send bid button not found.',
        partialSuccess: true,
      };
    }
    if (
      submitBtn.hasAttribute('disabled') ||
      submitBtn.getAttribute('aria-disabled') === 'true'
    ) {
      return {
        success: false,
        error: 'Send bid button is disabled.',
        partialSuccess: true,
      };
    }
    submitBtn.dispatchEvent(
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      })
    );

    // Wait for the success popup (role="status" and textContent includes 'quote added successfully')
    function waitForSuccessPopup(timeout = 5000) {
      return new Promise((resolve) => {
        const start = Date.now();
        const interval = setInterval(() => {
          const popup = Array.from(
            document.querySelectorAll('[role="status"]')
          ).find(
            (el) =>
              el.textContent &&
              el.textContent.toLowerCase().includes('quote added successfully')
          );
          if (popup) {
            clearInterval(interval);
            resolve(true);
          } else if (Date.now() - start > timeout) {
            clearInterval(interval);
            resolve(false);
          }
        }, 200);
      });
    }

    return waitForSuccessPopup().then((success) => {
      if (success) {
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Form submitted but did not detect success popup.',
          partialSuccess: true,
        };
      }
    });
  } catch (err: any) {
    return { success: false, error: err };
  }
}
