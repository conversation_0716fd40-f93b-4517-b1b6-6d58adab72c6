import * as Sentry from '@sentry/browser';

import {
  QuoteSidebarPortName,
  UpdateQuoteRequestDataAction,
} from 'constants/BackgroundScript';
import { ShipwellSubmitAction } from 'lib/hosts/quoting/shipwell';
import { PortalParseQuoteRequestResult } from 'types/ChromeScript';
import { QuoteRequest, TransportType } from 'types/QuoteRequest';
import { Maybe, Undef } from 'types/UtilityTypes';
import {
  PortalActionResult,
  SubmitQuoteToPortalData,
} from 'types/chromescript/QuotingPortal';
import { SidepanelMessage } from 'types/chromescript/util';
import { QuotingPortals } from 'types/enums/Integrations';
import { SuggestionSourceCategories } from 'types/suggestions/QuoteSuggestions';

/*
 * If user selects a load before opening side panel, we store initially selected quote request
 * and send it when QuoteSidebarPortName port connects (see chrome.runtime.onConnect below)
 */
let shipwellParsedQuoteRequest: Maybe<Partial<QuoteRequest>> = null;

// When the sidepanel first connects, send the initial data to QuoteSidebar
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name.startsWith(QuoteSidebarPortName)) {
    // Extract tab ID from port name (format: "QuoteSidebarPortName-{tabId}")
    const tabIdMatch = port.name.match(/-(\d+)$/);
    const targetTabId = tabIdMatch ? parseInt(tabIdMatch[1], 10) : undefined;

    if (shipwellParsedQuoteRequest && targetTabId) {
      await chrome.runtime.sendMessage({
        action: UpdateQuoteRequestDataAction,
        data: shipwellParsedQuoteRequest,
        targetTabId: targetTabId,
      } as SidepanelMessage);
    }
  }
});

// When user navigates to Shipwell tab, inject script to listen for quote request changes
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // IMPORTANT: Only process when the page is fully loaded
  if (changeInfo.status === 'complete') {
    if (tab.url?.includes('app.shipwell.com/load-board')) {
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        args: [tabId],
        func: registerQuoteRequestChangeListener,
      });
    }
  }
});

/**
 * When user first navigates to Shipwell tab:
 * 1. Attach parseQuoteRequestData and tabId function to window
 * 2. Parse the initially selected quote request's data
 */
chrome.tabs.onUpdated.addListener(async (tabId, _, tab) => {
  try {
    if (!tab.url) return;
    const url = new URL(tab.url);

    const isShipwell =
      url.origin?.includes('app.shipwell.com') &&
      url.pathname?.includes('load-board');
    if (!isShipwell) {
      return;
    }

    // Attach parsing function to window context for use by MutationObserver
    await chrome.scripting.executeScript({
      target: { tabId },
      args: [tabId],
      func: (tabId: number) => {
        window.__tabId = tabId;
        window.parseQuoteRequestData = (): any => {
          // Helper functions defined locally for the injected script
          function parseLocationString(locationText: string | undefined): any {
            if (!locationText) return {};
            const cleaned = locationText.trim();

            // Handle Shipwell format: "Hickory, NC 28602 US" or "Fort Mill, SC 29708 US"
            const shipwellMatch = cleaned.match(
              /^(.+?),\s*([A-Z]{2})\s*(\d{5})\s*US$/
            );
            if (shipwellMatch) {
              return {
                city: shipwellMatch[1].trim(),
                state: shipwellMatch[2],
                zip: shipwellMatch[3],
              };
            }

            // Fallback to general format: "City, ST" or "City, ST ZIP"
            const generalMatch = cleaned.match(
              /(.+),\s*([A-Z]{2})(?:\s*(\d{5}))?/
            );
            if (generalMatch) {
              return {
                city: generalMatch[1].trim(),
                state: generalMatch[2],
                zip: generalMatch[3] || '',
              };
            }

            return { addressLine1: cleaned };
          }

          function parseDateString(stopElement: Element) {
            const container = stopElement.parentElement; // .mb-3
            if (!container) return null;

            const children = container.querySelectorAll('div');
            let dateText = '';

            // Look for a child div that looks like a date
            children.forEach((child) => {
              const text = child.textContent?.trim() || '';
              if (/^\w{3}\s\w{3}\s\d{1,2},/.test(text)) {
                dateText = text.replace(/(\d{2}:\d{2})-\d{2}:\d{2}/, '$1');
                const hasYear = /\d{4}/.test(dateText);
                const year = new Date().getFullYear();

                dateText = hasYear ? dateText : `${dateText} ${year}`;
              }
            });

            let parsedDate: Date | null = null;
            if (dateText) {
              parsedDate = new Date(dateText);
              if (isNaN(parsedDate.getTime())) {
                parsedDate = null;
              }
            }

            return parsedDate;
          }

          function getFieldValue(label: string): string | null {
            // Find the label element directly
            const labelDiv = Array.from(
              document.querySelectorAll('.hI9kCPYQTym9P1hpAViH')
            ).find((div) => div.textContent?.trim() === label);

            if (labelDiv) {
              // The parent container `.kT2nyFdIKbBiDjFaOmcz` holds both label + value
              const container = labelDiv.closest('.kT2nyFdIKbBiDjFaOmcz');
              const valueDiv = container?.querySelector(
                '._MxdlQJIFoP48V7mvR_0'
              );

              return valueDiv?.textContent?.trim() || null;
            }

            return null;
          }

          function extractLoadId(): string {
            // On load board page, extract load ID from load details hyperlink like /load-board/LIDRMNM8H
            const loadIdLoadBoardPage = document
              .querySelector('.drawer-header a')
              ?.getAttribute('href')
              ?.split('/')
              .pop();
            console.log('loadIdLoadBoardPage', loadIdLoadBoardPage);

            if (loadIdLoadBoardPage) {
              return loadIdLoadBoardPage;
            }

            // On load details page, extract load ID from URL (e.g., app.shipwell.com/load-board/ABCD)
            const urlParts = window.location.pathname.split('/');
            const loadIdLoadDetailsPage = urlParts[urlParts.length - 1];

            return loadIdLoadDetailsPage || '';
          }

          try {
            const loadId = extractLoadId();

            // Find pickup location from dashboard summary
            const pickupElements = document.querySelectorAll(
              '.dashboard-summary__stop-address'
            );
            if (pickupElements.length < 2) {
              return { data: null }; // Need at least pickup and delivery
            }

            const pickupElement = pickupElements[0]; // First element is pickup
            const pickupText = pickupElement?.textContent?.trim() || '';
            const pickupLocation = parseLocationString(pickupText);

            const pickupDate = parseDateString(pickupElements[0]);

            // Find delivery location from dashboard summary
            const deliveryElement = pickupElements[1]; // Second element is delivery
            const deliveryText = deliveryElement?.textContent?.trim() || '';
            const deliveryLocation = parseLocationString(deliveryText);

            const deliveryDate = parseDateString(pickupElements[1]);

            // Try to find customer name, transport type, pickup and delivery date from various possible locations
            const customerName = getFieldValue('Customer') || '';
            const transportText = getFieldValue('Equipment') || '';
            const distance = getFieldValue('Estimated Distance') || '';
            const cleaned = distance.replace(/[^0-9.]/g, '');
            const distanceMiles = parseFloat(cleaned) || null;

            let transportType: TransportType | undefined = undefined;

            if (/Van/i.test(transportText)) {
              transportType = 'VAN' as TransportType;
            } else if (/Reefer/i.test(transportText)) {
              transportType = 'REEFER' as TransportType;
            } else if (/hot[-_\s]*shot/i.test(transportText)) {
              // Order matters; Shipwell lists hotshot as "Flatbed Hotshot" so we need to check Hotshot before Flatbed
              transportType = 'HOTSHOT' as TransportType;
            } else if (/box/i.test(transportText)) {
              transportType = 'BOX TRUCK' as TransportType;
            } else if (/Flatbed/i.test(transportText)) {
              transportType = 'FLATBED' as TransportType;
            }

            return {
              data: {
                customer: { name: customerName },
                customerExternalTMSID: '',
                transportType: transportType || ('VAN' as TransportType),
                pickupCity: pickupLocation.city || '',
                pickupState: pickupLocation.state || '',
                pickupZip: pickupLocation.zip || '',
                pickupDate: pickupDate ? pickupDate.toISOString() : null,
                deliveryCity: deliveryLocation.city || '',
                deliveryState: deliveryLocation.state || '',
                deliveryZip: deliveryLocation.zip || '',
                deliveryDate: deliveryDate ? deliveryDate.toISOString() : null,
                fuelSurchargePerMile: null,
                fuelSurchargeTotal: null,
                distanceMiles: distanceMiles,
                sourceCategory:
                  'quoting-portal' as SuggestionSourceCategories.QuotingPortal,
                source: 'shipwell' as QuotingPortals,
                sourceExternalID: loadId,
                sourceURL: window.location.href,
                htmlSnippet:
                  document.querySelector('.dashboard-summary__details')
                    ?.innerHTML || '',
              },
            };
          } catch (error: any) {
            return {
              data: null,
              error: `Error parsing Shipwell quote request: ${error.message}`,
            };
          }
        };
      },
    });

    // Parse initial quote request data
    const parseResult = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        try {
          return window.parseQuoteRequestData?.();
        } catch (error: any) {
          return {
            data: null,
            error: error?.message || new Error(error).message,
          };
        }
      },
    });

    const result = parseResult?.[0]
      ?.result as Undef<PortalParseQuoteRequestResult>;
    if (result?.error) {
      console.error(
        'Error parsing Shipwell quote request data: ',
        result.error
      );
      Sentry.captureException(
        'Error parsing Shipwell quote request data: ' + result.error
      );
      return;
    }

    // Store the parsed data for when sidepanel connects
    if (parseResult?.[0]?.result?.data) {
      shipwellParsedQuoteRequest = parseResult[0].result.data;
    }

    // Send to sidepanel if it's already open
    if (parseResult?.[0]?.result?.data) {
      try {
        await chrome.runtime.sendMessage({
          action: UpdateQuoteRequestDataAction,
          data: parseResult[0].result.data,
          targetTabId: tabId,
        });
      } catch (error) {
        // "Receiving end does not exist error" expected if side panel isn't open yet
        if (
          error instanceof Error &&
          !error.message.includes('Receiving end does not exist')
        ) {
          Sentry.captureException('Error sending message to sidepanel' + error);
        }
      }
    }
  } catch (error: any) {
    Sentry.captureException('Error in Shipwell tab update handler: ' + error);
  }
});

/**
 * Listen for messages to submit a quote to Shipwell.
 */
chrome.runtime.onMessage.addListener(
  (message: SidepanelMessage, _, sendResponse) => {
    if (message.action === ShipwellSubmitAction) {
      handleShipwellSubmit(message, sendResponse);
      return true; // Indicates async response
    }
  }
);

/**
 * Registers a MutationObserver to detect when load data changes on the page
 */
function registerQuoteRequestChangeListener(_tabId: number) {
  const callback: MutationCallback = function () {
    try {
      // Check if we're on a load detail page and data is available
      const hasLoadData =
        document.querySelector('.dashboard-summary__stop-address') !== null;

      const parseResult =
        hasLoadData && window.parseQuoteRequestData
          ? (window.parseQuoteRequestData?.() as PortalParseQuoteRequestResult)
          : { data: null };

      if (parseResult?.error) {
        Sentry.captureException(
          'Error parsing Shipwell quote request data: ' + parseResult.error
        );
        return;
      }

      chrome.runtime.sendMessage({
        action: 'updateQuoteRequestData',
        data: parseResult?.data ?? null,
        targetTabId: window.__tabId,
      } as SidepanelMessage);
    } catch (error) {
      Sentry.captureException(
        'Shipwell MutationObserver callback error: ' + error
      );
    }
  };

  const observer = new MutationObserver(callback);

  // Watch the shipment drawer container for changes when load preview updates
  const targetNode =
    document.querySelector('[class*="shipment-drawer"]') || document.body;

  if (targetNode) {
    observer.observe(targetNode, {
      childList: true,
      subtree: true,
    });
  }

  // Initial check after a short delay to ensure DOM is ready
  setTimeout(() => callback([], observer), 1000);
}

async function handleShipwellSubmit(
  message: SidepanelMessage,
  sendResponse: (response: any) => void
) {
  try {
    const targetTabId = message.targetTabId;
    if (targetTabId === undefined) {
      sendResponse({ success: false, error: 'No target tab ID provided.' });
      return;
    }

    const tab = await chrome.tabs.get(targetTabId as number);
    if (!tab?.id || !tab.url?.includes('app.shipwell.com')) {
      sendResponse({ success: false, error: 'Shipwell tab not found.' });
      return;
    }

    // Inject script to fill and submit the quote form
    const result = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      args: [message.data as SubmitQuoteToPortalData],
      func: submitQuoteToShipwell,
    });

    const scriptResult = result?.[0]?.result;
    if (scriptResult) {
      sendResponse(scriptResult);
    } else {
      sendResponse({
        success: false,
        error: 'Script executed but returned no result.',
      });
    }
  } catch (err: any) {
    sendResponse({ success: false, error: err?.message ?? String(err) });
  }
}

async function submitQuoteToShipwell({
  flatRate,
  isSubmitOnPortalEnabled,
}: SubmitQuoteToPortalData): Promise<PortalActionResult> {
  try {
    // 1. Look for quote input
    const modal = document.querySelector('[id^="modal-described"]');
    let quoteInput = modal?.querySelector('#total-input');

    // 2. If quote form modal not already rendered, try to find and click "Place a Bid" button to open the quote modal
    if (!quoteInput) {
      const quoteButton =
        // 'Place a Bid' button on load board page
        Array.from(document.querySelectorAll('button')).find((btn) =>
          btn.textContent?.toLowerCase().includes('place a bid')
        ) ??
        // Load details page
        document.querySelector('#newBid');

      if (quoteButton) {
        (quoteButton as HTMLElement).click();

        // Wait for form to appear
        await new Promise((resolve, reject) => {
          let attempts = 0;
          const maxAttempts = 20;
          const interval = setInterval(() => {
            quoteInput = document.querySelector('#total-input');

            if (quoteInput) {
              clearInterval(interval);
              resolve(quoteInput);
            } else if (++attempts >= maxAttempts) {
              clearInterval(interval);
              reject(
                new Error(
                  'Quote form did not appear after clicking quote button.'
                )
              );
            }
          }, 150);
        });
      } else {
        throw new Error('Quote form not found and no quote button available.');
      }
    }

    if (!quoteInput) {
      throw new Error('Quote input not found.');
    }

    // 3. Find and fill the rate input
    const rateInput = quoteInput as HTMLInputElement;
    // Clear and set the rate value
    rateInput.value = '';
    rateInput.value = flatRate.toString();

    // Trigger events to ensure the form recognizes the input
    rateInput.dispatchEvent(new Event('input', { bubbles: true }));
    rateInput.dispatchEvent(new Event('change', { bubbles: true }));
    rateInput.dispatchEvent(new Event('blur', { bubbles: true }));

    // 4. If submit on portal is not enabled, return success
    if (!isSubmitOnPortalEnabled) {
      return { success: true };
    }

    // 5. Find and click the submit button
    const submitButton: HTMLElement | null =
      modal?.querySelector('button[type="submit"]') ||
      // Future proofing for future modal changes
      (Array.from(modal?.querySelectorAll('button') ?? []).find((btn) =>
        btn.textContent?.toLowerCase().includes('submit')
      ) as HTMLElement);

    if (!submitButton) {
      return {
        success: false,
        error: 'Submit button not found in quote form.',
        partialSuccess: true,
      };
    }

    if (
      submitButton.hasAttribute('disabled') ||
      submitButton.getAttribute('aria-disabled') === 'true'
    ) {
      return {
        success: false,
        error: 'Submit button is disabled.',
        partialSuccess: true,
      };
    }

    // 6. Click the submit button
    submitButton.dispatchEvent(
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      })
    );

    // TODO: Wait for success indication, unable to test in dev so skipping for now
    return {
      success: true,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || String(err),
    };
  }
}
