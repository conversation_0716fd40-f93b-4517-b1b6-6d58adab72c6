import { ChromeEvents } from '@pages/content/src/types';

import { activeSidePanelTabs } from '../index';

// Notify tabs when side panel is opened/close to toggle popup
export function sendSidePanelMessage(isOpen: boolean) {
  chrome.tabs.query({ lastFocusedWindow: true, active: true }, (tabs) => {
    tabs.forEach((tab) => {
      if (tab.id) {
        chrome.tabs
          .sendMessage(tab.id, {
            command: ChromeEvents.SidePanelStateUpdated,
            open: isOpen,
          } as any)
          .catch((err) =>
            console.error(
              `Error sending side panel state to tab ${tab.id}:`,
              err
            )
          );
      }
    });
  });
}

// Returns the extension pin status to SidebarOpener
chrome.runtime.onMessage.addListener(function (message, _, callback) {
  if (message.command === ChromeEvents.CheckExtensionPin) {
    checkIsPinned(callback);
  }
  return true;
});

// Returns the current tab to SidebarOpener
chrome.runtime.onMessage.addListener(function (message, _, callback) {
  if (message.command === ChromeEvents.GetCurrentTab) {
    getCurrentTab(callback);
  }
  return true;
});

// Returns the sidepanel open status on the passed in tab to SidebarOpener
chrome.runtime.onMessage.addListener(function (message, _, callback) {
  if (message.command === ChromeEvents.CheckSidePanelOpen) {
    checkSidePanelOpenOnTab(message.tabId, callback);
  }
  return true;
});

function checkIsPinned(callback: (response?: boolean) => void) {
  chrome.action.getUserSettings((settings) => {
    callback(settings.isOnToolbar);
  });
}

function getCurrentTab(callback: (response?: chrome.tabs.Tab) => void) {
  chrome.tabs.query({ active: true }, (tabs) => {
    if (tabs.length > 0) {
      callback(tabs[0]);
    } else {
      callback();
    }
  });
}

function checkSidePanelOpenOnTab(
  tabId: number,
  callback: (response?: boolean) => void
) {
  callback(activeSidePanelTabs.has(tabId));
}
