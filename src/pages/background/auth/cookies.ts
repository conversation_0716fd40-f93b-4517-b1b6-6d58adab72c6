import DRUMKIT_AUTH_URL from '@constants/DrumkitAuthUrl';
import { ChromeEvents } from '@pages/content/src/types';

export const COOKIE_NAME = 'drumkit-auth';

export function checkAuthCookies(authCallback: (response?: string) => void) {
  chrome.cookies.get({ url: DRUMKIT_AUTH_URL, name: COOKIE_NAME }, (cookie) => {
    if (cookie !== null) {
      authCallback(decodeURIComponent(cookie?.value));
      // Must send callback or extension will hang for several minutes
    } else {
      authCallback();
    }
  });
}

export function removeAuthCookies(authCallback: (response?: string) => void) {
  chrome.cookies.remove(
    { url: DRUMKIT_AUTH_URL, name: COOKIE_NAME },
    (value) => {
      authCallback(value?.name);
    }
  );
}

export function cookieChangeListener() {
  chrome.cookies.onChanged.addListener((info) => {
    if (
      DRUMKIT_AUTH_URL?.includes(info.cookie.domain) &&
      info.cookie.name === COOKIE_NAME
    ) {
      chrome.runtime.sendMessage({ command: ChromeEvents.CookieUpdated });
    }
  });
}
