# Background vs. Content Scripts

## Overview

In Chrome Extensions, there are several types of scripts:

- **Background scripts**: Run in the extension's background context (service worker or persistent background page). They have access to the full Chrome extension API and can coordinate actions across tabs and windows.
- **Content scripts**: Run in the context of web pages. They can read and modify the DOM of the page, but have limited access to the Chrome extension API.
- **Injected scripts**: JavaScript code injected directly into the web page's context (e.g., via `chrome.scripting.executeScript`). These scripts run as if they were part of the page itself.

## Debugging Logs

- **Injected scripts**: Logs from within injected scripts appear in the **tab's console** (DevTools for the web page).
- **Background scripts**: Logs appear in the **service worker inspector** (chrome://extensions > click 'service worker' link under your extension).
- **Side panel scripts**: Logs appear in the **side panel's console** (right-click inside the side panel and select 'Inspect').

## ReferenceError: Function Does Not Exist

### Why This Happens

Injected scripts (using `chrome.scripting.executeScript`) **must be entirely self-contained**. They cannot reference variables, functions, or types from the background or content script context. If you try to reference an external function or import, you'll get a `ReferenceError` in the tab's console.

#### Bad Usage (will throw ReferenceError):

```js
// background.js or content script
function myHelper() {
  return 42;
}
chrome.scripting.executeScript({
  target: { tabId },
  func: () => myHelper(), // ReferenceError: myHelper is not defined
});
```

#### Good Usage (self-contained):

```js
chrome.scripting.executeScript({
  target: { tabId },
  func: () => {
    function myHelper() {
      return 42;
    }
    return myHelper();
  },
});

// OR

chrome.scripting.executeScript({
  target: { tabId },
  func: myHelper; // The function is serialized and passed to webpage context for execution, hence why it must be self-contained
  },
);

function myHelper() : number {
    return 42;
}
```

#### Sharing Functions via `window`

If you want to avoid duplicating logic, you can attach a function to the `window` object in the page context:

```js
declare global {
  interface Window {
    __shipmentEventListenerAdded?: boolean;
    myHelper?: () => number;
  }
}

chrome.tabs.onUpdated.addListener(async (tabId, _, tab) => {
    // Add function to window
    await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
            window.myHelper =
                function (): {
                    return 42;
                }
            };
        }),
    };

    // Later, in an injected script:
    await chrome.scripting.executeScript({
    target: { tabId },
    func: () => {
        if (typeof window.myHelper === 'function') {
        return window.myHelper();
        }
        return null;
    },
    });
)

// Later, in an event callback
function registerEventListener() {
  // Prevents duplicate event listeners
  if (window.__shipmentEventListenerAdded) return;
  window.__shipmentEventListenerAdded = true;

  document.addEventListener('shipmentItemSelected', () => {
    try {
      const data = window.myHelper?.() ?? null;
      chrome.runtime.sendMessage({
        action: 'updateQuoteRequestData',
        data,
      });
    } catch (error) {
      captureException(
        'freightview shipmentItemSelected callback error: ' + error
      );
    }
  });
}
```

# Error Handling

There are 2 slightly different approaches to Sentry initialization and error capture depending on the different contexts of the extension:

1. **Content Scripts:** Uses recommended explicit `BrowserClient` initialization for Chrome extensions. Must use `initializeSentry` and `captureException` utility functions
2. **Sidepanel Context:** Same as above.
3. **Background worker**: Because the background worker operates in a completely separate context from the webpage, it requires its own Sentry initialization. This uses the standard `Sentry.init` and `Sentry.captureException` SDK funcs

> ⚠️
> Thrown exceptions are not automatically captured by background worker. Must use `try { ... } catch (e) { Sentry.captureException }`
> See examples in src/pages/background/modules/e2open.ts and src/pages/background/modules/freightview.ts

```tsx
// src/pages/background/index.ts
function initializeSentryForBackground() {
  Sentry.init({
    dsn: SENTRY_DSN,
    environment: ENVIRONMENT,
    release: `${APP_VERSION}-${ENVIRONMENT}`,
    integrations: [
      captureConsoleIntegration({
        levels: ['error', 'warn'],
        ...Sentry.getDefaultIntegrations({}),
      }),
    ],
    tracesSampleRate: 0.5,
  });
}
```

## Best Practices

- **Keep injected scripts self-contained**: All logic and helpers must be defined inside the injected function.
- **Do not use imports or external references** in injected scripts.
- **Use the `window` object** to share functions between content scripts and injected scripts if needed.
- **Debug in the right console**: Know where your logs will appear depending on the script type.
- **TypeScript**: You can extend the `Window` interface in a `declare global` block to add custom properties for type safety.

## Summary Table

| Script Type       | Runs In           | Can Access Chrome API | Can Access Page DOM                                    | Can Use Imports | Where Logs Appear        |
| ----------------- | ----------------- | --------------------- | ------------------------------------------------------ | --------------- | ------------------------ |
| Background script | Extension context | Yes                   | No (must inject DOM scraping content script)           | Yes             | Service worker inspector |
| Content script    | Web page context  | Limited               | Yes                                                    | Yes             | Tab's console            |
| Sidepanel         | Extension context | Yes                   | No (must use BG to inject DOM scraping content script) | Yes             | Sidepanel's console      |

## Accessing Quoting/Bidding Portals

### FreightView

- Get client's FreightView _carrier_ login username (different from shipper or vendor logins)
- Go to https://carrier.freightview.com/ and input the email, then check RDS for an email titled "Freightview Carrier Login" (this assumes the user is onboarded to Drumkit)
- Copy & paste the login code in the email into FreightView
