import { createRoot } from 'react-dom/client';

import refreshOnUpdate from 'virtual:reload-on-update-in-view';

import isProd from '@utils/isProd';
import { initializeSentry } from '@utils/sentry/getSentry';

import SidePanel from './SidePanel';

refreshOnUpdate('pages/sidepanel');

function init() {
  if (isProd()) {
    initializeSentry();
  }
  const appContainer = document.querySelector('#app-container');
  if (!appContainer) {
    throw new Error('Can not find #app-container');
  }
  const root = createRoot(appContainer);
  root.render(<SidePanel />);
}

init();
