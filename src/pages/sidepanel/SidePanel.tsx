import React, { useEffect, useState } from 'react';

import { App } from '@pages/content/src/app';
import {
  ChromeEvents,
  Identifier,
  IdentifierType,
} from '@pages/content/src/types';

import { DrumkitPlatform } from 'contexts/sidebarStateContext';

const SidePanel: React.FC = () => {
  const [parsedLoadIdentifiers, setParsedLoadIdentifiers] = useState<
    Identifier[]
  >([]);
  const [threadId, setThreadId] = useState<string>('');
  const [currentTabId, setCurrentTabId] = useState<number | undefined>();

  const updateCurrentChromeTabId = async () => {
    const tab: chrome.tabs.Tab = await chrome.runtime.sendMessage({
      command: ChromeEvents.GetCurrentTab,
    });

    if (tab.id) {
      setCurrentTabId(tab.id);
    }

    return tab.id;
  };

  const handleMessage = async (message: any) => {
    let currentEventLoopTabId = currentTabId;

    // We don't want to discard messages before port connection logic has completed, so if
    // the tabId is not set, we update it here by querying Chrome's API for the active tab.
    if (!currentTabId) {
      currentEventLoopTabId = await updateCurrentChromeTabId();
    }

    if (
      message.action === ChromeEvents.ParsedIdentifierUpdated &&
      message.parsedIdentifier &&
      // Handling the tabId check with a local scoped non-state variable since, if updated by
      // updateCurrentChromeTabId, the state setter would only update it in the next event loop
      message.tabId === currentEventLoopTabId
    ) {
      const { type, value } = message.parsedIdentifier;

      setParsedLoadIdentifiers([]);
      setThreadId('');

      switch (type) {
        case IdentifierType.LoadId:
          setParsedLoadIdentifiers([message.parsedIdentifier]);
          return;

        case IdentifierType.ThreadId:
          setThreadId(value);
          return;
      }
    }
  };

  useEffect(() => {
    async function connectPort() {
      try {
        const tabId = await updateCurrentChromeTabId();
        // Open a port connection to let background script know sidepanel has loaded
        // NOTE: background script relies on drumkitSidepanel-tab${tab.id} format. If changing, be sure to
        // also update chrome.runtime.onConnect.addListener
        if (tabId) {
          chrome.runtime.connect({ name: `drumkitSidepanel-${tabId}` });
        } else {
          console.error('Unable to connect port: missing tab id');
        }
      } catch (error) {
        console.error('Error fetching extension info:', error);
      }
    }

    connectPort();
  }, []);

  useEffect(() => {
    chrome.runtime.onMessage.addListener(handleMessage);

    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, [currentTabId]);

  return (
    <App
      loadIdentifiers={parsedLoadIdentifiers}
      threadId={threadId}
      wrapperPlatform={DrumkitPlatform.Sidepanel}
      isChromeSidePanel={true}
      style={{ marginLeft: '-1px' }} // Gets rid of weird gap in sidepanels noticeable in dark mode
    />
  );
};

export default SidePanel;
