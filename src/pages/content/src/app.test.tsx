import { render, screen, waitFor } from '@testing-library/react';

import { DrumkitPlatform } from 'contexts/sidebarStateContext';

import { App } from './app';

// Mock all the constants
jest.mock('@constants/DrumkitApiUrl', () => ({
  DRUMKIT_API_URL: 'mock_url',
}));

jest.mock('@constants/DrumkitAuthUrl', () => ({
  DRUMKIT_AUTH_URL: 'mock_url',
}));

jest.mock('@constants/SentryDsn', () => ({
  SENTRY_DSN: 'mock_sentry_dsn',
}));

jest.mock('@constants/SkipIngestEmail', () => ({
  SKIP_INGEST_EMAIL: 'mock_skip_ingest_email',
}));

jest.mock('@constants/PosthogApiKey', () => ({
  POSTHOG_API_KEY: 'mock_posthog_api_key',
}));

jest.mock('@constants/Environment', () => ({
  ENVIRONMENT: 'development',
}));

jest.mock('@constants/AppVersion', () => ({
  APP_VERSION: '0.0.1',
}));

jest.mock('@auth/AuthService', () => ({
  getCurrentUser: jest.fn().mockResolvedValue({ email: '<EMAIL>' }),
  hasValidUserAuth: jest.fn().mockResolvedValue(true),
}));

// Mock all the components to avoid TypeScript issues
jest.mock('components/AuthProvider', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='auth-provider'>{children}</div>
  ),
}));

jest.mock('components/ErrorBoundary', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='error-boundary'>{children}</div>
  ),
}));

jest.mock('components/RequireAuth', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='require-auth'>{children}</div>
  ),
}));

jest.mock('components/ServiceProvider', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='service-provider'>{children}</div>
  ),
}));

jest.mock('components/SidebarStateContext', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='sidebar-state-provider'>{children}</div>
  ),
}));

jest.mock('components/SidebarViewContext', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='sidebar-view-provider'>{children}</div>
  ),
}));

jest.mock('contexts/themeContext', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='theme-provider'>{children}</div>
  ),
}));

jest.mock('./SidebarWrapper', () => ({
  __esModule: true,
  default: () => <div data-testid='sidebar-wrapper'>Loading...</div>,
}));

// Mock the Chrome APIs for sidepanel testing
const mockChromeTabsQuery = jest.fn();
const mockChromeRuntimeOnMessage = {
  addListener: jest.fn(),
  removeListener: jest.fn(),
};

// Mock chrome.tabs.query to return a proper array for sidepanel context
mockChromeTabsQuery.mockResolvedValue([
  {
    id: 123,
    url: 'https://example.com',
    title: 'Test Tab',
    active: true,
  },
]);

// Mock chrome.runtime.onMessage
const mockChromeRuntime = {
  onMessage: mockChromeRuntimeOnMessage,
};

// Set up the global chrome mock
Object.defineProperty(global, 'chrome', {
  value: {
    tabs: {
      query: mockChromeTabsQuery,
    },
    runtime: mockChromeRuntime,
  },
  writable: true,
});

describe('appTest', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('content loaded in sidepanel', async () => {
    render(
      <App
        threadId='123'
        loadIdentifiers={[]}
        wrapperPlatform={DrumkitPlatform.Sidepanel}
        isChromeSidePanel={true}
      />
    );

    await waitFor(() => {
      screen.getByText('Loading...');
    });

    // Verify that chrome.tabs.query was called
    expect(mockChromeTabsQuery).toHaveBeenCalledWith({
      active: true,
      currentWindow: true,
    });
  });
});
