import { useContext, useEffect, useState } from 'react';

import SearchBar from 'components/SearchBar';
import Titlebar, { TitlebarButtons } from 'components/Titlebar';
import SidebarLoader from 'components/loading/SidebarLoader';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { SidebarViewContext } from 'contexts/sidebarViewContext';
import useFetchEmail from 'hooks/useFetchEmail';
import { useServiceFeatures } from 'hooks/useServiceContext';
import LoadsSidebar from 'pages/LoadView/LoadsSidebar';
import QuoteSidebar from 'pages/QuoteView/QuoteSidebar';
import { initialIngestionMessage } from 'types/IngestionMessage';
import SidebarView from 'types/enums/SidebarView';

import { LoadSearchProvider } from '../../../alexandria/contexts/loadSearchContext';
import { Identifier, IdentifierType } from './types';

/**
 * `SidebarWrapper` adapts the sidebar based on feature flags and the current
 * platform context (e.g. Gmail, Aljex).
 */
export default function SidebarWrapper({
  loadIdentifiers,
  threadId,
}: {
  loadIdentifiers: Identifier[];
  threadId?: string;
}) {
  const { currentView, setCurrentView } = useContext(SidebarViewContext);
  const {
    currentState: { isChromeSidePanel },
  } = useContext(SidebarStateContext);

  const {
    isLoading: isServiceFeaturesLoading,
    serviceFeaturesEnabled: { isLoadViewEnabled, isQuoteViewEnabled },
  } = useServiceFeatures();

  const [emailLoadIds, setEmailLoadIds] = useState<string[]>([]);

  const { email, isLoading: isEmailLoading } = useFetchEmail(
    threadId,
    initialIngestionMessage
  );

  const parsedLoadIdentifiers =
    loadIdentifiers
      .filter((identifier) => identifier?.type === IdentifierType.LoadId)
      .map((identifier) => identifier?.value) || [];

  // We always check if there are valid load identifiers to re-render the sidebar with,
  // though valid freight tracking IDs extracted from the email are prioritized.
  let initialLoadIds: string[] = [];
  if (emailLoadIds.length > 0) {
    initialLoadIds = emailLoadIds;
  } else if (parsedLoadIdentifiers.length > 0) {
    initialLoadIds = parsedLoadIdentifiers;
  }

  const determineDefaultView = (): SidebarView => {
    if (isLoadViewEnabled && initialLoadIds.length > 0) {
      return SidebarView.Loads;
    }

    // Check Quote View feature flag and if email is still loading to prevent screens flashing
    if (isQuoteViewEnabled && !isEmailLoading) {
      return SidebarView.Quote;
    }

    // One of the two views must be enabled.
    // If Quote View is not enabled, then default to Load View
    return SidebarView.Loads;
  };

  useEffect(() => {
    // Skip getting/setting default view if service features are still loading
    if (isServiceFeaturesLoading) {
      return;
    }

    const defaultView = determineDefaultView();

    if (currentView !== defaultView) {
      setCurrentView(defaultView);
    }
  }, [isLoadViewEnabled, isQuoteViewEnabled, loadIdentifiers, isEmailLoading]);

  useEffect(() => {
    if (email?.freightTrackingIDs) {
      setEmailLoadIds(email.freightTrackingIDs);
    }
  }, [email]);

  if (isServiceFeaturesLoading) {
    return <SidebarLoader />;
  }

  return (
    <LoadSearchProvider initialFreightTrackingIDs={initialLoadIds}>
      <div
        style={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          overflowY: 'auto',
          overflowX: 'hidden',
          borderLeft: '1px solid rgb(229, 231, 235)',
        }}
        id='drumkit-content-view-root'
      >
        <Titlebar>
          <TitlebarButtons
            hideSearchBar={isLoadViewEnabled || isChromeSidePanel}
          />
          {currentView === SidebarView.Loads && <SearchBar />}
        </Titlebar>

        {currentView === SidebarView.Quote ? (
          <QuoteSidebar email={email} />
        ) : (
          currentView === SidebarView.Loads && <LoadsSidebar email={email} />
        )}
      </div>
    </LoadSearchProvider>
  );
}
