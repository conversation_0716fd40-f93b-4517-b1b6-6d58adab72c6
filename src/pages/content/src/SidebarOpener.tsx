import { useEffect, useRef, useState } from 'react';

import { GripVertical, XIcon } from 'lucide-react';

import Logo from '@src/alexandria/assets/drumkit-34.png';

import { Button } from 'components/Button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Typography } from 'components/typography';
import { Maybe } from 'types/UtilityTypes';

import { ChromeEvents, Identifier, IdentifierType } from './types';

export type SidePanelStateMessage = {
  command: ChromeEvents.SidePanelStateUpdated;
  open: boolean;
};

export type ProStateMessage = {
  command: ChromeEvents.UpdateTabWithIdentifier;
  identifier: Identifier;
};

export const SidebarOpenerElementID = 'drumkit-popup-view-root';

// Set to 24 hours by default
const BUTTON_SNOOZE_TIME = 24 * 60 * 60 * 1000;
// Snap to edge when within this many pixels of screen edge
const SNAP_THRESHOLD = 100;
// <PERSON>ie helpers
const COOKIE_HIDE_UNTIL = 'drumkitOpenerHideUntil';
const COOKIE_TOP_POSITION = 'drumkitOpenerTopPosition';
const COOKIE_LEFT_POSITION = 'drumkitOpenerLeftPosition';
const COOKIE_RIGHT_POSITION = 'drumkitOpenerRightPosition';
const COOKIE_SIDE = 'drumkitOpenerSide';

type Side = 'left' | 'right';

function setCookie(name: string, value: string, days = 30) {
  const expires = new Date(Date.now() + days * 864e5).toUTCString();
  document.cookie = `${name}=${encodeURIComponent(value)}; expires=${expires}; path=/`;
}

function getCookie(name: string): Maybe<string> {
  const match = document.cookie
    .split('; ')
    .find((row) => row.startsWith(name + '='));
  return match ? decodeURIComponent(match.split('=')[1]) : null;
}

export default function Popup() {
  const [isPinned, setIsPinned] = useState<Maybe<boolean>>(null);
  const [isSidePanelOpen, setIsSidePanelOpen] = useState<boolean>(true);
  const [hasIdentifier, setHasIdentifier] = useState<Maybe<boolean>>(null);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragOffset, setDragOffset] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const [side, setSide] = useState<Side>('right');
  const [isAtEdge, setIsAtEdge] = useState<boolean>(true);
  const [identifierType, setIdentifierType] =
    useState<Maybe<IdentifierType>>(null);
  const dragRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    async function fetchExtensionInfo() {
      try {
        const response: boolean = await chrome.runtime.sendMessage({
          command: ChromeEvents.CheckExtensionPin,
        });
        setIsPinned(response);

        const tab: chrome.tabs.Tab = await chrome.runtime.sendMessage({
          command: ChromeEvents.GetCurrentTab,
        });

        if (tab?.id) {
          const isOpen: boolean = await chrome.runtime.sendMessage({
            command: ChromeEvents.CheckSidePanelOpen,
            tabId: tab.id,
          });
          setIsSidePanelOpen(isOpen);

          const checkProResponse = await chrome.runtime.sendMessage({
            command: ChromeEvents.CheckProAvailable,
          });
          setHasIdentifier(checkProResponse?.hasIdentifier);
        }
      } catch (error) {
        console.error('Error fetching extension info:', error);
      }
    }

    fetchExtensionInfo();
  }, []);

  function handleProAndSidePanelStatus(
    msg: SidePanelStateMessage | ProStateMessage
  ) {
    if (msg.command === ChromeEvents.SidePanelStateUpdated) {
      if (msg.open !== undefined) {
        setIsSidePanelOpen(msg.open);
      }
    } else if (msg.command === ChromeEvents.UpdateTabWithIdentifier) {
      const hasIdentifier = !!msg.identifier?.value;
      setHasIdentifier(hasIdentifier);

      if (hasIdentifier) {
        setIdentifierType(msg.identifier?.type);
      }
    }
  }

  // Restore saved position on mount
  useEffect(() => {
    const elt = document.getElementById(SidebarOpenerElementID);
    if (!elt) return;

    const topPosition = getCookie(COOKIE_TOP_POSITION);
    const leftPosition = getCookie(COOKIE_LEFT_POSITION);
    const rightPosition = getCookie(COOKIE_RIGHT_POSITION);
    const savedSide = (getCookie(COOKIE_SIDE) as Maybe<Side>) || 'right';

    setSide(savedSide);

    if (topPosition) {
      const savedY = parseInt(topPosition, 10);
      if (!isNaN(savedY)) {
        const maxY = Math.max(0, window.innerHeight - elt.offsetHeight);
        const y = Math.max(0, Math.min(savedY, maxY));
        elt.style.top = `${y}px`;
      }
    }

    if (savedSide === 'right' && rightPosition) {
      // Restore from right position
      const savedRight = parseInt(rightPosition, 10);
      if (!isNaN(savedRight)) {
        elt.style.right = `${Math.max(0, savedRight)}px`;
        elt.style.left = 'auto';
        setIsAtEdge(savedRight === 0);
      }
    } else if (savedSide === 'left' && leftPosition) {
      // Restore from left position
      const savedX = parseInt(leftPosition, 10);
      if (!isNaN(savedX)) {
        const screenWidth = window.innerWidth;
        const maxX = Math.max(0, screenWidth - elt.offsetWidth);
        const x = Math.max(0, Math.min(savedX, maxX));
        elt.style.left = `${x}px`;
        elt.style.right = 'auto';
        setIsAtEdge(savedX === 0);
      }
    }
  }, []);

  useEffect(() => {
    chrome.runtime.onMessage.addListener(handleProAndSidePanelStatus);

    return () => {
      chrome.runtime.onMessage.removeListener(handleProAndSidePanelStatus);
    };
  }, []);

  // Show/hide with cookie-based suppression
  useEffect(() => {
    const elt = document.getElementById(SidebarOpenerElementID);
    if (elt) {
      const hideUntilStr = getCookie(COOKIE_HIDE_UNTIL);
      const now = Date.now();
      const isSuppressed =
        hideUntilStr != null && now < Number(hideUntilStr || 0);

      // Only show popup if sidepanel is closed AND we have an identifier AND not within snooze window
      const shouldShow = !isSidePanelOpen && hasIdentifier && !isSuppressed;

      if (shouldShow) {
        elt.style.display = 'block';
      } else {
        elt.style.display = 'none';
      }
    }
  }, [isSidePanelOpen, hasIdentifier]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging && dragRef.current) {
        const rootElement = document.getElementById(SidebarOpenerElementID);
        if (rootElement) {
          const newX = e.clientX - dragOffset.x;
          const newY = e.clientY - dragOffset.y;

          const screenWidth = window.innerWidth;
          const maxX = screenWidth - rootElement.offsetWidth;
          const maxY = window.innerHeight - rootElement.offsetHeight;

          const constrainedX = Math.max(0, Math.min(newX, maxX));
          const constrainedY = Math.max(0, Math.min(newY, maxY));

          // Use left positioning during drag (will be converted on mouse up)
          rootElement.style.left = `${constrainedX}px`;
          rootElement.style.right = 'auto';
          rootElement.style.top = `${constrainedY}px`;

          // Update isAtEdge in real-time during drag
          const atLeftEdge = constrainedX === 0;
          const atRightEdge = constrainedX === maxX;
          setIsAtEdge(atLeftEdge || atRightEdge);

          // Update side in real-time based on position
          const elementCenterX = constrainedX + rootElement.offsetWidth / 2;
          const screenCenterX = screenWidth / 2;
          setSide(elementCenterX < screenCenterX ? 'left' : 'right');
        }
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);

      const rootElement = document.getElementById(SidebarOpenerElementID);
      if (rootElement) {
        const rect = rootElement.getBoundingClientRect();
        let leftPosition = Math.max(0, Math.floor(rect.left));
        const topPosition = Math.max(0, Math.floor(rect.top));

        const screenWidth = window.innerWidth;
        const screenCenterX = screenWidth / 2;
        const elementCenterX = rect.left + rect.width / 2;

        // Determine which side based on center of screen
        const newSide: Side = elementCenterX < screenCenterX ? 'left' : 'right';

        // Snap to edge if within threshold
        if (leftPosition < SNAP_THRESHOLD) {
          // Snap to left edge
          leftPosition = 0;
        } else if (rect.right > screenWidth - SNAP_THRESHOLD) {
          // Snap to right edge
          leftPosition = screenWidth - rect.width;
        }

        setSide(newSide);
        setCookie(COOKIE_SIDE, newSide);
        setCookie(COOKIE_TOP_POSITION, String(topPosition));

        // Apply the snapped position and save the appropriate position cookie
        if (newSide === 'right') {
          const rightPosition = screenWidth - leftPosition - rect.width;
          const finalRight = Math.max(0, rightPosition);
          rootElement.style.right = `${finalRight}px`;
          rootElement.style.left = 'auto';
          setCookie(COOKIE_RIGHT_POSITION, String(finalRight));
          setIsAtEdge(finalRight === 0);
        } else {
          rootElement.style.left = `${leftPosition}px`;
          rootElement.style.right = 'auto';
          setCookie(COOKIE_LEFT_POSITION, String(leftPosition));
          setIsAtEdge(leftPosition === 0);
        }
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault();

    const rootElement = document.getElementById(SidebarOpenerElementID);
    if (rootElement) {
      const rect = rootElement.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
      setIsDragging(true);
    }
  };

  function handleClose(e: React.MouseEvent): void {
    e.stopPropagation();
    const elt = document.getElementById(SidebarOpenerElementID);
    if (elt) {
      const hideUntil = Date.now() + BUTTON_SNOOZE_TIME;
      setCookie(COOKIE_HIDE_UNTIL, String(hideUntil));
      elt.style.display = 'none';
    }
  }

  const handleMainClick = async () => {
    chrome.runtime.sendMessage({ command: ChromeEvents.OpenSidePanel });
  };

  return (
    <section
      className='transition-all duration-300 ease-in-out'
      style={{
        width: isHovered ? '220px' : '50px', // max-w-md is 220px, square is 50px
        maxWidth: '220px',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <TooltipProvider>
        <style
          dangerouslySetInnerHTML={{
            __html:
              "@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600&display=swap');",
          }}
        />

        <div
          className={`p-[12px] transition-all duration-300 ease-in-out ${
            isAtEdge
              ? side === 'right'
                ? 'rounded-l-lg'
                : 'rounded-r-lg'
              : 'rounded-lg'
          }`}
          style={{
            backgroundColor: 'white',
            border: '1px solid #FE9659',
            height:
              isHovered && isPinned
                ? '100px'
                : isHovered && !isPinned
                  ? '168px'
                  : '50px',
            width: '100%',
            overflow: 'hidden',
          }}
        >
          {/* Always visible logo section */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <img
                src={Logo}
                loading='lazy'
                alt='Drumkit Logo'
                className='w-[24px] h-auto flex-shrink-0'
              />
              {/* Title only shows on hover */}
              {isHovered && (
                <Typography variant='body-sm' className='font-semibold'>
                  {identifierType === IdentifierType.ThreadId
                    ? 'Email detected!'
                    : 'Load detected!'}
                </Typography>
              )}
            </div>

            {/* Right side controls - only show on hover */}
            {isHovered && (
              <div className='flex items-center gap-1'>
                {/* Drag handle */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      ref={dragRef}
                      data-drag-handle
                      className='p-1 rounded cursor-grab active:cursor-grabbing'
                      style={{
                        color: 'black',
                        transition: 'opacity 0.2s',
                      }}
                      onMouseDown={handleDragStart}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      onMouseEnter={(e) =>
                        (e.currentTarget.style.opacity = '0.8')
                      }
                      onMouseLeave={(e) =>
                        (e.currentTarget.style.opacity = '1')
                      }
                    >
                      <GripVertical size={16} />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    side={side === 'right' ? 'left' : 'right'}
                    className='text-black text-xs border-black'
                  >
                    Drag to move
                  </TooltipContent>
                </Tooltip>

                {/* Close button */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <XIcon
                      style={{
                        width: '20px',
                        height: '20px',
                        cursor: 'pointer',
                        color: 'black',
                        transition: 'opacity 0.2s',
                      }}
                      onClick={handleClose}
                      onMouseEnter={(e) =>
                        (e.currentTarget.style.opacity = '0.8')
                      }
                      onMouseLeave={(e) =>
                        (e.currentTarget.style.opacity = '1')
                      }
                    />
                  </TooltipTrigger>
                  <TooltipContent
                    side={side === 'right' ? 'left' : 'right'}
                    className='text-black text-xs border-black'
                  >
                    Clear Suggestion
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>

          {/* Expanded content only shows on hover */}
          {isHovered && (
            <>
              <Button
                buttonNamePosthog='Click to open Drumkit'
                className='w-full text-center h-[44px] mt-[8px] mb-[4px] text-sm'
                onMouseDown={handleMainClick}
              >
                Open Drumkit
              </Button>

              {!isPinned && (
                <p className='text-xs italic mt-1' style={{ color: 'black' }}>
                  💡 Pro Tip: Pin the extension by clicking the 🧩 extension
                  icon on the top right and then clicking the 📌 icon next to
                  Drumkit.
                </p>
              )}
            </>
          )}
        </div>
      </TooltipProvider>
    </section>
  );
}
