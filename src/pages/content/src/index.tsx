import { createRoot } from 'react-dom/client';

import { findAndExtractGmailThreadID } from '@pages/background/modules/email-clients/gmail';
import { findAndExtractNewAljexProNumber } from '@pages/background/modules/tms/new-aljex';
import { findAndExtractRelayProNumber } from '@pages/background/modules/tms/relay';
import { findAndExtractStarkShipmentId } from '@pages/background/modules/tms/stark';
import { findAndExtractTurvoProNumber } from '@pages/background/modules/tms/turvo';

import { determineHostWebsite } from 'lib/hosts/interface';
import { CustomHost } from 'types/DrumkitHosts';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';

import SidebarOpener, { SidebarOpenerElementID } from './SidebarOpener';
import { Identifier } from './types';
import { ChromeEvents, MessageSources } from './types';

init();

function startIdentifierObserver(
  extractIdentifier: () => Maybe<Identifier>,
  sourceName: string
): () => void {
  let currentIdentifier: Maybe<Identifier> = null;
  let observer: MutationObserver | null = null;

  function handleIdentifierChange(newIdentifier: Maybe<Identifier>) {
    if (newIdentifier !== currentIdentifier) {
      currentIdentifier = newIdentifier;

      chrome.runtime
        .sendMessage({
          command: ChromeEvents.IdentifierUpdatedFromContentScript,
          parsedIdentifier: newIdentifier,
          source: sourceName,
        })
        .catch((err) => console.error('Error sending identifier update:', err));
    }
  }

  const stableContainer = document.body;

  observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        const newIdentifier = extractIdentifier();
        handleIdentifierChange(newIdentifier);
      }
    }
  });

  observer.observe(stableContainer, {
    childList: true,
    subtree: true,
    characterData: false,
  });

  return () => {
    if (observer) observer.disconnect();
  };
}

async function init() {
  // Automatically start Drumkit on certain host pages, such as Relay planning board
  const currentHost = determineHostWebsite();

  if (currentHost) {
    // Aljex PRO number observer, only for the new Aljex website (aljex.descartes.com);
    // Old Aljex has separate logic
    if (
      currentHost === TMS.Aljex &&
      location.href.includes('aljex.descartes.com')
    ) {
      const cleanup = startIdentifierObserver(
        findAndExtractNewAljexProNumber,
        'NewAljexMutationObserver'
      );
      (window as any).cleanupNewAljexProNumberObserver = cleanup;
    }

    if (currentHost === TMS.Turvo) {
      const cleanup = startIdentifierObserver(
        findAndExtractTurvoProNumber,
        'TurvoMutationObserver'
      );
      (window as any).cleanupTurvoProNumberObserver = cleanup;
    }

    if (currentHost === CustomHost.Gmail) {
      const cleanup = startIdentifierObserver(
        findAndExtractGmailThreadID,
        'GmailMutationObserver'
      );
      (window as any).cleanupGmailThreadIDObserver = cleanup;
    }

    if (currentHost === TMS.Stark) {
      const cleanup = startIdentifierObserver(
        () => findAndExtractStarkShipmentId(location.href),
        'StarkMutationObserver'
      );
      (window as any).cleanupStarkShipmentObserver = cleanup;
    }

    injectDrumkitSidebarOpener();
  }

  chrome.runtime.onMessage.addListener((msg, _send, sendResponse) => {
    // Sent from background script
    if (msg.command === ChromeEvents.ParseNewAljexPro) {
      const parsedIdentifier = findAndExtractNewAljexProNumber();

      chrome.runtime
        .sendMessage({
          command: ChromeEvents.IdentifierUpdatedFromContentScript,
          parsedIdentifier,
          source: MessageSources.NewAljexManualParse,
        })
        .catch((err) => console.error('Error sending parsed Aljex Pro#:', err));
    }

    if (msg.command === ChromeEvents.ParseTurvoPro) {
      const parsedIdentifier = findAndExtractTurvoProNumber();

      console.log('parsedIdentifier', parsedIdentifier);

      chrome.runtime
        .sendMessage({
          command: ChromeEvents.IdentifierUpdatedFromContentScript,
          parsedIdentifier,
          source: MessageSources.TurvoManualParse,
        })
        .catch((err) => console.error('Error sending parsed Turvo Pro#:', err));
    }

    if (msg.command === ChromeEvents.ParseRelayPro) {
      const parsedIdentifier = findAndExtractRelayProNumber(location.href);

      chrome.runtime
        .sendMessage({
          command: ChromeEvents.IdentifierUpdatedFromContentScript,
          parsedIdentifier,
          source: MessageSources.RelayManualParse,
        })
        .catch((err) => console.error('Error sending parsed Relay Pro#:', err));
    }

    if (msg.command === ChromeEvents.ParseStarkShipment) {
      const parsedIdentifier = findAndExtractStarkShipmentId(location.href);

      chrome.runtime
        .sendMessage({
          command: ChromeEvents.IdentifierUpdatedFromContentScript,
          parsedIdentifier,
          source: MessageSources.StarkManualParse,
        })
        .catch((err) => console.error('Error sending parsed Stark Shipment ID:', err));
    }

    if (msg.command === ChromeEvents.ParseGmailThreadId) {
      const parsedIdentifier = findAndExtractGmailThreadID();

      chrome.runtime
        .sendMessage({
          command: ChromeEvents.IdentifierUpdatedFromContentScript,
          parsedIdentifier,
          source: MessageSources.GmailManualParse,
        })
        .catch((err) =>
          console.error('Error sending parsed Gmail Thread ID:', err)
        );
    }

    // Needed due to chrome know the message was handled
    sendResponse();
  });
}

function injectDrumkitSidebarOpener() {
  // Create root element for the popup
  const rootElement = document.createElement('div');
  rootElement.id = SidebarOpenerElementID;
  rootElement.classList.add('drumkit-popup-discrete-root-element');

  rootElement.style.position = 'fixed';
  rootElement.style.top = '10px';
  rootElement.style.right = '0px';
  rootElement.style.zIndex = '10000';
  rootElement.style.display = 'none';

  // Create Shadow DOM for isolation from host page
  const shadowRoot = rootElement.attachShadow({ mode: 'open' });

  document.body.append(rootElement);

  // Inject Tailwind CSS into the Shadow DOM, otherwise the shadown DOM won't have our styling
  const tailwindLink = document.createElement('link');
  tailwindLink.rel = 'stylesheet';
  tailwindLink.href = chrome.runtime.getURL('assets/css/Sidepanel.chunk.css');
  shadowRoot.appendChild(tailwindLink);

  const container = document.createElement('div');
  container.id = 'drumkit-popup-view-root';
  container.classList.add('drumkit-popup-discrete-root-element');

  shadowRoot.appendChild(container);

  createRoot(container).render(<SidebarOpener />);
}
