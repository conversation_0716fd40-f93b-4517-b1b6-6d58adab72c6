/* Antd overrides */

.ant-picker-input {
  font-size: 14px !important;
}

.ant-picker-suffix {
  display: none !important;
}

.ant-select {
  color: hsl(var(--neutral-input-text)) !important;
  background-color: hsl(var(--neutral-input-bg)) !important;
  border: 1px solid hsl(var(--neutral-input-border)) !important;
  width: 100%;
  height: 32px !important;
  margin-top: 4px !important;
  border-radius: 4px !important;

  &.ant-select-disabled {
    border: 1px solid hsl(var(--neutral-input-disabled-border)) !important;

    .ant-select-selector {
      background-color: hsl(var(--neutral-input-disabled-bg)) !important;
    }
  }
}

.ant-select-selection-placeholder {
  color: hsl(var(--neutral-input-placeholder)) !important;
}

.ant-select .ant-select-selector,
.ant-select .ant-select-selection-search-input {
  color: hsl(var(--neutral-input-text)) !important;
  background-color: hsl(var(--neutral-input-bg)) !important;
  cursor: pointer !important;
  border: none !important;
  border-radius: 4px !important;
  transition: background-color 0s !important;
}

.ant-select .ant-select-selection-item,
.ant-select .ant-select-selection-search {
  color: hsl(var(--neutral-input-text)) !important;
  cursor: pointer !important;
  font-size: 14px !important;
}

.ant-select-arrow {
  color: hsl(var(--neutral-500)) !important;
}

.search-filter .ant-select-selector {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin-right: 16px !important;
}

.search-filter .ant-select-focused .ant-select-selector {
  box-shadow: none !important;
}

.search-filter .ant-select-selection-item {
  background: transparent !important;
  padding: 0 !important;
  font-size: 14px !important;
}

.search-filter .ant-select-arrow {
  right: 0px !important;
}

.search-filter .ant-select-arrow svg {
  width: 12px !important;
}

.search-container .ant-picker-suffix {
  display: flex !important;
}

.search-container .ant-picker.ant-picker-outlined {
  border: 1px solid hsl(var(--neutral-input-border)) !important;
  border-radius: 4px !important;
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.search-container .ant-input.ant-input-outlined {
  font-size: 12px !important;
  border: 1px solid hsl(var(--neutral-input-border)) !important;
  padding: 4px 4px 4px 6px !important;
  border-radius: 4px !important;
}

.search-container .ant-picker-input {
  font-size: 10px !important;
}

.search-container .ant-select-selector,
.search-container .ant-select-selection-search-input {
  height: 100% !important;
}

.ant-carousel .slick-dots {
  bottom: -16px;
}

.ant-carousel .slick-slider {
  width: calc(100% - 48px);
  margin: 0px auto;
}

.ant-carousel .single-card.slick-slider {
  width: 100%;
}

.ant-carousel .slick-slide {
  transition: all 0.2s ease;
}

.ant-carousel .slick-slide:has(.carousel-card-hover:hover) {
  transform: scale(1.025);
}

.ant-carousel .slick-dots li button {
  background: hsl(var(--neutral-800));
}

.ant-carousel .slick-dots li.slick-active::after,
.ant-carousel .slick-dots li.slick-active button {
  background: hsl(var(--neutral-600));
  opacity: 1;
}

.ant-carousel .slick-prev,
.ant-carousel .slick-next {
  color: hsl(var(--neutral-500));
}

.ant-carousel .slick-prev {
  margin-left: -30px;
}

.ant-carousel .slick-next {
  margin-right: -30px;
}

/* select dropdown fights for z-index with drumkit-root element on TMS Wrapper */
.ant-select-dropdown {
  z-index: 9999999 !important;
  min-width: 200px !important;
  background-color: hsl(var(--neutral-input-bg)) !important;
  border-color: hsl(var(--neutral-input-border)) !important;

  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: hsl(var(--info-200)) !important;
  }

  .ant-select-item-option-content {
    color: hsl(var(--neutral-input-text)) !important;
  }

  .ant-select-item-option-selected .ant-select-item-option-content {
    color: invert(hsl(var(--neutral-input-text))) !important;
  }
}

/* Make select option groups more readable */
.ant-select-item-group {
  font-weight: 700 !important;
  color: #585757 !important;
  padding-bottom: 0px !important;
  line-height: 2 !important;
}

/* Add a border that behaves as a separator to the top of select option group */
.ant-select-item-group:not(:first-child) {
  margin-top: 8px !important;
  border-top-right-radius: 0px !important;
  border-top-left-radius: 0px !important;
  border-top: 1.5px solid #e8e8e8 !important;
}

.select-trigger > span {
  font-size: 14px !important;
}

.ant-float-btn-body {
  border-radius: 4px !important;
  background-color: hsl(var(--brand)) !important;
}

.ant-float-btn-icon {
  display: none;
}

.ant-float-btn-description {
  font-family: 'Plus Jakarta Sans', sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}
