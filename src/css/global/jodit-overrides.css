
/* Jodit */

#drumkit-content-view-root {
  /* Vulcan needs to override Jo<PERSON>'s border */
  .jodit-container {
    background-color: #f5f5f6 !important;
    border: 1px solid #dadada !important;
    border-radius: 3px !important;
  }

  /* Offsetting the unordered lists to the left for proper display */
  .jodit-wysiwyg ul,
  .jodit-dialog__content ul {
    list-style-type: disc;
    margin: 0;
    padding: 0 20px 10px;
  }

  /* Setting nested unordered lists bullet style */
  .jodit-wysiwyg ul ul,
  .jodit-dialog__content ul ul {
    list-style-type: circle;
  }

  /* Setting double nested unordered lists bullet style */
  .jodit-wysiwyg ul ul ul,
  .jodit-dialog__content ul ul ul {
    list-style-type: square;
  }

  /* Offsetting the unordered lists to the left for proper display */
  .jodit-wysiwyg ol,
  .jodit-dialog__content ol {
    list-style-type: decimal;
    margin: 0;
    padding: 0 20px 10px;
  }

  /* Setting table cell borders */
  .jodit-wysiwyg td,
  .jodit-dialog__content td {
    border: solid 1px;
  }

  /* Setting smaller font size for tab buttons when 3 or more tabs */
  .jodit-tabs__buttons:has(button:nth-child(3)) .jodit-ui-button__text {
    padding: 1px !important;
    font-size: 12px !important;
  }

  /* Setting color and underline for links */
  .jodit-wysiwyg a {
    color: #0066cc;
    text-decoration: underline;
  }


  /* Dark mode support for Jodit editor */
  .dark {
    .jodit {
      border-color: hsl(var(--neutral-input-border)) !important;
    }

    .jodit-container {
      border-radius: 4px !important;
    }

    .jodit-toolbar-button__trigger svg {
      fill: hsl(var(--neutral-input-text)) !important;
    }

    .jodit-toolbar-button__trigger:hover svg {
      fill: invert(hsl(var(--neutral-input-text))) !important;
    }

    .jodit-toolbar__box {
      background-color: hsl(var(--neutral-input-bg)) !important;
      border-color: hsl(var(--neutral-input-border)) !important;
    }

    .jodit-status-bar {
      background-color: hsl(var(--neutral-input-bg)) !important;
      color: hsl(var(--neutral-input-text)) !important;
    }

    .jodit-popup__content {
      background-color: hsl(var(--neutral-input-bg)) !important;

      .jodit-toolbar-button__text {
        color: hsl(var(--neutral-input-text)) !important;
      }

      .jodit-toolbar-button__button:hover .jodit-toolbar-button__text {
        color: invert(hsl(var(--neutral-input-text))) !important;
      }
    }

    .jodit-form__inserter .jodit-form__center {
      color: hsl(var(--neutral-input-text)) !important;
    }

    .jodit-toolbar-button__icon svg {
      fill: hsl(var(--neutral-input-text)) !important;
    }

    .jodit-toolbar-button_brush svg,
    .jodit-toolbar-button_brushCell svg {
      stroke: hsl(var(--neutral-input-text)) !important;
    }

    .jodit-toolbar-button__button:hover svg {
      fill: invert(hsl(var(--neutral-input-text))) !important;
    }

    .jodit-toolbar-button_brush .jodit-toolbar-button__button:hover svg,
    .jodit-toolbar-button_brushCell .jodit-toolbar-button__button:hover svg {
      stroke: #444444 !important;
    }

    .jodit-input::placeholder {
      color: hsl(var(--neutral-input-placeholder)) !important;
    }

    .jodit-tabs__buttons[role="tablist"] {
      background-color: hsl(var(--neutral-400)/0.5) !important;

      .jodit-ui-button[aria-pressed="false"] .jodit-ui-button__text {
        color: hsl(var(--neutral-input-text)) !important;
      }
      .jodit-ui-button:hover .jodit-ui-button__text {
        color: hsl(var(--neutral-300)) !important;
      }
    }
  }
}

/* Fixing the popup position when there are tabs (e.g. color picker) */
.jodit-popup:has(.jodit-popup__content:has(.jodit-tabs)) {
  left: 20px !important;
}
