/* This is a hack to fix anti-aliasing for rounded borders in dark mode */
/* Ref: https://stackoverflow.com/questions/23135196/css-1px-border-stroke-breaks-around-border-radius */
.dark {
  .border-neutral-input-border:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-input-border)) inset, 0 0 1px 0px hsl(var(--neutral-input-border));
  }
  .border-neutral-input-disabled-border:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-input-disabled-border)) inset, 0 0 1px 0px hsl(var(--neutral-input-disabled-border));
  }
  .border-neutral-100:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-100)) inset, 0 0 1px 0px hsl(var(--neutral-100));
  }
  .border-neutral-200:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-200)) inset, 0 0 1px 0px hsl(var(--neutral-200));
  }
  .border-neutral-300:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-300)) inset, 0 0 1px 0px hsl(var(--neutral-300));
  }
  .border-neutral-400:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-400)) inset, 0 0 1px 0px hsl(var(--neutral-400));
  }
  .border-neutral-500:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-500)) inset, 0 0 1px 0px hsl(var(--neutral-500));
  }
  .border-neutral-600:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-600)) inset, 0 0 1px 0px hsl(var(--neutral-600));
  }
  .border-neutral-700:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-700)) inset, 0 0 1px 0px hsl(var(--neutral-700));
  }
  .border-neutral-800:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-800)) inset, 0 0 1px 0px hsl(var(--neutral-800));
  }
  .border-neutral-900:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-900)) inset, 0 0 1px 0px hsl(var(--neutral-900));
  }
  .border-brand:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--brand-main)) inset, 0 0 1px 0px hsl(var(--brand-main));
  }
  .border-accent:not([class*="border-t"]):not([class*="border-b"]):not([class*="border-l"]):not([class*="border-r"]) {
    box-shadow: 0 0 1px 0px hsl(var(--accent-main)) inset, 0 0 1px 0px hsl(var(--accent-main));
  }

  .ant-select {
    box-shadow: 0 0 1px 0px hsl(var(--neutral-input-border)) inset, 0 0 1px 0px hsl(var(--neutral-input-border));
  }
}
