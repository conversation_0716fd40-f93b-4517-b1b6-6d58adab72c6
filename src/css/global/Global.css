/* Anti-aliasing */
@import './anti-aliasing.css';

/* Library styles overrides */
@import './jodit-overrides.css';
@import './antd-overrides.css';

/* Vanilla CSS animations */
@import './animations.css';

/* Custom Tailwind v4 layer import setup to avoid messing up with Gmail's styles */
@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme);
@import "tw-animate-css";

@layer utilities {
  #drumkit-content-view-root,
  #drumkit-popup-view-root {
    @import "tailwindcss/preflight.css";
    @tailwind utilities;
  }
}


#drumkit-content-view-root,
#drumkit-popup-view-root {
  background: hsl(var(--sidepanel-bg));
  min-height: 100%;
}

#drumkit-popup-view-root {
  background: transparent;
  min-height: 0;
  display: inline-block;
}

#drumkit-content-view-root *,
#drumkit-popup-view-root * {
  font-family: 'Plus Jakarta Sans', sans-serif;

  /*
    Though not ideal for accessibility, outline currently causes a lot of issues through multiple forms
    TODO: Refactor forms to use outline properly
  */
  outline: none !important;
}

/* Outline is disabled, but we still show focus states for inputs through the border */
#drumkit-content-view-root *:not([disabled]):not([readonly]):focus-visible {
  border-color: hsl(var(--brand-main)) !important;
}

#drumkit-content-view-root::-webkit-scrollbar,
#shadow-root > div::-webkit-scrollbar {
  width: 8px;
}

#drumkit-content-view-root::-webkit-scrollbar-thumb,
#shadow-root > div::-webkit-scrollbar-thumb {
  background: hsl(var(--neutral-400));
  width: 2px;
  border: 2px solid transparent; /* Creates the gap */
  background-clip: content-box; /* Important: clips background to content area */
  border-radius: 16px;
}

#drumkit-content-view-root::-webkit-scrollbar-track,
#shadow-root > div::-webkit-scrollbar-track {
  background: hsl(var(--sidepanel-bg));
}

#drumkit-content-view-root input:read-only, #drumkit-content-view-root input[disabled] {
  color: hsl(var(--neutral-input-disabled-text)) !important;
  background-color: hsl(var(--neutral-input-disabled-bg)) !important;
  border-color: hsl(var(--neutral-input-disabled-border)) !important;
}

#drumkit-content-view-root .lucide {
  color: hsl(var(--neutral-800));
  stroke: hsl(var(--neutral-800));
}

#drumkit-content-view-root .data-\[state\=active\]\:text-brand-main[data-state="active"] .lucide {
  color: hsl(var(--brand-main));
  stroke: hsl(var(--brand-main));
}

.drumkit-sidebar-modifier {
  width: calc(100vw - 300px);
  overflow-x: scroll;
}

.drumkit-discrete-root-element {
  background: hsl(var(--sidepanel-bg));
  width: 300px;
  height: 100vh;
  position: fixed;
  z-index: 9999999;
  right: 0;
  bottom: 0;
  top: 0;
}

/* Custom classes to automatically resize AI hint label if there's not enough space for it
https://medium.com/swlh/hiding-an-element-when-there-is-no-enough-space-thanos-snap-technique-8a11e31267c0*/
.ai-hint-span {
  max-width: max(
    0px,
    calc((90% - 72px) * 999)
  ); /* 72px approximate width of icon + padding + text */
  overflow: hidden;
}

.ai-hint-text {
  max-width: max(
    0px,
    calc((70% - 48px) * 999) /* 48px = approximate width of 'AI-filled' */
  ); /* 70% to account for the space the sparkle icon takes up */
  overflow: hidden;
}

.select-trigger > span {
  font-size: 14px !important;
}

[role='tablist']::-webkit-scrollbar,
[role='tablist'] div::-webkit-scrollbar {
  background-color: transparent;
  width: 16px;
}

[role='tablist']::-webkit-scrollbar-track,
[role='tablist'] div::-webkit-scrollbar-track {
  background-color: transparent;
}

[role='tablist']::-webkit-scrollbar-thumb,
[role='tablist'] div::-webkit-scrollbar-thumb {
  background-color: hsl(var(--neutral-300));
  border-radius: 16px;
  border: 4px solid hsl(var(--neutral-100));
}

[role='tablist']::-webkit-scrollbar-thumb:hover,
[role='tablist'] div::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--neutral-400));
}

[role='tablist']::-webkit-scrollbar-button,
[role='tablist'] div::-webkit-scrollbar-button {
  display: none;
}

.mcp-logo-icon {
  fill: hsl(var(--accent-main));
}

.mcp-logo-icon:hover {
  fill: hsl(var(--neutral-400));
}

/* Tailwind variables - declared on Global.css for Vulcan due to Tailwind scope issues */
html {
  /* Color Palette defaults - duplicates simply meant to act as fallbacks */
  --brand: 32 100% 49%;         /* #f98600 */
  --brand-main: 32 100% 49%;    /* #f98600 */
  --accent: 199 89% 48%;        /* #0ea5e9 */
  --accent-main: 199 89% 48%;   /* #0ea5e9 */
  --success: 142 76% 36%;       /* #00ba34 */
  --success-main: 142 76% 36%;  /* #00ba34 */
  --error: 0 79% 54%;           /* #e92c2c */
  --error-main: 0 79% 54%;      /* #e92c2c */
  --warning: 45 100% 49%;       /* #f9c200 */
  --warning-main: 45 100% 49%;  /* #f9c200 */
  --info: 217 91% 60%;          /* #3b82f6 */
  --info-main: 217 91% 60%;     /* #3b82f6 */
  --neutral: 0 0% 34%;          /* #585757 */
  --neutral-main: 0 0% 34%;     /* #585757 */

  /* Brand Colors - Orange (Primary) */
  --brand-50: 32 100% 98%; /* #fff4e8 */
  --brand-100: 32 100% 91%; /* #fee7cc */
  --brand-200: 32 100% 84%; /* #fed7aa */
  --brand-300: 32 100% 77%; /* #fec085 */
  --brand-400: 32 100% 63%; /* #fe9659 */
  --brand-500: 32 100% 49%; /* #f98600 */
  --brand-600: 32 100% 41%; /* #ea580c */
  --brand-700: 32 100% 33%; /* #c2410c */
  --brand-800: 32 100% 25%; /* #9a3412 */
  --brand-900: 32 100% 17%; /* #7c2d12 */

  /* Accent Colors - Blue (AI Features) */
  --accent-50: 199 89% 97%; /* #f0f8ff */
  --accent-100: 199 89% 91%; /* #cce7ff */
  --accent-200: 199 89% 85%; /* #a8d8ff */
  --accent-300: 199 89% 75%; /* #7dd3fc */
  --accent-400: 199 89% 60%; /* #38bdf8 */
  --accent-500: 199 89% 48%; /* #0ea5e9 */
  --accent-600: 199 89% 44%; /* #0284c7 */
  --accent-700: 199 89% 38%; /* #0369a1 */
  --accent-800: 199 89% 32%; /* #075985 */
  --accent-900: 199 89% 26%; /* #0c4a6e */

  /* Success Colors - Green */
  --success-50: 142 76% 97%; /* #f0fdf4 */
  --success-100: 142 76% 91%; /* #ccf1d6 */
  --success-200: 142 76% 85%; /* #a7f3d0 */
  --success-300: 142 76% 75%; /* #6ee7b7 */
  --success-400: 142 76% 60%; /* #34d399 */
  --success-500: 142 76% 36%; /* #00ba34 */
  --success-600: 142 76% 44%; /* #33c85d */
  --success-700: 142 76% 29%; /* #00952a */
  --success-800: 142 76% 22%; /* #047857 */
  --success-900: 142 76% 15%; /* #065f46 */

  /* Error Colors - Red */
  --error-50: 0 79% 98%; /* #fff0f0 */
  --error-100: 0 79% 97%; /* #ffebeb */
  --error-200: 0 79% 95%; /* #fecaca */
  --error-300: 0 79% 90%; /* #fca5a5 */
  --error-400: 0 79% 80%; /* #f87171 */
  --error-500: 0 79% 54%; /* #e92c2c */
  --error-600: 0 79% 62%; /* #ed5656 */
  --error-700: 0 79% 49%; /* #e90e0e */
  --error-800: 0 79% 42%; /* #dc2626 */
  --error-900: 0 79% 35%; /* #b91c1c */

  /* Warning Colors - Yellow */
  --warning-50: 45 100% 98%; /* #fffbeb */
  --warning-100: 45 100% 96%; /* #fefae8 */
  --warning-200: 45 100% 93%; /* #fef5d0 */
  --warning-300: 45 100% 88%; /* #fde68a */
  --warning-400: 45 100% 80%; /* #facc15 */
  --warning-500: 45 100% 49%; /* #f9c200 */
  --warning-600: 45 100% 57%; /* #fbd644 */
  --warning-700: 45 100% 55%; /* #facc15 */
  --warning-800: 45 100% 47%; /* #eab308 */
  --warning-900: 45 100% 39%; /* #a16207 */

  /* Info Colors - Blue (different from accent) */
  --info-50: 217 91% 98%; /* #f8fafc */
  --info-100: 217 91% 97%; /* #eff6ff */
  --info-200: 217 91% 93%; /* #dbeafe */
  --info-300: 217 91% 85%; /* #93c5fd */
  --info-400: 217 91% 75%; /* #60a5fa */
  --info-500: 217 91% 60%; /* #3b82f6 */
  --info-600: 217 91% 54%; /* #2563eb */
  --info-700: 217 91% 49%; /* #1d4ed8 */
  --info-800: 217 91% 44%; /* #1e40af */
  --info-900: 217 91% 39%; /* #1e3a8a */

  /* Neutral Colors - Gray */
  --neutral-50: 0 0% 100%; /* #ffffff */
  --neutral-100: 0 0% 97%; /* #f7f7f7 */
  --neutral-200: 0 0% 94%; /* #f0f0f0 */
  --neutral-300: 0 0% 85%; /* #d9d9d9 */
  --neutral-400: 0 0% 59%; /* #969696 */
  --neutral-500: 0 0% 34%; /* #585757 */
  --neutral-600: 0 0% 25%; /* #404040 */
  --neutral-700: 0 0% 18%; /* #2e2e2e */
  --neutral-800: 0 0% 11%; /* #1c1c1c */
  --neutral-900: 0 0% 4%; /* #0a0a0a */

  /* Platform Background Colors - Light theme */
  --sidepanel-bg: 0 0% 100%;   /* #fcfcfc */

  /* Input Colors */
  --neutral-input-bg: 0 0% 100%;             /* --neutral-50 */
  --neutral-input-border: 0 0% 34%;          /* --neutral-500 */
  --neutral-input-text: 0 0% 34%;            /* --neutral-500 */
  --neutral-input-placeholder: 0 0% 65%;
  --neutral-input-disabled-bg: 0 0% 94%;     /* --neutral-200 */
  --neutral-input-disabled-text: 0 0% 34%;   /* --neutral-500 */
  --neutral-input-disabled-border: 0 0% 59%; /* --neutral-400 */
}

html.dark {
  /* Color Palette defaults - duplicates simply meant to act as fallbacks */
  --brand: 350 60% 65%;         /* #d9668a */
  --brand-main: 350 60% 65%;    /* #d9668a */
  --accent: 199 89% 48%;        /* #0ea5e9 */
  --accent-main: 199 89% 48%;   /* #0ea5e9 */
  --success: 142 76% 36%;       /* #00ba34 */
  --success-main: 142 76% 36%;  /* #00ba34 */
  --error: 0 79% 54%;           /* #e92c2c */
  --error-main: 0 79% 54%;      /* #e92c2c */
  --warning: 45 100% 49%;       /* #f9c200 */
  --warning-main: 45 100% 49%;  /* #f9c200 */
  --info: 217 91% 60%;          /* #3b82f6 */
  --info-main: 217 91% 60%;     /* #3b82f6 */
  --neutral: 0 0% 34%;          /* #585757 */
  --neutral-main: 0 0% 34%;     /* #585757 */

  /* Brand Colors - Dark theme */
  --brand-50: 350 60% 12%;  /* Dark background */
  --brand-100: 350 60% 18%; /* Darker border */
  --brand-200: 350 60% 25%; /* Darker shade */
  --brand-300: 350 60% 35%; /* Medium dark */
  --brand-400: 350 60% 50%; /* Medium */
  --brand-500: 350 60% 65%; /* Regular */
  --brand-600: 350 60% 75%; /* Light */
  --brand-700: 350 60% 80%; /* Lighter */
  --brand-800: 350 60% 85%; /* Very light */
  --brand-900: 350 60% 90%; /* Lightest */

  /* Accent Colors - Dark theme */
  --accent-50: 199 89% 8%; /* Dark background */
  --accent-100: 199 89% 12%; /* Darker border */
  --accent-200: 199 89% 16%; /* Darker shade */
  --accent-300: 199 89% 25%; /* Medium dark */
  --accent-400: 199 89% 40%; /* Medium */
  --accent-500: 199 89% 55%; /* Regular */
  --accent-600: 199 89% 65%; /* Light */
  --accent-700: 199 89% 70%; /* Lighter */
  --accent-800: 199 89% 75%; /* Very light */
  --accent-900: 199 89% 80%; /* Lightest */

  /* Success Colors - Dark theme */
  --success-50: 142 76% 8%; /* Dark background */
  --success-100: 142 76% 12%; /* Darker border */
  --success-200: 142 76% 16%; /* Darker shade */
  --success-300: 142 76% 25%; /* Medium dark */
  --success-400: 142 76% 40%; /* Medium */
  --success-500: 142 76% 55%; /* Regular */
  --success-600: 142 76% 65%; /* Light */
  --success-700: 142 76% 70%; /* Lighter */
  --success-800: 142 76% 75%; /* Very light */
  --success-900: 142 76% 80%; /* Lightest */

  /* Error Colors - Dark theme */
  --error-50: 0 79% 8%; /* Dark background */
  --error-100: 0 79% 12%; /* Darker border */
  --error-200: 0 79% 16%; /* Darker shade */
  --error-300: 0 79% 25%; /* Medium dark */
  --error-400: 0 79% 40%; /* Medium */
  --error-500: 0 79% 60%; /* Regular */
  --error-600: 0 79% 68%; /* Light */
  --error-700: 0 79% 70%; /* Lighter */
  --error-800: 0 79% 75%; /* Very light */
  --error-900: 0 79% 80%; /* Lightest */

  /* Warning Colors - Dark theme */
  --warning-50: 45 100% 8%; /* Dark background */
  --warning-100: 45 100% 12%; /* Darker border */
  --warning-200: 45 100% 16%; /* Darker shade */
  --warning-300: 45 100% 25%; /* Medium dark */
  --warning-400: 45 100% 40%; /* Medium */
  --warning-500: 45 100% 55%; /* Regular */
  --warning-600: 45 100% 63%; /* Light */
  --warning-700: 45 100% 65%; /* Lighter */
  --warning-800: 45 100% 70%; /* Very light */
  --warning-900: 45 100% 75%; /* Lightest */

  /* Info Colors - Dark theme */
  --info-50: 217 91% 8%; /* Dark background */
  --info-100: 217 91% 12%; /* Darker border */
  --info-200: 217 91% 16%; /* Darker shade */
  --info-300: 217 91% 25%; /* Medium dark */
  --info-400: 217 91% 40%; /* Medium */
  --info-500: 217 91% 66%; /* Regular */
  --info-600: 217 91% 70%; /* Light */
  --info-700: 217 91% 75%; /* Lighter */
  --info-800: 217 91% 80%; /* Very light */
  --info-900: 217 91% 85%; /* Lightest */

  /* Neutral Colors - Dark theme */
  --neutral-50: 240 10% 3.9%; /* Dark background */
  --neutral-100: 240 3.7% 15.9%; /* Darker secondary background */
  --neutral-200: 240 3.7% 12%; /* Even darker tertiary background */
  --neutral-300: 240 3.7% 25%; /* Medium dark */
  --neutral-400: 240 3.7% 45%; /* Medium */
  --neutral-500: 240 3.7% 75%; /* Regular */
  --neutral-600: 240 3.7% 80%; /* Light */
  --neutral-700: 240 3.7% 85%; /* Lighter */
  --neutral-800: 240 3.7% 90%; /* Very light */
  --neutral-900: 240 3.7% 95%; /* Lightest */

  /* Platform Background Colors - Dark theme */
  --sidepanel-bg: 0 0% 6.6%; /* Sidepanel's dark background */

  /* Input Colors */
  --neutral-input-bg: 0 0% 20%;
  --neutral-input-border: 0 0% 40%;
  --neutral-input-text: 0 0% 100%;
  --neutral-input-placeholder: 0 0% 59%;
  --neutral-input-disabled-bg: 0 0% 12%;
  --neutral-input-disabled-text: 0 0% 65%;
  --neutral-input-disabled-border: 0 0% 25%;
}

/* Tailwind v4 Theme Configuration */
@theme {
  --color-brand-50: hsl(var(--brand-50));
  --color-brand-100: hsl(var(--brand-100));
  --color-brand-200: hsl(var(--brand-200));
  --color-brand-300: hsl(var(--brand-300));
  --color-brand-400: hsl(var(--brand-400));
  --color-brand-500: hsl(var(--brand-500));
  --color-brand-600: hsl(var(--brand-600));
  --color-brand-700: hsl(var(--brand-700));
  --color-brand-800: hsl(var(--brand-800));
  --color-brand-900: hsl(var(--brand-900));
  --color-brand: hsl(var(--brand-main));
  --color-brand-main: hsl(var(--brand-main));

  --color-accent-50: hsl(var(--accent-50));
  --color-accent-100: hsl(var(--accent-100));
  --color-accent-200: hsl(var(--accent-200));
  --color-accent-300: hsl(var(--accent-300));
  --color-accent-400: hsl(var(--accent-400));
  --color-accent-500: hsl(var(--accent-500));
  --color-accent-600: hsl(var(--accent-600));
  --color-accent-700: hsl(var(--accent-700));
  --color-accent-800: hsl(var(--accent-800));
  --color-accent-900: hsl(var(--accent-900));
  --color-accent: hsl(var(--accent-main));
  --color-accent-main: hsl(var(--accent-main));

  --color-success-50: hsl(var(--success-50));
  --color-success-100: hsl(var(--success-100));
  --color-success-200: hsl(var(--success-200));
  --color-success-300: hsl(var(--success-300));
  --color-success-400: hsl(var(--success-400));
  --color-success-500: hsl(var(--success-500));
  --color-success-600: hsl(var(--success-600));
  --color-success-700: hsl(var(--success-700));
  --color-success-800: hsl(var(--success-800));
  --color-success-900: hsl(var(--success-900));
  --color-success: hsl(var(--success-main));
  --color-success-main: hsl(var(--success-main));

  --color-error-50: hsl(var(--error-50));
  --color-error-100: hsl(var(--error-100));
  --color-error-200: hsl(var(--error-200));
  --color-error-300: hsl(var(--error-300));
  --color-error-400: hsl(var(--error-400));
  --color-error-500: hsl(var(--error-500));
  --color-error-600: hsl(var(--error-600));
  --color-error-700: hsl(var(--error-700));
  --color-error-800: hsl(var(--error-800));
  --color-error-900: hsl(var(--error-900));
  --color-error: hsl(var(--error-main));
  --color-error-main: hsl(var(--error-main));

  --color-warning-50: hsl(var(--warning-50));
  --color-warning-100: hsl(var(--warning-100));
  --color-warning-200: hsl(var(--warning-200));
  --color-warning-300: hsl(var(--warning-300));
  --color-warning-400: hsl(var(--warning-400));
  --color-warning-500: hsl(var(--warning-500));
  --color-warning-600: hsl(var(--warning-600));
  --color-warning-700: hsl(var(--warning-700));
  --color-warning-800: hsl(var(--warning-800));
  --color-warning-900: hsl(var(--warning-900));
  --color-warning: hsl(var(--warning-main));
  --color-warning-main: hsl(var(--warning-main));

  --color-info-50: hsl(var(--info-50));
  --color-info-100: hsl(var(--info-100));
  --color-info-200: hsl(var(--info-200));
  --color-info-300: hsl(var(--info-300));
  --color-info-400: hsl(var(--info-400));
  --color-info-500: hsl(var(--info-500));
  --color-info-600: hsl(var(--info-600));
  --color-info-700: hsl(var(--info-700));
  --color-info-800: hsl(var(--info-800));
  --color-info-900: hsl(var(--info-900));
  --color-info: hsl(var(--info-main));
  --color-info-main: hsl(var(--info-main));

  --color-neutral-50: hsl(var(--neutral-50));
  --color-neutral-100: hsl(var(--neutral-100));
  --color-neutral-200: hsl(var(--neutral-200));
  --color-neutral-300: hsl(var(--neutral-300));
  --color-neutral-400: hsl(var(--neutral-400));
  --color-neutral-500: hsl(var(--neutral-500));
  --color-neutral-600: hsl(var(--neutral-600));
  --color-neutral-700: hsl(var(--neutral-700));
  --color-neutral-800: hsl(var(--neutral-800));
  --color-neutral-900: hsl(var(--neutral-900));
  --color-neutral: hsl(var(--neutral-main));
  --color-neutral-main: hsl(var(--neutral-main));

  --color-neutral-sidepanel: hsl(var(--sidepanel-bg));
  --color-neutral-input-bg: hsl(var(--neutral-input-bg));
  --color-neutral-input-border: hsl(var(--neutral-input-border));
  --color-neutral-input-text: hsl(var(--neutral-input-text));
  --color-neutral-input-placeholder: hsl(var(--neutral-input-placeholder));
  --color-neutral-input-disabled-bg: hsl(var(--neutral-input-disabled-bg));
  --color-neutral-input-disabled-text: hsl(var(--neutral-input-disabled-text));
  --color-neutral-input-disabled-border: hsl(var(--neutral-input-disabled-border));

  --font-family-sans: 'Plus Jakarta Sans', sans-serif;

  --border-radius: 0.5rem;
  --border-radius-md: 1rem;

  --container-padding: 2rem;
  --container-max-width: 1400px;

  --screen-xxs: 330px;

  --keyframes-accordion-down: {
    from: { height: 0 }
    to: { height: var(--radix-accordion-content-height) }
  }

  --keyframes-accordion-up: {
    from: { height: var(--radix-accordion-content-height) }
    to: { height: 0 }
  }

  --keyframes-fade-in: {
    from: { opacity: 0 }
    to: { opacity: 1 }
  }

  --keyframes-fade-out: {
    from: { opacity: 1 }
    to: { opacity: 0 }
  }

  --keyframes-zoom-in: {
    from: { opacity: 0; transform: scale(0.95) }
    to: { opacity: 1; transform: scale(1) }
  }

  --keyframes-zoom-out: {
    from: { opacity: 1; transform: scale(1) }
    to: { opacity: 0; transform: scale(0.95) }
  }

  --keyframes-slide-in-from-top: {
    from: { transform: translateY(-100%) }
    to: { transform: translateY(0) }
  }

  --keyframes-slide-in-from-bottom: {
    from: { transform: translateY(100%) }
    to: { transform: translateY(0) }
  }

  --keyframes-slide-in-from-left: {
    from: { transform: translateX(-100%) }
    to: { transform: translateX(0) }
  }

  --keyframes-slide-in-from-right: {
    from: { transform: translateX(100%) }
    to: { transform: translateX(0) }
  }

  --keyframes-slide-out-to-top: {
    from: { transform: translateY(0) }
    to: { transform: translateY(-100%) }
  }

  --keyframes-slide-out-to-bottom: {
    from: { transform: translateY(0) }
    to: { transform: translateY(100%) }
  }

  --keyframes-slide-out-to-left: {
    from: { transform: translateX(0) }
    to: { transform: translateX(-100%) }
  }

  --keyframes-slide-out-to-right: {
    from: { transform: translateX(0) }
    to: { transform: translateX(100%) }
  }

  --keyframes-slide-in-from-top-2: {
    from: { transform: translateY(-0.5rem) }
    to: { transform: translateY(0) }
  }

  --keyframes-slide-in-from-bottom-2: {
    from: { transform: translateY(0.5rem) }
    to: { transform: translateY(0) }
  }

  --keyframes-slide-in-from-left-2: {
    from: { transform: translateX(-0.5rem) }
    to: { transform: translateX(0) }
  }

  --keyframes-slide-in-from-right-2: {
    from: { transform: translateX(0.5rem) }
    to: { transform: translateX(0) }
  }

  --keyframes-slide-in-from-top-48: {
    from: { transform: translateY(-48%) }
    to: { transform: translateY(0) }
  }

  --keyframes-slide-in-from-left-50: {
    from: { transform: translateX(-50%) }
    to: { transform: translateX(0) }
  }

  --keyframes-slide-out-to-top-48: {
    from: { transform: translateY(0) }
    to: { transform: translateY(-48%) }
  }

  --keyframes-slide-out-to-left-50: {
    from: { transform: translateX(0) }
    to: { transform: translateX(-50%) }
  }

  --keyframes-fade-out-80: {
    from: { opacity: 1 }
    to: { opacity: 0.2 }
  }

  --keyframes-slide-out-to-right-full: {
    from: { transform: translateX(0) }
    to: { transform: translateX(100%) }
  }

  --keyframes-slide-in-from-top-full: {
    from: { transform: translateY(-100%) }
    to: { transform: translateY(0) }
  }

  --keyframes-slide-in-from-bottom-full: {
    from: { transform: translateY(100%) }
    to: { transform: translateY(0) }
  }

  --animation-accordion-down: accordion-down 0.2s ease-out;
  --animation-accordion-up: accordion-up 0.2s ease-out;
  --animation-fade-in: fade-in 0.2s ease-out;
  --animation-fade-out: fade-out 0.2s ease-out;
  --animation-zoom-in: zoom-in 0.2s ease-out;
  --animation-zoom-out: zoom-out 0.2s ease-out;
  --animation-slide-in-from-top: slide-in-from-top 0.2s ease-out;
  --animation-slide-in-from-bottom: slide-in-from-bottom 0.2s ease-out;
  --animation-slide-in-from-left: slide-in-from-left 0.2s ease-out;
  --animation-slide-in-from-right: slide-in-from-right 0.2s ease-out;
  --animation-slide-out-to-top: slide-out-to-top 0.2s ease-out;
  --animation-slide-out-to-bottom: slide-out-to-bottom 0.2s ease-out;
  --animation-slide-out-to-left: slide-out-to-left 0.2s ease-out;
  --animation-slide-out-to-right: slide-out-to-right 0.2s ease-out;
  --animation-slide-in-from-top-2: slide-in-from-top-2 0.2s ease-out;
  --animation-slide-in-from-bottom-2: slide-in-from-bottom-2 0.2s ease-out;
  --animation-slide-in-from-left-2: slide-in-from-left-2 0.2s ease-out;
  --animation-slide-in-from-right-2: slide-in-from-right-2 0.2s ease-out;
  --animation-slide-in-from-top-48: slide-in-from-top-48 0.2s ease-out;
  --animation-slide-in-from-left-50: slide-in-from-left-50 0.2s ease-out;
  --animation-slide-out-to-top-48: slide-out-to-top-48 0.2s ease-out;
  --animation-slide-out-to-left-50: slide-out-to-left-50 0.2s ease-out;
  --animation-fade-out-80: fade-out-80 0.2s ease-out;
  --animation-slide-out-to-right-full: slide-out-to-right-full 0.2s ease-out;
  --animation-slide-in-from-top-full: slide-in-from-top-full 0.2s ease-out;
  --animation-slide-in-from-bottom-full: slide-in-from-bottom-full 0.2s ease-out;
}

#shadow-root > div {
  overflow: auto !important;
}
