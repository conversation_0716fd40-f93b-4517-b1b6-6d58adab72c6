// Used for OneNetwork Appointment Scheduling
export const SEARCH_OPTIONS = {
  REF_NO: 'ref_no',
  SHIP_NO: 'ship_no',
} as const;

export type SearchOption = (typeof SEARCH_OPTIONS)[keyof typeof SEARCH_OPTIONS];

export const SEARCH_OPTION_LABELS: Record<SearchOption, string> = {
  [SEARCH_OPTIONS.SHIP_NO]: 'Shipment No',
  [SEARCH_OPTIONS.REF_NO]: 'Order Ref No',
};

export const DEFAULT_SEARCH_OPTION: SearchOption = SEARCH_OPTIONS.SHIP_NO;
