import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type LaneHistoryFromServiceRequest = {
  quoteRequestId: number;
  originCity: string;
  originState: string;
  originZip: string;
  originCountry: string;
  destinationCity: string;
  destinationState: string;
  destinationZip: string;
  destinationCountry: string;
  transportType: string;
};

export type MonthData = {
  month: string;
  reports: number;
  highRate: number;
  p75Rate: number;
  lowRate: number;
  p25Rate: number;
  averageRate: number;
};

export type Last7DaysData = {
  reports: number;
  averageRate: number;
  lowRate: number;
  highRate: number;
};

export type LaneHistoryFromServiceResponse = {
  origin: string;
  destination: string;
  equipment: string;
  months: MonthData[];
  last7Days: Last7DaysData;
};

export async function getLaneHistoryFromService(
  request: LaneHistoryFromServiceRequest
): Promise<Result<LaneHistoryFromServiceResponse, ApiError>> {
  try {
    const response = await axios.post<LaneHistoryFromServiceResponse>(
      `quote/private/get-from-service/lane-history`,
      request
    );
    return ok(response.data);
  } catch (error: unknown) {
    captureException(error, { functionName: 'getLaneHistoryFromService' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get lane history from service' });
    }

    return err({
      message:
        error.response?.data?.message ||
        'Failed to get lane history from service',
    });
  }
}
