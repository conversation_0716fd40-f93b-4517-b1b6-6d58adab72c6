import { UseFormReturn } from 'react-hook-form';

import { isAxiosError } from 'axios';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { toast } from 'hooks/useToaster';
import {
  BatchQuoteFormValues,
  ProfitType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { Email } from 'types/Email';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import {
  BatchQuickQuoteBody,
  BatchQuotePrivateBody,
  BatchQuoteResponse,
  BatchQuoteResult,
  BatchQuoteStatus,
} from 'types/api/BatchQuote';
import { GenericSuggestion } from 'types/suggestions/CoreSuggestions';

import {
  QuickQuoteResponseQuote,
  mapQuoteFormStopsToStops,
} from './getQuickQuote';

interface GetBatchQuoteProps {
  email: Maybe<Email>;
  clickedSuggestion: Maybe<GenericSuggestion[]>;
  formValues: BatchQuoteFormValues;
  formMethods: UseFormReturn<BatchQuoteFormValues>;
  setQuoteNotConfidentHandler: React.Dispatch<React.SetStateAction<boolean>>;
  setProfit: React.Dispatch<React.SetStateAction<number>>;
  profitType: ProfitType;
  testFailLastQuote?: boolean;
}

export async function getBatchQuote({
  email,
  clickedSuggestion,
  formValues,
  formMethods,
  setQuoteNotConfidentHandler,
  setProfit,
  profitType,
  testFailLastQuote,
}: GetBatchQuoteProps): Promise<MaybeUndef<BatchQuoteResponse>> {
  setQuoteNotConfidentHandler(false);

  // Transform the form values to match backend structure
  const transformedQuickQuotes: BatchQuotePrivateBody[] = formValues.quotes.map(
    (quote, index) => {
      const suggestion = clickedSuggestion?.[index];

      const quoteStops = quote.stops.map((stop: any, stopIndex: number) => ({
        order: stopIndex,
        state: stop.state,
        city: stop.city,
        zip: stop.zip,
        country: stop.country,
      }));

      // Map QuoteFormStop[] to Stop[] for the backend
      const mappedStops = mapQuoteFormStopsToStops(
        quote.stops,
        quote.pickupDate,
        quote.deliveryDate
      );

      return {
        loadId: '', // Default empty string for loadId
        transportType: quote.transportType,
        stops: quoteStops,
        newStops: mappedStops,
        pickupDate: quote.pickupDate.toISOString(),
        deliveryDate: quote.deliveryDate.toISOString(),
        customerName: quote.customerName,
        emailID: email?.id,
        threadID: email?.threadID,
        quoteRequestId: suggestion?.id,
        selectedQuickQuoteId: undefined,
      };
    }
  );

  const body: BatchQuickQuoteBody = {
    quickQuotes: transformedQuickQuotes,
    testFailLastQuote: testFailLastQuote,
  };

  try {
    const { data } = await axios.post<BatchQuoteResponse>(`/batch-quote`, body);

    const lowConfidenceResults = data.results.filter(
      (result: BatchQuoteResult) =>
        result.status === BatchQuoteStatus.Success &&
        result.quotes?.some(
          (quote: QuickQuoteResponseQuote) =>
            quote.metadata?.error &&
            quote.metadata.error.includes('unable') &&
            quote.metadata.error.includes('confident')
        )
    );

    if (lowConfidenceResults.length > 0) {
      const firstLowConfResult = lowConfidenceResults[0];

      setProfit(
        profitType === ProfitType.Amount
          ? firstLowConfResult.configuration?.defaultFlatMargin || 100
          : firstLowConfResult.configuration?.defaultPercentMargin || 10
      );

      toast({
        description: `Greenscreens quote confidence is below ${
          firstLowConfResult.configuration?.lowConfidenceThreshold
            ? firstLowConfResult.configuration.lowConfidenceThreshold + '%.'
            : 'your minimum threshold.'
        } Try another quoting approach, then return to complete the form.`,
        variant: 'destructive',
      });

      // Filter out low confidence quotes from each result
      data.results.forEach((result: BatchQuoteResult) => {
        if (result.quotes) {
          result.quotes = result.quotes.filter(
            (quote: QuickQuoteResponseQuote) =>
              !(
                quote.metadata?.error &&
                quote.metadata.error.includes('unable') &&
                quote.metadata.error.includes('confident')
              )
          );
        }
      });

      if (
        data.results.every(
          (r: BatchQuoteResult) => (r.quotes?.length ?? 0) === 0
        )
      ) {
        setQuoteNotConfidentHandler(true);
      }
    }

    return data;
  } catch (error) {
    const defaultErrorMessage =
      'Please try again in a few moments or contact our team if the issue persists.';

    if (isAxiosError(error)) {
      const data = error.response?.data;
      formMethods.setError('root', {
        message: data?.error || data?.message || defaultErrorMessage,
      });
    } else {
      formMethods.setError('root', {
        message: defaultErrorMessage,
      });
    }

    return undefined;
  }
}
