// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { CarrierGroup } from 'types/CarrierGroup';
import captureException from 'utils/captureException';

export async function searchCarrierGroups(
  name: string
): Promise<CarrierGroup[]> {
  try {
    const apiUrl = `/carrier-groups/search?name=${encodeURIComponent(name)}`;

    const response = await axios.get(apiUrl);
    const data = response.data;

    if (!data || !data.carrierGroups) {
      return [];
    }

    const carrierGroups = Array.isArray(data.carrierGroups)
      ? data.carrierGroups
      : [];

    // Filter out groups that have no carriers
    const filteredCarrierGroups = carrierGroups.filter(
      (group: CarrierGroup) => group.carriers && group.carriers.length > 0
    );

    return filteredCarrierGroups;
  } catch (error) {
    captureException(error, { functionName: 'searchCarrierGroups' });
    return [];
  }
}
