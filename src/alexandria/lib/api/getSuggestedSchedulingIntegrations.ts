import { isAxiosError } from 'axios';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import {
  GetSchedulingIntegrationsByCustomerResponse,
  IntegrationWithUsage,
} from 'types/SchedulingAssociations';
import { Maybe } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

interface GetSuggestedSchedulingIntegrationsParams {
  tmsIntegrationId: number;
  warehouseId: string | number;
  tmsCustomerExternalID: Maybe<string>;
  customerName: Maybe<string>;
}

export async function getSuggestedSchedulingIntegrations({
  tmsIntegrationId,
  warehouseId,
  tmsCustomerExternalID,
  customerName,
}: GetSuggestedSchedulingIntegrationsParams): Promise<IntegrationWithUsage[]> {
  const params: Record<string, Maybe<string | number>> = {
    tmsIntegrationId,
    warehouseId,
    tmsCustomerExternalID: tmsCustomerExternalID ?? null,
    customerName: customerName ?? null,
  };

  try {
    const response =
      await axios.get<GetSchedulingIntegrationsByCustomerResponse>(
        '/scheduling/integrations',
        { params }
      );

    const data: GetSchedulingIntegrationsByCustomerResponse =
      response.data ?? {};

    if (Array.isArray(data.integrationsWithUsage)) {
      return data.integrationsWithUsage as IntegrationWithUsage[];
    }

    return [];
  } catch (error) {
    captureException(error, {
      functionName: 'getSuggestedSchedulingIntegrations',
      tmsIntegrationId: String(tmsIntegrationId),
      warehouseId: String(warehouseId),
      hasTmsCustomerExternalID: Boolean(tmsCustomerExternalID),
      hasCustomerName: Boolean(customerName),
    });

    if (isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if ((error as Error)?.message === 'Extension context invalidated.') {
      throw error;
    }

    // Safe fallback
    return [];
  }
}
