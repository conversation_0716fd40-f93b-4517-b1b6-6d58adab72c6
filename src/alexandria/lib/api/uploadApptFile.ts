import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export interface AppointmentFile {
  file: File;
  fileName: string;
  size: number;
}

interface UploadFileResponse {
  key: string;
  url: string;
  fileName: string;
  size: number;
}

// Helper function to upload a single file to Opendock storage
export async function uploadSingleFile(
  file: AppointmentFile
): Promise<Result<UploadFileResponse, ApiError>> {
  const formData = new FormData();
  formData.append('file', file.file, file.fileName);

  try {
    const response = await axios.post<UploadFileResponse>(
      '/appt/upload-file',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'uploadSingleFile' });

    if (!isAxiosError(error)) {
      return err({ message: `Failed to upload file ${file.fileName}` });
    }

    return err({
      message:
        error.response?.data?.error || `Failed to upload file ${file.fileName}`,
    });
  }
}
