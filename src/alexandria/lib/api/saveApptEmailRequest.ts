// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { AppointmentEmailTemplatesResponse } from 'hooks/useFetchEmailTemplates';
import captureException from 'utils/captureException';

// Types for save template API
type SaveTemplateRequest = {
  body: string;
  subject: string;
  id?: number;
  templateType?: string;
  freightTrackingId?: string;
};

export async function saveApptEmailRequest(
  templateText: string,
  emailTemplates: AppointmentEmailTemplatesResponse,
  freightTrackingID: string
) {
  // Save template data to API
  const templateData: SaveTemplateRequest = {
    body: templateText,
    subject: emailTemplates.appointmentRequestTemplate.subject,
    id: emailTemplates.appointmentRequestTemplate.id, // You can add an ID if the template has one
    templateType: emailTemplates.appointmentRequestTemplate.templateType,
    freightTrackingId: freightTrackingID,
  };
  try {
    await axios.post(`appt/email-request`, templateData);
    return { success: true };
  } catch (error) {
    captureException(error, { functionName: 'saveTemplate' });
    return {
      success: false,
      error: 'Failed to save template',
    };
  }
}
