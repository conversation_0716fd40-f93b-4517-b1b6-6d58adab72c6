import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

type ValidatedPONumber = {
  poNumber: string;
  isValid: boolean;
  error: string;
  facilities?: Record<string, string>; // Manhattan-specific: facility_id -> facility_text mapping
  doorType?: Record<string, string>; // Costco-specific: door_type_id -> door_type_text mapping
};

export type CompanyInfo = {
  name: string;
  id: string;
};

export type OperationInfo = {
  name: string;
  id: string;
};

export type ProIdField = {
  label: string;
  fieldName: string;
};

export type DateField = {
  label: string;
  fieldName: string;
  exists: boolean;
};

export type ValidateApptResponse = {
  validatedPONumbers: ValidatedPONumber[];
  companies?: CompanyInfo[];
  operations?: OperationInfo[];
  requiresCompanySelection?: boolean;
  requiresOperationSelection?: boolean;
  proIdFields?: ProIdField[];
  dateFields?: DateField[];
};

export async function validateAppt(
  warehouseId: string,
  source: string,
  poNumbers: string[],
  additionalParams?: any
): Promise<Result<ValidateApptResponse, ApiError>> {
  try {
    const response = await axios.post<ValidateApptResponse>(`appt/validate`, {
      warehouseId,
      source,
      poNumbers,
      ...additionalParams,
    });
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'validateAppt' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to validate appointment' });
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message:
        error.response?.data?.message ||
        error.response?.data ||
        'Failed to validate appointment',
    });
  }
}
