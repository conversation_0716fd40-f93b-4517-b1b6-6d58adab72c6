import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { SchedulingPortals } from 'types/Appointment';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type WarehouseCarrierScacs = {
  id: string;
  name: string;
};

type WarehouseCarrierScacsResponse = {
  scacs: WarehouseCarrierScacs[];
};

export async function getWarehouseCarrierScacs(
  requestSource: string,
  source: SchedulingPortals,
  integrationID: number = 0
): Promise<Result<WarehouseCarrierScacsResponse, ApiError>> {
  try {
    const url = `/carrier-scacs?source=${encodeURIComponent(source)}&requestSource=${requestSource}&integrationID=${integrationID}`;
    const { data } = await axios.get<WarehouseCarrierScacsResponse>(url);
    return ok(data);
  } catch (error) {
    captureException(error, { functionName: 'getWarehouseCarrierScacs' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get carrier scacs from warehouse' });
    }

    return err({
      message:
        error.response?.data.message ||
        'Failed to get carrier scacs from warehouse',
    });
  }
}
