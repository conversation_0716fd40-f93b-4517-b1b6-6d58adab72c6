import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import {
  DATQuoteLocationType,
  DATQuoteTimeframe,
  QuoteCountries,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type UpdateLaneRateResponse = {
  rate: {
    target: number;
    low: number;
    high: number;
    targetPerMile: number;
    lowPerMile: number;
    highPerMile: number;
    fuelSurchargePerMile: number;
    fuelSurchargePerTrip: number;
  };
  metadata?: {
    reports?: number;
    companies?: number;
    timeframe?: DATQuoteTimeframe;
  };
};

export type UpdateLaneRateRequest = {
  originCity: string;
  originState: string;
  originZip: string;
  originCountry: QuoteCountries;
  destinationCity: string;
  destinationState: string;
  destinationZip: string;
  destinationCountry: QuoteCountries;
  transportType: string;
  specificTimeFrame: DATQuoteTimeframe;
  specificAreaType: DATQuoteLocationType;
};

export async function updateRateView(
  request: UpdateLaneRateRequest
): Promise<Result<UpdateLaneRateResponse, ApiError>> {
  try {
    const response = await axios.post<UpdateLaneRateResponse>(
      `/quote/private/lane-rate/dat`,
      request
    );
    return ok(response.data);
  } catch (error: unknown) {
    captureException(error, { functionName: 'updateRateView' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to update rate view' });
    }

    return err({
      message: error.response?.data || 'Failed to update rate view',
    });
  }
}
