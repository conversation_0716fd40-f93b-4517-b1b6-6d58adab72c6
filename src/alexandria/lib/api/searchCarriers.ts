import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { TMSCarrier } from 'types/Load';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';
import { CarrierSearchableFields } from 'utils/loadInfoAndBuilding';

type GetCarriersResponse = {
  carrierList: TMSCarrier[];
  tmsTenant: string;
};

export async function searchCarriers(
  tmsID: number,
  key: CarrierSearchableFields,
  value: string
): Promise<Result<GetCarriersResponse, ApiError>> {
  const params = [
    `tmsID=${encodeURIComponent(tmsID)}`,
    `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
  ];

  try {
    const response = await axios.get<GetCarriersResponse>(
      `/carriers/search?${params.join('&')}`
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'searchCarriers' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to search carriers' });
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message: error.response?.data.message || 'Failed to search carriers',
    });
  }
}
