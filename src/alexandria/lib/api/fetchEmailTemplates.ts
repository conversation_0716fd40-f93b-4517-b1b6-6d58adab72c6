import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { EmailTemplatesResponseMap } from 'hooks/useFetchEmailTemplates';
import { StopTypes } from 'types/Appointment';
import { TemplateType } from 'types/EmailTemplates';
import { Maybe } from 'types/UtilityTypes';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

/**
 * This function is an axios-based equivalent of useFetchEmailTemplates hook.
 * Use this when you need to fetch email templates conditionally or upon user input,
 * where SWR won't work due to React hook rules.
 */

export type FetchEmailTemplatesProps<T extends TemplateType> = {
  loadID?: Maybe<number>;
  templateType: T;
  stopType?: StopTypes;
  carrierQuoteQuery?: string;
};

export default async function fetchEmailTemplates<T extends TemplateType>({
  loadID,
  templateType,
  stopType,
  carrierQuoteQuery,
}: FetchEmailTemplatesProps<T>): Promise<
  Result<EmailTemplatesResponseMap[T], ApiError>
> {
  try {
    const endpoint = buildEmailTemplatesEndpoint(
      loadID,
      templateType,
      stopType,
      carrierQuoteQuery
    );

    if (!endpoint) {
      return err({ message: 'Invalid parameters for email template request' });
    }

    const response = await axios.get(endpoint);
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'fetchEmailTemplates' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to fetch email templates' });
    }

    return err({
      message:
        error.response?.data.message || 'Failed to fetch email templates',
    });
  }
}

function buildEmailTemplatesEndpoint(
  loadID?: Maybe<number>,
  templateType?: TemplateType,
  stopType?: StopTypes,
  carrierQuoteQuery?: string
): string | null {
  switch (templateType) {
    case TemplateType.CARRIER:
      return `load/${loadID}/sops/carrier`;
    case TemplateType.APPOINTMENT:
      return `appt/${loadID}/sops${stopType ? `?stopType=${stopType}` : ''}`;
    case TemplateType.CARRIER_QUOTE_BY_GROUP:
    case TemplateType.CARRIER_QUOTE_BY_LOCATION:
      return `email-templates/carrier-quote?${carrierQuoteQuery}&templateType=${templateType}`;
    default:
      return null;
  }
}
