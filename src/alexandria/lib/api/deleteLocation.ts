import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type DeleteLocationResponse = {
  message: string;
};

// Soft delete a TMSLocation in RDS
export async function deleteLocation(
  tmsLocationId: number
): Promise<Result<DeleteLocationResponse, ApiError>> {
  try {
    const response = await axios.delete<DeleteLocationResponse>(
      `/locations/${encodeURIComponent(tmsLocationId)}`
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'deleteLocation' });

    if (!isAxiosError(error)) {
      return err({ message: 'Oops, something went wrong!' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message: error.response?.data.message || 'Oops, something went wrong!',
    });
  }
}
