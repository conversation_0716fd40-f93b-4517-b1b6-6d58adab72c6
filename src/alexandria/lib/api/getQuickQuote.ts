// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { UseFormReturn } from 'react-hook-form/dist/types/form';

import { AxiosError, isAxiosError } from 'axios';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { toast } from 'hooks/useToaster';
import {
  DATQuoteLocationType,
  DATQuoteTimeframe,
  ProfitType,
  QuickQuoteInputs,
  QuoteFormStop,
  QuoteTypeInSource,
  SelectedQuoteType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { Email } from 'types/Email';
import { Stop, TransportType } from 'types/QuoteRequest';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import { Quoting } from 'types/enums/Integrations';
import { GenericSuggestion } from 'types/suggestions/CoreSuggestions';

interface GetQuickQuoteProps {
  isMultiStopQuickQuoteEnabled: boolean;
  email: Maybe<Email>;
  clickedSuggestion: Maybe<GenericSuggestion>;
  formValues: QuickQuoteInputs;
  formMethods: UseFormReturn<QuickQuoteInputs>;
  setQuoteNotConfidentHandler: React.Dispatch<React.SetStateAction<boolean>>;
  setProfit: React.Dispatch<React.SetStateAction<number>>;
  profitType: ProfitType;
}

interface QuickQuoteBody {
  loadId: string;
  transportType: TransportType;
  newStops: Array<Stop>; // TODO: For new multi-stop format, rename to `stops` after v.48.0
  pickupDate: Date;
  deliveryDate: Date;
  customerName: string;
  emailID?: number;
  threadID?: string;
  quoteRequestId?: number;
  selectedQuickQuoteId?: number;
}

export interface QuickQuoteResponse {
  quoteRequestId: number;
  stops: QuickQuoteResponseStop[];
  selectedRateName: SelectedQuoteType;
  // HotShot and Box Truck are approximated with Flatbed and Van respectively
  // so we need to indicate the transport type for this data to the user
  inputtedTransportType: TransportType;
  submittedTransportType: TransportType;
  configuration?: {
    lowConfidenceThreshold: number;
    mediumConfidenceThreshold: number;
    belowThresholdMessage: string;
    defaultPercentMargin: number;
    defaultFlatMargin: number;
    fscProvider: string;
  };
  quoteReplyDraftTemplate: QuoteReplyDraftTemplate;

  quotes: QuickQuoteResponseQuote[];
  quoteErrors?: QuickQuoteResponseQuoteError[];
  isZipCodeLookup: boolean;
}

export type QuickQuoteResponseStop = {
  order: number;
  city: string;
  state: string;
  zip: string;
  country: string;
};

export type QuickQuoteResponseQuote = {
  id: number;
  source: Quoting;
  type: QuoteTypeInSource;
  rates: QuoteRates;
  distance: number;
  metadata?: QuoteMetadata;
};

export type QuickQuoteResponseQuoteError = {
  source: Quoting;
  error: string;
};

export type QuoteRates = {
  target: number;
  low: number;
  high: number;
  targetPerMile: number;
  lowPerMile: number;
  highPerMile: number;
};

export type RouteSegment = {
  order: number; // Leg order, 0-indexed
  startStopIndex: number; // Index reference in stops array
  endStopIndex: number; // Index reference in stops array
  startCityState: string; // City, State
  endCityState: string; // City, State
  distanceMiles: number; // Miles
  rates: Maybe<QuoteRates>;
};

interface QuoteReplyDraftTemplate {
  subject: string;
  body: string;
}
export type QuoteMetadata = {
  error?: string;

  // GS Metadata
  confidenceLevel?: number;

  // DAT Metadata
  companies?: number;
  reports?: number;
  timeframe?: DATQuoteTimeframe;
  originName?: string;
  originType?: DATQuoteLocationType;
  destinationName?: string;
  destinationType?: DATQuoteLocationType;
  fuelSurchargePerMile?: number;
  // For DAT longest leg multi-stop quotes, we use a flat fee for each intermediate stop
  stopFeeUSD?: number;
  stopFeeUSDLow?: number;
  stopFeeUSDMedium?: number;
  stopFeeUSDHigh?: number;

  // For multi-stop
  legs?: Maybe<RouteSegment[]>;
};

// TODO: This will be refactored in ENG-2961
export interface QuickQuoteResponse {
  quoteRequestId: number;
  stops: QuickQuoteResponseStop[];
  selectedRateName: SelectedQuoteType;
  // HotShot and Box Truck are approximated with Flatbed and Van respectively
  // so we need to indicate the transport type for this data to the user
  inputtedTransportType: TransportType;
  submittedTransportType: TransportType;
  configuration?: {
    lowConfidenceThreshold: number;
    mediumConfidenceThreshold: number;
    belowThresholdMessage: string;
    defaultPercentMargin: number;
    defaultFlatMargin: number;
    fscProvider: string;
  };
  quoteReplyDraftTemplate: QuoteReplyDraftTemplate;
  quotes: QuickQuoteResponseQuote[];
  quoteErrors?: QuickQuoteResponseQuoteError[];
  isZipCodeLookup: boolean;
}

type ErrorData = {
  error: string;
  threshold: Maybe<number>;
  defaultPercentMargin: number;
  defaultFlatMargin: number;
};

interface ValidationError {
  type: 'validation_error';
  message: string;
  details: StopValidationError[];
}

interface StopValidationError {
  stopIndex: number;
  field: string;
  type: string;
  message: string;
  value: string;
}

const defaultErrorMessage =
  'Unable to retrieve quote. Please try again in a few moments or contact our team if the issue persists.';

export async function getQuickQuote({
  isMultiStopQuickQuoteEnabled,
  email,
  clickedSuggestion,
  formValues,
  formMethods,
  setQuoteNotConfidentHandler,
  setProfit,
  profitType,
}: GetQuickQuoteProps): Promise<MaybeUndef<QuickQuoteResponse>> {
  setQuoteNotConfidentHandler(false);

  // Map QuoteFormStop[] to Stop[] for the backend
  const mappedStops = mapQuoteFormStopsToStops(
    formValues.stops,
    formValues.pickupDate,
    formValues.deliveryDate
  );

  const body = {
    ...formValues,
    ...(email && { emailID: email.id, threadID: email.threadID }),
    ...(clickedSuggestion && { quoteRequestId: clickedSuggestion.id }),
    loadId: formValues.loadId || '',
    newStops: mappedStops,
    // Don't send legacy 'stops' field if multi-stop is enabled so API can perform backwards-compatibility check
    // TODO: Deprecate after v.0.48.0
    stops: isMultiStopQuickQuoteEnabled ? undefined : formValues.stops,
  } as QuickQuoteBody;

  try {
    const { data } = await axios.post<QuickQuoteResponse>(
      `/quote/quickquote/v2`,
      body
    );

    // Check for non-omitted errors from below-threshold confidence GS quotes
    const lowConfGSQuotes = data.quotes.filter(
      (quote: QuickQuoteResponseQuote) =>
        quote.metadata?.error &&
        quote.metadata?.error.includes('unable') &&
        quote.metadata?.error.includes('confident')
    );

    if (lowConfGSQuotes.length) {
      setProfit(
        profitType === ProfitType.Amount
          ? data.configuration?.defaultFlatMargin || 100
          : data.configuration?.defaultPercentMargin || 10
      );
      toast({
        description: `Greenscreens quote confidence is below
                ${
                  data.configuration?.lowConfidenceThreshold
                    ? data.configuration?.lowConfidenceThreshold + '%.'
                    : 'your minimum threshold.'
                }
                Try another quoting approach, then return to complete the form.`,
        variant: 'destructive',
      });

      data.quotes = data.quotes.filter(
        (quote: QuickQuoteResponseQuote) => !lowConfGSQuotes.includes(quote)
      );
      data.quotes.length === 0 && setQuoteNotConfidentHandler(true);
    }

    return data;
  } catch (error) {
    if (isAxiosError(error)) {
      const status = error.response?.status;
      const data = error.response?.data;

      switch (status) {
        case 503: {
          setQuickQuoteFormError(error, formMethods);
          return;
        }
        case 400: {
          setQuickQuoteFormError(error, formMethods);
          return;
        }
        default: {
          const errorMessage =
            typeof data === 'string' ? data : data?.error || data?.message;

          formMethods.setError('root', {
            message: errorMessage || defaultErrorMessage,
          });
          return;
        }
      }
    }

    // Non-Axios error fallback
    formMethods.setError('root', {
      message: defaultErrorMessage,
    });
  }
  return null;
}

const setQuickQuoteFormError = (
  error: AxiosError<any, any>,
  formMethods: UseFormReturn<QuickQuoteInputs>
) => {
  const data = error.response?.data;

  // Handle structured validation errors for multi-stop loads
  if (data?.type === 'validation_error') {
    const validationError: ValidationError = data;

    // Check if there was a transient error while validating locations
    const isNonValidationError = validationError.details.some(
      (detail) => getValidationErrorMessage(detail.message) === ''
    );

    if (isNonValidationError) {
      toast({
        description: defaultErrorMessage,
        variant: 'destructive',
      });
      return;
    }

    // Set each validation error in the form
    validationError.details.forEach((detail) => {
      const fieldPath = `stops.${detail.stopIndex}.location`;
      const userMessage = getValidationErrorMessage(detail.message);

      // Set field-specific error
      formMethods.setError(fieldPath as `stops.${number}.location`, {
        type: 'warning',
        message: userMessage,
      });
    });

    // Set root error with summary
    formMethods.setError('root' as any, {
      message: 'One or more locations are invalid. Please check your input.',
    });

    return;
  }

  // Handle legacy string-based errors for 2-stop loads (for backwards compatibility with 2-stop)
  const legacyData: ErrorData = data;

  const isPickupError =
    legacyData.error.includes('stop 0') || legacyData.error.includes('stops.0');
  const isDropoffError =
    legacyData.error.includes('stop 1') || legacyData.error.includes('stops.1');

  const userMessage = getValidationErrorMessage(legacyData.error);
  if (!userMessage) {
    toast({
      description: defaultErrorMessage,
      variant: 'destructive',
    });
    return;
  }

  // Handle legacy 2-stop errors
  if (isPickupError) {
    formMethods.setError('stops.0.location' as any, {
      type: 'warning',
      message: userMessage,
    });
    formMethods.setError('root' as any, { message: userMessage });
  } else if (isDropoffError) {
    formMethods.setError('stops.1.location' as any, {
      type: 'warning',
      message: userMessage,
    });
    formMethods.setError('root' as any, { message: userMessage });
  } else {
    formMethods.setError('root' as any, { message: userMessage });
  }

  // Handle other errors
  toast({
    description: `Unable to retrieve quote. Please retry or contact our team if the issue persists.`,
    variant: 'destructive',
  });
};

// Helper function to map validation errors to user-friendly messages
const getValidationErrorMessage = (errorString: string): string => {
  // Check for specific error patterns in the error string
  if (
    errorString.toLowerCase().includes('invalid zip code') || // USPS
    errorString.toLowerCase().includes('zip code not found')
  ) {
    return `The location doesn't exist. Please enter a valid zip code.`;
  }

  if (
    errorString.toLowerCase().includes('zipcode must be 5 characters') || // USPS
    errorString.toLowerCase().includes('is not 5 digits if usa') // USPS
  ) {
    return `Zip code must be 5 characters.`;
  }

  if (
    errorString.toLowerCase().includes('no zip code found for city/state') ||
    errorString.toLowerCase().includes('city/state not found')
  ) {
    return `The location doesn't exist. Please enter a valid city and state pair.`;
  }

  if (
    errorString.toLowerCase().includes('invalid location') ||
    errorString.toLowerCase().includes('location not found') ||
    errorString.toLowerCase().includes('no results')
  ) {
    return `The location does not exist. Please check your input.`;
  }

  // If no specific pattern matches, return empty string
  return '';
};

// Helper function to map QuoteFormStop[] to Stop[]
export const mapQuoteFormStopsToStops = (
  quoteInputStops: QuoteFormStop[],
  pickupDate: Maybe<Date>,
  deliveryDate: Maybe<Date>
): Stop[] => {
  return quoteInputStops.map((stop, index) => {
    const isFirstStop = index === 0;
    const isLastStop = index === quoteInputStops.length - 1;

    const baseStop: Stop = {
      stopType: 'stop' as const,
      stopNumber: index,
      address: {
        city: stop.city,
        state: stop.state,
        zip: stop.zip,
        country: stop.country || 'US',
        name: '',
        addressLine1: '',
        addressLine2: '',
      },
    };

    if (isFirstStop) {
      return {
        ...baseStop,
        stopType: 'pickup' as const,
        readyTime: pickupDate,
      };
    } else if (isLastStop) {
      return {
        ...baseStop,
        stopType: 'dropoff' as const,
        mustDeliver: deliveryDate,
      };
    } else {
      return baseStop;
    }
  });
};
