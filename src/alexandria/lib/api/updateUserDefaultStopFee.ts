import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export async function updateUserDefaultStopFee(
  stopFee: number
): Promise<Result<void, ApiError>> {
  const requestBody = { stopFee };

  try {
    const resp = await axios.patch('user/config/stop-fee', requestBody);

    return ok(resp.data);
  } catch (error) {
    captureException(error, { functionName: 'updateUserDefaultStopFee' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to update user default stop fee' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message:
        error.response?.data.message ||
        'Failed to update user default stop fee',
    });
  }
}
