import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { DATMarketInformation } from 'components/QuoteCard';
import { TransportType } from 'types/QuoteRequest';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

import { QuickQuoteResponseStop } from './getQuickQuote';

type GetDATMarketConditionsProps = {
  integrationID: number;
  transportType: TransportType;
  stops: QuickQuoteResponseStop[];
};

type DATMarketInformationResponse = {
  pickupMarketInformation: DATMarketInformation;
  dropoffMarketInformation: DATMarketInformation;
};

export async function getDATMarketInformation(
  body: GetDATMarketConditionsProps
): Promise<Result<DATMarketInformationResponse, ApiError>> {
  try {
    const response = await axios.post(
      `/quote/quickquote/dat/market-information`,
      body
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getDATMarketInformation' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get DAT market information' });
    }

    return err({
      message:
        error.response?.data.message || 'Failed to get DAT market information',
    });
  }
}
