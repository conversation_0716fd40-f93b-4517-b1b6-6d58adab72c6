import { isAxiosError } from 'axios';
import 'dayjs/locale/en';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { fileToBase64 } from 'components/AttachmentUpload';
import { Attachment } from 'types/EmailAttachment';
import { TMSLocationWithDistance } from 'types/Load';
import { EmailCarriersBody } from 'types/QuoteRequest';
import {
  EmailCarriersError,
  EmailCarriersResponse,
} from 'types/api/EmailCarriersResult';
import captureException from 'utils/captureException';

export interface NewAttachment {
  data: string;
  fileName: string;
  mimeType: string;
}

export async function emailCarriersForQuotes(
  actualRequest: EmailCarriersBody,
  carrierEmails: string[],
  ccEmails: string[],
  subject: string,
  emailBody: string,
  selectedExistingAttachments: Attachment[],
  newAttachments?: File[] | NewAttachment[]
): Promise<Result<EmailCarriersResponse, EmailCarriersError>> {
  try {
    // Process file attachments to base64 format if they're not already processed
    const processedAttachments = await processNewAttachments(newAttachments);

    const carriers = actualRequest.carriers || [];
    const fromEmail = actualRequest.from || '';

    const requestBody = {
      carrierEmails: carrierEmails,
      carriers: carriers as TMSLocationWithDistance[],
      subject: subject,
      emailBody: emailBody,
      from: fromEmail,
      ccEmails: ccEmails,
      selectedExistingAttachments: selectedExistingAttachments,
      newAttachments: processedAttachments,
      actualRequest: actualRequest,
      carrierGroupId: actualRequest.carrierGroupId,
    };

    const response = await axios.post(
      `/quote/request/carrierNetwork`,
      requestBody
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'emailCarriersForQuotes' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to email carriers', countSuccess: 0 });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      results: error.response?.data.results || [],
      countSuccess: error.response?.data.countSuccess ?? 0,
      message:
        error.response?.data.message ||
        error.response?.data ||
        'Failed to email carriers',
    });
  }
}

const processNewAttachments = async (
  newAttachments?: File[] | NewAttachment[]
): Promise<NewAttachment[]> => {
  const processedAttachments: NewAttachment[] = [];

  if (newAttachments && newAttachments.length > 0) {
    if ('name' in newAttachments[0] && newAttachments[0] instanceof File) {
      for (const file of newAttachments as File[]) {
        const base64Data = await fileToBase64(file);
        processedAttachments.push({
          data: base64Data,
          fileName: file.name,
          mimeType: file.type || 'application/octet-stream',
        });
      }
    } else {
      processedAttachments.push(...(newAttachments as NewAttachment[]));
    }
  }
  return processedAttachments;
};
