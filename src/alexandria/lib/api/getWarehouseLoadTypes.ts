import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type WarehouseLoadTypes = {
  id: string;
  name: string;
  direction: string;
  allowCarrierDockSelection: boolean;
  docks: WarehouseDock[];
};

type WarehouseDock = {
  id: string;
  name: string;
};

type WarehouseLoadTypesResponse = {
  loadTypes: WarehouseLoadTypes[];
};

export async function getWarehouseLoadTypes(
  warehouseID: string,
  source: string,
  integrationID: number = 0
): Promise<Result<WarehouseLoadTypesResponse, ApiError>> {
  const params = [
    `warehouseID=${encodeURIComponent(warehouseID)}`,
    `source=${source}`,
    `integrationID=${integrationID}`,
  ];

  try {
    const url = `/loadtypes?${params.join('&')}`;
    const { data } = await axios.get<WarehouseLoadTypesResponse>(url);
    return ok(data);
  } catch (error) {
    captureException(error, { functionName: 'getWarehouseLoadTypes' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get load types from OpenDock' });
    }

    return err({
      message:
        error.response?.data.message ||
        'Failed to get load types from OpenDock',
    });
  }
}
