import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { Exception } from 'types/Exception';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export async function getExceptionHistory(
  loadId: number
): Promise<Result<Exception[], ApiError>> {
  try {
    const response = await axios.get(`/load/${loadId}/exceptions`);
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getExceptionHistory' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get exception history' });
    }

    return err({
      message:
        error.response?.data.message || 'Failed to get exception history',
    });
  }
}
