import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { TMSCustomer } from 'types/Load';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

type GetCustomersResponse = {
  customerList: TMSCustomer[];
  tmsTenant: string;
  message: string;
};

export async function getCustomers(
  tmsID: number,
  forceRefresh?: boolean
): Promise<Result<GetCustomersResponse, ApiError>> {
  try {
    const response = await axios.get<GetCustomersResponse>(
      `/customers?tmsID=${tmsID}${forceRefresh ? '&forceRefresh=true' : ''}`
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getCustomers' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get list of customers from TMS' });
    }

    return err({
      message:
        error.response?.data.message ||
        'Failed to get list of customers from TMS',
    });
  }
}
