import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { TMSLocation } from 'types/Load';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type CreateLocationResponse = {
  location: TMSLocation;
  message: string;
};

export async function createLocation(data: {
  location: TMSLocation;
  tmsID: number;
}): Promise<Result<CreateLocationResponse, ApiError>> {
  try {
    const response = await axios.post<CreateLocationResponse>(
      `/locations`,
      data
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'createLocation' });

    if (!isAxiosError(error)) {
      return err({ message: 'Oops, something went wrong!' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message: error.response?.data.message || 'Oops, something went wrong!',
      location: error.response?.data.location,
    });
  }
}
