import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type UpdateLoadAppointmentRequest = {
  load: {
    freightTrackingID: string;
    pickup?: {
      apptStartTime?: string;
      apptEndTime?: string;
      apptType?: string;
    };
    consignee?: {
      apptStartTime?: string;
      apptEndTime?: string;
      apptType?: string;
    };
  };
};

export type UpdateLoadAppointmentResult = {
  message: string;
};

const ERROR_MESSAGES = {
  GENERIC: 'Oops, something went wrong!',
  UNAUTHORIZED: 'Unauthorized access',
  EXTENSION_INVALIDATED: 'Extension context invalidated.',
} as const;

/**
 * Updates load appointment details in the TMS system
 * @param loadId - The ID of the load to update
 * @param data - The appointment data to update
 * @returns Promise with Result containing success or error
 */
export async function updateLoadAppointment(
  loadId: number,
  data: UpdateLoadAppointmentRequest
): Promise<Result<UpdateLoadAppointmentResult, ApiError>> {
  try {
    // Validate input parameters
    if (!loadId || loadId <= 0) {
      return err({ message: 'Invalid load ID provided' });
    }

    // if (!data?.load?.freightTrackingID) {
    //   return err({ message: 'Freight tracking ID is required' });
    // }

    const response = await axios.patch<UpdateLoadAppointmentResult>(
      `/load/${loadId}/update-appointment`,
      data
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, {
      functionName: 'updateLoadAppointment',
      loadId,
      hasFreightTrackingID: !!data?.load?.freightTrackingID,
    });

    if (!isAxiosError(error)) {
      return err({ message: ERROR_MESSAGES.GENERIC });
    }

    // Handle specific error cases
    if (error.response?.status === 401) {
      throw error; // Re-throw for auth handling
    }

    if (error.message === ERROR_MESSAGES.EXTENSION_INVALIDATED) {
      throw error; // Re-throw for extension handling
    }

    return err({
      message: error.response?.data?.message || ERROR_MESSAGES.GENERIC,
    });
  }
}
