import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import { EmailLabels } from 'types/enums/EmailLabels';
import captureException from 'utils/captureException';

type PostIngestEmailResponse = {
  message: string;
};

export async function reprocessEmailContents(
  emailId: number,
  labelsToReprocess: EmailLabels[]
): Promise<Result<PostIngestEmailResponse, ApiError>> {
  try {
    const resp = await axios.post<PostIngestEmailResponse>(
      `/email/reprocess-content/${emailId}`,
      { labelsToReprocess }
    );

    return ok(resp.data);
  } catch (error) {
    captureException(error, { functionName: 'reprocessEmailContents' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to reprocess email contents' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message:
        error.response?.data?.message || 'Failed to reprocess email contents',
    });
  }
}
