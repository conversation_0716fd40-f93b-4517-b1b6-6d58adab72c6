import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { Order } from 'types/Order';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type OrderResponse = {
  order: Order;
};

export default async function fetchOrderByTrackingId(
  trackingId: string
): Promise<Result<OrderResponse, ApiError>> {
  try {
    const response = await axios.get<OrderResponse>(
      `orders/tracking/${trackingId}`
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'fetchOrderByTrackingId' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to fetch order' });
    }

    if (error.response?.status === 404) {
      return err({ message: 'Order not found' });
    }

    return err({
      message: error.response?.data?.message || 'Failed to fetch order',
    });
  }
}
