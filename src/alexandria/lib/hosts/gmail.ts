import { AvailableTabs } from 'constants/SidebarTabs';
import { NormalizedLoad } from 'types/Load';

import { HostInterface } from './interface';

export class Gmail implements HostInterface {
  getFieldMoniker(_: keyof NormalizedLoad): string {
    return '';
  }

  parseFreightTrackingID = (): string => '';
  parseExternalTMSID = (): string => '';

  determineDefaultLoadTab = (): AvailableTabs => AvailableTabs.LoadInformation;

  shouldAutomaticallyOpenDrumkit(): boolean {
    // Automatically open when viewing an email
    return document.querySelector('h2[data-legacy-thread-id]') !== null;
  }
}
