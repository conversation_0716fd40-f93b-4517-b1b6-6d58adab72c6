import { AvailableTabs } from 'constants/SidebarTabs';
import { Dropoff, NormalizedLoad, Pickup } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';

import { HostInterface } from './interface';

export class Revenova implements HostInterface {
  getFieldMoniker(key: keyof NormalizedLoad): string {
    switch (key) {
      case 'externalTMSID':
        return 'Load ID';
      case 'freightTrackingID':
        return 'Pro Number';
      default:
        return '';
    }
  }

  parseFreightTrackingID(): string {
    // Revenova-specific logic for parsing freight tracking ID
    // This would depend on how Revenova structures their URLs or data
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('loadId') || urlParams.get('proNumber') || '';
  }

  parseExternalTMSID(): string {
    // Revenova-specific logic for parsing external TMS ID
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('loadId') || urlParams.get('externalId') || '';
  }

  parsePickup(): Maybe<Pickup> {
    return null;
  }

  parseDropoff(): Maybe<Dropoff> {
    // Revenova-specific logic for parsing dropoff information
    // This would depend on how Revenova displays dropoff data on their pages
    // For now, return null as we don't have the specific implementation
    return null;
  }

  determineDefaultLoadTab(): AvailableTabs {
    // Determine which tab should be shown by default for Revenova loads
    // This could be based on user preferences or Revenova-specific requirements
    return AvailableTabs.LoadInformation;
  }

  shouldAutomaticallyOpenDrumkit(): boolean {
    // Determine if Drumkit should automatically open on Revenova pages
    // This could be based on specific conditions or user settings
    return true;
  }
}
