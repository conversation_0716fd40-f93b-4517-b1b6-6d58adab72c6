import { AvailableTabs } from 'constants/SidebarTabs';
import { NormalizedLoad } from 'types/Load';

import { HostInterface } from './interface';

export function getCurrentUrl() {
  return window.location.href;
}

export class Stark implements HostInterface {
  private getCurrentUrl: () => string;

  constructor(getCurrentUrlFn?: () => string) {
    this.getCurrentUrl = getCurrentUrlFn || getCurrentUrl;
  }

  getFieldMoniker(key: keyof NormalizedLoad): string {
    switch (key) {
      case 'externalTMSID':
        return 'Stark Shipment ID';
      case 'freightTrackingID':
        return 'Freight Tracking ID';
      default:
        return '';
    }
  }

  parseFreightTrackingID(): string {
    // For Stark, we'll parse from the URL or DOM if needed
    // Based on the URL pattern: https://apps-staging.transfix.io/#/shipment-details/1734986
    const url = this.getCurrentUrl();
    const match = url.match(/\/shipment-details\/(\d+)/);
    if (match) {
      return match[1];
    }
    return '';
  }

  // ExternalTMSID = Shipment ID for Stark
  parseExternalTMSID(): string {
    const url = this.getCurrentUrl();
    const match = url.match(/\/shipment-details\/(\d+)/);
    if (match) {
      return match[1];
    }
    return '';
  }

  determineDefaultLoadTab(): AvailableTabs {
    return AvailableTabs.LoadInformation;
  }

  shouldAutomaticallyOpenDrumkit(): boolean {
    const url = this.getCurrentUrl();
    return url.includes('/shipment-details/');
  }
}
