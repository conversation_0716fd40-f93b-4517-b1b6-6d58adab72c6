import { AvailableTabs } from 'constants/SidebarTabs';
import { HostInterface } from 'lib/hosts/interface';
import { NormalizedLoad } from 'types/Load';

export class McleodEnterprise implements HostInterface {
  getFieldMoniker(key: keyof NormalizedLoad): string {
    switch (key) {
      case 'externalTMSID':
        return '';
      case 'freightTrackingID':
        return 'Load ID';
      default:
        return '';
    }
  }
  parseFreightTrackingID(): string {
    return '';
  }

  // ExternalTMSID = FreightTrackingID for Aljex
  parseExternalTMSID(): string {
    return this.parseFreightTrackingID();
  }

  determineDefaultLoadTab(): AvailableTabs {
    return AvailableTabs.LoadInformation;
  }

  shouldAutomaticallyOpenDrumkit(): boolean {
    return false;
  }
}
