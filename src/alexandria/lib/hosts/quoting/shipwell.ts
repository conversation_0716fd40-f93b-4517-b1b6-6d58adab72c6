import { QuotingPortal } from 'lib/hosts/quoting/interface';
import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

export const ShipwellSubmitAction = 'shipwell-submit-quote';

export class Shipwell implements QuotingPortal {
  submitAction = ShipwellSubmitAction;

  /**
   * Determines if a quote can be submitted to Shipwell based on the current tab and document state.
   * @param tab The current Chrome tab
   * @param html The current document HTML
   * @returns boolean indicating if a quote can be submitted
   */
  canSubmit(tab: Undef<chrome.tabs.Tab>, html: string): boolean {
    if (!tab?.url) return false;
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    try {
      const url = new URL(tab.url);
      const isShipwell = url.origin?.includes('shipwell.com');

      if (!isShipwell) return false;

      // Check if the modal is already open and the quote input is present
      const isQuoteInputPresent = doc.querySelector('#total-input') !== null;

      // Otherwise, check if the "Place a Bid" button is present so Drumkit can open the modal
      const isLoadBoardWithBidding = Array.from(
        doc.querySelectorAll('button')
      ).find((btn) => btn.textContent?.toLowerCase().includes('place a bid'));
      const isLoadDetailsWithBidding = doc.querySelector('#newBid');

      return (
        isQuoteInputPresent ||
        !!isLoadBoardWithBidding ||
        !!isLoadDetailsWithBidding
      );
    } catch (error: any) {
      captureException(
        'Error checking Shipwell if quote can be submitted:',
        error
      );
      return false;
    }
  }
}
