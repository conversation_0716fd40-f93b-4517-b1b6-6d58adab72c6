import { E2O<PERSON> } from 'lib/hosts/quoting/e2open';
import { FreightView } from 'lib/hosts/quoting/freightview';
import { Shipwell } from 'lib/hosts/quoting/shipwell';
import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

export interface QuotingPortal {
  /**
   * The message in chrome.runtime.sendMessage() to submit a quote to this portal.
   */
  submitAction: string;
  /**
   * Determines if a quote can be submitted to this portal based on the current tab and if the DOM has the
   * necessary elements for opening the form to input the quote (if applicable), and inputting the quote amount.
   * @param tab The current Chrome tab
   * @param document The current document
   * @returns boolean indicating if a quote can be submitted
   */
  canSubmit(tab: Undef<chrome.tabs.Tab>, html: string): boolean;
}

/**
 * Creates a new QuotingPortal instance based on the URL.
 * @param url The URL to determine which portal to create
 * @returns A new QuotingPortal instance or undefined if no matching portal is found
 */
export function newQuotingPortal(url: Undef<string>): Undef<QuotingPortal> {
  if (!url) return undefined;

  try {
    const parsedUrl = new URL(url);

    if (parsedUrl.origin?.includes('na-app.tms.e2open.com')) {
      return new E2Open();
    }

    if (parsedUrl.origin?.includes('carrier.freightview')) {
      return new FreightView();
    }

    if (parsedUrl.origin?.includes('app.shipwell.com')) {
      return new Shipwell();
    }

    return undefined;
  } catch (error: any) {
    captureException(error, { url });
    return undefined;
  }
}
