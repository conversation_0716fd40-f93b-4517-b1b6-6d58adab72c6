import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

import { QuotingPortal } from './interface';

export const E2OpenSubmitAction = 'e2open-submit-quote';
export class E2Open implements QuotingPortal {
  submitAction = E2OpenSubmitAction;
  /**
   * Determines if a quote can be submitted to E2Open based on the current tab and document state.
   * @param tab The current Chrome tab
   * @param document The current document
   * @returns boolean indicating if a quote can be submitted
   */
  canSubmit(tab: Undef<chrome.tabs.Tab>, html: string): boolean {
    if (!tab?.url) return false;
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    try {
      const url = new URL(tab.url);
      const isE2Open =
        url.origin?.includes('na-app.tms.e2open.com') &&
        (this.isDetailedQuotePage(tab.url) || this.isSimpleQuotePage(tab.url));

      if (!isE2Open) return false;

      // Check if we're on a quote page with the necessary elements
      return (
        this.detailedPageHasSubmitButton(doc) ||
        this.simplePageHasSubmitButton(doc)
      );
    } catch (error: any) {
      captureException('Error checking E2Open quote page:', error);
      return false;
    }
  }

  isSimpleQuotePage(url: Undef<string>): boolean {
    if (!url) return false;
    const parser = new URL(url);
    return parser.pathname?.toLowerCase().includes('makeanoffer');
  }

  isDetailedQuotePage(url: Undef<string>): boolean {
    if (!url) return false;
    const parser = new URL(url);
    return parser.pathname
      ?.toLowerCase()
      .includes('detailedspotmarketoffer.do');
  }

  detailedPageHasSubmitButton(doc: Document): boolean {
    return doc.querySelector('#bttnSaveRate') !== null;
  }

  simplePageHasSubmitButton(doc: Document): boolean {
    return doc.querySelector('button[name="submitbutton"]') !== null;
  }
}
