import { AvailableTabs } from 'constants/SidebarTabs';
import { NormalizedLoad } from 'types/Load';

import { HostInterface } from './interface';

export class Tai implements HostInterface {
  getFieldMoniker(key: keyof NormalizedLoad): string {
    switch (key) {
      case 'externalTMSID':
        return '';
      case 'freightTrackingID':
        return 'PRO #';
      default:
        return '';
    }
  }

  parseFreightTrackingID(): string {
    return '';
  }

  parseExternalTMSID(): string {
    return '';
  }

  determineDefaultLoadTab(): AvailableTabs {
    return AvailableTabs.LoadInformation;
  }

  shouldAutomaticallyOpenDrumkit(): boolean {
    return location.href.includes('taicloud.net');
  }
}
