import { AvailableTabs } from 'constants/SidebarTabs';
import { NormalizedLoad } from 'types/Load';

import { HostInterface } from './interface';

export function getCurrentUrl() {
  return window.location.href;
}

export class Relay implements HostInterface {
  private getCurrentUrl: () => string;

  constructor(getCurrentUrlFn?: () => string) {
    this.getCurrentUrl = getCurrentUrlFn || getCurrentUrl;
  }

  getFieldMoniker(key: keyof NormalizedLoad): string {
    switch (key) {
      case 'externalTMSID':
        return 'Relay Load #';
      case 'freightTrackingID':
        return 'Booking ID';
      default:
        return '';
    }
  }

  // FreightTrackingID = Booking ID for Relay
  parseFreightTrackingID(): string {
    const url = this.getCurrentUrl();

    switch (true) {
      case url.includes('load_board_load_detail'): {
        return (
          document
            .querySelector('.text-info.pl-3.d-inline')
            ?.textContent?.trim() ?? ''
        );
      }

      case url.includes('tracking_load_detail'): {
        const match = url.match(/(\d{7})/);

        if (match) {
          return match[1];
        }
        break;
      }

      case url.includes('hub'): {
        const header = document.querySelector(
          'tr[data-phx-id="c1-load-signature"][data-phx-component="1"]'
        );
        const numElement =
          header?.querySelector('span.tw-text-neutral-500.tw-text-xs') ??
          header?.querySelector('span:first-of-type');

        return numElement?.textContent?.trim() ?? '';
      }
    }

    // Planning Board modals include only Relay Ref # and Customer Ref/BOL
    return '';
  }

  // ExternalTMSID = Load # for Relay
  parseExternalTMSID(): string {
    const url = this.getCurrentUrl();

    // URLs are in the form of:
    //  * https://{training.}relaytms.com/sourcing/load_board_load_detail/{(2|3)\d{6,7}}
    //  * https://{training.}relaytms.com/planning_board/stop_management/{(2|3)\d{6,7}}
    if (
      url.includes('load_board_load_detail') ||
      url.includes('planning_board/stop_management')
    ) {
      const match = url.match(/((2|3)\d{5,7})/);
      if (match) {
        return match[1];
      }
    } else if (url.includes('planning_board')) {
      const elements = document.getElementsByClassName('col load-number');
      if (elements.length === 0) {
        return '';
      }
      const element = elements[0] as HTMLElement;
      return element?.innerHTML?.trim() ?? '';
    } else if (url.includes('tracking_load_detail')) {
      return (
        document
          .querySelector('.relay-reference-number.d-inline')
          ?.textContent?.trim() ?? ''
      );
    } else if (url.includes('hub')) {
      const match = url.match(/((2|3)\d{5,7})/);
      if (match) {
        return match[1];
      }
    }
    return '';
  }

  determineDefaultLoadTab(): AvailableTabs {
    const url = this.getCurrentUrl();
    switch (true) {
      case url.includes('planning_board') ||
        url.includes('load_board_load_detail'):
        return AvailableTabs.AppointmentScheduling;

      // There was a specific ask to show appt scheduling tab instead of track and trace (10/8/25 - NFI Target Team)
      // Appt scheduling tab used much more heavily by NFI Target Team than track and trace is used
      // TODO: we should have a per user or per usergroup config that respects initial tab to open with
      case url.includes('tracking_board'):
        return AvailableTabs.AppointmentScheduling;

      // TODO: we should have a per user or per usergroup config that respects initial tab to open with
      case url.includes('tracking_load_detail'):
        return AvailableTabs.AppointmentScheduling;

      default:
        return AvailableTabs.LoadInformation;
    }
  }

  shouldAutomaticallyOpenDrumkit(): boolean {
    const url = this.getCurrentUrl();
    return (
      url.includes('planning_board') ||
      url.includes('load_board_load_detail') ||
      url.includes('tracking_load_detail')
    );
  }
}
