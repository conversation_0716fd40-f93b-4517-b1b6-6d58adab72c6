import { Maybe, Undef } from 'types/UtilityTypes';

/**
 * Get the current tab from the active window. To be used when Drumkit is running as a
 * [Chromium side panel](https://developer.chrome.com/docs/extensions/reference/api/sidePanel).
 * @returns The current tab or undefined if chrome is not available
 *
 * NOTE: Because a user can have multiple tabs open and a sidepanel open on each one,
 * and we need conditional rendering based on the current webpage, we cannot attach a listener to all the sidepanel ports.
 * Instead, we check the current tab the currently running sidepanel is attached to on each request.
 * Furthermore, the tabId may not change but the URL does, hence why this Tab object is not attached to SidebarStateContext.
 */
export async function getCurrentTab(): Promise<Undef<chrome.tabs.Tab>> {
  if (typeof chrome === 'undefined' || !chrome.tabs) {
    return undefined;
  }
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  return tab;
}

/**
 * Gets the HTML content of a tab by injecting a content script.
 * @param tabId The ID of the tab to get HTML from
 * @returns A promise that resolves to the tab's HTML content
 */
export async function getTabHTML(tabId: number): Promise<Maybe<string>> {
  return new Promise((resolve) => {
    if (typeof chrome === 'undefined' || chrome?.runtime === undefined) {
      resolve(null);
    }
    const listener = (message: any) => {
      if (message.action === 'getTabHTMLResponse' && message?.tabId === tabId) {
        chrome.runtime.onMessage.removeListener(listener);
        resolve(message.html);
      }
    };

    chrome.runtime.onMessage.addListener(listener);

    // Inject content script to get HTML
    chrome.scripting
      .executeScript({
        target: { tabId },
        func: () => document.documentElement.outerHTML,
      })
      .then((results) => {
        if (results?.[0]?.result) {
          chrome.runtime.onMessage.removeListener(listener);
          resolve(results[0].result);
        } else {
          chrome.runtime.onMessage.removeListener(listener);
          resolve(null);
        }
      })
      .catch((error) => {
        console.error('Error getting tab HTML:', error);
        chrome.runtime.onMessage.removeListener(listener);
        resolve(null);
      });
  });
}
