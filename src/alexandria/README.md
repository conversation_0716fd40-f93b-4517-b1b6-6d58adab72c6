# alexandria

Shared component library for Vulcan, Vesta, and Verona repos (Drumkit's Chrome and Outlook extensions)

`alexandria` is a git submodule. For more info read [Notion: Working with Git Submodules](https://www.notion.so/drumkitai/Working-with-Git-Submodules-15e2b16b087a801b89d9e8b154b45324?pvs=4).

# Dependencies

Install those dependencies using `yarn` or `npm`:

```
react-hook-form
@hookform/error-message
clsx
tailwind-merge
react-content-loader
@radix-ui/react-accordion
lucide-react
@radix-ui/react-slot
class-variance-authority
@radix-ui/react-select
@radix-ui/react-tabs
```

Due to this being a child directory, there are references to some paths in Vulcan and Vesta. Any errors associated with them can be silenced with:

```
@ts-expect-error - defined in parent repo
```
