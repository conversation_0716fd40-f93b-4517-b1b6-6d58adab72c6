import * as React from 'react';

import Yardview from '../assets/yardview-logo.png';
import { cn } from '../utils/shadcn';

export default function YardviewLogo(
  props: React.ImgHTMLAttributes<HTMLImageElement>
): React.JSX.Element {
  const { className: classNameProp, ...otherProps } = props;

  return (
    <img
      src={Yardview}
      alt='Yardview logo'
      className={cn('scale-125', classNameProp)}
      {...otherProps}
    />
  );
}
