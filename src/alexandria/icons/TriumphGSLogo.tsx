import * as React from 'react';

export default function TriumphLogo(
  props: React.SVGProps<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg width='173' height='37' viewBox='0 0 173 37' fill='none' {...props}>
      <path
        id='Vector'
        d='M45.0957 9.34457H52.1288V29.0284H57.0275V9.34457H64.0605V4.51117H45.0957V9.34457Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_2'
        d='M70.7017 11.8663V10.6405H65.873V29.0284H70.7017V19.0814C70.7017 17.5894 70.9607 16.5456 71.4715 15.9642C72.0104 15.3828 72.7872 15.0886 73.8159 15.0886C74.8446 15.0886 75.7544 15.4879 76.6151 16.2794L77.8748 11.5511C77.056 10.6895 75.9293 10.2552 74.4807 10.2552C73.0321 10.2552 71.7305 10.7946 70.7017 11.8663Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_3'
        d='M82.3814 3.60053C81.6326 3.60053 80.9818 3.88073 80.422 4.44112C79.8831 4.9805 79.6172 5.61795 79.6172 6.36748C79.6172 7.117 79.8831 7.76846 80.422 8.32885C80.9818 8.86823 81.6326 9.13442 82.3814 9.13442C83.1302 9.13442 83.7671 8.86823 84.3059 8.32885C84.8658 7.76846 85.1457 7.117 85.1457 6.36748C85.1457 5.61795 84.8658 4.9805 84.3059 4.44112C83.7671 3.88073 83.1302 3.60053 82.3814 3.60053Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_4'
        d='M84.7955 10.6405H79.9668V29.0284H84.7955V10.6405Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_5'
        d='M99.5344 20.6925C99.5344 22.2126 99.2684 23.2844 98.7296 23.9148C98.2187 24.5452 97.4349 24.8605 96.3852 24.8605C95.3355 24.8605 94.5517 24.5452 94.0409 23.9148C93.53 23.2844 93.2711 22.1776 93.2711 20.5875V10.6405H88.4424V21.0078C88.4424 23.6696 89.0022 25.7361 90.1219 27.2071C91.2416 28.6782 92.9422 29.4137 95.2305 29.4137C97.071 29.4137 98.5056 28.8743 99.5344 27.8025V29.0284H104.363V10.6405H99.5344V20.6925Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_6'
        d='M127.772 10.2552C125.392 10.2552 123.447 11.0398 121.928 12.6019C121.228 11.7613 120.494 11.1658 119.724 10.8156C118.975 10.4443 118.023 10.2552 116.855 10.2552C115.196 10.2552 113.859 10.7946 112.831 11.8663V10.6405H108.002V29.0284H112.831V18.9763C112.831 17.4353 113.062 16.3635 113.53 15.7541C114.02 15.1236 114.776 14.8084 115.805 14.8084C116.834 14.8084 117.575 15.1236 118.044 15.7541C118.513 16.3845 118.744 17.4913 118.744 19.0814V29.0284H123.573V18.9763C123.573 17.4353 123.804 16.3635 124.273 15.7541C124.762 15.1236 125.518 14.8084 126.547 14.8084C127.576 14.8084 128.317 15.1236 128.786 15.7541C129.255 16.3845 129.486 17.4913 129.486 19.0814V29.0284H134.315V18.6611C134.315 13.0572 132.131 10.2552 127.772 10.2552Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_7'
        d='M146.842 10.2552C145.421 10.2552 144.126 10.7666 142.958 11.7963V10.6405H138.129V35.7881L142.958 33.3715V27.9777C144.007 28.9373 145.302 29.4137 146.842 29.4137C149.081 29.4137 150.83 28.5591 152.09 26.8569C153.35 25.1267 153.98 22.8641 153.98 20.0621C153.98 16.7908 153.315 14.3391 151.985 12.7069C150.655 11.0748 148.941 10.2552 146.842 10.2552ZM146.212 24.8605C144.812 24.8605 143.727 24.3351 142.958 23.2844V16.3495C143.748 15.3198 144.742 14.8084 145.932 14.8084C148.031 14.8084 149.081 16.5947 149.081 20.1672C149.081 23.2984 148.122 24.8605 146.212 24.8605Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_8'
        d='M166.205 10.2552C164.364 10.2552 162.93 10.7946 161.901 11.8663V3.49546L157.072 5.91215V29.0284H161.901V18.9763C161.901 17.4563 162.16 16.3845 162.671 15.7541C163.21 15.1236 164 14.8084 165.05 14.8084C166.1 14.8084 166.884 15.1236 167.394 15.7541C167.912 16.3845 168.164 17.4913 168.164 19.0814V29.0284H172.993V18.6611C172.993 15.9992 172.433 13.9328 171.313 12.4618C170.194 10.9907 168.486 10.2552 166.205 10.2552Z'
        className='fill-neutral-700'
      />
      <path
        id='Vector_9'
        d='M23.7588 13.4635V3.74763L20.0078 0V16.9729H36.9642L33.4582 13.4635H23.7588Z'
        fill='#00A4BF'
      />
      <path
        id='Vector_10'
        d='M20.0078 37L23.5139 33.4905V23.7817H33.2202L36.9642 20.0201H20.0078V37Z'
        fill='#00A4BF'
      />
      <path
        id='Vector_11'
        d='M13.4503 13.2113H3.73698L0 16.9729H16.9564V0L13.4503 3.50947V13.2113Z'
        fill='#00A4BF'
      />
      <path
        id='Vector_12'
        d='M3.50605 23.5365H13.1984V33.2524L16.9564 37V20.0201H0L3.50605 23.5365Z'
        fill='#00A4BF'
      />
    </svg>
  );
}
