import * as React from 'react';

import Costco from '../assets/costco-logo.png';
import { cn } from '../utils/shadcn';

export default function CostcoLogo(
  props: React.ImgHTMLAttributes<HTMLImageElement>
): React.JSX.Element {
  const { className: classNameProp, ...otherProps } = props;

  return (
    <img
      src={Costco}
      alt='Costco logo'
      className={cn('filter brightness-75 scale-150', classNameProp)}
      {...otherProps}
    />
  );
}
