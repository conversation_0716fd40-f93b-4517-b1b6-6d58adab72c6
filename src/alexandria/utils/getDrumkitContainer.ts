export function getDrumkitContainer() {
  const rootElement = document.getElementById('drumkit-content-view-root');

  if (rootElement) {
    // If the root element has a shadow root, return the shadow root element
    if (rootElement.shadowRoot) {
      return rootElement.shadowRoot.querySelector('#drumkit-content-view-root');
    }

    return rootElement;
  }
  return document.getElementById('container');
}
