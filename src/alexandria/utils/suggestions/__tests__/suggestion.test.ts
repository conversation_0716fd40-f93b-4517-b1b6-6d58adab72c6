import { reorderFields } from '../suggestionFormat';

describe('reorderFields', () => {
  it('reorders fields based on priorityOrder and appends remaining fields', () => {
    const changesDisplayedList: [string, any][] = [
      ['field1', 'value1'],
      ['field2', 'value2'],
      ['field3', 'value3'],
      ['field4', 'value4'],
    ];
    const priorityOrder = ['field3', 'field1'];

    const result = reorderFields(changesDisplayedList, priorityOrder);

    expect(result).toEqual([
      ['field3', 'value3'], // From priorityOrder
      ['field1', 'value1'], // From priorityOrder
      ['field2', 'value2'], // Remaining fields
      ['field4', 'value4'], // Remaining fields
    ]);
  });

  it('returns the original list if no priorityOrder is provided', () => {
    const changesDisplayedList: [string, any][] = [
      ['field1', 'value1'],
      ['field2', 'value2'],
    ];

    const result = reorderFields(changesDisplayedList, null);

    expect(result).toEqual(changesDisplayedList);
  });

  it('handles missing priorityOrder fields gracefully', () => {
    const changesDisplayedList: [string, any][] = [
      ['field1', 'value1'],
      ['field2', 'value2'],
      ['field3', 'value3'],
    ];
    const priorityOrder = ['field4', 'field1'];

    const result = reorderFields(changesDisplayedList, priorityOrder);

    expect(result).toEqual([
      ['field1', 'value1'], // field1 is in priorityOrder
      ['field2', 'value2'], // Remaining fields
      ['field3', 'value3'], // Remaining fields
    ]);
  });

  it('does not handle duplicate entries in priorityOrder', () => {
    const changesDisplayedList: [string, any][] = [
      ['field1', 'value1'],
      ['field2', 'value2'],
    ];
    const priorityOrder = ['field1', 'field1'];

    const result = reorderFields(changesDisplayedList, priorityOrder);

    expect(result).toEqual([
      ['field1', 'value1'], // field1 only appears once
      ['field1', 'value1'], // Remaining fields
      ['field2', 'value2'], // Remaining field
    ]);
  });

  it('returns an empty list when changesDisplayedList is empty', () => {
    const changesDisplayedList: [string, any][] = [];
    const priorityOrder = ['field1', 'field2'];

    const result = reorderFields(changesDisplayedList, priorityOrder);

    expect(result).toEqual([]);
  });

  it('handles empty priorityOrder correctly', () => {
    const changesDisplayedList: [string, any][] = [
      ['field1', 'value1'],
      ['field2', 'value2'],
    ];
    const priorityOrder: string[] = [];

    const result = reorderFields(changesDisplayedList, priorityOrder);

    expect(result).toEqual([
      ['field1', 'value1'],
      ['field2', 'value2'],
    ]);
  });

  it('preserves order for remaining fields when priorityOrder is incomplete', () => {
    const changesDisplayedList: [string, any][] = [
      ['field1', 'value1'],
      ['field2', 'value2'],
      ['field3', 'value3'],
      ['field4', 'value4'],
    ];
    const priorityOrder = ['field3', 'field1'];

    const result = reorderFields(changesDisplayedList, priorityOrder);

    expect(result).toEqual([
      ['field3', 'value3'], // From priorityOrder
      ['field1', 'value1'], // From priorityOrder
      ['field2', 'value2'], // Remaining fields (original order)
      ['field4', 'value4'], // Remaining fields (original order)
    ]);
  });
});
