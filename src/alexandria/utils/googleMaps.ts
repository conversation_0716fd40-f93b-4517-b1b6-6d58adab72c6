export const constructGoogleMapsUrl = (
  stops: { city: string; state: string }[]
) => {
  const origin = encodeURIComponent(stops[0].city + ',' + stops[0].state);
  const waypointStops = stops
    .slice(1, -1)
    .map((stop) => encodeURIComponent(stop.city + ',' + stop.state))
    .join('|');
  const waypoints = waypointStops ? `&waypoints=${waypointStops}` : '';
  const destination = encodeURIComponent(
    stops[stops.length - 1].city + ',' + stops[stops.length - 1].state
  );

  return `https://www.google.com/maps/dir/?api=1&origin=${origin}${waypoints}&destination=${destination}`;
};
