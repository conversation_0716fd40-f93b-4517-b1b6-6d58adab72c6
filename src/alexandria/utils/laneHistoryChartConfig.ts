// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { ChartConfig } from 'components/Chart';

export const chartConfig: ChartConfig = {
  maxRate: {
    label: 'Max',
    color: 'hsl(var(--error))', // error color
  },
  averageRate: {
    label: 'Avg',
    color: 'hsl(var(--brand))', // brand color
  },
  lowestRate: {
    label: 'Min',
    color: 'hsl(var(--success))', // success color
  },
};

export const chartConfigPercentile: ChartConfig = {
  maxRate: {
    label: '75%',
    color: 'hsl(var(--error))', // error color
  },
  averageRate: {
    label: '50%',
    color: 'hsl(var(--brand))', // brand color
  },
  lowestRate: {
    label: '25%',
    color: 'hsl(var(--success))', // success color
  },
};

// reference: QuoteView/Quoting/RequestQuickQuote.tsx
