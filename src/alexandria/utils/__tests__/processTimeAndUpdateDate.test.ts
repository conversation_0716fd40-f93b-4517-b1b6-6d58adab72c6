import { processTimeAndUpdateDate } from '../processTimeAndUpdateDate';

// Mock the captureException util to avoid dependency on @constants/AppVersion
jest.mock('utils/captureException', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('../timepickerFunctions', () => ({
  parseTimeInput: (timeString: string) => {
    if (timeString === '14:30' || timeString === '2:30 PM') {
      return { hours: 14, minutes: 30 };
    } else if (timeString === '9:15 AM' || timeString === '09:15') {
      return { hours: 9, minutes: 15 };
    } else if (timeString === '00:00' || timeString === '12:00 AM') {
      return { hours: 0, minutes: 0 };
    } else {
      return null;
    }
  },
}));

describe('processTimeAndUpdateDate', () => {
  beforeEach(() => {
    jest.useRealTimers();
  });

  test('should set time on a provided Date object', () => {
    const baseDate = new Date(2023, 5, 15);
    const result = processTimeAndUpdateDate('14:30', baseDate);

    expect(result).toBeInstanceOf(Date);
    expect(result?.getFullYear()).toBe(2023);
    expect(result?.getMonth()).toBe(5);
    expect(result?.getDate()).toBe(15);
    expect(result?.getHours()).toBe(14);
    expect(result?.getMinutes()).toBe(30);
  });

  test('should handle alternative time format (AM/PM)', () => {
    const baseDate = new Date(2023, 5, 15);
    const result = processTimeAndUpdateDate('9:15 AM', baseDate);

    expect(result?.getHours()).toBe(9);
    expect(result?.getMinutes()).toBe(15);
  });

  test('should work with date provided as string', () => {
    const dateString = '2023-06-15T10:00:00';
    const dateObject = new Date(dateString);
    const result = processTimeAndUpdateDate('14:30', dateObject);

    expect(result?.getHours()).toBe(14);
    expect(result?.getMinutes()).toBe(30);
  });

  test('should use current date when no date is provided', () => {
    // Mock the current date
    const mockDate = new Date(2023, 5, 15, 10, 0);
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);

    const result = processTimeAndUpdateDate('14:30', null);

    expect(result?.getFullYear()).toBe(2023);
    expect(result?.getMonth()).toBe(5);
    expect(result?.getDate()).toBe(15);
    expect(result?.getHours()).toBe(14);
    expect(result?.getMinutes()).toBe(30);
  });

  test('should return null for invalid time input', () => {
    const baseDate = new Date(2023, 5, 15);
    const result = processTimeAndUpdateDate('invalid', baseDate);

    expect(result).toBeNull();
  });

  test('should handle midnight correctly', () => {
    const baseDate = new Date(2023, 5, 15, 14, 30);
    const result = processTimeAndUpdateDate('00:00', baseDate);

    expect(result?.getHours()).toBe(0);
    expect(result?.getMinutes()).toBe(0);
  });
});
