import { Maybe, Undef } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';

export const getLoadURLInTMS = (
  loadId: string,
  source: string,
  tenant: Undef<string>
): Maybe<string> => {
  if (!loadId) return null;

  switch (source) {
    case TMS.Turvo:
      return tenant
        ? `https://app.turvo.com/#/${tenant}/shipments/${loadId}/details`
        : null;
    case TMS.Tai:
      return `https://atl.taicloud.net/back-office/shipment/shipment-details/${loadId}/domesticSearch`;
    case TMS.GlobalTranzTMS:
      return `https://tms.globaltranz.com/tl/sales-order/${loadId}`;
    case TMS.Relay:
      return `https://www.relaytms.com/sourcing/load_board_load_detail/${loadId}`;
    case TMS.Stark:
      // Support both staging and production
      // Default to production, but can be overridden by checking the current page URL
      if (loadId) {
        // Check if we're currently on a staging page to determine which URL to use
        let isStaging = false;
        if (typeof window !== 'undefined') {
          isStaging = window.location.href.includes('apps-staging');
        }
        return isStaging
          ? `https://apps-staging.transfix.io/#/shipment-details/${loadId}`
          : `https://apps.transfix.io/#/shipment-details/${loadId}`;
      }
      return null;
    case TMS.Aljex:
    case TMS.ThreeG:
      return null;
    default:
      return null;
  }
};
