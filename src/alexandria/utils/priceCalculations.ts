import {
  CarrierCostType,
  ProfitType,
} from '../pages/QuoteView/Quoting/RequestQuickQuote/types';

// FIXME: Not handling line haul correctly.
const calculateFlatCarrierCost = (
  carrierCost: number,
  carrierCostType: CarrierCostType,
  distance: number
): number => {
  return carrierCostType === CarrierCostType.Flat
    ? carrierCost
    : carrierCost * distance;
};

const calculateFinalProfit = (
  profit: number,
  profitType: ProfitType,
  carrierCostType: CarrierCostType,
  flatCarrierCost: number
): number => {
  return profitType === ProfitType.Percentage ||
    carrierCostType === CarrierCostType.Flat
    ? profit // Percentage profit or flat cost stays as-is
    : profit * flatCarrierCost; // Convert to flat profit $ value
};

export const calculatePricing = (
  carrierCost: number,
  carrierCostType: CarrierCostType,
  profit: number,
  profitType: ProfitType,
  distance: number
): { flatCarrierCost: number; finalProfit: number } => {
  const flatCarrierCost = calculateFlatCarrierCost(
    carrierCost,
    carrierCostType,
    distance
  );
  const finalProfit = calculateFinalProfit(
    profit,
    profitType,
    carrierCostType,
    flatCarrierCost
  );

  return { flatCarrierCost, finalProfit };
};
