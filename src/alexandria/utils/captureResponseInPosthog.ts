// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { PostHog } from 'posthog-js/react';

import { StatusResponseEventsPosthog } from 'types/enums/CustomPosthogEvents';

/**
 * Generic function to capture PostHog events for submit responses
 * @param posthog - PostHog instance
 * @param isError - Whether the response is an error
 * @param logProperties - Additional properties to log
 */
export function captureResponseInPosthog(
  posthog: PostHog | undefined,
  isError: boolean,
  logProperties?: { [key: string]: any }
): void {
  const event = isError
    ? StatusResponseEventsPosthog.SubmitError
    : StatusResponseEventsPosthog.SubmitSuccess;

  posthog?.capture(event, logProperties);
}
