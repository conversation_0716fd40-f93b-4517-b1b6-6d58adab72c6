// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { Jodit } from 'jodit-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { Config } from 'jodit/esm/config';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import cellButtons from 'jodit/esm/plugins/inline-popup/config/items/cells';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { DeepPartial, IJodit } from 'jodit/esm/types';

type GetJoditEditorConfigProps = {
  refToUpdateAfterInit?: React.MutableRefObject<IJodit | null>;
};

export const getJoditEditorConfig = ({
  refToUpdateAfterInit,
}: GetJoditEditorConfigProps = {}): DeepPartial<Config> => {
  return {
    readonly: false,
    placeholder: 'Enter your text',
    toolbarAdaptive: false,
    toolbarButtonSize: 'small',
    showXPathInStatusbar: false,
    useNativeTooltip: true,
    addNewLine: false,
    hidePoweredByJodit: true,
    events: {
      afterInit: (jodit: IJodit) =>
        refToUpdateAfterInit && (refToUpdateAfterInit.current = jodit),
    },
    popup: {
      td: Jodit.atom(tableInlinePopupButtons),
      th: Jodit.atom(tableInlinePopupButtons),
    },
    askBeforePasteHTML: false,
    style: { fontSize: '14px' }, // Default font size
    buttons: [
      'bold',
      'italic',
      'underline',
      'strikethrough',
      '|',
      'fontsize',
      'ul',
      'ol',
      '\n',
      { name: 'brush', icon: paintBucketIcon },
      '|',
      'table',
      'link',
      'eraser',
    ],
  };
};

// One annoying quirk about Jodit is that newlines must be wrapped in <p> tags,
// otherwise they will have a weird behavior with the backspace key.
export const nl2br = (content: string): string => {
  return content.replace(/\n/g, '<p><br></p>');
};

const paintBucketIcon = `
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-paint-bucket-icon lucide-paint-bucket">
<path fill="none" d="M 21.119 12.003 L 11.784 2.667 L 1.748 12.703 C 0.858 13.61 0.858 15.063 1.748 15.971 L 7.817 22.039 C 8.75 22.973 10.149 22.973 11.083 22.039 L 21.119 12.003 Z" style="></path>
<path fill="none" d="M 4.468 1.783 L 9.468 6.783" style="></path>
<path fill="none" d="M 2 13 L 19.146 13" style="></path>
<path d="M 22.847 20.945 C 22.847 22.484 21.18 23.446 19.847 22.677 C 19.228 22.319 18.847 21.659 18.847 20.945 C 18.847 19.345 20.547 18.545 20.847 16.945 C 21.147 18.545 22.847 19.345 22.847 20.945 Z" style="paint-order: stroke;"></path>
<path d="M 9.573 11.934 L 15.604 16.315 L 3.541 16.315 L 9.573 11.934 Z" bx:shape="triangle 3.541 11.934 12.063 4.381 0.5 0 1@2e4cd761" style="stroke-width: 5px; transform-box: fill-box; transform-origin: 50% 50%;" transform="matrix(-0.9998, -0.019988, 0.019988, -0.9998, -0.130647, 2.972426)"></path>
<ellipse style="stroke-width: 3px;" cx="20.885" cy="20.794" rx="1.378" ry="1.545"></ellipse>
</svg>
`;

const tableInlinePopupButtons = cellButtons.map((button: any) =>
  button === 'brushCell' ? { name: 'brushCell', icon: paintBucketIcon } : button
);
