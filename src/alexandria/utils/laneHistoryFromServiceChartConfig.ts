// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { ChartConfig } from 'components/Chart';

export const laneHistoryFromServiceChartConfig: ChartConfig = {
  averageRate: {
    label: 'Average',
    color: '#f98600', // tailwind config orange-main
  },
  highRate: {
    label: 'High',
    color: '#e92c2c', // tailwind config red-main
  },
  lowRate: {
    label: 'Low',
    color: '#00ba34', // tailwind config green-main
  },
  p75Rate: {
    label: '75th Percentile',
    color: '#e92c2c', // tailwind config red-main
  },
  p25Rate: {
    label: '25th Percentile',
    color: '#00ba34', // tailwind config green-main
  },
};
