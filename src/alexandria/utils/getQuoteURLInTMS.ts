import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';

export const getQuoteURLInTMS = (
  quoteId: string,
  source: Maybe<TMS>,
  tenant: Maybe<string>
): Maybe<string> => {
  if (!quoteId) return null;

  switch (source) {
    case TMS.Turvo:
      return tenant
        ? `https://app.turvo.com/#/${tenant}/shipments/${quoteId}/details`
        : null;
    default:
      return null;
  }
};
