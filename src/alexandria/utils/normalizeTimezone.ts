import { LoadBuildingSuggestions } from 'types/suggestions/LoadBuildingSuggestions';
import { SuggestedLoad } from 'types/suggestions/LoadBuildingSuggestions';

/**
 * Maps timezone abbreviations to IANA timezone identifiers.
 */
const TIMEZONE_ABBREVIATION_MAP: Record<string, string> = {
  // Pacific Time
  PST: 'America/Los_Angeles',
  PDT: 'America/Los_Angeles',
  PT: 'America/Los_Angeles',

  // Mountain Time
  MST: 'America/Denver',
  MDT: 'America/Denver',
  MT: 'America/Denver',

  // Central Time
  CST: 'America/Chicago',
  CDT: 'America/Chicago',
  CT: 'America/Chicago',

  // Eastern Time
  EST: 'America/New_York',
  EDT: 'America/New_York',
  ET: 'America/New_York',

  // Atlantic Time
  AST: 'America/Halifax',
  ADT: 'America/Halifax',
  AT: 'America/Halifax',
};

function isValidIANATimezone(timezone: string): boolean {
  try {
    // Try to create a DateTimeFormat with the timezone
    // This will throw if the timezone is invalid
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch {
    return false;
  }
}

/**
 * Normalizes a timezone string to IANA format.
 * If the input is already in IANA format, returns it as-is.
 * If the input is a timezone abbreviation (e.g., "PST", "EDT"), converts it to IANA format.
 *
 * @param timezone - The timezone string to normalize (e.g., "PST", "America/Chicago")
 * @returns The IANA timezone identifier, or undefined if the timezone cannot be normalized
 *
 * @example
 * normalizeTimezone('PST') // returns 'America/Los_Angeles'
 * normalizeTimezone('America/Chicago') // returns 'America/Chicago'
 * normalizeTimezone('EDT') // returns 'America/New_York'
 * normalizeTimezone('invalid') // returns undefined
 */
export function normalizeTimezone(
  timezone: string | undefined | null
): string | undefined {
  if (!timezone) {
    return undefined;
  }

  const trimmedTimezone = timezone.trim();

  if (isValidIANATimezone(trimmedTimezone)) {
    return trimmedTimezone;
  }

  const upperTimezone = trimmedTimezone.toUpperCase();
  const mappedTimezone = TIMEZONE_ABBREVIATION_MAP[upperTimezone];

  if (mappedTimezone) {
    return mappedTimezone;
  }

  return undefined;
}

/**
 * Normalizes timezone abbreviations to IANA format in load building suggestions.
 * This ensures that timezones like "CDT", "PST" are converted to proper IANA identifiers
 * like "America/Chicago", "America/Los_Angeles" before they're used in forms.
 */
export function normalizeSuggestionTimezones(
  suggestions: LoadBuildingSuggestions[]
): LoadBuildingSuggestions[] {
  return suggestions.map((suggestion) => {
    const suggested = suggestion.suggested as SuggestedLoad;

    return {
      ...suggestion,
      suggested: {
        ...suggested,
        pickup: suggested.pickup
          ? {
              ...suggested.pickup,
              timezone: normalizeTimezone(suggested.pickup.timezone) || '',
            }
          : suggested.pickup,
        consignee: suggested.consignee
          ? {
              ...suggested.consignee,
              timezone: normalizeTimezone(suggested.consignee.timezone) || '',
            }
          : suggested.consignee,
      },
    };
  });
}
