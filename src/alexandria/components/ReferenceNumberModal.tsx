import { JSX } from 'react';

import { Modal } from 'antd';
import { Select as AntdSelect } from 'antd';

import { Checkbox } from 'components/Checkbox';
import { Label } from 'components/Label';
import { Input } from 'components/input/Input';
import { AdditionalReference } from 'types/Load';

import { Grid } from './layout';

interface ReferenceNumberModalProps {
  isOpen: boolean;
  onCancel: () => void;
  onOk: () => void;
  editingRefIndex: number | null;
  tempRefNumber: AdditionalReference;
  onTempRefChange: (field: keyof AdditionalReference, value: any) => void;
  stop: 'pickup' | 'consignee';
  qualifierOptions: { description: string; code: string }[];
}

export function ReferenceNumberModal({
  qualifierOptions,
  isOpen,
  onCancel,
  onOk,
  editingRefIndex,
  tempRefNumber,
  onTempRefChange,
  stop,
}: ReferenceNumberModalProps): JSX.Element {
  // Check if required fields are filled in.
  const isFormValid =
    tempRefNumber.qualifier?.trim() && tempRefNumber.number?.trim();

  return (
    <Modal
      title={`${editingRefIndex !== null ? 'Edit' : 'Add'} Reference Number - ${stop === 'pickup' ? 'Pickup' : 'Consignee'} Location`}
      open={isOpen}
      onOk={onOk}
      onCancel={onCancel}
      okText={editingRefIndex !== null ? 'Update' : 'Add'}
      cancelText='Cancel'
      width={520}
      closable={{ 'aria-label': 'Close Modal' }}
      okButtonProps={{ disabled: !isFormValid }}
    >
      <div className='py-4 space-y-4'>
        {/* Qualifier Select */}
        <div className='relative'>
          <Label name='qualifier'>
            <span>
              Qualifier <span className='text-error-500'>*</span>
            </span>
          </Label>
          <AntdSelect
            showSearch
            className='mt-1 w-full rounded-md'
            variant='borderless'
            style={{
              border: '1px solid black',
              borderRadius: '0.25rem',
            }}
            placeholder='Select qualifier...'
            value={tempRefNumber.qualifier}
            onChange={(value) => onTempRefChange('qualifier', value)}
            options={qualifierOptions.map((option) => ({
              value: option.code,
              label: `${option.code} - ${option.description}`,
            }))}
          />
        </div>

        {/* Reference Number */}
        <div>
          <Label name='refNumber'>
            <span>
              Reference number <span className='text-error-500'>*</span>
            </span>
          </Label>
          <Input
            placeholder='Enter reference number...'
            value={tempRefNumber.number}
            onChange={(e) => onTempRefChange('number', e.target.value)}
            className='mt-1 text-[14px] font-normal leading-[30px] text-neutral-900'
          />
        </div>

        {/* Weight and Pieces Grid */}
        <Grid cols='2' gap='lg' className='mx-0 w-full'>
          <div>
            <Label name='weight'>Weight</Label>
            <Input
              type='number'
              placeholder='Enter weight...'
              step={0.01}
              value={tempRefNumber.weight}
              onChange={(e) =>
                onTempRefChange('weight', parseFloat(e.target.value) || 0)
              }
              className='mt-1 text-[14px] font-normal leading-[30px] text-neutral-900'
            />
          </div>
          <div>
            <Label name='pieces'>Pieces</Label>
            <Input
              type='number'
              placeholder='Enter number of pieces...'
              step={1}
              value={tempRefNumber.pieces}
              onChange={(e) =>
                onTempRefChange('pieces', parseInt(e.target.value) || 0)
              }
              className='mt-1 text-[14px] font-normal leading-[30px] text-neutral-900'
            />
          </div>
        </Grid>

        {/* Send to Driver Checkbox */}
        <div className='flex items-center space-x-2'>
          <Checkbox
            onCheckedChange={(checked) =>
              onTempRefChange('shouldSendToDriver', checked)
            }
            checked={tempRefNumber.shouldSendToDriver}
            label='Send to driver with load assignment'
            labelClassName='text-[14px] font-normal leading-none text-neutral-900 peer-disabled:cursor-not-allowed whitespace-normal! peer-disabled:opacity-70'
          />
        </div>
      </div>
    </Modal>
  );
}
