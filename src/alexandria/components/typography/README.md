# Typography Component

A comprehensive typography component designed to reduce Tailwind class duplication and improve consistency across the codebase.

## Usage

```tsx
import { Typography } from 'components/typography';

// Headings
<Typography variant="h1">Main Heading</Typography>
<Typography variant="h2">Section Heading</Typography>
<Typography variant="h3">Subsection Heading</Typography>

// Body text
<Typography variant="body1">Regular paragraph text</Typography>
<Typography variant="body2">Smaller body text</Typography>

// Specialized text
<Typography variant="caption">Caption text</Typography>
<Typography variant="link">Clickable link</Typography>
<Typography variant="error">Error message</Typography>
```

## Migration Examples

### Headings

```tsx
// Before
<h1 className="text-3xl font-bold leading-tight tracking-tight">Title</h1>
<h2 className="text-2xl font-semibold leading-tight tracking-tight">Section</h2>
<h3 className="text-xl font-semibold leading-tight">Subsection</h3>

// After
<Typography variant="h1">Title</Typography>
<Typography variant="h2">Section</Typography>
<Typography variant="h3">Subsection</Typography>
```

### Form Labels

```tsx
// Before
<span className="text-sm font-medium text-neutral-900">Field Label</span>
<span className="text-xs text-error-500">Error message</span>
<span className="text-xs text-neutral-500">Hint text</span>

// After
<Typography variant="label">Field Label</Typography>
<Typography variant="error">Error message</Typography>
<Typography variant="hint">Hint text</Typography>
```

### Interactive Text

```tsx
// Before
<span className="text-brand-main hover:text-brand-600 underline cursor-pointer">
  Click here
</span>

// After
<Typography variant="link">Click here</Typography>
```

### Card Content

```tsx
// Before
<div className="text-sm text-neutral-500 dark:text-neutral-400">
  Card description
</div>

// After
<Typography variant="caption">Card description</Typography>
```

## Variants

### Headings

- `h1` - `text-3xl font-bold leading-tight tracking-tight`
- `h2` - `text-2xl font-semibold leading-tight tracking-tight`
- `h3` - `text-xl font-semibold leading-tight`
- `h4` - `text-lg font-semibold leading-tight`
- `h5` - `text-base font-semibold leading-tight`
- `h6` - `text-sm font-semibold leading-tight`

### Body Text

- `body1` - `text-base leading-relaxed`
- `body2` - `text-sm leading-relaxed`
- `body3` - `text-xs leading-relaxed`

### Specialized Text

- `caption` - `text-xs text-neutral-500 dark:text-neutral-400`
- `overline` - `text-xs font-medium uppercase tracking-wider`
- `subtitle1` - `text-lg leading-relaxed`
- `subtitle2` - `text-base leading-relaxed`

### Interactive Text

- `link` - `text-brand-main hover:text-brand-600 underline cursor-pointer`
- `button` - `text-sm font-medium`

### Form Text

- `label` - `text-sm font-medium text-neutral-900`
- `input` - `text-[13px] text-neutral-500`
- `error` - `text-xs text-error-500`
- `hint` - `text-xs text-neutral-500`

## Props

### Color Variants

- `default` - `text-neutral-900 dark:text-neutral-50`
- `muted` - `text-neutral-500 dark:text-neutral-400`
- `brand` - `text-brand-main`
- `error` - `text-error-500`
- `success` - `text-success-500`
- `warning` - `text-warning-500`
- `info` - `text-info-500`

### Weight Variants

- `normal` - `font-normal`
- `medium` - `font-medium`
- `semibold` - `font-semibold`
- `bold` - `font-bold`

### Alignment

- `left` - `text-left`
- `center` - `text-center`
- `right` - `text-right`
- `justify` - `text-justify`

## Advanced Usage

### Custom HTML Elements

```tsx
// Render as different HTML element
<Typography variant="h1" as="h1">Heading</Typography>
<Typography variant="link" as="a" href="/link">Link</Typography>
```

### Combining Props

```tsx
<Typography variant='body1' color='brand' weight='semibold' align='center'>
  Centered branded text
</Typography>
```

### With Layout Components

```tsx
import { Stack, Typography } from 'components';

<Stack spacing='md'>
  <Typography variant='h2'>Section Title</Typography>
  <Typography variant='body1' color='muted'>
    Section description with more details about what this section contains.
  </Typography>
  <Typography variant='caption'>Additional information or metadata</Typography>
</Stack>;
```

## Benefits

1. **Consistency**: Standardized typography across the application
2. **Maintainability**: Typography changes only need to be made in one place
3. **Semantic**: Clear intent with variant names instead of utility classes
4. **Accessibility**: Proper heading hierarchy and semantic HTML elements
5. **Theme Support**: Built-in dark mode support
6. **Type Safety**: Full TypeScript support with proper prop validation

## Best Practices

1. Use semantic variants when possible (e.g., `variant="h1"` for main headings)
2. Combine with layout components for complex text layouts
3. Use the `as` prop to render appropriate HTML elements
4. Leverage color variants for consistent theming
5. Use weight variants sparingly - prefer semantic variants first
6. Consider accessibility when choosing variants (proper heading hierarchy)
