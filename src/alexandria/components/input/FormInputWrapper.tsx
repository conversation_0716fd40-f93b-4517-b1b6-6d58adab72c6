import React from 'react';
import { useFormContext } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';

import AIHintLabel, { AIHintProps } from 'components/AIHint';
import RequiredHint from 'components/RequiredHint';
import { Typography } from 'components/typography';
import { cn } from 'utils/shadcn';

type FormInputWrapperProps = AIHintProps & {
  name: string;
  label: string;
  required?: boolean;
  description?: string;
  requiredIcon?: boolean;
  className?: string;
};

export default function FormInputWrapper({
  name,
  label,
  required,
  description,
  children,
  className,
  ...rest
}: React.PropsWithChildren<FormInputWrapperProps>) {
  const {
    formState: { errors },
  } = useFormContext();

  return (
    <label className='flex flex-col w-full'>
      <div className='mb-1'>
        <span
          className={cn(
            'inline text-sm text-neutral-800 break-words',
            className
          )}
        >
          {label}
          {required && <RequiredHint />}
          <AIHintLabel name={name} {...rest} />
        </span>
        {description && (
          <p className='text-xs text-neutral-500 break-words mt-1'>
            {description}
          </p>
        )}
      </div>
      {children}
      <ErrorMessage
        errors={errors}
        name={name}
        render={({ message }: { message: string }) => (
          <Typography variant='body-xs' className='text-error-500'>
            {message}
          </Typography>
        )}
      />
    </label>
  );
}
