import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Controller,
  ControllerRenderProps,
  UseFormReturn,
} from 'react-hook-form';

import { Plus, X } from 'lucide-react';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import { Input } from 'components/input';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { NormalizedLoad } from 'types/Load';

interface MultiSelectPillInputProps<TFieldKey extends keyof NormalizedLoad> {
  name: TField<PERSON>ey;
  label: string;
  control: UseFormReturn<NormalizedLoad>['control'];
  originalSuggestionData?: NormalizedLoad;
}

export function MultiSelectPillInput<TFieldKey extends keyof NormalizedLoad>({
  name,
  label,
  control,
  originalSuggestionData,
}: MultiSelectPillInputProps<TFieldKey>) {
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customValue, setCustomValue] = useState('');

  // Refs for managing the one-time initialization side effect
  const hasInitialized = useRef(false);
  const fieldRef = useRef<ControllerRenderProps<
    NormalizedLoad,
    TFieldKey
  > | null>(null);

  const rawDataString = useMemo(() => {
    const fieldValue = originalSuggestionData?.[name];
    return typeof fieldValue === 'string' ? fieldValue : '';
  }, [originalSuggestionData, name]);

  const allOptions = useMemo(() => {
    if (!rawDataString) return [];

    return rawDataString
      .split(',')
      .map((item: string) => item.trim())
      .filter((item: string) => item);
  }, [rawDataString]);

  useEffect(() => {
    // We only proceed if we have options, the field ref, and we haven't initialized yet.
    if (fieldRef.current && allOptions.length > 0 && !hasInitialized.current) {
      const { value } = fieldRef.current;
      const selectedValueString = (value as string) || '';

      // Only set the first option if the field is currently empty (not set by RHF defaults)
      if (!selectedValueString) {
        // Use setTimeout(0) to defer the state change until after the current render/task queue completes.
        setTimeout(() => {
          if (fieldRef.current) {
            // Re-check ref in case component unmounted
            hasInitialized.current = true;
            fieldRef.current.onChange(allOptions[0]);
          }
        }, 0);
      } else {
        // If the field already has a value (likely from defaultValues), mark as initialized
        // to prevent later runs, but don't change the value.
        hasInitialized.current = true;
      }
    }
  }, [allOptions]);

  const isCustomValue = (itemValue: string) => {
    return !allOptions.includes(itemValue);
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => {
        // Store the field object for use in useEffect
        fieldRef.current = field as ControllerRenderProps<
          NormalizedLoad,
          TFieldKey
        >;

        const selectedValueString = (field.value as string) || '';
        const selectedArray = selectedValueString
          .split(',')
          .map((item: string) => item.trim())
          .filter((item: string) => item);

        const handlePillClick = (itemValue: string) => {
          const isCurrentlySelected = selectedArray.includes(itemValue);

          const newSelectedArray = isCurrentlySelected
            ? selectedArray.filter((item) => item !== itemValue)
            : [...selectedArray, itemValue];

          field.onChange(newSelectedArray.join(','));
        };

        const handleClear = () => {
          field.onChange('');
        };

        const handleAddCustom = () => {
          const trimmedValue = customValue.trim();
          if (trimmedValue) {
            if (selectedValueString) {
              const currentArray = selectedValueString
                .split(',')
                .map((item: string) => item.trim())
                .filter((item: string) => item);

              if (!currentArray.includes(trimmedValue)) {
                const newArray = [...currentArray, trimmedValue];
                field.onChange(newArray.join(','));
              }
            } else {
              field.onChange(trimmedValue);
            }

            setCustomValue('');
            setShowCustomInput(false);
          }
        };

        const handleRemoveCustom = (itemToRemove: string) => {
          if (selectedValueString) {
            const currentArray = selectedValueString
              .split(',')
              .map((item: string) => item.trim())
              .filter((item: string) => item);
            const newArray = currentArray.filter(
              (item: string) => item !== itemToRemove
            );
            field.onChange(newArray.join(','));
          }
        };

        return (
          <Flex direction='col' gap='sm'>
            <Label name={name}>{label}</Label>

            {/* Input Bar (Read-Only) */}
            <Flex align='center' gap='xs'>
              <Input
                type='text'
                // Tooltip title shows the full value
                title={
                  selectedValueString || `No ${label.toLowerCase()} selected`
                }
                value={selectedValueString}
                readOnly
                placeholder={
                  allOptions.length > 0
                    ? `Select options below or add custom...`
                    : `Add custom ${label.toLowerCase()}`
                }
                className='cursor-default'
              />

              {/* Clear Button */}
              {selectedValueString && (
                <Button
                  type='button'
                  onClick={handleClear}
                  variant='unstyled'
                  size='auto'
                  className='p-1 rounded-full hover:bg-neutral-200 transition-colors'
                  buttonNamePosthog={null}
                >
                  <X className='h-3 w-3 text-neutral-500' />
                </Button>
              )}
            </Flex>

            {/* Pills Container */}
            <Flex wrap='wrap' gap='sm'>
              {/* -------------------- Standard Options Pills -------------------- */}
              {allOptions.map((itemValue: string) => {
                const isSelected = selectedArray.includes(itemValue);
                return (
                  <Button
                    key={itemValue}
                    type='button'
                    onClick={() => handlePillClick(itemValue)}
                    variant='unstyled'
                    size='auto'
                    className={`
                      px-3 py-1.5 rounded-full text-sm font-medium transition-colors
                      ${
                        isSelected
                          ? 'bg-accent-100 text-accent-700 border border-accent-200'
                          : 'bg-neutral-100 text-neutral-700 border border-neutral-200 hover:bg-neutral-200'
                      }
                    `}
                    buttonNamePosthog={null}
                  >
                    <Typography variant='body-sm' weight='medium'>
                      {itemValue}
                    </Typography>
                  </Button>
                );
              })}

              {/* -------------------- Custom Value Pills -------------------- */}
              {selectedArray
                // Filter the selected array to only show items not in the original list
                .filter((item: string) => isCustomValue(item))
                .map((itemValue: string) => (
                  <Flex
                    key={itemValue}
                    align='center'
                    gap='xs'
                    className='px-3 py-1.5 rounded-full text-sm font-medium bg-brand-100 text-brand-700 border border-brand-200'
                  >
                    <Typography variant='body-sm'>{itemValue}</Typography>
                    <Button
                      type='button'
                      onClick={() => handleRemoveCustom(itemValue)}
                      variant='unstyled'
                      size='auto'
                      className='ml-1 p-0.5 rounded-full hover:bg-brand-200 transition-colors'
                      title={`Remove custom ${label.toLowerCase()} ${itemValue}`}
                      buttonNamePosthog={null}
                    >
                      <X className='h-3 w-3' />
                    </Button>
                  </Flex>
                ))}

              {/* -------------------- Add Custom Button -------------------- */}
              {!showCustomInput && (
                <Button
                  type='button'
                  onClick={() => setShowCustomInput(true)}
                  variant='unstyled'
                  size='auto'
                  className='flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium bg-neutral-100 text-neutral-700 border border-neutral-200 hover:bg-neutral-200 transition-colors'
                  buttonNamePosthog={null}
                >
                  <Plus className='h-3 w-3' />
                  <Typography variant='body-sm' weight='medium'>
                    Add Custom
                  </Typography>
                </Button>
              )}

              {/* -------------------- Custom Input Field -------------------- */}
              {showCustomInput && (
                <Flex align='center' gap='xs'>
                  <Input
                    type='text'
                    value={customValue}
                    onChange={(e) => setCustomValue(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleAddCustom();
                      } else if (e.key === 'Escape') {
                        setShowCustomInput(false);
                        setCustomValue('');
                      }
                    }}
                    placeholder={`Enter ${label.toLowerCase()}`}
                    autoFocus
                  />
                  <Button
                    type='button'
                    onClick={handleAddCustom}
                    variant='unstyled'
                    size='auto'
                    className='px-2 py-1 rounded text-sm bg-brand-100 text-brand-700 border border-brand-200 hover:bg-brand-200 transition-colors'
                    buttonNamePosthog={null}
                  >
                    <Typography variant='body-sm' weight='medium'>
                      Add
                    </Typography>
                  </Button>
                  <Button
                    type='button'
                    onClick={() => {
                      setShowCustomInput(false);
                      setCustomValue('');
                    }}
                    variant='unstyled'
                    size='auto'
                    className='px-2 py-1 rounded text-sm bg-neutral-100 text-neutral-700 border border-neutral-200 hover:bg-neutral-200 transition-colors'
                    buttonNamePosthog={null}
                  >
                    <Typography variant='body-sm' weight='medium'>
                      Cancel
                    </Typography>
                  </Button>
                </Flex>
              )}
            </Flex>
          </Flex>
        );
      }}
    />
  );
}
