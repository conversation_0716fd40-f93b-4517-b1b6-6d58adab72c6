import React, { useEffect, useMemo, useRef, useState } from 'react';
import { UseControllerProps, useController } from 'react-hook-form';

import { Check, ChevronDown, X } from 'lucide-react';

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from 'components/Command';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from 'components/Dropdown';
import { Label } from 'components/Label';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Input } from 'components/input';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { NormalizedLoad } from 'types/Load';
import { cn } from 'utils/shadcn';

export interface ReferenceOption {
  value: string;
  label: string;
  source: string;
}

interface ReferenceNumberInputProps<T extends Record<string, any>>
  extends UseControllerProps<T> {
  label: string;
  placeholder?: string;
  required?: boolean;
  description?: string;
  load: NormalizedLoad;
  className?: string;
  // Custom function to generate options, if not provided will use default logic
  generateOptions?: (load: NormalizedLoad) => ReferenceOption[];
}

export function ReferenceNumberInput<T extends Record<string, any>>({
  name,
  control,
  rules,
  label,
  placeholder = 'Enter reference number',
  required = false,
  description,
  load,
  className,
  generateOptions,
}: ReferenceNumberInputProps<T>) {
  const {
    field: { value, onChange, onBlur },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules: {
      ...rules,
      ...(required && { required: 'This field is required' }),
    },
  });

  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value || '');
  const [showAddNew, setShowAddNew] = useState(false);
  const [newValue, setNewValue] = useState('');
  const [customValues, setCustomValues] = useState<string[]>([]);

  // Parse comma-separated value into array of selected values
  const selectedValues = useMemo(() => {
    if (!value) return [];
    return value
      .split(',')
      .map((v: string) => v.trim())
      .filter((v: string) => v);
  }, [value]);

  // Format display value with truncation
  const displayValue = useMemo(() => {
    if (!value) return '';

    const values = value
      .split(',')
      .map((v: string) => v.trim())
      .filter((v: string) => v);

    if (values.length <= 2) {
      return value; // Show all if 2 or fewer
    }

    // Show first 2 values + count
    const firstTwo = values.slice(0, 2).join(', ');
    const remainingCount = values.length - 2;

    return `${firstTwo}, ... (${remainingCount} more)`;
  }, [value]);

  // Handle multi-select changes
  const handleValueToggle = (optionValue: string) => {
    const newSelectedValues = selectedValues.includes(optionValue)
      ? selectedValues.filter((v: string) => v !== optionValue)
      : [...selectedValues, optionValue];

    // Clean up the value - remove empty strings and extra spaces
    const cleanedValues = newSelectedValues
      .filter((v: string) => v.trim())
      .map((v: string) => v.trim());

    const newValue = cleanedValues.join(', ');
    onChange(newValue);
    setInputValue(newValue);
  };

  const inputContainerRef = useRef<HTMLDivElement>(null);
  const [dropdownWidth, setDropdownWidth] = useState<number | undefined>(
    undefined
  );

  useEffect(() => {
    if (inputContainerRef.current) {
      setDropdownWidth(inputContainerRef.current.offsetWidth);
    }
  }, []);

  // Sync input value when form value changes
  useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  // Generate reference options from load data
  const referenceOptions = useMemo(() => {
    if (generateOptions) {
      return generateOptions(load);
    }

    // Initialize a Map to deduplicate options by value
    const uniqueOptionsMap = new Map<string, ReferenceOption>();

    // Helper to add options, only adding if value is new
    const addOption = (option: ReferenceOption) => {
      // Only add if value is new - prevents duplicates
      if (!uniqueOptionsMap.has(option.value)) {
        uniqueOptionsMap.set(option.value, option);
      }
    };

    // Helper to process candidate arrays (filter, sort, and add options)
    const processCandidates = (candidates: any, source: string) => {
      if (!candidates) return;

      const candidateArray = Array.isArray(candidates)
        ? candidates
        : (Object.values(candidates) as string[]);

      candidateArray
        .filter(
          (candidate: string) =>
            candidate && candidate.trim() && !candidate.includes(',')
        )
        .sort()
        .forEach((candidate: string) => {
          addOption({
            value: candidate,
            label: candidate,
            source,
          });
        });
    };

    // 1. Process Custom Values (Prioritized)
    [...customValues].sort().forEach((customValue) => {
      addOption({ value: customValue, label: customValue, source: 'custom' });
    });

    // 2. Process Currently Selected Values (Ensures deselected values can be re-selected)
    selectedValues.forEach((selectedValue: string) => {
      if (!uniqueOptionsMap.has(selectedValue)) {
        addOption({
          value: selectedValue,
          label: selectedValue,
          source: 'selected',
        });
      }
    });

    // 3. Process Core Load Values and Candidates

    // Add poNums (split by comma)
    if (load?.poNums) {
      const poNumbers = load.poNums
        .split(',')
        .map((po) => po.trim())
        .filter((po) => po && !po.includes(',')) // Filter out comma-separated values
        .sort();

      poNumbers.forEach((po) => {
        addOption({ value: po, label: po, source: 'poNums' });
      });
    }

    // Add TMS ID (freightTrackingID)
    if (load?.freightTrackingID && !load.freightTrackingID.includes(',')) {
      addOption({
        value: load.freightTrackingID,
        label: `TMS ID: ${load.freightTrackingID}`,
        source: 'freightTrackingID',
      });
    }

    // Add pickup refNumber (remove "PO " prefix if present)
    if (load?.pickup?.refNumber) {
      const pickupRef = load.pickup.refNumber.replace(/^PO\s*/i, '');
      if (pickupRef && !pickupRef.includes(',')) {
        addOption({
          value: pickupRef,
          label: `Pickup Ref: ${pickupRef}`,
          source: 'pickup',
        });
      }
    }

    // Add pickup reference candidates
    processCandidates(load?.pickup?.refNumberCandidates, 'pickup');

    // Add consignee refNumber
    if (load?.consignee?.refNumber && !load.consignee.refNumber.includes(',')) {
      addOption({
        value: load.consignee.refNumber,
        label: load.consignee.refNumber,
        source: 'consignee',
      });
    }

    // Add consignee reference candidates
    processCandidates(load?.consignee?.refNumberCandidates, 'consignee');

    // Add customer refNumber
    if (load?.customer?.refNumber && !load.customer.refNumber.includes(',')) {
      addOption({
        value: load.customer.refNumber,
        label: load.customer.refNumber,
        source: 'customer',
      });
    }

    // Add customer reference candidates
    processCandidates(load?.customer?.refNumberCandidates, 'customer');

    // Final output - convert Map to sorted array
    return Array.from(uniqueOptionsMap.values()).sort((a, b) =>
      a.value.localeCompare(b.value)
    );
  }, [load, generateOptions, customValues, selectedValues]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Clean up the value when user types
    const cleanedValue = newValue
      .split(',')
      .map((v: string) => v.trim())
      .filter((v: string) => v)
      .join(', ');

    onChange(cleanedValue);
  };

  const handleAddNew = () => {
    if (newValue.trim()) {
      const trimmedValue = newValue.trim();

      // If user types multiple values separated by commas, add them all
      const valuesToAdd = trimmedValue
        .split(',')
        .map((v: string) => v.trim())
        .filter((v: string) => v);

      const newSelectedValues = [...selectedValues, ...valuesToAdd];
      const newValueString = newSelectedValues.join(', ');
      onChange(newValueString);

      // Add to custom values list if not already there
      valuesToAdd.forEach((value) => {
        if (!customValues.includes(value)) {
          setCustomValues([...customValues, value]);
        }
      });

      setNewValue('');
      setShowAddNew(false);
    }
  };

  const handleRemoveCustom = (customValue: string) => {
    // Remove from custom values list
    setCustomValues(customValues.filter((v) => v !== customValue));

    // Remove from current selection if it's selected
    if (selectedValues.includes(customValue)) {
      const newSelectedValues = selectedValues.filter(
        (v: string) => v !== customValue
      );
      const newValueString = newSelectedValues.join(', ');
      onChange(newValueString);
    }
  };

  const handleInputBlur = () => {
    // Ensure the form value is updated when input loses focus
    onChange(inputValue);
    onBlur();
  };

  const toggleDropdown = () => {
    if (referenceOptions.length > 0) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={cn('space-y-2 w-full', className)}>
      <Label name={name as string} required={required} className='mb-1'>
        {label}
      </Label>
      {description && (
        <Typography variant='body-xs' className='text-neutral-500'>
          {description}
        </Typography>
      )}
      <div className='flex' ref={inputContainerRef}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Input
                value={displayValue}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
                placeholder={placeholder}
                className={cn(
                  'flex-1 rounded-r-none',
                  error && 'border-error-500 focus:border-error-500'
                )}
              />
            </TooltipTrigger>
            {value && value !== displayValue && (
              <TooltipContent>
                <Typography variant='body-sm' className='max-w-xs break-words'>
                  {value}
                </Typography>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
        {referenceOptions.length > 0 && (
          <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
              <button
                type='button'
                className='px-3 flex items-center justify-center cursor-pointer border border-l-0 border-neutral-input-border bg-neutral-200 hover:bg-neutral-300 transition-colors rounded-r-md rounded-l-none'
                onClick={toggleDropdown}
                title={`${referenceOptions.length} reference options available`}
              >
                <Flex align='center' gap='xs'>
                  <span className='text-xs text-neutral-700 font-medium'>
                    {selectedValues.length > 0
                      ? `${selectedValues.length}/${referenceOptions.length}`
                      : referenceOptions.length}
                  </span>
                  <ChevronDown
                    className={cn(
                      'h-4 w-4 text-neutral-500 transition-transform',
                      isOpen && 'rotate-180'
                    )}
                  />
                </Flex>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align='end'
              style={{ width: dropdownWidth ? `${dropdownWidth}px` : 'auto' }}
              className='w-full px-0 py-0.5 z-[9999] bg-neutral-200 border border-neutral-300 shadow-lg rounded-md overflow-hidden'
              sideOffset={2}
              side='bottom'
            >
              <Command className='rounded-lg'>
                <CommandInput
                  placeholder='Search and select options...'
                  className='border-0 focus:ring-0 rounded-t-lg h-10 text-xs'
                />
                <CommandList className='max-h-64'>
                  <CommandEmpty className='py-6 text-center text-sm text-neutral-500'>
                    {referenceOptions.length === 0
                      ? 'No reference options available'
                      : 'No reference options found.'}
                  </CommandEmpty>
                  <CommandGroup>
                    {referenceOptions.map((option) => {
                      const isSelected = selectedValues.includes(option.value);
                      return (
                        <CommandItem
                          key={`${option.source}-${option.value}`}
                          value={option.value}
                          onSelect={() => handleValueToggle(option.value)}
                          className='cursor-pointer hover:bg-neutral-100 transition-colors mx-0.5 rounded'
                        >
                          <Flex
                            align='center'
                            gap='sm'
                            className='py-1 px-1 w-full'
                          >
                            <div className='flex items-center justify-center w-4 h-4 border border-neutral-300 rounded-sm'>
                              {isSelected && (
                                <Check className='w-3 h-3 text-neutral-900' />
                              )}
                            </div>
                            <Typography
                              weight='medium'
                              className='text-neutral-900 flex-1'
                              variant='body-sm'
                            >
                              {option.value}
                            </Typography>
                            {option.source === 'custom' && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveCustom(option.value);
                                }}
                                className='p-1 hover:bg-neutral-200 rounded transition-colors'
                                title='Remove custom value'
                              >
                                <X className='w-3 h-3 text-neutral-500 hover:text-neutral-700' />
                              </button>
                            )}
                          </Flex>
                        </CommandItem>
                      );
                    })}

                    {/* Add new value option */}
                    {showAddNew ? (
                      <CommandItem className='mx-0.5 rounded'>
                        <Flex align='center' gap='sm' className='w-full'>
                          <Input
                            value={newValue}
                            onChange={(e) => setNewValue(e.target.value)}
                            placeholder='Enter new reference number'
                            className='flex-1 text-sm'
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                handleAddNew();
                              } else if (e.key === 'Escape') {
                                setShowAddNew(false);
                                setNewValue('');
                              }
                            }}
                            autoFocus
                          />
                          <button
                            onClick={handleAddNew}
                            disabled={!newValue.trim()}
                            className='px-2 py-1 bg-brand-500 text-white text-xs rounded hover:bg-brand-600 disabled:bg-neutral-300 disabled:cursor-not-allowed'
                          >
                            Add
                          </button>
                          <button
                            onClick={() => {
                              setShowAddNew(false);
                              setNewValue('');
                            }}
                            className='px-2 py-1 bg-neutral-300 text-neutral-700 text-xs rounded hover:bg-neutral-400'
                          >
                            Cancel
                          </button>
                        </Flex>
                      </CommandItem>
                    ) : (
                      <CommandItem
                        onSelect={() => setShowAddNew(true)}
                        className='cursor-pointer hover:bg-neutral-100 transition-colors mx-0.5 rounded border-t border-neutral-200'
                      >
                        <Flex align='center' gap='sm' className='w-full py-2'>
                          <Typography
                            variant='body-sm'
                            className='text-brand-600 font-medium'
                          >
                            + Add reference number
                          </Typography>
                        </Flex>
                      </CommandItem>
                    )}
                  </CommandGroup>
                </CommandList>
              </Command>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      {error && (
        <Typography variant='body-xs' className='text-error-500'>
          {error.message}
        </Typography>
      )}
    </div>
  );
}
