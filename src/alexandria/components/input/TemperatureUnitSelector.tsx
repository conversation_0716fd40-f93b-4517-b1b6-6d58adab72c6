import * as React from 'react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore library installed on parent module, overriding tsc check
import * as ToggleGroup from '@radix-ui/react-toggle-group';

import { TemperatureUnit } from 'types/enums/TemperatureUnit';
import { cn } from 'utils/shadcn';

export interface TemperatureUnitSelectorProps {
  value: TemperatureUnit;
  onChange: (value: TemperatureUnit) => void;
  className?: string;
}

const ToggleGroupItem = React.forwardRef<
  React.ElementRef<typeof ToggleGroup.Item>,
  React.ComponentPropsWithoutRef<typeof ToggleGroup.Item>
>(({ className, children, ...props }, ref) => (
  <ToggleGroup.Item
    ref={ref}
    className={cn(
      'inline-flex items-center justify-center whitespace-nowrap rounded-[4px] px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-primary data-[state=on]:text-primary-foreground data-[state=off]:bg-muted hover:bg-muted/80',
      className
    )}
    {...props}
  >
    {children}
  </ToggleGroup.Item>
));
ToggleGroupItem.displayName = 'ToggleGroupItem';

const TemperatureUnitSelector = React.forwardRef<
  HTMLDivElement,
  TemperatureUnitSelectorProps
>(({ value, onChange, className }, ref) => {
  return (
    <ToggleGroup.Root
      type='single'
      value={value}
      onValueChange={(val: TemperatureUnit) => val && onChange(val)}
      className={cn(
        'inline-flex h-8 items-center justify-center rounded-[4px] bg-muted p-1',
        className
      )}
      aria-label='Temperature unit selection'
      ref={ref}
    >
      <ToggleGroupItem
        value={TemperatureUnit.Fahrenheit}
        aria-label='Fahrenheit'
      >
        °F
      </ToggleGroupItem>
      {/* TODO: ENG-3885 Add support for Celsius throughout the app */}
      {/* <ToggleGroupItem value={TemperatureUnit.Celsius} aria-label='Celsius'>
        °C
      </ToggleGroupItem> */}
    </ToggleGroup.Root>
  );
});

TemperatureUnitSelector.displayName = 'TemperatureUnitSelector';

export { TemperatureUnitSelector };
