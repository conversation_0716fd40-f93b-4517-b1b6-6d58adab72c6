import { useEffect, useState } from 'react';
import {
  Controller,
  FieldErrors,
  FieldValues,
  RegisterOptions,
  UseControllerProps,
} from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { RefreshCwIcon, XCircleIcon } from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

import {
  DebounceSelect,
  GenericLocationOption,
  ValueType,
} from 'components/DebounceSelect';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { hasRequiredValidation } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { useFieldAttributes } from 'hooks/useLoadContext';
import {
  FieldAttributes,
  getFieldAttribute,
  initFieldAttributes,
} from 'types/LoadAttributes';
import { Maybe, Undef } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import {
  CarrierSearchableFields,
  GenericCompanySearchableFields,
} from 'utils/loadInfoAndBuilding';

type RHFDebounceSelectProps<TFieldValues extends FieldValues> = Pick<
  UseControllerProps<TFieldValues>,
  'name' | 'rules' | 'control' | 'disabled'
> & {
  label: string;
  errors: FieldErrors;
  data: Maybe<any[]>; // List of objects (locations/customers) which will then be mapped to options
  fetchOptions: (
    field: GenericCompanySearchableFields,
    searchTerm: string
  ) => Promise<ValueType[]>;
  mapOptions: (data: Maybe<any[]>) => ValueType[];
  refreshHandler: () => void;
  resetOptionsHandler?: () => void;
  isLoading: boolean;

  required?: boolean; // Default = true
  requiredIcon?: boolean;
  placeholder?: string; // Default = 'Search'
  showSearchParamDropdown?: boolean; // Default = true
  searchFieldDefault?: GenericCompanySearchableFields | CarrierSearchableFields; // Default = 'name'
  searchParamOptions?: {
    key: GenericCompanySearchableFields | CarrierSearchableFields;
    label: string;
  }[];
  // Optional fields for parent component to control the value/display of its child DebounceSelect input.
  // Example: The debounce select is used to search a location by street address, and when selected, we want to update the parent
  // `${stop}.externalTMSID` field with the selected location's externalTMSID,
  //  and `${stop}.addressLine1` with the selected location's addressLine1. See example usage in
  //   [McleodSectionForms/Stop.tsx](https://github.com/drumkitai/alexandria/pages/QuoteView/LoadBuilding/McleodSectionForms/Stop.tsx#L190-L210)
  controllingParentValue?: ValueType;
  valueRenderer?: (selectedOption: ValueType) => ValueType;
  parentOnChange?: (value: ValueType) => void;
};

// TODO: Use more type safety https://shorturl.at/SxZ7T
export function RHFDebounceSelect<TFieldValues extends FieldValues>({
  name,
  label,
  disabled,
  control,
  errors,
  rules,
  fetchOptions,
  mapOptions,
  data: initData,
  isLoading: initIsLoading,
  refreshHandler,
  resetOptionsHandler,
  controllingParentValue: initParentValue,
  valueRenderer,
  parentOnChange,
  placeholder = 'Search',
  required = true,
  showSearchParamDropdown = true,
  searchFieldDefault = 'name',
  searchParamOptions = [
    { key: 'name', label: 'Name' },
    { key: 'addressLine1', label: 'Street' },
  ],
}: RHFDebounceSelectProps<TFieldValues>) {
  const allFieldAttrs = useFieldAttributes();

  const thisFieldAttr: FieldAttributes = (() => {
    // Handle fields of type `ValueUnit`
    const normalizedName = name.replace('.val', '');

    const res = getFieldAttribute(allFieldAttrs, normalizedName);
    return res ?? initFieldAttributes;
  })();

  required = required || hasRequiredValidation(rules as RegisterOptions);
  const [curParentValue, setCurParentValue] =
    useState<Undef<ValueType>>(initParentValue);
  const [curDataList, setCurDataList] = useState<Maybe<ValueType[]>>(initData);
  const [isLoadingDebounceSelect, setIsLoadingDebounceSelect] = useState(false);
  const [selectedObj, setSelectedObj] = useState<Maybe<ValueType>>(null);
  const [searchField, setSearchField] = useState<string>(searchFieldDefault);

  const posthog = usePostHog();

  useEffect(() => {
    if (initData) {
      setCurDataList(initData);
    }
  }, [initData]);

  useEffect(() => {
    setIsLoadingDebounceSelect(initIsLoading);
  }, [initIsLoading]);

  useEffect(() => {
    setCurParentValue(initParentValue);
  }, [initParentValue]);

  // Helper function to create user-friendly display value
  const getDisplayValue = (selectedOption: ValueType): ValueType => {
    if (valueRenderer) {
      return valueRenderer(selectedOption);
    }

    // For location selections, show name instead of ID
    const displayLabel = selectedOption.name || selectedOption.label;
    return {
      label: displayLabel,
      value: selectedOption.value,
    };
  };

  // Helper function to create user-friendly label
  const getFieldLabel = (): string => {
    // For location fields, just show the base label
    return label;
  };

  return (
    <Flex align='start' gap='sm' className='w-full whitespace-nowrap'>
      {/* Simplified search controls - only show dropdown when explicitly needed */}
      {showSearchParamDropdown &&
        !thisFieldAttr.isReadOnly &&
        searchParamOptions.length > 1 && (
          <div className='w-[85px] shrink-0'>
            <Label name='searchParam' className='font-medium'>
              Search by
            </Label>
            <Select
              value={searchField}
              disabled={disabled || thisFieldAttr.isReadOnly}
              onValueChange={(value) => {
                setSearchField(value);
                posthog?.capture(
                  ButtonNamePosthog.LoadBuildingLocationNameVsStreet,
                  {
                    searchField,
                  }
                );
              }}
            >
              <SelectTrigger className='h-8 w-[85px] text-xs !mt-1'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {searchParamOptions.map((option) => (
                  <SelectItem
                    key={option.key}
                    value={option.key}
                    className='text-xs'
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

      {/* Main search input section */}
      <div className='w-full overflow-auto'>
        <Label name={name} required={required} className='text-sm font-medium'>
          {getFieldLabel()}
        </Label>

        <Flex align='center' gap='sm'>
          <div className='flex-1 min-w-0'>
            <Controller
              name={name}
              control={control}
              disabled={disabled || thisFieldAttr.isReadOnly}
              rules={{ required: required ? 'Required' : undefined, ...rules }}
              render={({ field }) => {
                const currentValue = curParentValue ?? field.value;

                let selectedOption: ValueType = mapOptions(curDataList).find(
                  (option) => option.value === currentValue
                );
                // Only use selectedObj fallback if we're not actively searching (i.e., if curDataList is empty)
                if (
                  !selectedOption &&
                  selectedObj &&
                  field.value &&
                  (!curDataList || curDataList.length === 0)
                ) {
                  selectedOption = mapOptions([selectedObj])[0];
                }

                // Update selectedObj when field value matches an item in the data list
                if (currentValue && curDataList) {
                  const matchingObj = curDataList.find(
                    (item) => item.externalTMSID === currentValue
                  );
                  if (
                    matchingObj &&
                    (!selectedObj || selectedObj.externalTMSID !== currentValue)
                  ) {
                    setSelectedObj(matchingObj);
                  }
                } else if (!currentValue && selectedObj) {
                  // Clear selectedObj when field is cleared
                  setSelectedObj(null);
                }

                return (
                  <Flex align='center' gap='sm' className='w-full'>
                    <div className='flex-1 min-w-0'>
                      <DebounceSelect
                        showSearch
                        disabled={
                          isLoadingDebounceSelect ||
                          disabled ||
                          thisFieldAttr.isReadOnly
                        }
                        className='h-10 text-sm border-neutral-500 !mt-1'
                        placeholder={
                          isLoadingDebounceSelect
                            ? 'Loading...'
                            : placeholder || 'Search locations...'
                        }
                        optionFilterProp='children'
                        fetchOptions={(search) =>
                          fetchOptions(
                            searchField as GenericCompanySearchableFields,
                            search
                          )
                        }
                        onChange={(value) => {
                          parentOnChange
                            ? parentOnChange(value)
                            : field.onChange(value.value);
                          const selectedObj = curDataList?.find(
                            (c) => c.externalTMSID === value.value
                          );
                          setSelectedObj(selectedObj);
                        }}
                        value={
                          selectedOption
                            ? getDisplayValue(selectedOption)
                            : null
                        }
                        options={mapOptions(curDataList)}
                        optionRender={(option) => (
                          <GenericLocationOption
                            option={option.data}
                            optionFieldsToRender={[
                              'addressLine1',
                              'addressLine2',
                              'city',
                              'state',
                              'zipCode',
                            ]}
                          />
                        )}
                        notFoundContent={
                          <div className='p-4 text-center text-neutral-500'>
                            <div className='text-sm'>No results found</div>
                            <div className='text-xs mt-1'>
                              {name.includes('customer')
                                ? 'Try searching with 2 or more characters'
                                : 'Try searching with 3 or more characters'}
                            </div>
                          </div>
                        }
                        onOpenChange={(visible) =>
                          !visible && resetOptionsHandler
                            ? resetOptionsHandler()
                            : null
                        }
                        styles={{
                          popup: { root: { maxWidth: '100%' } },
                        }}
                        style={{
                          width: '100%',
                        }}
                      />
                    </div>

                    {/* Clear button - outside the select */}
                    {field.value && !required && (
                      <button
                        title='Clear selection'
                        onClick={() => {
                          field.onChange(null);
                          setSelectedObj(null);
                        }}
                        className='shrink-0 p-1 hover:text-error-600 rounded-full transition-colors'
                      >
                        <XCircleIcon className='w-4 h-4' />
                      </button>
                    )}
                  </Flex>
                );
              }}
            />
          </div>

          <button
            title='Refresh'
            onClick={refreshHandler}
            type='button'
            className='shrink-0 py-2 px-1 hover:text-info-600 rounded-full transition-colors'
            disabled={isLoadingDebounceSelect}
          >
            {isLoadingDebounceSelect ? (
              <ButtonLoader className='mr-0' />
            ) : (
              <RefreshCwIcon className='h-4 w-4' />
            )}
          </button>
        </Flex>

        {/* Show external TMS ID under the search input when selected */}
        {selectedObj?.externalTMSID && (
          <div className='text-xs text-neutral-500 mt-1'>
            ID: {selectedObj.externalTMSID}
          </div>
        )}

        <ErrorMessage
          errors={errors}
          name={name}
          render={({ message }) => (
            <Typography variant='body-xs' className='text-error-500 mt-1'>
              {message}
            </Typography>
          )}
        />
      </div>
    </Flex>
  );
}
