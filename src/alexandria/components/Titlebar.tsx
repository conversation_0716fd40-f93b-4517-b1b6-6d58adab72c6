import React, { useContext, useState } from 'react';

import {
  CircleDollarSignIcon,
  CircleHelpIcon,
  CircleUserIcon,
  ExternalLinkIcon,
  LaptopIcon,
  Loader2,
  LogOutIcon,
  MoonIcon,
  SunIcon,
  TruckIcon,
} from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore constants is in the parent dir
import DRUMKIT_AUTH_URL from '@constants/DrumkitAuthUrl';

import {
  DrumkitPlatform,
  SidebarStateContext,
} from 'contexts/sidebarStateContext';
import { SidebarViewContext } from 'contexts/sidebarViewContext';
import { useTheme } from 'contexts/themeContext';
import { useAuth } from 'hooks/useAuth';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { AGENTQ_LOGO } from 'icons/AgentQLogo';
import { DRUMKIT_FULL_NAME_LOGO } from 'icons/DrumkitFullNameLogo';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import SidebarView from 'types/enums/SidebarView';
import { cn } from 'utils/shadcn';

import { Button } from './Button';
import { Dialog, DialogContent } from './Dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './Dropdown';
import SearchBar from './SearchBar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './Tooltip';
import { Flex, Grid } from './layout';

interface TitlebarButtonsProps {
  outlookLogout?: () => void;
  hideSearchBar?: boolean;
}

const HELP_CENTER_URL = 'https://help.drumkit.ai/';

export function TitlebarButtons({
  outlookLogout,
  hideSearchBar = false,
}: TitlebarButtonsProps) {
  const { logout } = useAuth();
  const { currentView, setCurrentView } = useContext(SidebarViewContext);
  const {
    currentState: { drumkitPlatform, openExternalUrl },
  } = useContext(SidebarStateContext);
  const { theme, toggleTheme } = useTheme();

  const {
    serviceFeaturesEnabled: { isQuoteViewEnabled, isLoadViewEnabled },
  } = useServiceFeatures();

  const [isHelpCenterOpen, setIsHelpCenterOpen] = useState(false);
  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);

  const logoutHandler = async () => {
    if (outlookLogout) {
      outlookLogout();
    } else {
      openExternalUrl(`${DRUMKIT_AUTH_URL}/logout`);
      await logout();
    }
  };

  const navigateToPortalHandler = async () => {
    openExternalUrl(DRUMKIT_AUTH_URL);
  };

  return (
    <>
      {currentView === SidebarView.Loads && !hideSearchBar && (
        <div className='w-full max-w-[400px] min-w-[120px] shrink'>
          <SearchBar />
        </div>
      )}
      <TooltipProvider>
        <>
          {isLoadViewEnabled && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  buttonNamePosthog={ButtonNamePosthog.SwitchToLoadsView}
                  variant='titlebarActionIcon'
                  size='xs'
                  className={cn(
                    currentView === SidebarView.Loads &&
                      'bg-brand [&>svg]:!stroke-white'
                  )}
                  onClick={() => setCurrentView(SidebarView.Loads)}
                >
                  <TruckIcon className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                className='border-brand-700'
                side='bottom'
                align='center'
              >
                Loads
              </TooltipContent>
            </Tooltip>
          )}

          {isQuoteViewEnabled && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  buttonNamePosthog={ButtonNamePosthog.SwitchToQuoteView}
                  variant='titlebarActionIcon'
                  size='xs'
                  className={cn(
                    currentView === SidebarView.Quote &&
                      'bg-brand [&>svg]:!stroke-white'
                  )}
                  onClick={() => setCurrentView(SidebarView.Quote)}
                >
                  <CircleDollarSignIcon className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent
                className='border-brand-700'
                side='bottom'
                align='center'
              >
                Quotes
              </TooltipContent>
            </Tooltip>
          )}
        </>

        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenu
              open={profileDropdownOpen}
              onOpenChange={setProfileDropdownOpen}
            >
              <DropdownMenuTrigger asChild>
                <Button
                  buttonNamePosthog={ButtonNamePosthog.ProfileDropdown}
                  variant='titlebarActionIcon'
                  size='xs'
                  className={cn(
                    profileDropdownOpen && 'bg-brand [&>svg]:!stroke-white'
                  )}
                >
                  <CircleUserIcon className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent className='w-40 mr-2 rounded-xl'>
                <DropdownMenuItem onClick={toggleTheme} className='rounded-lg'>
                  {theme === 'dark' ? (
                    <SunIcon className={'mr-2 h-4 w-4'} />
                  ) : (
                    <MoonIcon className={'mr-2 h-4 w-4'} />
                  )}
                  <span>{theme === 'dark' ? 'Light Mode' : 'Dark Mode'}</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => navigateToPortalHandler()}
                  className='rounded-lg'
                >
                  <LaptopIcon className='mr-2 h-4 w-4' />
                  <span>Drumkit Portal</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  className='rounded-lg'
                  onClick={() => {
                    // For Chrome sidepanels open in new tab instead of dialog
                    if (drumkitPlatform === DrumkitPlatform.Sidepanel) {
                      openExternalUrl(HELP_CENTER_URL);
                    } else {
                      setIsHelpCenterOpen(true);
                    }
                  }}
                >
                  <CircleHelpIcon className='mr-2 h-4 w-4' />
                  <span>Help Center</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => logoutHandler()}
                  className='rounded-lg'
                >
                  <LogOutIcon className='mr-2 h-4 w-4' />
                  <span>Log Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TooltipTrigger>
          <TooltipContent
            className='border-brand-700'
            side='bottom'
            align='center'
          >
            Profile
          </TooltipContent>
        </Tooltip>

        {/* Help Center dialog, triggerable through profile dropdown */}
        <Dialog open={isHelpCenterOpen} onOpenChange={setIsHelpCenterOpen}>
          <DialogContent className='w-72 h-[800px]'>
            <HelpCenterDialog />
          </DialogContent>
        </Dialog>
      </TooltipProvider>
    </>
  );
}

export default function Titlebar({ children }: { children: React.ReactNode }) {
  const {
    serviceFeaturesEnabled: { isLeanSolutionsCustomer },
  } = useServiceFeatures();

  return (
    <Flex direction='col'>
      <div className='w-full min-h-12 h-12 flex flex-row items-center px-3 bg-brand-50'>
        <img
          src={isLeanSolutionsCustomer ? AGENTQ_LOGO : DRUMKIT_FULL_NAME_LOGO}
          className='h-6'
        />
        <div className='flex-1' />
        <Flex justify='end' gap='md' className='mt-1 pl-3 z- w-full'>
          {/* Render TitlebarButtons in header, assuming it's the first child */}
          {Array.isArray(children) ? children[0] : children}
        </Flex>
      </div>
      <div className='w-full px-3 py-2'>
        {/* Render SearchBar below header, assuming it's the second child */}
        {Array.isArray(children) && children[1]}
      </div>
    </Flex>
  );
}

function HelpCenterDialog() {
  const [helpCenterLoading, setHelpCenterLoading] = useState(true);

  const {
    currentState: { openExternalUrl },
  } = useContext(SidebarStateContext);

  return (
    <Grid gap='lg' className='relative'>
      <Button
        buttonNamePosthog={ButtonNamePosthog.HelpCenterDialog}
        variant='ghost'
        className='h-4 w-4 p-0 absolute top-4 right-10 hover:border-none'
        onClick={() => openExternalUrl(HELP_CENTER_URL)}
      >
        <ExternalLinkIcon className='h-4 w-4 stroke-neutral-600 hover:stroke-neutral-900' />
      </Button>

      <iframe
        src={HELP_CENTER_URL}
        className={cn('w-full h-full -z-1', helpCenterLoading && 'opacity-0')}
        onLoad={() => setHelpCenterLoading(false)}
      />

      {helpCenterLoading && (
        <div className='w-full h-full absolute top-[50%] -translate-y-1/2 flex justify-center items-center z-10'>
          <Loader2 className='h-8 w-8 animate-spin' />
        </div>
      )}
    </Grid>
  );
}
