import React, { ErrorInfo } from 'react';

import { isAxiosError } from 'axios';
import { AlertCircleIcon } from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import Login from '@auth/Login';

import ReloadExtension from 'components/ReloadExtension';
import <PERSON>kitLogo from 'icons/DrumkitLogo';
import { Maybe } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

interface Props {
  children: React.ReactNode;
}

interface State {
  error: Maybe<Error>;
}

// NOTE: ErrorBoundary can only be defined as a class component
// https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary
class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI.
    return { error };
  }

  componentDidCatch(error: Error, _info: ErrorInfo) {
    if (!error) {
      return;
    }

    if (isAxiosError(error) && error.response?.status === 401) {
      return;
    }

    if (error.message === 'Extension context invalidated.') {
      return;
    }

    if (
      error.message ===
      "Cannot read properties of undefined (reading 'sendMessage')"
    ) {
      return;
    }

    captureException(error);
  }

  render() {
    const error = this.state.error;

    if (error && isAxiosError(error) && error.response?.status === 401) {
      return <Login />;
    }

    if (error && error.message === 'Extension context invalidated.') {
      return <ReloadExtension />;
    }

    if (
      error &&
      error.message ===
        "Cannot read properties of undefined (reading 'sendMessage')"
    ) {
      return <ReloadExtension />;
    }

    if (error && error.message.includes('Should have a queue')) {
      return <ReloadExtension />;
    }

    if (error) {
      return <ErrorMessage />;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

// ErrorMessage must use native styles rather than Tailwind since it can happen
// outside of Tailwind's context wrapper
const ErrorMessage = () => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '64px 24px 16px 24px',
    }}
  >
    <div
      style={{
        marginBottom: '16px',
        position: 'relative',
      }}
    >
      <DrumkitLogo
        style={{
          width: '96px',
          height: '96px',
        }}
      />
      <AlertCircleIcon
        style={{
          position: 'absolute',
          bottom: '0',
          right: '-8px',
          stroke: '#ef4444',
          fill: '#fafafa',
        }}
      />
    </div>

    <h3
      style={{
        marginBottom: '8px',
        textAlign: 'center',
        fontWeight: 'bold',
        fontSize: '24px',
        lineHeight: '36px',
        margin: '0 0 24px 0',
      }}
    >
      Drumkit had an unexpected error!
    </h3>

    <p
      style={{
        marginBottom: '8px',
        textAlign: 'center',
        fontSize: '14px',
        lineHeight: '20px',
        margin: '0 0 8px 0',
      }}
    >
      Please refresh the page to try again.
    </p>
    <p
      style={{
        marginBottom: '16px',
        textAlign: 'center',
        fontSize: '14px',
        lineHeight: '20px',
        margin: '0 0 16px 0',
      }}
    >
      {`If the problem continues, reach out to our support team `}
      <a
        href='mailto:<EMAIL>'
        style={{
          color: '#3b82f6',
          textDecoration: 'underline',
        }}
      >
        here
      </a>
      {` or email us <NAME_EMAIL>.`}
    </p>
    <p
      style={{
        marginBottom: '24px',
        textAlign: 'center',
        fontSize: '14px',
        lineHeight: '20px',
        margin: '0 0 24px 0',
      }}
    >
      We apologize for the inconvenience.
    </p>

    <button
      onClick={() => window.location.reload()}
      style={{
        height: '36px',
        fontSize: '14px',
        paddingLeft: '48px',
        paddingRight: '48px',
        backgroundColor: '#f98600',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        cursor: 'pointer',
        fontWeight: '500',
      }}
    >
      Refresh Page
    </button>
  </div>
);
