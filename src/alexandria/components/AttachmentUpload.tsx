import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore react-dropzone is in the parent dir
import { FileRejection, FileWithPath, useDropzone } from 'react-dropzone';

import { XIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';

import { cn } from '../utils/shadcn';

export interface ProcessedAttachment {
  data: string;
  fileName: string;
  mimeType: string;
  size: number;
  originalFile: File;
}

interface AttachmentUploaderProps {
  onFilesChange: (files: ProcessedAttachment[]) => void;
  maxFileSize?: number;
}

const formatBytes = (bytes: number) => {
  if (bytes < 1024) return `${bytes} bytes`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
  if (bytes < 1024 * 1024 * 1024) {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  }

  return `${(bytes / 1024 / 1024 / 1024).toFixed(2)} GB`;
};

// Helper function to convert File to base64
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const base64String = reader.result as string;
      // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
      const base64Content = base64String.split(',')[1];
      resolve(base64Content);
    };
    reader.onerror = (error) => reject(error);
  });
};

// Process a file to create a ProcessedAttachment
const processFile = async (file: File): Promise<ProcessedAttachment> => {
  const base64Data = await fileToBase64(file);
  return {
    data: base64Data,
    fileName: file.name,
    mimeType: file.type || 'application/octet-stream',
    size: file.size,
    originalFile: file,
  };
};

const AttachmentUpload: React.FC<AttachmentUploaderProps> = ({
  onFilesChange,
  maxFileSize = 6, // 6 MB is Lambda max
}) => {
  const [attachedFiles, setAttachedFiles] = useState<ProcessedAttachment[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleDrop = useCallback(
    async (acceptedFiles: FileWithPath[]) => {
      if (acceptedFiles.length === 0) return;

      setIsProcessing(true);
      try {
        const processedFiles = await Promise.all(
          acceptedFiles.map((file) => processFile(file))
        );

        const newFiles = [...attachedFiles, ...processedFiles];
        setAttachedFiles(newFiles);
        onFilesChange(newFiles);
      } catch (error) {
        console.error('Error processing files:', error);
      } finally {
        setIsProcessing(false);
      }
    },
    [attachedFiles, onFilesChange]
  );

  const handleRemoveFile = (fileToRemove: ProcessedAttachment) => {
    const newFiles = attachedFiles.filter((file) => file !== fileToRemove);
    setAttachedFiles(newFiles);
    onFilesChange(newFiles);
  };

  const { getRootProps, getInputProps, isDragActive, fileRejections } =
    useDropzone({
      onDrop: handleDrop,
      validator: (file: File) => {
        if (file.size > maxFileSize * 1024 * 1024) {
          return {
            code: 'file-too-large',
            message: `File exceeds maximum size of ${maxFileSize}MB`,
          };
        }

        const totalSizeMB =
          (attachedFiles.reduce((sum, f) => sum + f.size, 0) + file.size) /
          (1024 * 1024);
        if (totalSizeMB > maxFileSize) {
          return {
            code: 'total-file-size-too-large',
            message: `Total file size would exceed maximum of ${maxFileSize}MB. Please remove some files.`,
          };
        }

        return null;
      },
    });

  const uploadMessage = useMemo(() => {
    if (isDragActive) return 'Drop files here ...';
    if (isProcessing) return 'Processing files...';
    if (attachedFiles.length > 0) {
      return 'Select more files or drag and drop to upload';
    }

    return 'Select files or drag and drop to upload';
  }, [isDragActive, isProcessing, attachedFiles.length]);

  return (
    <Flex direction='col' gap='sm'>
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded py-3 px-5 text-center cursor-pointer',
          isDragActive && 'border-brand-main bg-brand-50',
          isProcessing && 'border-neutral-400 bg-neutral-100',
          !isDragActive &&
            !isProcessing &&
            'border-neutral-400 hover:border-brand-200 hover:bg-brand-50'
        )}
        aria-label='File drop zone'
        tabIndex={0}
      >
        <input {...getInputProps()} aria-hidden='true' />
        <Flex direction='col' gap='xs' align='center'>
          <Typography variant='body-sm' className='text-center'>
            {uploadMessage}
          </Typography>
          <Typography variant='body-xs' className='text-neutral-600'>
            Max file size: {maxFileSize}MB
          </Typography>
        </Flex>
      </div>
      {fileRejections.length > 0 && (
        <div>
          {fileRejections.map((rejection: FileRejection, index: number) => (
            <Typography key={index} variant='body-xs' textColor='error'>
              {rejection.file.name}: {rejection.errors[0].message}
            </Typography>
          ))}
        </div>
      )}
      {/* List of attached files */}
      {attachedFiles.length > 0 && (
        <ul className='flex flex-col gap-1.5 mt-2 w-full'>
          {attachedFiles.map((file: ProcessedAttachment, index: number) => (
            <li
              key={index}
              className='flex items-center justify-between bg-neutral-100 px-2.5 py-1.5 rounded border border-neutral-400'
            >
              <Flex direction='col' gap='xs'>
                <Typography variant='body-xs'>{file.fileName}</Typography>
                <Typography className='text-[10px] text-neutral-600'>
                  {formatBytes(file.size)}
                </Typography>
              </Flex>
              <Button
                buttonNamePosthog={ButtonNamePosthog.RemoveAttachment}
                variant='destructive'
                size='xs'
                type='button'
                onClick={() => handleRemoveFile(file)}
                aria-label={`Remove ${file.fileName}`}
                className='border-transparent hover:border-error-500'
              >
                <XIcon className='h-4 w-4' />
              </Button>
            </li>
          ))}
        </ul>
      )}
    </Flex>
  );
};

export default AttachmentUpload;
