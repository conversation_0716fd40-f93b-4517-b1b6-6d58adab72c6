import { useState } from 'react';
import { Matcher } from 'react-day-picker';
import { ControllerRenderProps } from 'react-hook-form';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { CalendarIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { FieldAttributes } from 'types/LoadAttributes';
import { MaybeUndef } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import { Calendar } from './Calendar';
import { Popover, PopoverContent, PopoverTrigger } from './Popover';

dayjs.extend(utc);
dayjs.extend(timezone);

export interface DatePickerProps {
  field: ControllerRenderProps<any>;
  thisFieldAttr?: FieldAttributes;
  highlightDirtyField?: boolean;
  highlightError?: boolean;
  logProperties?: Record<string, any>;
  className?: string;
  calendarClassName?: string;
  disabled?: Matcher | Matcher[];
  timezone?: MaybeUndef<string>;
}

function DatePicker({
  field,
  highlightDirtyField,
  highlightError,
  thisFieldAttr,
  logProperties,
  className,
  calendarClassName,
  disabled,
  timezone,
}: DatePickerProps) {
  const [isCalendarPopoverOpen, setIsCalendarPopoverOpen] = useState(false);

  const textPlaceholder =
    thisFieldAttr?.isReadOnly && !field.value ? '' : 'Pick a date';

  const fieldValueToDate = (value: any): Date | null => {
    if (!value) return null;
    return dayjs.isDayjs(value) ? value.toDate() : new Date(value as string);
  };

  const getTimezoneAdjustedDate = (
    date: Date,
    tz: MaybeUndef<string>
  ): Date => {
    if (!tz) return date;

    const dateInTz = dayjs(date).tz(tz);
    return dayjs()
      .tz(tz)
      .year(dateInTz.year())
      .month(dateInTz.month())
      .date(dateInTz.date())
      .toDate();
  };

  const formatDisplayDate = (
    date: Date | null,
    tz: MaybeUndef<string>
  ): string => {
    if (!date) return textPlaceholder;

    if (tz) {
      return dayjs(date).tz(tz).format('MM/DD/YY');
    }
    return dayjs(date).format('MM/DD/YY');
  };

  const currentDate = fieldValueToDate(field.value);

  return (
    <Popover
      open={isCalendarPopoverOpen}
      onOpenChange={(open) => setIsCalendarPopoverOpen(open)}
    >
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          size='sm'
          className={cn(
            'w-full justify-start text-left font-normal rounded-[4px] border-neutral-input-border bg-neutral-input-bg text-neutral-input-text py-1 h-8',
            'disabled:cursor-not-allowed disabled:bg-neutral-input-disabled-bg disabled:border-neutral-input-disabled-border disabled:opacity-100',
            !field.value && 'text-neutral-400',
            highlightDirtyField && 'bg-warning-50',
            highlightError && 'bg-error-50',
            className
          )}
          buttonNamePosthog={ButtonNamePosthog.ToggleDatePicker}
          logProperties={logProperties}
          disabled={thisFieldAttr?.isReadOnly}
        >
          <CalendarIcon
            className={cn(
              'mr-2 h-4 w-4 shrink-0 stroke-neutral-500',
              calendarClassName
            )}
          />
          <span className='text-[13px] text-neutral-500'>
            {formatDisplayDate(currentDate, timezone)}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0'>
        <Calendar
          mode='single'
          selected={
            currentDate
              ? getTimezoneAdjustedDate(currentDate, timezone)
              : undefined
          }
          onSelect={(newValue: Date | undefined) => {
            if (typeof newValue === 'undefined') {
              field.onChange(null);
              return;
            }
            let dateToSet = newValue;

            if (timezone) {
              const year = newValue.getFullYear();
              const month = newValue.getMonth() + 1; // JS months are 0-indexed
              const day = newValue.getDate();

              if (currentDate) {
                // Preserve existing time from it's timezone
                const existingDateTime = dayjs(currentDate).tz(timezone);
                const hour = existingDateTime.hour();
                const minute = existingDateTime.minute();

                dateToSet = dayjs
                  .tz(`${year}-${month}-${day}`, 'YYYY-M-D', timezone)
                  .hour(hour)
                  .minute(minute)
                  .toDate();
              } else {
                // No existing time, set to start of day in target timezone
                dateToSet = dayjs
                  .tz(`${year}-${month}-${day}`, 'YYYY-M-D', timezone)
                  .startOf('day')
                  .toDate();
              }
            } else {
              // Uses browser's local timezone when no timezone is provided
              if (currentDate && newValue) {
                newValue.setHours(currentDate.getHours());
                newValue.setMinutes(currentDate.getMinutes());
              }
              dateToSet = newValue;
            }

            field.onChange(dateToSet);
            setIsCalendarPopoverOpen(false);
          }}
          disabled={disabled}
        />
      </PopoverContent>
    </Popover>
  );
}

export { DatePicker };
