import { useState } from 'react';

import {
  ChevronDownIcon,
  ChevronUpIcon,
  Loader2,
  MailCheckIcon,
  MailPlusIcon,
  ShieldCheckIcon,
} from 'lucide-react';

import useFetchCarrierVerificationRequest from 'hooks/useFetchCarrierVerification';
import { useToast } from 'hooks/useToaster';
import FreightValidateLogo from 'icons/FreightValidateLogo';
import Highway<PERSON>ogo from 'icons/HighwayLogo';
import MCPLogo from 'icons/MCPLogo';
import TruckstopLogo from 'icons/TruckstopLogo';
import { inviteCarrier } from 'lib/api/inviteCarrier';
import { CarrierVerification } from 'types/CarrierVerification';
import { IntegrationCore } from 'types/Integration';
import { Maybe } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { CarrierVerification as CarrierVerificationEnum } from 'types/enums/Integrations';

import { Badge } from './Badge';
import { Button } from './Button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from './Card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from './Collapsible';
import { Skeleton } from './Skeleton';
import { Flex } from './layout';
import { Typography } from './typography';

interface CarrierVerificationCardProps {
  fromEmail: string;
  enabledIntegration: IntegrationCore;
}

export default function CarrierVerificationCard({
  fromEmail,
  enabledIntegration,
}: CarrierVerificationCardProps) {
  const { carrier, isLoading } = useFetchCarrierVerificationRequest(fromEmail);
  const { toast } = useToast();
  const [inviteLoading, setInviteLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  function getCarrierColors(carrier: Maybe<CarrierVerification>): {
    border: string;
    badge: string;
  } {
    if (!carrier)
      return {
        border: 'border-neutral-300',
        badge: 'border-neutral-500 text-neutral-700 bg-neutral-50',
      };

    switch (carrier.integration_name) {
      case CarrierVerificationEnum.FreightValidate: {
        const rating = carrier.risk_rating.toLowerCase();
        switch (true) {
          case rating.includes('none'):
            return {
              border: 'border-error-500',
              badge: 'border-error-500 text-error-700 bg-error-50',
            };
          case rating.includes('warning'):
            return {
              border: 'border-warning-500',
              badge: 'border-warning-500 text-warning-700 bg-warning-50',
            };
          default:
            return {
              border: 'border-success-500',
              badge: 'border-success-500 text-success-700 bg-success-50',
            };
        }
      }
      case CarrierVerificationEnum.Highway: {
        const highwayRating = carrier.risk_rating.toLowerCase();
        switch (true) {
          case highwayRating.includes('not authorized'):
            return {
              border: 'border-error-500',
              badge: 'border-error-500 text-error-700 bg-error-50',
            };
          case highwayRating.includes('review required'):
            return {
              border: 'border-warning-500',
              badge: 'border-warning-500 text-warning-700 bg-warning-50',
            };
          default:
            return {
              border: 'border-warning-400',
              badge: 'border-warning-400 text-warning-700 bg-warning-50',
            };
        }
      }
      case CarrierVerificationEnum.MyCarrierPortal: {
        const mcpRating = carrier.risk_rating.toLowerCase();
        switch (true) {
          case mcpRating.includes('unacceptablefail'):
            return {
              border: 'border-error-500',
              badge: 'border-error-500 text-error-700 bg-error-50',
            };
          case mcpRating.includes('unacceptablereview'):
            return {
              border: 'border-warning-500',
              badge: 'border-warning-500 text-warning-700 bg-warning-50',
            };
          default:
            return {
              border: 'border-accent-500',
              badge: 'border-accent-500 text-accent-700 bg-accent-50',
            };
        }
      }
      case CarrierVerificationEnum.Truckstop: {
        const truckstopRating = carrier.risk_rating.toLowerCase();
        switch (true) {
          case truckstopRating.includes('unauthorized'):
            return {
              border: 'border-error-500',
              badge: 'border-error-500 text-error-700 bg-error-50',
            };
          case truckstopRating.includes('unacceptablereview'):
            return {
              border: 'border-warning-500',
              badge: 'border-warning-500 text-warning-700 bg-warning-50',
            };
          default:
            return {
              border: 'border-error-600',
              badge: 'border-error-600 text-error-700 bg-error-50',
            };
        }
      }
      default:
        return {
          border: 'border-neutral-300',
          badge: 'border-neutral-500 text-neutral-700 bg-neutral-50',
        };
    }
  }

  function reviewText(carrier: Maybe<CarrierVerification>): string {
    if (!carrier) return '';
    switch (carrier.integration_name) {
      case CarrierVerificationEnum.Highway:
      case CarrierVerificationEnum.FreightValidate:
      case CarrierVerificationEnum.Truckstop:
        return carrier.risk_rating;

      case CarrierVerificationEnum.MyCarrierPortal: {
        const mcpReviewRating = carrier.risk_rating.toLowerCase();
        switch (true) {
          case mcpReviewRating.includes('unacceptablefail'):
            return 'Unacceptable Fail';
          case mcpReviewRating.includes('unacceptablereview'):
            return 'Review Required';
          default:
            return '';
        }
      }
    }
    return '';
  }

  function cardActions(carrier: Maybe<CarrierVerification>) {
    if (!carrier) return { viewProfile: null, invite: null };

    const isUnauthorized = (() => {
      const rating = carrier.risk_rating.toLowerCase();
      switch (carrier.integration_name) {
        case CarrierVerificationEnum.Highway:
          return rating.includes('not authorized');
        case CarrierVerificationEnum.MyCarrierPortal:
          return rating.includes('unacceptablefail');
        case CarrierVerificationEnum.FreightValidate:
          return rating.includes('warning');
        case CarrierVerificationEnum.Truckstop:
          return rating.includes('unauthorized');
        default:
          return false;
      }
    })();

    const profileUrl = (() => {
      switch (carrier.integration_name) {
        case CarrierVerificationEnum.MyCarrierPortal:
          return `https://mycarrierpackets.com/CarrierInformation/DOTNumber/${carrier.dot_number}/DocketNumber/${carrier.docket_number}`;
        case CarrierVerificationEnum.Highway:
          return `https://highway.com/carriers/${carrier.dot_number}`;
        case CarrierVerificationEnum.FreightValidate:
          return `https://freightvalidate.com/carrier/${carrier.dot_number}`;
        case CarrierVerificationEnum.Truckstop:
          return `https://truckstop.com/carrier/${carrier.dot_number}`;
        default:
          return '';
      }
    })();

    const viewProfile = profileUrl ? (
      <a
        key='View Profile'
        className='group flex items-center justify-center gap-2 h-11 px-6 w-40 rounded-[4px] text-sm font-medium border border-neutral-300 bg-neutral-50 text-neutral-900 hover:bg-neutral-100 hover:border-neutral-400 transition-colors'
        href={profileUrl}
        target='_blank'
        rel='noopener noreferrer'
      >
        {(() => {
          switch (carrier.integration_name) {
            case CarrierVerificationEnum.MyCarrierPortal:
              return (
                <MCPLogo
                  fill='#4786FE'
                  className='group-hover:fill-neutral-500 transition-colors'
                  width={20}
                  height={20}
                />
              );
            case CarrierVerificationEnum.Highway:
              return (
                <HighwayLogo
                  fill='#F8C617'
                  className='group-hover:fill-neutral-500 transition-colors'
                  width={20}
                  height={20}
                />
              );
            case CarrierVerificationEnum.FreightValidate:
              return (
                <FreightValidateLogo
                  fill='#4786FE'
                  className='group-hover:fill-neutral-500 transition-colors'
                  width={20}
                  height={20}
                />
              );
            case CarrierVerificationEnum.Truckstop:
              return (
                <TruckstopLogo
                  fill='#D90119'
                  className='group-hover:fill-neutral-500 transition-colors'
                  width={20}
                  height={20}
                />
              );
            default:
              return null;
          }
        })()}
        View Profile
      </a>
    ) : null;

    const invite =
      !isUnauthorized &&
      carrier?.integration_name == CarrierVerificationEnum.MyCarrierPortal &&
      !carrier?.completed_packet ? (
        <Button
          key='Invite'
          onClick={onInvite}
          disabled={inviteLoading}
          variant='outline'
          className='w-40 text-sm'
          buttonNamePosthog={ButtonNamePosthog.CarrierVerificationInvite}
        >
          {inviteLoading ? (
            <Loader2 className='h-4 w-4 animate-spin mr-2' />
          ) : carrier?.completed_packet ? (
            <>
              <MailCheckIcon className='h-4 w-4 mr-2' />
              In Network
            </>
          ) : (
            <>
              <MailPlusIcon className='h-4 w-4 mr-2' />
              Invite
            </>
          )}
        </Button>
      ) : null;

    return { viewProfile, invite };
  }

  async function onInvite() {
    setInviteLoading(true);

    const res = await inviteCarrier({
      dot_number: carrier?.dot_number.toString() || '',
      email: fromEmail,
    });

    if (res.isOk()) {
      toast({ description: 'Carrier invited.', variant: 'success' });
    } else {
      toast({ description: res.error.message, variant: 'destructive' });
    }

    setInviteLoading(false);
  }

  const carrierNotFound = !carrier && !isLoading;

  return (
    <div className='px-4 w-full'>
      <Card
        className={`w-full mt-4 relative rounded-[4px] bg-neutral-50/50 border-neutral-200 ${getCarrierColors(carrier).border}`}
      >
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <Button
              variant='ghost'
              className='w-full justify-between p-3 h-auto hover:bg-neutral-100/50 focus:outline-none hover:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 hover:border-transparent focus:border-transparent'
              buttonNamePosthog={ButtonNamePosthog.CarrierVerificationInvite}
            >
              <Flex
                direction='row'
                align='center'
                gap='sm'
                className='text-left flex-1 min-w-0'
              >
                {(() => {
                  // Determine which integration to show logo for
                  const integrationToShow =
                    carrier?.integration_name || enabledIntegration?.name;

                  switch (integrationToShow) {
                    case CarrierVerificationEnum.MyCarrierPortal:
                      return <MCPLogo fill='#4786FE' width={20} height={20} />;
                    case CarrierVerificationEnum.Highway:
                      return (
                        <HighwayLogo fill='#F8C617' width={20} height={20} />
                      );
                    case CarrierVerificationEnum.FreightValidate:
                      return (
                        <FreightValidateLogo
                          fill='#4786FE'
                          width={20}
                          height={20}
                        />
                      );
                    case CarrierVerificationEnum.Truckstop:
                      return (
                        <TruckstopLogo fill='#D90119' width={20} height={20} />
                      );
                    default:
                      // Default logo when no integration is specified
                      return (
                        <ShieldCheckIcon
                          className='text-neutral-500'
                          width={20}
                          height={20}
                        />
                      );
                  }
                })()}
                <Flex direction='col' className='flex-1 min-w-0'>
                  {carrier && (
                    <Typography
                      variant='body-sm'
                      weight='medium'
                      className='truncate'
                    >
                      {carrier.name}
                    </Typography>
                  )}
                  {carrierNotFound && (
                    <Typography variant='body-sm' textColor='muted'>
                      No carrier found
                    </Typography>
                  )}
                  {isLoading && (
                    <Typography variant='body-sm' textColor='muted'>
                      Loading...
                    </Typography>
                  )}
                  {carrier && (
                    <Flex
                      direction='row'
                      align='center'
                      gap='sm'
                      className='mt-2 flex-wrap'
                    >
                      {carrier.is_authorized_user ? (
                        <Badge
                          variant='outline'
                          className='border-success-500 text-success-700 bg-success-50 text-xs px-2 py-0.5'
                        >
                          ✓ Authorized User
                        </Badge>
                      ) : (
                        <Badge
                          variant='outline'
                          className='border-warning-500 text-warning-700 bg-warning-50 text-xs px-2 py-0.5'
                        >
                          ⏳ Pending User Auth
                        </Badge>
                      )}
                      {(() => {
                        const reviewStatus = reviewText(carrier);
                        if (!reviewStatus) return null;

                        return (
                          <Badge
                            variant='outline'
                            className={`text-xs px-2 py-0.5 ${getCarrierColors(carrier).badge}`}
                          >
                            {reviewStatus}
                          </Badge>
                        );
                      })()}
                    </Flex>
                  )}
                </Flex>
              </Flex>
              {isOpen ? (
                <ChevronUpIcon className='h-4 w-4 flex-shrink-0' />
              ) : (
                <ChevronDownIcon className='h-4 w-4 flex-shrink-0' />
              )}
            </Button>
          </CollapsibleTrigger>

          <CollapsibleContent>
            {carrierNotFound ? (
              <CardContent className='pt-0 pb-6'>
                <Typography variant='body-sm' textColor='muted' align='center'>
                  No carrier information available for this email address.
                </Typography>
              </CardContent>
            ) : isLoading ? (
              <CardContent className='space-y-2 pt-0 pb-6'>
                <Skeleton className='h-4 w-[250px]' />
                <Skeleton className='h-4 w-[200px]' />
              </CardContent>
            ) : (
              <>
                <CardHeader className='space-y-1 pb-2 pt-0'></CardHeader>
                <CardContent className='space-y-2 pt-2 pb-4'>
                  {carrier?.dba && (
                    <div>
                      <Typography
                        variant='body-xs'
                        textColor='muted'
                        className='uppercase tracking-wide'
                      >
                        DBA
                      </Typography>
                      <Typography variant='body-sm' className='mt-1'>
                        {carrier.dba}
                      </Typography>
                    </div>
                  )}
                  <Flex direction='row' gap='lg' wrap='wrap'>
                    <div className='flex-1 min-w-0'>
                      <Typography
                        variant='body-xs'
                        textColor='muted'
                        className='uppercase tracking-wide'
                      >
                        DOT Number
                      </Typography>
                      <Typography variant='body-sm' className='mt-1'>
                        {carrier?.dot_number}
                      </Typography>
                    </div>
                    <div className='flex-1 min-w-0'>
                      <Typography
                        variant='body-xs'
                        textColor='muted'
                        className='uppercase tracking-wide'
                      >
                        MC Number
                      </Typography>
                      <Typography variant='body-sm' className='mt-1'>
                        {carrier?.docket_number}
                      </Typography>
                    </div>
                  </Flex>
                </CardContent>
                {!carrierNotFound && !isLoading && (
                  <CardFooter className='flex justify-center gap-2 p-4 pt-0 mt-0'>
                    {Object.values(cardActions(carrier)).filter(Boolean)}
                  </CardFooter>
                )}
              </>
            )}
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}
