import { JSX, useCallback, useEffect, useMemo } from 'react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import Login from '@auth/Login';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore constants is in the parent dir
import APP_VERSION from '@constants/AppVersion';

import SidebarLoader from 'components/loading/SidebarLoader';
import { useAuth } from 'hooks/useAuth';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { toast } from 'hooks/useToaster';
import fetchServiceFeatures, {
  convertServiceDataToService,
} from 'lib/api/fetchServiceFeatures';

type RequireAuthProps = {
  children: JSX.Element;
  admin?: boolean;
};

type IdentifiedUserProps = {
  [key: string]: any;
};

function RequireAuth({ children }: RequireAuthProps) {
  const { loading, user } = useAuth();
  const {
    setService,
    setIsLoading: setIsServiceFeaturesLoading,
    isLoading: isServiceFeaturesLoading,
  } = useServiceFeatures();
  const posthog = usePostHog();

  const handleFetchServiceFeatures = useCallback(
    async (serviceID: number) => {
      setIsServiceFeaturesLoading(true);

      const serviceData = await fetchServiceFeatures(serviceID);
      if (serviceData.isOk()) {
        setService(convertServiceDataToService(serviceData.value));
      } else {
        toast({
          description: 'Error while fetching service features.',
          variant: 'destructive',
        });
      }

      setIsServiceFeaturesLoading(false);
    },
    [setService, setIsServiceFeaturesLoading]
  );

  useEffect(() => {
    const userServiceID = user?.service_id;

    if (!loading && userServiceID) {
      handleFetchServiceFeatures(userServiceID);
    }
  }, [loading, user, handleFetchServiceFeatures]);

  useMemo(() => {
    const personProperties: IdentifiedUserProps = {
      email: user?.email,
    };

    if (APP_VERSION) {
      personProperties.$drumkit_version = APP_VERSION;
    }

    posthog?.identify(user?.email, personProperties);
  }, [user, posthog]);

  if (loading || (user && isServiceFeaturesLoading)) {
    return <SidebarLoader />;
  }

  return user ? children : <Login />;
}

export default RequireAuth;
