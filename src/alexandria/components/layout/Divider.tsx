import React from 'react';

import { type VariantProps, cva } from 'class-variance-authority';

import { cn } from 'utils/shadcn';

const dividerVariants = cva('', {
  variants: {
    orientation: {
      horizontal: 'w-full',
      vertical: 'h-full',
    },
    variant: {
      default: 'bg-neutral-200',
      muted: 'bg-neutral-100',
      strong: 'bg-neutral-300',
      dashed: 'border-dashed border-neutral-400',
      dotted: 'border-dotted border-neutral-400',
    },
    size: {
      sm: 'h-px',
      md: 'h-0.5',
      lg: 'h-1',
    },
    spacing: {
      none: '',
      sm: 'my-2',
      md: 'my-4',
      lg: 'my-6',
      xl: 'my-8',
    },
  },
  defaultVariants: {
    orientation: 'horizontal',
    variant: 'default',
    size: 'sm',
    spacing: 'none',
  },
});

interface DividerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof dividerVariants> {
  children?: React.ReactNode;
  text?: React.ReactNode;
}

const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  (
    {
      className,
      orientation,
      variant,
      size,
      spacing,
      children,
      text,
      ...props
    },
    ref
  ) => {
    if (text || children) {
      return (
        <div
          ref={ref}
          className={cn('flex items-center', spacing, className)}
          {...props}
        >
          <div
            className={cn(
              'flex-1',
              dividerVariants({ orientation: 'horizontal', variant, size })
            )}
          />
          <div className='px-3 text-sm text-neutral-500'>
            {text || children}
          </div>
          <div
            className={cn(
              'flex-1',
              dividerVariants({ orientation: 'horizontal', variant, size })
            )}
          />
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={cn(
          dividerVariants({ orientation, variant, size, spacing }),
          className
        )}
        {...props}
      />
    );
  }
);

Divider.displayName = 'Divider';

export { Divider, dividerVariants };
export type { DividerProps };
