import React from 'react';

import { type VariantProps, cva } from 'class-variance-authority';

import { cn } from 'utils/shadcn';

const gridVariants = cva('grid', {
  variants: {
    cols: {
      '1': 'grid-cols-1',
      '2': 'grid-cols-2',
      '3': 'grid-cols-3',
      '4': 'grid-cols-4',
      '5': 'grid-cols-5',
      '6': 'grid-cols-6',
      '12': 'grid-cols-12',
      auto: 'grid-cols-auto',
      none: 'grid-cols-none',
    },
    gap: {
      none: '',
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-3',
      lg: 'gap-4',
      xl: 'gap-6',
      '2xl': 'gap-8',
    },
    gapX: {
      none: '',
      xs: 'gap-x-1',
      sm: 'gap-x-2',
      md: 'gap-x-3',
      lg: 'gap-x-4',
      xl: 'gap-x-6',
      '2xl': 'gap-x-8',
    },
    gapY: {
      none: '',
      xs: 'gap-y-1',
      sm: 'gap-y-2',
      md: 'gap-y-3',
      lg: 'gap-y-4',
      xl: 'gap-y-6',
      '2xl': 'gap-y-8',
    },
    align: {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
      baseline: 'items-baseline',
    },
    justify: {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    },
    width: {
      auto: 'w-auto',
      full: 'w-full',
      fit: 'w-fit',
      max: 'max-w-full',
    },
  },
  defaultVariants: {
    cols: '1',
    gap: 'none',
    gapX: 'none',
    gapY: 'none',
    align: 'start',
    justify: 'start',
    width: 'auto',
  },
});

interface GridProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof gridVariants> {
  children: React.ReactNode;
}

const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  (
    {
      className,
      cols,
      gap,
      gapX,
      gapY,
      align,
      justify,
      width,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          gridVariants({ cols, gap, gapX, gapY, align, justify, width }),
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Grid.displayName = 'Grid';

export { Grid, gridVariants };
export type { GridProps };
