import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';

export default function RequiredHint() {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className='text-error-400 ml-0.5'>*</span>
        </TooltipTrigger>
        <TooltipContent
          side='top'
          className='rounded-md bg-neutral-800 text-neutral-50 text-xs px-2 py-1 shadow-md opacity-90'
        >
          Required
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
