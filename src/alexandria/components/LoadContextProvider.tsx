import React from 'react';

import { LoadContext, LoadContextType } from 'contexts/load';
import { initLoadAttributes } from 'types/Email';

type LoadContextProviderProps = React.PropsWithChildren<
  LoadContextType & {
    disabled?: boolean;
  }
>;

export default function LoadContextProvider({
  children,
  disabled = false,
  ...props
}: LoadContextProviderProps) {
  // When disabled, provide default values that show all fields. This is to support `UnifiedAljexLoadForm` which is disentangled from universal `LoadInfoTab`. Other TMSes to be refactored soon.
  const contextValue: LoadContextType = disabled
    ? {
        fieldAttributes: [],
        loadAttrsObj: initLoadAttributes,
        invalidateLoad: () => Promise.resolve(),
      }
    : props;

  return (
    <LoadContext.Provider value={contextValue}>{children}</LoadContext.Provider>
  );
}
