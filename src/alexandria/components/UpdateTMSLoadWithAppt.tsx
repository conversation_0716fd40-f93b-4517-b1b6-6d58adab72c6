import React, { useCallback, useContext, useMemo, useState } from 'react';
import { SubmitError<PERSON>and<PERSON>, useForm } from 'react-hook-form';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { TMSContext } from 'contexts/tms';
import { useToast } from 'hooks/useToaster';
import { updateLoadAppointment } from 'lib/api/updateLoadAppointment';
import { StopTypes } from 'types/Appointment';
import { Load, NormalizedLoad } from 'types/Load';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { titleCase } from 'utils/formatStrings';
import { cn } from 'utils/shadcn';

import { Button } from './Button';
import DateTimeInput from './input/DateTimeInput';
import { Divider } from './layout/Divider';
import { Flex } from './layout/Flex';
import { Typography } from './typography/Typography';

// Ensure dayjs has the needed plugins
dayjs.extend(utc);
dayjs.extend(timezone);

interface UpdateTMSLoadWithApptProps {
  load: Load | NormalizedLoad;
  stopType: StopTypes;
  appointmentStartTime: Date;
  appointmentEndTime: Date;
  timezone?: string;
  freightTrackingId: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

interface FormData {
  appointmentStartTime: Date;
  appointmentEndTime: Date;
}

interface DateConversionResult {
  success: boolean;
  date?: Date;
  error?: string;
}

const DATE_FORMAT_OPTIONS: Intl.DateTimeFormatOptions = {
  timeZone: undefined, // Will be set dynamically
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false,
} as const;

const UpdateTMSLoadWithAppt: React.FC<UpdateTMSLoadWithApptProps> = ({
  load,
  stopType,
  appointmentStartTime,
  appointmentEndTime,
  timezone,
  freightTrackingId,
  onSuccess,
  onError,
  className = '',
}) => {
  const { tmsName } = useContext(TMSContext);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  /**
   * Converts UTC date to local date with UTC components for display consistency
   * This ensures dayjs(localDate).format('HH:mm') shows the same time as dayjs.utc(utcDate).format('HH:mm')
   */
  const convertUTCToLocalWithUTCComponents = (
    date: Date
  ): DateConversionResult => {
    try {
      const utcYear = date.getUTCFullYear();
      const utcMonth = date.getUTCMonth();
      const utcDate = date.getUTCDate();
      const utcHours = date.getUTCHours();
      const utcMinutes = date.getUTCMinutes();
      const utcSeconds = date.getUTCSeconds();

      const localDateWithUTCComponents = new Date(
        utcYear,
        utcMonth,
        utcDate,
        utcHours,
        utcMinutes,
        utcSeconds
      );

      if (isNaN(localDateWithUTCComponents.getTime())) {
        return {
          success: false,
          error: 'Invalid date created from UTC components',
        };
      }

      return { success: true, date: localDateWithUTCComponents };
    } catch (error) {
      return {
        success: false,
        error: `Failed to convert UTC to local: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  };

  /**
   * Converts date to warehouse timezone for display
   */
  const convertToWarehouseTimezone = (
    date: Date,
    targetTimezone: string
  ): DateConversionResult => {
    try {
      const displayString = date.toLocaleString('en-US', {
        ...DATE_FORMAT_OPTIONS,
        timeZone: targetTimezone,
      });

      const [datePart, timePart] = displayString.split(', ');
      if (!datePart || !timePart) {
        return {
          success: false,
          error: 'Invalid date format from toLocaleString',
        };
      }

      const [month, day, year] = datePart.split('/');
      const [hours, minutes, seconds] = timePart.split(':');

      if (!month || !day || !year || !hours || !minutes || !seconds) {
        return {
          success: false,
          error: 'Invalid date components from timezone conversion',
        };
      }

      const newDate = new Date(
        parseInt(year, 10),
        parseInt(month, 10) - 1,
        parseInt(day, 10),
        parseInt(hours, 10),
        parseInt(minutes, 10),
        parseInt(seconds, 10)
      );

      if (isNaN(newDate.getTime())) {
        return {
          success: false,
          error: 'Invalid date created from timezone conversion',
        };
      }

      return { success: true, date: newDate };
    } catch (error) {
      return {
        success: false,
        error: `Failed to convert to warehouse timezone: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  };

  /**
   * Converts a date to display format matching how it was shown in open slots
   */
  const convertToDisplayFormat = useCallback(
    (date: Date): Date => {
      if (!timezone) {
        // For platforms without timezone (E2open, Manhattan, OneNetwork, Retalix)
        const result = convertUTCToLocalWithUTCComponents(date);
        if (result.success && result.date) {
          return result.date;
        }
        return date; // Fallback to original date
      }

      // For platforms with timezone (Yardview, Opendock)
      const result = convertToWarehouseTimezone(date, timezone);
      if (result.success && result.date) {
        return result.date;
      }
      return date; // Fallback to original date
    },
    [timezone]
  );

  // Memoize date conversions to avoid unnecessary recalculations
  const convertedStartTime = useMemo(
    () => convertToDisplayFormat(appointmentStartTime),
    [appointmentStartTime, convertToDisplayFormat]
  );

  const convertedEndTime = useMemo(
    () => convertToDisplayFormat(appointmentEndTime),
    [appointmentEndTime, convertToDisplayFormat]
  );

  const formMethods = useForm<FormData>({
    defaultValues: {
      appointmentStartTime: convertedStartTime,
      appointmentEndTime: convertedEndTime,
    },
  });

  const { control, handleSubmit, watch } = formMethods;
  const watchedStartTime = watch('appointmentStartTime');

  // Check if TMS integration is available
  const isTMSAvailable = tmsName && tmsName !== '';

  /**
   * Gets the display name for a TMS system
   * @param tmsName - The TMS system name
   * @returns Human-readable TMS name
   */
  const getTMSDisplayName = (tmsName: string): string => {
    if (!tmsName) return '';
    return titleCase(tmsName.replace(/[-_]/g, ' '));
  };

  const onInvalid: SubmitErrorHandler<FormData> = (errors) => {
    // Get the first error message to show to the user
    const firstError = Object.values(errors)[0] as any;
    const errorMessage =
      firstError?.message || 'Please fix the form errors before submitting';

    toast({
      description: errorMessage,
      variant: 'destructive',
    });
  };

  const handleUpdateTMS = async (data: FormData) => {
    if (!isTMSAvailable) {
      toast({
        description: 'No TMS integration available for this load',
        variant: 'destructive',
      });
      return;
    }

    // Validate that end time is after start time
    if (data.appointmentEndTime <= data.appointmentStartTime) {
      toast({
        description: 'End time must be after start time',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    setIsSuccess(false);

    try {
      /**
       * Formats local date with UTC components back to proper UTC date for API
       */
      const formatLocalWithUTCComponentsToUTC = (date: Date): string => {
        const year = date.getFullYear();
        const month = date.getMonth();
        const day = date.getDate();
        const hours = date.getHours();
        const minutes = date.getMinutes();
        const seconds = date.getSeconds();

        const utcDate = new Date(
          Date.UTC(year, month, day, hours, minutes, seconds)
        );
        return utcDate.toISOString();
      };

      /**
       * Formats date to warehouse timezone for API
       */
      const formatToWarehouseTimezone = (
        date: Date,
        targetTimezone: string
      ): string => {
        try {
          const warehouseDate = dayjs(date).tz(targetTimezone, true);
          if (!warehouseDate.isValid()) {
            return date.toISOString();
          }
          return warehouseDate.toISOString();
        } catch {
          return date.toISOString(); // Fallback to original date
        }
      };

      /**
       * Formats a date for TMS API - handles timezone conversion appropriately
       */
      const formatDateForTMS = (date: Date): string => {
        if (!timezone) {
          // For platforms without timezone (E2open, Manhattan, OneNetwork, Retalix)
          return formatLocalWithUTCComponentsToUTC(date);
        }

        // For platforms with timezone (Yardview, Opendock)
        return formatToWarehouseTimezone(date, timezone);
      };

      // Prepare the load data for appointment update - only send required fields
      const requestData = {
        load: {
          freightTrackingID: freightTrackingId,
          // Update pickup appointment details
          ...(stopType === 'pickup' && {
            pickup: {
              apptStartTime: formatDateForTMS(data.appointmentStartTime),
              apptEndTime: formatDateForTMS(data.appointmentEndTime),
              apptType: 'By appointment',
            },
          }),
          // Update consignee appointment details
          ...(stopType === 'dropoff' && {
            consignee: {
              apptStartTime: formatDateForTMS(data.appointmentStartTime),
              apptEndTime: formatDateForTMS(data.appointmentEndTime),
              apptType: 'By appointment',
            },
          }),
        },
      };

      const result = await updateLoadAppointment(load.ID!, requestData);

      if (result.isOk()) {
        setIsSuccess(true);
        onSuccess?.();
      } else {
        const errorMessage = result.error.message || 'Failed to update TMS';

        toast({
          description: errorMessage,
          variant: 'destructive',
        });
        onError?.(errorMessage);
      }
    } catch {
      const errorMessage = 'An unexpected error occurred while updating TMS';

      toast({
        description: errorMessage,
        variant: 'destructive',
      });
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isTMSAvailable) {
    return null; // Don't show anything if no TMS integration
  }

  return (
    <div className={cn('mt-4', className)}>
      <Divider variant='strong' className='mb-4' />
      <div className='mb-4'>
        <Typography
          variant='h5'
          weight='semibold'
          className='text-neutral-900 mb-1'
        >
          Update TMS
        </Typography>
        <Typography variant='body-xs' textColor='muted' className='mb-2'>
          Update load in TMS with newly scheduled appointment details.
        </Typography>
        <Flex direction='row' gap='md' align='center'>
          <Typography variant='body-xs' weight='medium'>
            <strong>TMS:</strong> {getTMSDisplayName(tmsName)}
          </Typography>
          <Typography variant='body-xs' weight='medium'>
            <strong>Stop Type:</strong>{' '}
            {stopType === 'pickup' ? 'Pickup' : 'Dropoff'}
          </Typography>
        </Flex>
      </div>

      <Flex direction='col' gap='sm' className='w-full'>
        <DateTimeInput
          control={control}
          name='appointmentStartTime'
          label='Start Time'
          hideAIHint={true}
          preventNormalizedLabelTZ={true}
          options={{
            required: 'Start time is required',
          }}
        />

        <DateTimeInput
          control={control}
          name='appointmentEndTime'
          label='End Time'
          preventNormalizedLabelTZ={true}
          hideAIHint={true}
          options={{
            required: 'End time is required',
            validate: (value: Date) => {
              if (watchedStartTime && value <= watchedStartTime) {
                return 'End time must be after start time';
              }
              return true;
            },
          }}
        />
        <Flex direction='col' justify='center' gap='sm' className='mt-4 w-full'>
          {isSuccess && (
            <Flex
              direction='col'
              justify='center'
              gap='sm'
              className='mb-2 rounded p-3 bg-success-200 w-full'
            >
              <Typography variant='h6' weight='semibold'>
                TMS Updated Successfully 🎉
              </Typography>
              <Typography variant='body-sm'>
                You can now leave this page or schedule a{' '}
                {/* Prompt user to schedule complimentary appointment */}
                {stopType === 'pickup' ? 'dropoff' : 'pickup'} appointment
                above.
              </Typography>
            </Flex>
          )}

          <Button
            onClick={handleSubmit(handleUpdateTMS, onInvalid)}
            disabled={isLoading}
            variant='default'
            size='default'
            buttonNamePosthog={ButtonNamePosthog.UpdateTMSLoadWithAppointment}
            className='w-full'
          >
            {isLoading ? 'Updating load in TMS...' : 'Update TMS'}
          </Button>
        </Flex>
      </Flex>
    </div>
  );
};

export { UpdateTMSLoadWithAppt };
