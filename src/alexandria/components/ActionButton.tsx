import { motion } from 'framer-motion';

interface ActionButtonProps {
  onClick: () => void;
  className: string;
  icon: React.ComponentType<{ className?: string }>;
  'aria-label': string;
  type?: 'button' | 'submit' | 'reset';
}

export function ActionButton({
  onClick,
  className,
  icon: Icon,
  'aria-label': ariaLabel,
  type = 'button',
}: ActionButtonProps) {
  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`p-1 rounded-full transition-colors hover:cursor-pointer ${className}`}
      type={type}
      aria-label={ariaLabel}
    >
      <Icon className='h-4 w-4' />
    </motion.button>
  );
}
