import { JSX } from 'react';

import Logo from '../assets/drumkit-34.png';
import { Typography } from './typography';

interface SpeechBubbleProps {
  summary: string;

  callToActionLabel?: string;

  // the actual action performed by the CTA button, if shown
  callToAction?: () => void;

  // instead of using label + CTA function, also accept injecting elements directly
  callToActionElement?: JSX.Element;
}

export default function SpeechBubble({
  summary,
  callToActionLabel,
  callToAction,
  callToActionElement,
}: SpeechBubbleProps) {
  return (
    <section className='flex gap-3 mb-3 pl-3 pr-2'>
      <img
        src={Logo}
        loading='lazy'
        alt='Drumkit Logo'
        className='shrink-0 self-start mt-3 w-4 h-auto'
      />

      <div className='grow justify-center ml-0 px-2 py-3 rounded-lg bg-neutral-100 w-fit '>
        <Typography variant='body-xs'>{summary}</Typography>
        <br />
        <>
          {callToActionElement ?? (
            <Typography
              variant='body-xs'
              weight='medium'
              className='text-accent-main cursor-pointer underline'
              onClick={callToAction}
            >
              {callToActionLabel}
            </Typography>
          )}
        </>
      </div>
    </section>
  );
}
