import {
  FocusEventH<PERSON>ler,
  JSX,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { captureException } from '@sentry/browser';
import _ from 'lodash';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CopyIcon,
  InfoIcon,
} from 'lucide-react';

import KitDefaultProfitTooltip from 'components/AISuggestions/KitDefaultProfitTooltip';
import KitDefaultValueTooltip from 'components/AISuggestions/KitDefaultValueTooltip';
import { Button } from 'components/Button';
import { Label } from 'components/Label';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import { Input } from 'components/input';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography/Typography';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { toast } from 'hooks/useToaster';
import { getFuelPriceEIA } from 'lib/api/getFuelPriceEIA';
import { updateUserDefaultStopFee } from 'lib/api/updateUserDefaultStopFee';
import {
  DistanceSource,
  FuelType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/QuickQuoteForm';
import {
  CarrierCostType,
  ProfitType,
  SelectedQuoteType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { Maybe } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { QuotingPortals, integrationNameMap } from 'types/enums/Integrations';
import { copyToClipboard } from 'utils/copyToClipboard';
import { cn } from 'utils/shadcn';

export enum CarrierPriceCalculatorParent {
  QuickQuote = 'QuickQuote',
  CarrierQuote = 'CarrierQuote',
}

export type CarrierPriceCalculatorProps = {
  defaultFSCProvider?: string;
  parentQuoteRequestId: number;
  showTitle: boolean;
  mileage: number;
  mileageSource: Maybe<DistanceSource>;
  finalPrice: Maybe<number>;
  fuelEstimate: number;
  portalFuelSurchargeSource: Maybe<QuotingPortals>;
  portalFuelSurcharge: Maybe<number>; // FSC AI-parsed from bidding portals like Freightview, E2Open, etc.
  datFuelSurcharge: Maybe<number>;
  profit: number;
  setProfitHandler?:
    | React.Dispatch<React.SetStateAction<number>>
    | ((value: number) => void);
  profitType: ProfitType;
  setProfitTypeHandler:
    | React.Dispatch<React.SetStateAction<ProfitType>>
    | ((value: ProfitType) => void);
  maxDistance: number;
  carrierCost: number;
  carrierCostType: CarrierCostType;
  terminatingActionHandler: () => Promise<void>;
  setCarrierCostTypeHandler: React.Dispatch<
    React.SetStateAction<CarrierCostType>
  >;
  onBlurHandler?: FocusEventHandler<HTMLInputElement>;
  setIsCarrierButtonClickedHandler?: React.Dispatch<
    React.SetStateAction<boolean>
  >;
  setSelectedQuoteIndexHandler?: React.Dispatch<
    React.SetStateAction<number | null>
  >;
  setCarrierCostHandler?: React.Dispatch<React.SetStateAction<number>>;
  setFuelEstimateHandler?: React.Dispatch<React.SetStateAction<number>>;
  setFinalPriceHandler?: React.Dispatch<React.SetStateAction<Maybe<number>>>;
  selectedQuickQuoteId?: number;
  calculatorParent: CarrierPriceCalculatorParent;
  setStopFeeHandler?:
    | React.Dispatch<React.SetStateAction<number>>
    | ((value: number) => void);
  stopFee?: number;
  selectedQuoteType?: SelectedQuoteType;
};

// Add your dev domain to view alternative labels
// TODO: Add settings page to Drumkit Portal
const altLabelsDomains = ['fetchfreight.com'];

// TIP: Do NOT use _.round() on any of the intermediate *numeric* values in this component as this causes precision issues
// when switching from flat to per mile or vice versa.
// Instead, always perform numeric calculations using floating point arithmetic, then call formatCostByType() and formatMarginByType()
// to format the results as strings with the correct number of decimal places.
export default function CarrierPriceCalculator({
  parentQuoteRequestId,
  showTitle,
  mileage: initMileage,
  defaultFSCProvider,
  finalPrice,
  fuelEstimate,
  datFuelSurcharge,
  portalFuelSurchargeSource,
  portalFuelSurcharge,
  profit,
  setProfitHandler,
  profitType,
  setProfitTypeHandler,
  carrierCost,
  carrierCostType,
  terminatingActionHandler,
  setCarrierCostTypeHandler,
  onBlurHandler,
  setIsCarrierButtonClickedHandler,
  setSelectedQuoteIndexHandler,
  setCarrierCostHandler,
  setFuelEstimateHandler,
  setFinalPriceHandler,
  calculatorParent,
  setStopFeeHandler,
  stopFee,
  selectedQuoteType,
}: CarrierPriceCalculatorProps): JSX.Element {
  const {
    currentState: { inboxEmailAddress },
  } = useContext(SidebarStateContext);

  const {
    serviceFeaturesEnabled: { isQuoteCalculatorMarginEnabled },
    serviceID,
  } = useServiceFeatures();

  // Format input values as strings to avoid floating point errors
  const [mileage, setMileage] = useState(initMileage);
  const [buyInputValue, setBuyInputValue] = useState(
    formatCostByType(carrierCost, carrierCostType)
  );

  const [isDefaultProfit, setIsDefaultProfit] = useState(true);
  const [isDefaultProfitType, setIsDefaultProfitType] = useState(true);
  const [profitInputValue, setProfitInputValue] = useState(
    formatProfitByType(profit, carrierCostType, profitType)
  );
  const [hasCopiedSellPrice, setHasCopiedSellPrice] = useState(false);
  const [hasCopiedLinehaulPrice, setHasCopiedLinehaulPrice] = useState(false);

  const [stopFeeInputValue, setStopFeeInputValue] = useState<string>(
    String(stopFee ?? 0)
  );
  const [defaultStopFee, setDefaultStopFee] = useState(stopFee);
  const [isDefaultStopFee, setIsDefaultStopFee] = useState(true);

  const [isQuoteDetailsOpen, setIsQuoteDetailsOpen] = useState(false);
  const [fuelPriceEIA, setFuelPriceEIA] = useState<Maybe<number>>(null);

  const [fuelSurcharge, setFuelSurcharge] = useState<number>(0.3);
  const [fuelSurchargeInputValue, setFuelSurchargeInputValue] =
    useState('0.30');
  const [selectedFuelType, setSelectedFuelType] = useState<FuelType>(
    FuelType.None
  );

  const [highlightLinehaulCalculation, setHighlightLinehaulCalculation] =
    useState(false);
  const [linehaulPrice, setLinehaulPrice] = useState<number>(0);

  // For Posthog logging
  const priceCalculatorProperties = useMemo(() => {
    return {
      serviceID,
      parentQuoteRequestId,
      mileage,
      finalPrice,
      fuelEstimate,
      profit: profit,
      profitType,
      carrierCost,
      carrierCostType,
      fuelSurcharge,
      selectedFuelType,
      stopFee,
      selectedQuoteType,
    };
  }, [
    serviceID,
    parentQuoteRequestId,
    mileage,
    finalPrice,
    fuelEstimate,
    profit,
    profitType,
    carrierCost,
    carrierCostType,
    fuelSurcharge,
    selectedFuelType,
    stopFee,
    selectedQuoteType,
  ]);

  useEffect(() => {
    setBuyInputValue(formatCostByType(carrierCost, carrierCostType));
  }, [carrierCost, carrierCostType]);

  useEffect(() => {
    // Syncs profitInputValue when the initialProfit (from QQF.profit),
    // or the relevant types (carrierCostType from CPC state, profitType from QQF prop) change.
    setProfitInputValue(
      formatProfitByType(profit, carrierCostType, profitType)
    );
  }, [profit, carrierCostType, profitType]);

  // If a portal fuel surcharge is provided, open the fuel details panel
  useEffect(() => {
    if (portalFuelSurcharge) {
      setIsQuoteDetailsOpen(true);
    }
  }, [portalFuelSurcharge]);

  useEffect(() => {
    setStopFeeInputValue(String(stopFee ?? 0));
  }, [stopFee]);

  useEffect(() => {
    if (stopFee !== undefined && stopFee > 0 && defaultStopFee === 0) {
      setDefaultStopFee(stopFee);
    }
  }, [stopFee, defaultStopFee]);

  useEffect(() => {
    const roundedInitMileage = _.round(initMileage);
    setMileage(roundedInitMileage);
  }, [initMileage]);

  useEffect(() => {
    if (carrierCostType === CarrierCostType.Flat) {
      setLinehaulPrice((finalPrice ?? 0) - fuelEstimate);
    } else {
      setLinehaulPrice((finalPrice ?? 0) - fuelSurcharge);
    }
  }, [finalPrice, fuelEstimate, fuelSurcharge, carrierCostType]);

  useEffect(() => {
    fetchEIAFuelPrice();
  }, []);

  // Determines the fuel type to use based on the provider hierarchy and available FSC data
  useEffect(() => {
    const fuelTypeMap: { [key: string]: FuelType } = {
      DAT: FuelType.DAT,
      DOE: FuelType.DOE,
      Portal: FuelType.Portal,
    };

    const selectedFuelType: Maybe<FuelType> =
      fuelTypeMap[defaultFSCProvider?.toUpperCase() || ''] ||
      // If no provider is set, use the portal FSC if available
      (portalFuelSurcharge ? FuelType.Portal : null) ||
      // If no portal FSC is available, use the DAT FSC if available
      (datFuelSurcharge ? FuelType.DAT : null) ||
      // If no portal or DAT FSC is available, use the DOE FSC if available
      (fuelPriceEIA ? FuelType.DOE : null);

    if (selectedFuelType) {
      handleSelectedFuelToggle(selectedFuelType);
    }
  }, [datFuelSurcharge, fuelPriceEIA, portalFuelSurcharge, defaultFSCProvider]);

  useEffect(() => {
    setProfitInputValue(
      formatProfitByType(profit, carrierCostType, profitType)
    );
  }, [profit, profitType]);

  useEffect(() => {
    setFuelEstimateHandler && setFuelEstimateHandler(mileage * fuelSurcharge);
  }, [mileage, fuelSurcharge]);

  const fetchEIAFuelPrice = async () => {
    const res = await getFuelPriceEIA();
    if (res.isOk()) {
      setFuelPriceEIA(res.value.fuelPrice);
    }
  };

  useEffect(() => {
    if (!setFinalPriceHandler) return;

    const numericProfitInput = Number(profitInputValue);

    if (!isNaN(carrierCost) && !isNaN(numericProfitInput)) {
      let calculatedFinalPrice = 0;

      if (profitType === ProfitType.Amount) {
        // Flat margin: Add the margin to the carrier cost
        calculatedFinalPrice = carrierCost + numericProfitInput;
      } else {
        // ProfitType.Percentage
        if (isQuoteCalculatorMarginEnabled) {
          // Margin formula: Price = Cost / (1 - Profit_Percentage)
          if (numericProfitInput >= 100) {
            // Profit margin cannot be 100% or more.
            // Using 99.9% as a practical upper limit to avoid division by zero or negative.
            calculatedFinalPrice = carrierCost / (1 - 0.999); // e.g. carrierCost / 0.001 = carrierCost * 1000
          } else {
            calculatedFinalPrice = carrierCost / (1 - numericProfitInput / 100);
          }
        } else {
          // Markup formula: Price = Cost * (1 + Markup_Percentage)
          calculatedFinalPrice = carrierCost * (1 + numericProfitInput / 100);
        }
      }
      setFinalPriceHandler(calculatedFinalPrice);
    }
  }, [
    profitInputValue,
    carrierCost,
    profitType,
    carrierCostType,
    isQuoteCalculatorMarginEnabled,
    setFinalPriceHandler,
  ]);

  const handleBuyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    setBuyInputValue(value);

    if (setSelectedQuoteIndexHandler) {
      setSelectedQuoteIndexHandler(null);
    }
    if (setCarrierCostHandler && value !== '') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        setCarrierCostHandler(numValue);
      }
    }
    if (setIsCarrierButtonClickedHandler) {
      setIsCarrierButtonClickedHandler(false);
    }
  };

  const handleProfitChange = (value: string) => {
    setProfitInputValue(value);

    // Compare the current value with the default price margin
    setIsDefaultProfit(Number(value) === profit);

    if (setProfitHandler && value !== '') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        setProfitHandler(numValue);
      }
    }
  };

  const handleChangeMileage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMileage(parseFloat(value));
  };

  const handleChangeFuelSurcharge = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;

    if (/^(\d+(\.\d{0,2})?|\.\d{0,2})?$/.test(value)) {
      setFuelSurchargeInputValue(value);

      const numericValue = parseFloat(value);
      if (!isNaN(numericValue)) {
        setFuelSurcharge(numericValue);
      } else {
        setFuelSurcharge(0);
      }
    }
  };

  const handleProfitTypeToggle = () => {
    const newProfitType =
      profitType === ProfitType.Amount
        ? ProfitType.Percentage
        : ProfitType.Amount;

    const currentNumericProfit = Number(profitInputValue);
    const currentCarrierCost = carrierCost;
    let currentFinalPrice = 0;

    // Determine the current finalPrice based on existing values and calculation mode
    if (profitType === ProfitType.Amount) {
      currentFinalPrice = currentCarrierCost + currentNumericProfit;
    } else {
      // ProfitType.Percentage
      if (isQuoteCalculatorMarginEnabled) {
        if (currentNumericProfit >= 100) {
          currentFinalPrice = currentCarrierCost / (1 - 0.999);
        } else {
          currentFinalPrice =
            currentCarrierCost / (1 - currentNumericProfit / 100);
        }
      } else {
        currentFinalPrice =
          currentCarrierCost * (1 + currentNumericProfit / 100);
      }
    }

    let newProfitValue: number;

    if (newProfitType === ProfitType.Percentage) {
      // Switching from Flat ($) to Percentage (%)
      // New Profit % = ((Final Price / Carrier Cost) - 1) * 100 for markup
      // New Profit % = (1 - (Carrier Cost / Final Price)) * 100 for margin
      if (currentCarrierCost > 0 && currentFinalPrice > 0) {
        if (isQuoteCalculatorMarginEnabled) {
          if (currentFinalPrice === currentCarrierCost) {
            // Avoid division by zero or negative if cost equals price (0% margin)
            newProfitValue = 0;
          } else if (currentFinalPrice < currentCarrierCost) {
            // Loss scenario, represent as negative margin
            newProfitValue = (1 - currentCarrierCost / currentFinalPrice) * 100; // will be negative
          } else {
            newProfitValue = (1 - currentCarrierCost / currentFinalPrice) * 100;
          }
        } else {
          newProfitValue = (currentFinalPrice / currentCarrierCost - 1) * 100;
        }
      } else {
        newProfitValue = 0; // Default to 0 if carrierCost or finalPrice is zero
      }
    } else {
      // Switching from Percentage (%) to Flat ($)
      // New Profit $ = Final Price - Carrier Cost
      newProfitValue = currentFinalPrice - currentCarrierCost;
    }

    const newProfitFormatted = formatProfitByType(
      newProfitValue,
      carrierCostType,
      newProfitType
    );

    setProfitInputValue(newProfitFormatted);
    setProfitHandler?.(newProfitValue);

    // Update whether new margin value matches default
    if (profitType && profitType === newProfitType) {
      setIsDefaultProfit(newProfitValue === profit);
    }
    if (profitType) {
      setIsDefaultProfitType(newProfitType === profitType);
    }

    // Toggle the margin type
    setProfitTypeHandler(newProfitType);
  };

  const handleSelectedFuelToggle = (newType: FuelType) => {
    setSelectedFuelType(newType);

    let newFuelSurchargeValue: number;

    switch (newType) {
      case FuelType.DAT:
        newFuelSurchargeValue = datFuelSurcharge!;
        break;
      case FuelType.DOE:
        if (fuelPriceEIA) {
          // 10% of Diesel gallon price
          newFuelSurchargeValue = _.round(fuelPriceEIA * 0.1, 2);
        } else {
          newFuelSurchargeValue = 0;
        }
        break;
      case FuelType.Portal:
        newFuelSurchargeValue = portalFuelSurcharge!;
        break;
      default:
        newFuelSurchargeValue = 0;
    }

    setFuelSurcharge(newFuelSurchargeValue);
    setFuelSurchargeInputValue(newFuelSurchargeValue.toFixed(2));
  };

  const handleFuelSurchargeBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    onBlurHandler?.(e);

    const numericValue = parseFloat(fuelSurchargeInputValue);
    if (!isNaN(numericValue)) {
      setFuelSurchargeInputValue(numericValue.toFixed(2));
      setFuelSurcharge(numericValue);
    } else {
      setFuelSurchargeInputValue('0.30');
      setFuelSurcharge(0.3);
    }
  };

  const handleUpdateDefaultStopFee = async (newFee: string) => {
    const feeValue = Number(newFee);
    if (isNaN(feeValue) || feeValue < 0) {
      toast({
        description: 'Please enter a valid stop fee.',
        variant: 'destructive',
      });
      return false;
    }

    const resp = await updateUserDefaultStopFee(feeValue);

    if (resp.isErr()) {
      toast({
        description: 'Failed to update default stop fee.',
        variant: 'destructive',
      });
      return false;
    }

    setDefaultStopFee(feeValue);
    setIsDefaultStopFee(true);
    setStopFeeInputValue(String(feeValue));
    setStopFeeHandler?.(feeValue);

    return true;
  };

  const handleChangeStopFee = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    if (value === '' || /^\d+$/.test(value)) {
      setStopFeeInputValue(value);
      const num = value === '' ? 0 : parseInt(value, 10);

      if (!isNaN(num)) {
        setStopFeeHandler?.(num);
        setIsDefaultStopFee(num === defaultStopFee);
      }
    }
  };

  const handleStopFeeFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    if (e.target.value === '0') {
      e.target.select();
    }
  };

  const handleStopFeeBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const num = value === '' ? 0 : parseInt(value, 10);

    if (!isNaN(num)) {
      setStopFeeInputValue(String(num));
      setStopFeeHandler?.(num);
      setIsDefaultStopFee(num === defaultStopFee);
    }

    onBlurHandler?.(e);
  };

  const handleCarrierCostTypeToggle = () => {
    if (buyInputValue) {
      const currentCarrierCost = carrierCost;
      const currentProfit = Number(profitInputValue);
      const newCarrierCostType =
        carrierCostType === CarrierCostType.Flat
          ? CarrierCostType.PerMile
          : CarrierCostType.Flat;

      let newCarrierCost: number;
      let newProfit = currentProfit;

      if (newCarrierCostType === CarrierCostType.PerMile) {
        // Convert from Flat to Per Mile
        newCarrierCost = currentCarrierCost / mileage;
        // Convert margin value when switching types
        if (profitType === ProfitType.Amount) {
          newProfit = currentProfit / mileage;
        }
      } else {
        // Convert from Per Mile to Flat
        newCarrierCost = currentCarrierCost * mileage;
        // Convert margin value when switching types
        if (profitType === ProfitType.Amount) {
          newProfit = currentProfit * mileage;
        }
      }

      setCarrierCostHandler?.(newCarrierCost);
      setProfitHandler?.(newProfit);
      setBuyInputValue(formatCostByType(newCarrierCost, newCarrierCostType));
      setProfitInputValue(
        formatProfitByType(newProfit, newCarrierCostType, profitType)
      );
      setCarrierCostTypeHandler(newCarrierCostType);
    }
  };

  const handleCopySellPriceToClipboard = async () => {
    if (!finalPrice) {
      toast({
        description: 'No quote to submit.',
        variant: 'info',
      });
      return;
    }

    const roundedFinalPrice = formatCostByType(finalPrice, carrierCostType);

    try {
      const success = await copyToClipboard(roundedFinalPrice);
      if (success) {
        setHasCopiedSellPrice(true);
        terminatingActionHandler();
        // Reset copied state after a delay
        setTimeout(() => setHasCopiedSellPrice(false), 2000);
      }
    } catch (error) {
      captureException(error);

      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  const handleCopyLinehaulToClipboard = async () => {
    const roundedLinehaulPrice = formatCostByType(
      linehaulPrice,
      carrierCostType
    );

    try {
      const success = await copyToClipboard(roundedLinehaulPrice);
      if (success) {
        setHasCopiedLinehaulPrice(true);
        // Use the utility function for final price and margin calculations
        terminatingActionHandler();
        // Reset copied state after a delay
        setTimeout(() => setHasCopiedLinehaulPrice(false), 2000);
      }
    } catch (error) {
      captureException(error);

      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  const validFuelValues = [
    datFuelSurcharge,
    portalFuelSurcharge,
    fuelPriceEIA,
  ].filter((v) => v != null);

  return (
    <>
      {showTitle && (
        <Typography
          variant='h6'
          weight='medium'
          className='mb-3 text-neutral-500'
        >
          Calculate Final Price
        </Typography>
      )}

      <Flex direction='col' gap='sm'>
        <Flex align='baseline' className='mb-2 xxs:gap-3'>
          {/* Carrier Cost */}
          <Flex direction='col' gap='xs' className='min-w-0'>
            <Label name='buy' className='text-neutral-500 text-sm! font-medium'>
              {altLabelsDomains.some((domain) =>
                inboxEmailAddress.toLowerCase().includes(domain.toLowerCase())
              )
                ? 'TTT'
                : 'Buy'}
            </Label>
            <Flex direction='col' align='center' gap='xs'>
              {' '}
              <Flex align='center' className='relative'>
                <span className='absolute left-1 xxs:left-2 text-sm text-neutral-400'>
                  $
                </span>
                <Input
                  type='number'
                  value={buyInputValue}
                  onChange={handleBuyChange}
                  onBlur={onBlurHandler ?? undefined}
                  className='pl-4 pr-1 xxs:pr-2 xxs:pl-5 py-1.5 text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
                  min={0}
                  step={carrierCostType === CarrierCostType.PerMile ? 0.01 : 1}
                  aria-label='Carrier cost input'
                />
                {carrierCostType === CarrierCostType.PerMile && (
                  <div className='absolute bottom-1.5 right-1 xxs:right-2 text-xs text-neutral-400'>
                    /mi
                  </div>
                )}
              </Flex>
              {Boolean(mileage) && (
                <Flex className='rounded-[4px] overflow-hidden border border-neutral-400 text-xs'>
                  <button
                    type='button'
                    title={'Flat Rate'}
                    onClick={handleCarrierCostTypeToggle}
                    className={`px-2 transition-colors  ${
                      carrierCostType === CarrierCostType.Flat
                        ? 'text-brand-400 font-medium bg-brand-50'
                        : 'text-neutral-500 hover:text-brand-400'
                    }`}
                  >
                    Flat
                  </button>
                  <button
                    title={'Per Mile'}
                    type='button'
                    onClick={handleCarrierCostTypeToggle}
                    className={`px-2 transition-colors  ${
                      carrierCostType === CarrierCostType.PerMile
                        ? 'text-brand-400 font-medium bg-brand-50'
                        : 'text-neutral-500 hover:text-brand-400'
                    }`}
                  >
                    /mi
                  </button>
                </Flex>
              )}
            </Flex>
          </Flex>

          {/* Markup or Margin */}
          <Flex direction='col' align='start' gap='xs' className='min-w-0'>
            <Label
              name='margin'
              className='text-neutral-500 text-sm! pl-4 font-medium'
            >
              {isQuoteCalculatorMarginEnabled ? 'Margin' : 'Markup'}
            </Label>
            <Flex direction='col' align='center' gap='xs'>
              <Flex align='center'>
                <span className='mx-1 xxs:mx-0 xxs:mr-2 text-sm text-neutral-400'>
                  +
                </span>
                <Flex align='center' className='relative'>
                  {profitType === ProfitType.Amount && (
                    <div className='absolute top-1.5 left-1 xxs:left-2 text-sm text-neutral-400'>
                      $
                    </div>
                  )}
                  <Input
                    type='number'
                    value={profitInputValue}
                    onChange={(e) => handleProfitChange(e.target.value)}
                    onBlur={onBlurHandler ?? undefined}
                    className={`min-w-[60px] text-sm pl-4 xxs:pl-5 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
                    min={0}
                    step={
                      profitType === ProfitType.Percentage
                        ? 1
                        : carrierCostType === CarrierCostType.PerMile
                          ? 0.01
                          : 1
                    }
                    aria-label='Margin input'
                  />

                  <KitDefaultProfitTooltip
                    profitInputValue={profitInputValue}
                    profitInputType={profitType}
                    isDefaultProfit={isDefaultProfit}
                    isDefaultProfitType={isDefaultProfitType}
                    setIsDefaultProfit={setIsDefaultProfit}
                    setIsDefaultProfitType={setIsDefaultProfitType}
                  />

                  {profitType !== ProfitType.Amount && (
                    <div className='absolute top-1.5 right-1 xxs:right-2 text-sm text-neutral-400'>
                      %
                    </div>
                  )}
                </Flex>
                <span className='mx-1 xxs:mx-0 xxs:ml-2 text-sm text-neutral-400'>
                  =
                </span>
              </Flex>

              {/* Markup or Margin Type Toggle */}
              <Flex className='rounded-[4px] overflow-hidden border border-neutral-400 text-xs'>
                <button
                  type='button'
                  title={'Use Dollar Profit'}
                  onClick={handleProfitTypeToggle}
                  className={`px-2 transition-colors  ${
                    profitType === ProfitType.Amount
                      ? 'text-brand-400 font-medium bg-brand-50'
                      : 'text-neutral-500 hover:text-brand-400'
                  }`}
                >
                  $
                </button>
                <button
                  type='button'
                  title={'Use Percentage Profit'}
                  onClick={handleProfitTypeToggle}
                  className={`px-2 transition-colors  ${
                    profitType === ProfitType.Percentage
                      ? 'text-brand-400 font-medium bg-brand-50'
                      : 'text-neutral-500 hover:text-brand-400'
                  }`}
                >
                  %
                </button>
              </Flex>
            </Flex>
          </Flex>

          {/* Final Price */}
          <Flex direction='col' gap='xs'>
            <Label
              name='sell'
              className='text-neutral-500 text-sm! font-medium'
            >
              {altLabelsDomains.some((domain) =>
                inboxEmailAddress.toLowerCase().includes(domain.toLowerCase())
              )
                ? 'TTC'
                : 'Sell'}
            </Label>
            <Flex align='center' className='relative'>
              <span className='absolute left-1 xxs:left-2 text-sm text-neutral-400'>
                $
              </span>
              <Input
                className={cn(
                  'pl-4 xxs:pl-5 pr-2 py-1.5 transition-all text-sm max-xxs:min-w-[70px] read-only:bg-neutral-50 read-only:text-neutral-400 read-only:border-neutral-400',
                  highlightLinehaulCalculation &&
                    '!bg-success-50 !border-success'
                )}
                type='text'
                value={
                  isNaN(finalPrice ?? 0)
                    ? ''
                    : formatCostByType(finalPrice ?? 0, carrierCostType)
                }
                disabled
                aria-label='Final price'
              />
              {carrierCostType === CarrierCostType.PerMile && (
                <div className='absolute bottom-1.5 right-1 xxs:right-2 text-xs text-neutral-400'>
                  /mi
                </div>
              )}

              <Button
                buttonNamePosthog={
                  calculatorParent === CarrierPriceCalculatorParent.QuickQuote
                    ? ButtonNamePosthog.QuickQuoteCopySellPrice
                    : ButtonNamePosthog.CarrierQuoteCopySellPrice
                }
                logProperties={priceCalculatorProperties}
                className={cn(
                  'absolute h-4 p-0 -top-5 right-1 border-none',
                  hasCopiedSellPrice ? 'cursor-default' : 'cursor-pointer'
                )}
                variant='ghost'
                type='button'
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  !hasCopiedSellPrice && handleCopySellPriceToClipboard();
                }}
              >
                {hasCopiedSellPrice ? (
                  <Tooltip open={true}>
                    <TooltipTrigger asChild>
                      <CheckIcon className='h-4 w-4' />
                    </TooltipTrigger>
                    <TooltipContent>Copied!</TooltipContent>
                  </Tooltip>
                ) : (
                  <CopyIcon className='h-4 w-4' />
                )}
              </Button>
            </Flex>

            {Boolean(mileage) && (
              <Flex
                justify='center'
                align='baseline'
                className='w-full rounded-[4px] border border-brand-400 text-xs'
              >
                {/* Total price shown below when CarrierCostType is Per Mile */}
                {finalPrice && carrierCostType === CarrierCostType.PerMile && (
                  <Typography
                    variant='body-xs'
                    className='px-0 text-neutral-400'
                  >
                    {`Total:  `}
                    <span className='text-xs text-brand-400 font-medium bg-brand-50'>
                      {`$${formatCostByType(finalPrice * mileage, CarrierCostType.Flat)}`}
                    </span>
                  </Typography>
                )}
                {/* Per-mile price shown below when CarrierCostType is Flat */}
                {finalPrice && carrierCostType === CarrierCostType.Flat && (
                  <Flex
                    justify='center'
                    gap='xs'
                    className='rounded w-full bg-brand-50 px-1 font-medium text-xs text-brand-400'
                  >
                    {`$${formatCostByType(finalPrice / mileage, CarrierCostType.PerMile)}`}
                    /mi
                  </Flex>
                )}
              </Flex>
            )}

            {!finalPrice && (
              <Typography variant='body-xs' className='text-error-500'>
                Required
              </Typography>
            )}
          </Flex>
        </Flex>

        {isQuoteDetailsOpen && setFuelEstimateHandler && (
          <div>
            <Flex align='baseline' gap='none' className='mb-2 xxs:gap-3'>
              {/* Mileage for Fuel */}
              <Flex direction='col' gap='xs' className='min-w-0'>
                <Label
                  name='mileage'
                  className='text-neutral-500 text-sm! font-medium'
                >
                  Distance
                </Label>
                <Flex direction='col' align='center' gap='xs'>
                  {' '}
                  <Flex align='center' className='relative'>
                    <Input
                      type='number'
                      value={mileage}
                      onChange={handleChangeMileage}
                      onBlur={onBlurHandler ?? undefined}
                      className='pl-2 pr-2 py-1.5 text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
                      min={0}
                      aria-label='Mileage input'
                    />
                    <div className='absolute bottom-1.5 right-1 xxs:right-2 text-xs text-neutral-400'>
                      mi
                    </div>
                  </Flex>
                </Flex>
              </Flex>

              {/* Fuel Surcharge */}
              {/* TODO: Add AI icon */}
              <Flex direction='col' align='start' gap='xs' className='min-w-0'>
                <Label
                  name='fsc'
                  className='w-full text-neutral-500 text-sm! px-4 font-medium'
                >
                  <Flex
                    justify='between'
                    align='center'
                    className='w-full gap-x-4'
                  >
                    <Typography variant='body-sm'>FSC</Typography>

                    <Tooltip delayDuration={10}>
                      <TooltipTrigger asChild>
                        <InfoIcon className='h-4 w-4' />
                      </TooltipTrigger>
                      <TooltipContent>
                        {selectedFuelType === FuelType.DOE && (
                          <div>
                            <Typography>10% of DOE per gallon price</Typography>
                            <Typography className='text-[12px] italic text-neutral-300 mt-2'>
                              {`This week's DOE Diesel Price: $${fuelPriceEIA}/gallon`}
                            </Typography>
                          </div>
                        )}
                        {selectedFuelType === FuelType.Portal &&
                          portalFuelSurchargeSource && (
                            <Typography>
                              {`AI detected FSC from ${integrationNameMap[portalFuelSurchargeSource]}`}
                            </Typography>
                          )}
                        {selectedFuelType === FuelType.DAT && (
                          <Typography>{`DAT estimated FSC`}</Typography>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </Flex>
                </Label>

                <Flex direction='col' align='center' gap='xs'>
                  <Flex align='center'>
                    <span className='mx-1 xxs:mx-0 xxs:mr-2 text-sm text-neutral-400'>
                      *
                    </span>
                    <Flex align='center' className='relative'>
                      <div className='absolute top-1.5 left-1 xxs:left-2 text-sm text-neutral-400'>
                        $
                      </div>
                      <Input
                        type='number'
                        value={fuelSurchargeInputValue}
                        onChange={handleChangeFuelSurcharge}
                        onBlur={handleFuelSurchargeBlur}
                        className={cn(
                          'min-w-[60px] text-sm pl-4 xxs:pl-5 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none',
                          highlightLinehaulCalculation &&
                            carrierCostType === CarrierCostType.PerMile &&
                            '!bg-error-50 !border-error-main'
                        )}
                        min={0}
                        step={0.01}
                        aria-label='Fuel Surcharge input'
                      />
                    </Flex>
                    <span className='mx-1 xxs:mx-0 xxs:ml-2 text-sm text-neutral-400'>
                      =
                    </span>
                  </Flex>
                </Flex>

                {validFuelValues.length >= 2 && (
                  <Flex className='mx-auto rounded-[4px] overflow-hidden border border-neutral-400 text-xs'>
                    {fuelPriceEIA && (
                      <button
                        type='button'
                        title={'Use DOE Fuel Rate'}
                        onClick={() => handleSelectedFuelToggle(FuelType.DOE)}
                        className={`text-[10px] px-2 transition-colors  ${
                          selectedFuelType === FuelType.DOE
                            ? 'text-neutral-700 font-medium bg-brand-50'
                            : 'text-neutral-400 hover:text-brand-main'
                        }`}
                      >
                        DOE
                      </button>
                    )}
                    {datFuelSurcharge && (
                      <button
                        type='button'
                        title={'Use DAT Fuel Rate'}
                        onClick={() => handleSelectedFuelToggle(FuelType.DAT)}
                        className={cn(
                          'text-[10px] px-2 transition-colors',
                          selectedFuelType === FuelType.DAT
                            ? 'text-brand-400 font-medium bg-brand-50'
                            : 'text-neutral-400 hover:text-brand-main'
                        )}
                      >
                        DAT
                      </button>
                    )}
                    {portalFuelSurcharge && (
                      <button
                        type='button'
                        title={`Use ${
                          portalFuelSurchargeSource
                            ? integrationNameMap[portalFuelSurchargeSource]
                            : 'Portal'
                        } Fuel Rate`}
                        onClick={() =>
                          handleSelectedFuelToggle(FuelType.Portal)
                        }
                        className={cn(
                          'text-[10px] px-2 transition-colors',
                          selectedFuelType === FuelType.Portal
                            ? 'text-brand-400 font-medium bg-brand-50'
                            : 'text-neutral-400 hover:text-brand-main'
                        )}
                      >
                        {portalFuelSurchargeSource
                          ? integrationNameMap[portalFuelSurchargeSource]
                          : 'Portal'}
                      </button>
                    )}
                  </Flex>
                )}
              </Flex>

              {/* Final Fuel Price */}
              <Flex direction='col' gap='xs'>
                <Label
                  name='totalFuel'
                  className='text-neutral-500 text-sm! font-medium'
                >
                  Fuel Estimate
                </Label>
                <Flex align='center' className='relative'>
                  <span className='absolute left-1 xxs:left-2 text-sm text-neutral-400'>
                    $
                  </span>
                  <Input
                    className={cn(
                      'pl-4 xxs:pl-5 pr-2 py-1.5 transition-all text-sm max-xxs:min-w-[70px] read-only:bg-neutral-50 read-only:text-neutral-400 read-only:border-neutral-400',
                      highlightLinehaulCalculation &&
                        carrierCostType === CarrierCostType.Flat &&
                        '!bg-error-50 !border-error-main'
                    )}
                    type='text'
                    value={
                      isNaN(mileage * fuelSurcharge)
                        ? ''
                        : formatCostByType(
                            mileage * fuelSurcharge,
                            CarrierCostType.Flat
                          )
                    }
                    disabled
                    aria-label='Final Fuel Price'
                  />
                </Flex>
              </Flex>
            </Flex>

            <Flex align='baseline' className='mx-auto mt-4'>
              {selectedQuoteType !== SelectedQuoteType.DAT_LONGEST_LEG ? (
                /* Empty distance column mock for spacing standards */
                <Flex
                  direction='col'
                  gap='xs'
                  className='basis-1/3 mx-1 max-w-[200px]'
                />
              ) : (
                <Flex
                  direction='col'
                  gap='xs'
                  className='basis-1/3 mx-1 max-w-[200px]'
                >
                  <Label
                    name='stopFee'
                    className='text-neutral-500 text-sm! font-medium'
                  >
                    Stop Fee
                  </Label>
                  <Flex align='center' className='relative'>
                    <span className='absolute left-1 text-sm text-neutral-400'>
                      $
                    </span>
                    <Input
                      type='number'
                      value={stopFeeInputValue}
                      onChange={handleChangeStopFee}
                      onBlur={handleStopFeeBlur}
                      onFocus={handleStopFeeFocus}
                      min={0}
                      className='pl-4 pr-2 py-1.5 text-sm read-only:bg-neutral-50 min-w-[90px] read-only:text-neutral-400 read-only:border-neutral-400'
                      aria-label='Stop Fee'
                    />

                    <KitDefaultValueTooltip
                      inputValue={stopFeeInputValue}
                      isDefaultValue={isDefaultStopFee}
                      setIsDefaultValue={setIsDefaultStopFee}
                      updateHandler={handleUpdateDefaultStopFee}
                      tooltipTextFormatter={(value) =>
                        `Click to save $${value} as default stop fee`
                      }
                      buttonName={ButtonNamePosthog.UpdateDefaultStopFee}
                    />
                  </Flex>
                </Flex>
              )}

              {/* Empty FSC column mock for spacing standards */}
              <Flex
                direction='col'
                align='start'
                gap='xs'
                className='basis-1/3 mx-1 max-w-[200px]'
              />

              <Flex
                direction='col'
                align='start'
                gap='xs'
                className='basis-1/3 mx-1 max-w-[200px] relative'
              >
                <Label
                  name='totalFuel'
                  className='text-neutral-500 text-sm! font-medium'
                >
                  Linehaul
                </Label>
                <Flex
                  align='center'
                  className='relative'
                  onMouseOver={() => setHighlightLinehaulCalculation(true)}
                  onMouseLeave={() => setHighlightLinehaulCalculation(false)}
                >
                  <span className='absolute left-1 text-sm text-neutral-400'>
                    $
                  </span>
                  <Input
                    className='pl-4 pr-2 py-1.5 text-sm read-only:bg-neutral-50 min-w-[90px] read-only:text-neutral-400 read-only:border-neutral-400'
                    type='text'
                    value={
                      isNaN(linehaulPrice)
                        ? ''
                        : formatCostByType(linehaulPrice, carrierCostType)
                    }
                    disabled
                    aria-label='Linehaul'
                  />
                  {carrierCostType === CarrierCostType.PerMile && (
                    <div className='absolute bottom-1.5 right-1 xxs:right-2 text-xs text-neutral-400'>
                      /mi
                    </div>
                  )}
                </Flex>

                {/* Show total linehaul or per-mile value below, styled like Sell price */}
                {Boolean(mileage) && (
                  <Flex
                    justify='center'
                    align='baseline'
                    className='rounded-[4px] overflow-hidden border border-brand-400 text-xs mt-1 w-full'
                  >
                    {/* Total linehaul shown below when CarrierCostType is PerMile */}
                    {linehaulPrice &&
                      carrierCostType === CarrierCostType.PerMile && (
                        <Typography className='text-xs px-0 text-neutral-400 justify-self-center'>
                          {`Total:  `}
                          <Typography
                            variant='body-xs'
                            weight='medium'
                            className='text-brand-400 bg-brand-50'
                          >
                            {`$${formatCostByType(linehaulPrice * mileage, CarrierCostType.Flat)}`}
                          </Typography>
                        </Typography>
                      )}
                    {/* Per-mile linehaul shown below when CarrierCostType is Flat */}
                    {linehaulPrice &&
                      carrierCostType === CarrierCostType.Flat && (
                        <Flex
                          justify='center'
                          gap='xs'
                          className='rounded w-full bg-brand-50 px-1 font-medium text-xs text-brand-400'
                        >
                          {`$${formatCostByType(linehaulPrice / mileage, CarrierCostType.PerMile)}`}
                          /mi
                        </Flex>
                      )}
                  </Flex>
                )}

                <Button
                  buttonNamePosthog={
                    calculatorParent === CarrierPriceCalculatorParent.QuickQuote
                      ? ButtonNamePosthog.QuickQuoteCopyLinehaulPrice
                      : ButtonNamePosthog.CarrierQuoteCopyLinehaulPrice
                  }
                  logProperties={priceCalculatorProperties}
                  className={cn(
                    'absolute h-4 p-0 top-1 right-1 border-none',
                    hasCopiedLinehaulPrice ? 'cursor-default' : 'cursor-pointer'
                  )}
                  variant='ghost'
                  type='button'
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    !hasCopiedLinehaulPrice && handleCopyLinehaulToClipboard();
                  }}
                >
                  {hasCopiedLinehaulPrice ? (
                    <Tooltip open={true}>
                      <TooltipTrigger asChild>
                        <CheckIcon className='h-4 w-4' />
                      </TooltipTrigger>
                      <TooltipContent>Copied!</TooltipContent>
                    </Tooltip>
                  ) : (
                    <CopyIcon className='h-4 w-4' />
                  )}
                </Button>
              </Flex>
            </Flex>
          </div>
        )}

        {setFuelEstimateHandler && (
          <Button
            className='w-40 mx-auto h-8 text-[14px] text-neutral-600 flex gap-2 hover:border-neutral-600 hover:bg-neutral-200'
            buttonNamePosthog={ButtonNamePosthog.ToggleQuoteDetails}
            onClick={() => setIsQuoteDetailsOpen(!isQuoteDetailsOpen)}
            type='button'
            variant='ghost'
          >
            {!isQuoteDetailsOpen ? (
              <ChevronDownIcon className='h-4 w-4' />
            ) : (
              <ChevronUpIcon className='h-4 w-4' />
            )}
            Adjust details
          </Button>
        )}
      </Flex>
    </>
  );
}

export const formatCostByType = (value: number, type: CarrierCostType) => {
  if (type === CarrierCostType.PerMile) {
    // Convert to string with max 2 decimals
    const formatted = value.toFixed(2);

    // Remove trailing zeros and decimal point if not needed
    return formatted.replace(/\.?0{2}$/, '');
  }
  // If flat rate, support only whole numbers
  return value.toFixed(0);
};

const formatProfitByType = (
  value: number,
  costType: CarrierCostType,
  profitType: ProfitType
) => {
  if (profitType === ProfitType.Percentage) {
    return value.toFixed(0);
  }

  // If cost type is per mile and margin type is flat, format margin to 2 decimal places
  if (costType === CarrierCostType.PerMile) {
    // Convert to string with max 2 decimals
    const formatted = value.toFixed(2);

    // Remove trailing zeros and decimal point if not needed
    return formatted.replace(/\.?0{2}$/, '');
  } else {
    return value.toFixed(0);
  }
};
