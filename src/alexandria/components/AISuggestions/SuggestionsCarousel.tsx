import React, { JSX, useContext, useEffect, useRef, useState } from 'react';

import { Carousel } from 'antd';
import { CarouselRef } from 'antd/es/carousel';
import dayjs from 'dayjs';
import { groupBy } from 'lodash';
import { InfoIcon, List, SparklesIcon } from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

import { LoadBuildingSuggestionCard } from 'components/AISuggestions/LoadBuildingCard';
import { QuickQuoteCard } from 'components/AISuggestions/QuickQuoteCard';
import {
  KeyValueElement,
  SuggestionCard,
} from 'components/AISuggestions/SuggestionsCard';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Typography } from 'components/typography';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useAuth } from 'hooks/useAuth';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { skipSuggestion } from 'lib/api/skipSuggestion';
import { Undef } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import {
  GenericSuggestion,
  SuggestionChangeRecord,
  SuggestionPipelines,
} from 'types/suggestions/CoreSuggestions';
import { LoadBuildingSuggestions } from 'types/suggestions/LoadBuildingSuggestions';
import { SuggestedLoadChange } from 'types/suggestions/LoadSuggestions';
import { SuggestedQuoteChange } from 'types/suggestions/QuoteSuggestions';
import { flattenSuggestionChanges } from 'utils/flattenSuggestionChanges';
import { isValidNonDateObject } from 'utils/isValidObject';
import { isLikelyPhoneNumber } from 'utils/phoneAndDateParser';
import {
  getFieldOrder,
  getSuggestionFormattedLabel,
  reorderFields,
} from 'utils/suggestions/suggestionFormat';

// FIXME: Weird carousel glitch when switching to LB tab, but doesn't happen for other tabs/suggestion pipelines
export default function SuggestionsCarousel({
  suggestions: initRawSuggestions,
}: {
  suggestions: GenericSuggestion[];
}) {
  const carouselRef = useRef<CarouselRef>(null);

  const { user } = useAuth();
  const { toast } = useToast();
  const posthog = usePostHog();
  const {
    setCurrentState,
    currentState: { curSuggestionList, clickedSuggestion },
  } = useContext(SidebarStateContext);

  const {
    serviceFeaturesEnabled: {
      isCarrierInfoSuggestionsEnabled,
      isAppointmentSuggestionsEnabled,
      isLoadBuildingEnabled,
      isBatchQuoteEnabled,
    },
  } = useServiceFeatures();

  const [optionalSuggestions, setOptionalSuggestions] = useState<
    GenericSuggestion[]
  >([]);
  const [currentCarouselIndex, setCurrentCarouselIndex] = useState(0);

  useEffect(() => {
    const optionalSuggestions = initRawSuggestions
      .sort(suggestionSortComparator)
      .filter(
        (suggestion) =>
          !(
            suggestion.pipeline === SuggestionPipelines.CarrierInfo &&
            !isCarrierInfoSuggestionsEnabled
          ) &&
          !(
            suggestion.pipeline === SuggestionPipelines.ApptConfirmation &&
            !isAppointmentSuggestionsEnabled
          ) &&
          !(
            suggestion.pipeline === SuggestionPipelines.LoadBuilding &&
            !isLoadBuildingEnabled
          )
      )
      .map((suggestion) => {
        let truthyFilterSuggestions;
        const isLoadSuggestion =
          suggestion.pipeline === SuggestionPipelines.CarrierInfo ||
          suggestion.pipeline === SuggestionPipelines.ApptConfirmation;

        const isLoadBuildingSuggestion =
          suggestion.pipeline === SuggestionPipelines.LoadBuilding;

        if (suggestion.pipeline === SuggestionPipelines.CheckCall) {
          truthyFilterSuggestions = Object.fromEntries(
            Object.entries(suggestion.suggested.checkCallChanges).filter(
              ([_, v]) => !!v
            )
          );
          return {
            ...suggestion,
            suggested: {
              checkCallChanges: truthyFilterSuggestions,
            },
          };
        }

        truthyFilterSuggestions = filterSuggestions(suggestion.suggested);
        if (isLoadBuildingSuggestion) {
          return {
            ...suggestion,
            suggested: truthyFilterSuggestions,
          } as LoadBuildingSuggestions;
        }
        return isLoadSuggestion
          ? ({
              ...suggestion,
              suggested: truthyFilterSuggestions,
            } as SuggestedLoadChange)
          : ({
              ...suggestion,
              suggested: truthyFilterSuggestions,
            } as SuggestedQuoteChange);
      });

    setOptionalSuggestions(optionalSuggestions);

    // Initialize currentState with filtered suggestions
    setCurrentState((prevState) => ({
      ...prevState,
      curSuggestionList: optionalSuggestions,
    }));
  }, [
    initRawSuggestions,
    isAppointmentSuggestionsEnabled,
    isCarrierInfoSuggestionsEnabled,
    isLoadBuildingEnabled,
    isBatchQuoteEnabled,
  ]);

  // Update suggestion list when user applies/rejects a suggestion in the carousel
  useEffect(() => {
    setOptionalSuggestions(curSuggestionList);
  }, [curSuggestionList]);

  const handleGoToSuggestion = async ({
    suggestionID,
    suggestionPipeline,
  }: {
    suggestionID?: number;
    suggestionPipeline?: SuggestionPipelines;
  }) => {
    const targetSuggestionIndex = optionalSuggestions.findIndex((suggestion) =>
      suggestionID
        ? suggestion.id === suggestionID
        : suggestion.pipeline === suggestionPipeline
    );

    if (targetSuggestionIndex > -1 && carouselRef.current) {
      carouselRef.current.goTo(targetSuggestionIndex, true);
    }
  };

  const handleGetDisplayedSuggestion = () => {
    if (!carouselRef.current) return;
    return optionalSuggestions[currentCarouselIndex];
  };

  useEffect(() => {
    // Re-register goToSuggestionInCarousel when optionalSuggestions changes because
    // goToSuggestionInCarousel uses the optionalSuggestions array value when the function is defined/registered which is empty,
    // not the current value of optionalSuggestions when it's called
    setCurrentState((prevState) => ({
      ...prevState,
      goToSuggestionInCarousel: handleGoToSuggestion,
      getDisplayedSuggestion: handleGetDisplayedSuggestion,
    }));
  }, [carouselRef, optionalSuggestions]);

  const handleClearSuggestion = async (
    e: React.MouseEvent<SVGSVGElement>,
    suggestion: GenericSuggestion
  ): Promise<void> => {
    e.preventDefault();
    e.stopPropagation();

    const res = await skipSuggestion(suggestion!.id);

    if (res.isOk()) {
      toast({
        description: 'Suggestion skipped.',
        variant: 'default',
      });

      setOptionalSuggestions(
        optionalSuggestions.filter((s) => s.id !== suggestion.id)
      );
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
    }
  };

  const handleApplySuggestion = (suggestion: GenericSuggestion): void => {
    setCurrentState((prevState) => ({
      ...prevState,
      clickedSuggestion: suggestion,
    }));

    const eventNamePosthog = getClickedSuggestionEventNamePosthog(
      suggestion.pipeline
    );
    if (suggestion.pipeline) {
      posthog?.capture(eventNamePosthog, {
        suggestionID: suggestion.id,
        serviceID: user?.service_id,
      });
    }
  };

  const renderBatchQuoteIndicator = (
    displayedSuggestion: GenericSuggestion | undefined,
    isBatchQuoteEnabled: boolean
  ) => {
    const quickQuoteSuggestions = optionalSuggestions.filter(
      (s) => s.pipeline === SuggestionPipelines.QuickQuote
    );
    if (
      quickQuoteSuggestions.length > 1 &&
      displayedSuggestion?.pipeline === SuggestionPipelines.QuickQuote &&
      isBatchQuoteEnabled
    ) {
      return (
        <div className='ml-6 mb-2 text-xs font-medium flex flex-col gap-1'>
          <span
            aria-label='Multiple quotes detected'
            className='flex items-center gap-1.5 font-medium text-xs'
          >
            <List className='h-4 w-4 text-info-main' />
            <Typography variant='body-xs'>Batch Quote detected</Typography>
            <Tooltip>
              <TooltipTrigger>
                <InfoIcon className='h-4 w-4' />
              </TooltipTrigger>
              <TooltipContent className='ml-[-100px] max-w-[240px] text-xs flex flex-col gap-1 my-2'>
                <span
                  aria-label='Multiple quotes detected'
                  className='flex items-center gap-1.5 font-semibold'
                >
                  <SparklesIcon className='w-4 h-4 text-info-main' />
                  Multiple requests for quotes detected.
                </span>
                <span className='ml-5 text-neutral-600'>
                  Quote them all at once with{' '}
                  <span className='font-bold text-neutral-700'>
                    Batch Quote
                  </span>{' '}
                  below.
                </span>
              </TooltipContent>
            </Tooltip>
          </span>
        </div>
      );
    }

    return null;
  };

  return (
    <TooltipProvider>
      {optionalSuggestions.length > 0 && (
        <div className='w-[calc(100%-32px)] mt-0 mb-8 mx-auto relative'>
          {renderBatchQuoteIndicator(
            optionalSuggestions[currentCarouselIndex],
            isBatchQuoteEnabled
          )}

          <Carousel
            ref={carouselRef}
            arrows={optionalSuggestions.length > 1}
            dots={optionalSuggestions.length > 1}
            className={optionalSuggestions.length > 1 ? '' : 'single-card'}
            beforeChange={(_, next) => setCurrentCarouselIndex(next)}
          >
            {(() => {
              // Group suggestions by pipeline
              const groupedSuggestions = groupBy(
                optionalSuggestions,
                (s) => s.pipeline
              );

              // Flatten grouped suggestions into a single array with position within pipeline group
              const suggestionsWithPositions = Object.entries(
                groupedSuggestions
              ).flatMap(([_, pipelineSuggestions]) =>
                pipelineSuggestions.map((suggestion, index) => ({
                  ...suggestion,
                  position: index + 1,
                  total: pipelineSuggestions.length,
                }))
              );

              return suggestionsWithPositions.map(
                ({ position, total, ...suggestion }) => {
                  const changes =
                    suggestion.pipeline === SuggestionPipelines.CheckCall
                      ? suggestion.suggested.checkCallChanges
                      : suggestion.suggested;

                  const flattenedChanges = flattenSuggestionChanges(changes);

                  const { elements } = getValidChangeElements(
                    flattenedChanges,
                    suggestion
                  );

                  if (!elements || !elements.length || elements.length === 0)
                    return;

                  return (
                    <SuggestionCard
                      key={`${suggestion.pipeline}-${suggestion.id}`}
                      suggestion={suggestion}
                      clickedSuggestion={clickedSuggestion}
                      handleClearSuggestion={handleClearSuggestion}
                      handleApplySuggestion={handleApplySuggestion}
                      // Show 1 of X only if there are multiple suggestions
                      pipelineGroupIndex={
                        optionalSuggestions.length > 1 ? position : null
                      }
                      pipelineGroupTotal={
                        optionalSuggestions.length > 1 ? total : null
                      }
                    />
                  );
                }
              );
            })()}
          </Carousel>
        </div>
      )}
    </TooltipProvider>
  );
}

/**
 * Generates a list of JSX elements displaying the valid changes between
 * `changesDisplayed` and `suggestion`, and returns the count of valid changes.
 *
 * A valid change includes:
 * - A label corresponding to a suggestion field.
 * - A valid value for that field.
 *
 * If a value is a date (detected by `dayjs`), it is formatted as 'MMM D, YYYY HH:mm A'.
 *
 * @param {SuggestionChangeRecord} changesDisplayed - The displayed changes object containing key-value pairs of changes.
 * @param {GenericSuggestion} suggestion - The suggestion data used to fetch the change label.
 * @returns {{ elements: (JSX.Element | undefined)[], validChangesCount: number }} An object with:
 *  - `elements`: An array of JSX elements representing valid changes.
 *  - `validChangesCount`: The count of valid change elements which is used in the `Show +X changes`/`Collapse` button.
 */
export const getValidChangeElements = (
  changesDisplayed: SuggestionChangeRecord,
  suggestion: GenericSuggestion
): { elements: Undef<JSX.Element>[]; validChangesCount: number } => {
  if (suggestion.pipeline === SuggestionPipelines.LoadBuilding) {
    const lbCard = LoadBuildingSuggestionCard(suggestion);
    if (lbCard) {
      return { elements: [lbCard], validChangesCount: 0 };
    }
  } else if (suggestion.pipeline === SuggestionPipelines.QuickQuote) {
    const qqCard = QuickQuoteCard(suggestion);
    if (qqCard) return { elements: [qqCard], validChangesCount: 0 };
  }

  const changesDisplayedList = Object.entries(changesDisplayed);

  if (!changesDisplayedList.length)
    return { elements: [], validChangesCount: 0 };

  let validChangesCount = 0;

  // Dynamically order fields: Priority fields first, then the rest
  const priorityOrder = getFieldOrder(suggestion.pipeline);
  const orderedFields = reorderFields(changesDisplayedList, priorityOrder);

  const elements = orderedFields
    .map(([name, val]) => {
      const changeLabel = getSuggestionFormattedLabel(
        suggestion.pipeline,
        name
      );

      // --- Begin timezone-aware date formatting logic ---
      let changeValue = val;
      const isDateField =
        val &&
        typeof val === 'string' &&
        val.length > 8 &&
        !isLikelyPhoneNumber(val) &&
        dayjs(val).isValid();

      // Try to find a corresponding timezone field
      let timezoneField: string | null = null;
      if (name.endsWith('Time')) {
        // e.g., pickupApptTime -> pickupApptTimezone
        timezoneField = `${name}zone`;
      }
      const timezoneValue =
        timezoneField && (changesDisplayed[timezoneField] as string);

      if (isDateField) {
        if (timezoneField && timezoneValue) {
          // Timezone present: display in the formatted timezone (e.g. 2023-06-19T07:00:00-07:00) -> 2023-06-19 07:00
          changeValue = dayjs(val).tz(timezoneValue).format('YYYY-MM-DD HH:mm');
        } else {
          // No timezone: display as-is, just strip the Z if present, and format for readability
          // (e.g., 2023-06-19T07:00:00Z -> 2023-06-19 07:00)
          //
          // If timezone-aware timestamp is present, display in the formatted timezone
          // (e.g. 2023-06-19T07:00:00-07:00) -> 2023-06-19 07:00
          changeValue = val.replace('T', ' ').replace('Z', '').slice(0, 16);
        }
      }
      // --- End timezone-aware date formatting logic ---

      if (!changeLabel || !changeValue) return;

      validChangesCount += 1;

      return (
        <KeyValueElement
          name={name}
          changeLabel={changeLabel}
          changeValue={changeValue}
        />
      );
    })
    .filter(Boolean); // Remove null values

  return { elements, validChangesCount };
};

const getClickedSuggestionEventNamePosthog = (
  pipeline: SuggestionPipelines
) => {
  switch (pipeline) {
    case SuggestionPipelines.CarrierInfo:
      return ButtonNamePosthog.CarrierInfoSuggestionClick;
    case SuggestionPipelines.ApptConfirmation:
      return ButtonNamePosthog.ApptConfirmationSuggestionClick;
    case SuggestionPipelines.CheckCall:
      return ButtonNamePosthog.CheckCallSuggestionClick;
    case SuggestionPipelines.QuickQuote:
      return ButtonNamePosthog.QuickQuoteSuggestionClick;
    case SuggestionPipelines.LoadBuilding:
      return ButtonNamePosthog.LoadBuildingSuggestionClick;
  }
};

// util function to handle for nested objects
const filterSuggestions = (obj: any): any =>
  isValidNonDateObject(obj)
    ? Object.fromEntries(
        Object.entries(obj)
          .filter(([_, value]) => value !== '' && !!value) // Filters out falsy values
          .map(([key, value]) => [
            key,
            // Recursively filter if value is a nested object
            isValidNonDateObject(value) ? filterSuggestions(value) : value,
          ])
      )
    : obj;

const suggestionSortComparator = (
  a: GenericSuggestion,
  b: GenericSuggestion
) => {
  // This makes sure that QQ suggestions always show up before LB
  if (
    a.pipeline === 'quick_quote_pipeline' &&
    b.pipeline === 'load_building_pipeline'
  )
    return -1;
  if (
    a.pipeline === 'load_building_pipeline' &&
    b.pipeline === 'quick_quote_pipeline'
  )
    return 1;

  // For other pipelines, maintain default order
  return 0;
};
