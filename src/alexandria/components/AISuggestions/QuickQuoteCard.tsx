import { JSX } from 'react';

import { NewShipmentCardCore } from 'components/AISuggestions/CoreNewShipmentCard';
import { KeyValueElement } from 'components/AISuggestions/SuggestionsCard';
import { CompanyCoreInfo, formatAddressCityStateZip } from 'types/Load';
import { Stop } from 'types/QuoteRequest';
import { Undef } from 'types/UtilityTypes';
import { SuggestedQuoteChange } from 'types/suggestions/QuoteSuggestions';

export const QuickQuoteCard = (
  suggestion: SuggestedQuoteChange
): Undef<JSX.Element> => {
  const sug = suggestion.suggested;

  const pickupAddr =
    formatQQAddress(sug.pickupCity, sug.pickupState, sug.pickupZip) ??
    'Unknown';
  const dropoffAddr =
    formatQQAddress(sug.deliveryCity, sug.deliveryState, sug.deliveryZip) ??
    'Unknown';

  if (!pickupAddr && !dropoffAddr) {
    return undefined;
  }

  // Calculate intermediate stops
  const intermediateStopsCount = calculateIntermediateStops(sug.stops);

  return (
    <NewShipmentCardCore
      pickupAddr={pickupAddr}
      dropoffAddr={dropoffAddr}
      pickupDate={''} // Don't show pickup date for quick quote carousel suggestion
      dropoffDate={''} // Don't show dropoff date for quick quote carousel suggestion
      additionalInfo={[
        <KeyValueElement
          name='transportType'
          changeLabel='Transport Type'
          changeValue={sug.transportType}
        />,
      ]}
      countIntermediateStops={intermediateStopsCount}
    />
  );
};

const formatQQAddress = (city: string, state: string, zip: string): string => {
  if (!city && !state && !zip) {
    return '';
  }

  const address: CompanyCoreInfo = {
    city: city,
    state: state,
    zipCode: zip,
  } as CompanyCoreInfo;

  return formatAddressCityStateZip(address);
};

const calculateIntermediateStops = (
  stops?: Array<Stop> | Record<string, Stop>
): number => {
  if (!stops) {
    return 0;
  }

  // Handle case where stops is an object with numeric keys instead of an array
  const stopsArray = Array.isArray(stops) ? stops : Object.values(stops);

  if (stopsArray.length <= 2) {
    return 0;
  }

  // Count stops that are not pickup or dropoff (intermediate stops)
  return stopsArray.filter(
    (stop) => stop.stopType !== 'pickup' && stop.stopType !== 'dropoff'
  ).length;
};
