import { useState } from 'react';

import { CheckIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import KitSettingsIcon from 'icons/KitSettingsIcon';
import { Maybe } from 'types/UtilityTypes';
import { cn } from 'utils/shadcn';

type KitDefaultValueTooltipProps = {
  inputValue: string;
  isDefaultValue: boolean;
  setIsDefaultValue: (value: boolean) => void;
  updateHandler: (newValue: string) => Promise<boolean>;
  tooltipTextFormatter: (value: string) => string;
  buttonName?: Maybe<string>;
};

export default function KitDefaultValueTooltip({
  inputValue,
  isDefaultValue,
  setIsDefaultValue,
  updateHandler,
  tooltipTextFormatter,
  buttonName = null,
}: KitDefaultValueTooltipProps) {
  const [hasUpdated, setHasUpdated] = useState(false);

  const handleUpdate = async () => {
    const success = await update<PERSON>and<PERSON>(inputValue);
    if (success) {
      setIsDefaultValue(true);
      setHasUpdated(true);
      setTimeout(() => setHasUpdated(false), 2000);
    }
  };

  const shouldShowTooltip = !isDefaultValue || hasUpdated;

  return shouldShowTooltip ? (
    <Button
      className={cn(
        'absolute h-4 p-0 -top-[24px] -right-[4px] border-none',
        hasUpdated ? 'cursor-default' : 'cursor-pointer'
      )}
      variant='ghost'
      type='button'
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        !hasUpdated && handleUpdate();
      }}
      buttonNamePosthog={buttonName}
    >
      {hasUpdated ? (
        <Tooltip open={true}>
          <TooltipTrigger asChild>
            <CheckIcon className='stroke-info-main h-4 w-4 mt-1' />
          </TooltipTrigger>
          <TooltipContent>Updated!</TooltipContent>
        </Tooltip>
      ) : (
        <Tooltip delayDuration={10}>
          <TooltipTrigger>
            <KitSettingsIcon className='fill-info-main h-[18px] w-[18px] animate-wobble-repeat-1' />
          </TooltipTrigger>
          <TooltipContent>{tooltipTextFormatter(inputValue)}</TooltipContent>
        </Tooltip>
      )}
    </Button>
  ) : null;
}
