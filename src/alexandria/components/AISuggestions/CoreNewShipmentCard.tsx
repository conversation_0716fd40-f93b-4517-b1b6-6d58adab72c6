import { JSX } from 'react';

import { ArrowRight, Calendar, MapPin } from 'lucide-react';

import { Flex } from 'components/layout';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { Maybe } from 'types/UtilityTypes';

type NewShipmentCoreProps = {
  pickupAddr: string;
  dropoffAddr: string;
  pickupDate: string;
  dropoffDate: string;
  additionalInfo?: Maybe<JSX.Element>[];
  countIntermediateStops?: number;
};

export const NewShipmentCardCore = ({
  pickupAddr,
  dropoffAddr,
  pickupDate,
  dropoffDate,
  additionalInfo,
  countIntermediateStops,
}: NewShipmentCoreProps) => {
  const {
    serviceFeaturesEnabled: { isMultiStopQuickQuoteEnabled },
  } = useServiceFeatures();

  return (
    <>
      <div className='space-y-1 text-xs'>
        <div className='max-w-full overflow-hidden grid grid-cols-[1fr_auto_1fr] items-center text-neutral-600'>
          {/* Pickup Address */}
          <Flex align='center'>
            <div className='shrink-0 w-3 mr-1 text-neutral-600'>
              <MapPin className='w-3' />
            </div>
            <div className='line-clamp-3 text-ellipsis text-xs'>
              {pickupAddr}
            </div>
          </Flex>

          <Flex justify='center' className='self-center mx-0.5'>
            {isMultiStopQuickQuoteEnabled &&
              countIntermediateStops !== undefined &&
              countIntermediateStops > 0 && (
                <span className='text-xs'>{`+${countIntermediateStops}`}</span>
              )}

            <ArrowRight className='w-4 h-4 text-neutral-600' />
          </Flex>

          {/* Dropoff Address */}
          <Flex align='center'>
            <div className='shrink-0 w-3 mr-1 text-neutral-600'>
              <MapPin className='w-3' />
            </div>
            <div className='line-clamp-3 text-ellipsis text-xs'>
              {dropoffAddr}
            </div>
          </Flex>
        </div>

        {/* Dates row */}
        <div className='grid grid-cols-[1fr_auto_1fr] gap-x-1 text-neutral-600'>
          {/* Pickup Date */}
          {pickupDate && (
            <Flex align='center'>
              <div className='shrink-0 w-3 mr-1 text-neutral-600'>
                <Calendar className='w-3' />
              </div>
              <div className='line-clamp-2 text-ellipsis text-xs'>
                {pickupDate}
              </div>
            </Flex>
          )}
          <div className='w-4' /> {/* Spacer to match arrow width */}
          {/* Dropoff Date */}
          {dropoffDate && (
            <Flex align='center'>
              <div className='shrink-0 w-3 mr-1 text-neutral-600'>
                <Calendar className='w-3' />
              </div>
              <div className='line-clamp-2 text-ellipsis text-xs'>
                {dropoffDate}
              </div>
            </Flex>
          )}
        </div>
      </div>

      <div className='mt-1 text-neutral-600'>{additionalInfo}</div>
    </>
  );
};
