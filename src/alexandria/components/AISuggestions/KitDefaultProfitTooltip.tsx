import { useState } from 'react';

import { CheckIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import { toast } from 'hooks/useToaster';
import KitSettingsIcon from 'icons/KitSettingsIcon';
import { updateUserDefaultPriceMargin } from 'lib/api/updateUserDefaultPriceMargin';
import { ProfitType } from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

type KitDefaultProfitTooltipProps = {
  // Form values
  profitInputValue: any;
  profitInputType: ProfitType;

  // Default Profit Type State
  isDefaultProfitType: boolean;
  setIsDefaultProfitType: (value: boolean) => void;

  // Default Profit Price State
  isDefaultProfit: boolean;
  setIsDefaultProfit: (value: boolean) => void;
};

export default function KitDefaultProfitTooltip({
  profitInputValue,
  profitInputType,
  isDefaultProfit,
  isDefaultProfitType,
  setIsDefaultProfit,
  setIsDefaultProfitType,
}: KitDefaultProfitTooltipProps) {
  const [hasUpdatedDefaultProfit, setHasUpdatedDefaultProfit] = useState(false);

  const handleUpdateDefaultProfit = async (newProfit: number) => {
    if (!newProfit) {
      toast({
        description: 'Please enter a valid profit',
        variant: 'destructive',
      });
      return;
    }

    const resp = await updateUserDefaultPriceMargin(newProfit, profitInputType);

    if (!resp.isOk()) {
      toast({
        description: 'Failed to update default value',
        variant: 'destructive',
      });
      return;
    }

    setIsDefaultProfit(true);
    setIsDefaultProfitType(true);
    setHasUpdatedDefaultProfit(true);
    // Reset copied state after a delay
    setTimeout(() => setHasUpdatedDefaultProfit(false), 2000);
  };

  // Show tooltip if the margin is not default or if it tooltip is in updated state
  const shouldShowTooltip =
    !isDefaultProfit || !isDefaultProfitType || hasUpdatedDefaultProfit;

  return shouldShowTooltip ? (
    <Button
      className={cn(
        'absolute h-4 p-0 -top-[24px] -right-[4px] border-none',
        hasUpdatedDefaultProfit ? 'cursor-default' : 'cursor-pointer'
      )}
      variant='ghost'
      type='button'
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        !hasUpdatedDefaultProfit &&
          handleUpdateDefaultProfit(Number(profitInputValue));
      }}
      buttonNamePosthog={ButtonNamePosthog.UpdateDefaultProfit}
    >
      {hasUpdatedDefaultProfit ? (
        <Tooltip open={true}>
          <TooltipTrigger asChild>
            <CheckIcon className='stroke-info-main h-4 w-4 mt-1' />
          </TooltipTrigger>
          <TooltipContent>Updated!</TooltipContent>
        </Tooltip>
      ) : (
        <Tooltip delayDuration={10}>
          <TooltipTrigger>
            <KitSettingsIcon className='fill-info-main h-[18px] w-[18px] animate-wobble-repeat-1' />
          </TooltipTrigger>
          <TooltipContent>
            {`Click to save ` +
              `${profitInputType === ProfitType.Amount ? '$' : ''}` +
              `${profitInputValue}` +
              `${profitInputType === ProfitType.Percentage ? '%' : ''}` +
              ` as default profit`}
          </TooltipContent>
        </Tooltip>
      )}
    </Button>
  ) : null;
}
