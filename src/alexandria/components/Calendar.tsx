import * as React from 'react';
import { DayFlag, DayPicker, SelectionState, UI } from 'react-day-picker';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { buttonVariants } from 'components/Button';
import { cn } from 'utils/shadcn';

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('p-3', className)}
      classNames={{
        [UI.Months]:
          'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
        [UI.Month]: 'flex flex-col space-y-4',
        [UI.MonthCaption]: 'inline-flex mx-auto mb-4',
        [UI.CaptionLabel]: 'text-sm font-medium',
        [UI.Nav]: 'space-x-1 flex items-center',
        [UI.PreviousMonthButton]: cn(
          buttonVariants({ variant: 'outline' }),
          'absolute h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 top-6 left-4'
        ),
        [UI.NextMonthButton]: cn(
          buttonVariants({ variant: 'outline' }),
          'absolute h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 top-6 right-4'
        ),
        [UI.MonthGrid]: 'w-full border-collapse space-y-1',
        [UI.Weekdays]: 'flex',
        [UI.Weekday]:
          'text-neutral-400 rounded-md w-9 font-normal text-[0.8rem] text-center',
        [UI.Week]: 'flex w-full mt-2',
        [UI.Day]: 'h-9 w-9 text-center p-0 relative rounded',
        [UI.DayButton]: cn(
          buttonVariants({ variant: 'ghost' }),
          'h-9 w-9 p-0 font-normal text-sm aria-selected:opacity-100'
        ),
        [SelectionState.selected]:
          'bg-brand-main text-neutral-50 hover:bg-brand-600 hover:text-neutral-50 focus:bg-brand-600 focus:text-neutral-50',
        [DayFlag.today]: 'text-brand-main',
        [DayFlag.outside]:
          'day-outside text-neutral-400 aria-selected:bg-brand-main/50 aria-selected:text-neutral-400',
        [DayFlag.disabled]: 'text-neutral-400 opacity-50',
        [SelectionState.range_middle]:
          'aria-selected:bg-brand-main aria-selected:text-neutral-50',
        [DayFlag.hidden]: 'invisible',
      }}
      components={{
        Chevron: ({ orientation, ...props }) => {
          if (orientation === 'left') {
            return <ChevronLeft className='h-4 w-4' {...props} />;
          }
          return <ChevronRight className='h-4 w-4' {...props} />;
        },
      }}
      {...props}
    />
  );
}
Calendar.displayName = 'Calendar';

export { Calendar };
