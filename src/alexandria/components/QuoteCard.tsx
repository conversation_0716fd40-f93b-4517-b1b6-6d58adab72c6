import React, { JSX, useCallback, useState } from 'react';

import _ from 'lodash';
import {
  ArrowLeft,
  ArrowRight,
  BadgeDollarSignIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CircleCheckIcon,
  CoinsIcon,
  InfoIcon,
  Loader2,
  MapPin,
  Navigation,
  ShieldCheckIcon,
  Truck,
} from 'lucide-react';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from 'components/Collapsible';
import QuoteConfidenceLevel from 'components/QuoteConfidenceLevel';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { getDATMarketInformation } from 'lib/api/getDATMarketInformation';
import { QuickQuoteResponseStop, RouteSegment } from 'lib/api/getQuickQuote';
import {
  DATQuoteLocationType,
  DATQuoteTimeframe,
  LaneTier,
  QuoteFormStop,
  QuoteTypeInSource,
  SelectedQuoteType,
  laneTierMap,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { TransportType } from 'types/QuoteRequest';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { Quoting } from 'types/enums/Integrations';
import { formatCurrency } from 'utils/formatCurrency';
import { titleCase } from 'utils/formatStrings';
import { getDrumkitContainer } from 'utils/getDrumkitContainer';
import { cn } from 'utils/shadcn';

import { Button } from './Button';
import { DATAreaTypeSlider } from './DATAreaTypeSlider';
import { DATTimeframeSlider } from './DATTimeframeSlider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './Tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipTrigger,
} from './Tooltip';
import { Flex, Grid } from './layout';
import { Typography } from './typography';

// TODO: Deduplicate with Metadata
export type TooltipContentType = {
  typeInSource: QuoteTypeInSource;
  isMultiStop: boolean;
  stops: QuoteFormStop[];
  routeSegments?: RouteSegment[];
  // GS specific
  isZipCodeLookup?: boolean;
  // DAT specific
  timeframe?: string;
  originName?: string;
  originType?: DATQuoteLocationType | LaneTier;
  destinationName?: string;
  destinationType?: DATQuoteLocationType | LaneTier;
  reports?: number;
  companies?: number;
  stopFeeUSDMedium?: number;
};

export type PriceRangeType = {
  lowEstimate: number;
  highEstimate: number;
};

export type DATMarketInformation = {
  stopLocation: string; // e.g. "Chicago, IL"
  inboundLoadToTruckRatio: number;
  inboundMarketConditionsIndexPrevDay: number;
  inboundMarketConditionsIndexPrevWeek: number;
  inboundMarketConditionsForecastNextDay: number;
  outboundLoadToTruckRatio: number;
  outboundMarketConditionsIndexPrevDay: number;
  outboundMarketConditionsIndexPrevWeek: number;
  outboundMarketConditionsForecastNextDay: number;
};

export type QuoteCardType = {
  type: SelectedQuoteType; // e.g. NETWORK, BUYPOWER, DAT
  // type: QuoteTypeInSource;
  title: string; // e.g. "Network Quote"
  subtitle?: string; // e.g. "Longest Leg"
  icon: React.ReactNode; // e.g. <GreenscreensLogo />
  cost: number; // e.g. 1125 (flat number, not a function)
  priceRange: Maybe<PriceRangeType>; // e.g. $1125 - $1350
  confidence: Maybe<number>; // e.g. 85 (confidence out of 100)
  costPerMile?: number; // e.g. 2.10 (flat number, not a function)
  priceRangePerMile?: Maybe<PriceRangeType>; // e.g. $2.05 - $2.15
  inputtedTransportType: TransportType; // Greenscreens does not support some enums like HOTSHOT and BOXTRUCK,
  actualTransportType: TransportType; // so the data is proxied with other types. We want to inform the user which data source was used
  quotingIntegrationName?: string; // e.g. "Greenscreens", "DAT", "Truckstop"

  // Additional props needed for fetching market conditions
  stops: QuickQuoteResponseStop[];
  isMultiStop?: boolean; // Flag to identify multi-stop quotes
  legs?: RouteSegment[]; // Individual leg data for multi-stop quotes
} & (
  | {
      tooltipContent: MaybeUndef<TooltipContentType>;
      tooltipConstructor: (content: TooltipContentType) => JSX.Element; // e.g. DATTooltipConstructor
    }
  | {
      tooltipContent?: undefined;
      tooltipConstructor?: undefined;
    }
);

type QuoteCardProps = {
  carrier: QuoteCardType;
  isSelected: boolean;
  onClick: () => void;
  lowConfidenceThreshold: number;
  mediumConfidenceThreshold: number;
  onUpdateRateView?: (
    timeframe: DATQuoteTimeframe,
    areaType: DATQuoteLocationType,
    setUpdateRateViewError: (error: Maybe<string>) => void
  ) => void;
  stops: QuickQuoteResponseStop[];
};

export const QuoteCard = ({
  carrier,
  isSelected,
  onClick,
  lowConfidenceThreshold,
  mediumConfidenceThreshold,
  onUpdateRateView,
}: QuoteCardProps) => {
  const [expandedTargetBuy, setExpandedTargetBuy] = useState(false);
  const [expandedPriceRange, setExpandedPriceRange] = useState(false);

  const {
    serviceFeaturesEnabled: { isUpdateRateViewEnabled },
  } = useServiceFeatures();

  const roundedCost = _.round(carrier.cost);
  const roundedCostPerMile = carrier.costPerMile
    ? _.round(carrier.costPerMile, 2)
    : null;

  const [isMarketInformationExpanded, setIsMarketInformationExpanded] =
    useState(false);
  const [isMarketInformationLoading, setIsMarketInformationLoading] =
    useState(false);
  const [marketInformationData, setMarketInformationData] = useState<
    Maybe<{
      pickupMarketInformation?: DATMarketInformation;
      dropoffMarketInformation?: DATMarketInformation;
    }>
  >();

  const {
    serviceFeaturesEnabled: {
      isDATMarketConditionsEnabled,
      isDATMarketConditionsForecastEnabled,
      isDATLoadToTruckRatioEnabled,
    },
    quotingIntegrations,
  } = useServiceFeatures();

  const roundedPriceRange = carrier.priceRange
    ? {
        lowEstimate: _.round(carrier.priceRange.lowEstimate),
        highEstimate: _.round(carrier.priceRange.highEstimate),
      }
    : null;

  const roundedPriceRangePerMile = carrier.priceRangePerMile
    ? {
        lowEstimate: _.round(carrier.priceRangePerMile.lowEstimate, 2),
        highEstimate: _.round(carrier.priceRangePerMile.highEstimate, 2),
      }
    : null;

  const roundedConfidence = carrier.confidence
    ? _.round(carrier.confidence)
    : null;

  const isDATRateView = carrier.type === SelectedQuoteType.DAT_RATEVIEW;
  const [updateRateViewError, setUpdateRateViewError] =
    useState<Maybe<string>>(null);

  const fetchMarketInformation = useCallback(async () => {
    const datIntegration = quotingIntegrations.find(
      (integration) => integration.name === Quoting.DAT
    );

    const isAnyMarketInformationEnabled =
      isDATMarketConditionsEnabled ||
      isDATMarketConditionsForecastEnabled ||
      isDATLoadToTruckRatioEnabled;

    if (
      !isAnyMarketInformationEnabled ||
      !isDATRateView ||
      !datIntegration ||
      !carrier.stops
    ) {
      return;
    }

    setIsMarketInformationLoading(true);

    try {
      const result = await getDATMarketInformation({
        integrationID: datIntegration?.id,
        transportType: carrier.actualTransportType,
        stops: carrier.stops,
      });

      if (result.isOk()) {
        setMarketInformationData({
          pickupMarketInformation: result.value.pickupMarketInformation,
          dropoffMarketInformation: result.value.dropoffMarketInformation,
        });
      }
    } catch (error) {
      console.error('Failed to fetch market conditions:', error);
    } finally {
      setIsMarketInformationLoading(false);
    }
  }, [
    isDATMarketConditionsEnabled,
    isDATMarketConditionsForecastEnabled,
    isDATLoadToTruckRatioEnabled,
    isDATRateView,
    quotingIntegrations,
    carrier,
  ]);

  const handleMarketInformationToggle = async () => {
    setIsMarketInformationExpanded(!isMarketInformationExpanded);

    if (
      isMarketInformationExpanded ||
      isMarketInformationLoading ||
      !!marketInformationData
    ) {
      return;
    }

    fetchMarketInformation();
  };

  // Use fetched data if available, otherwise fall back to carrier data
  const pickupMarketInformation =
    marketInformationData?.pickupMarketInformation;
  const dropoffMarketInformation =
    marketInformationData?.dropoffMarketInformation;

  // Check if this quote should show expandable route leg sections
  const shouldShowExpansion =
    carrier.isMultiStop &&
    carrier.legs &&
    carrier.legs.length > 1 &&
    carrier.type === SelectedQuoteType.DAT_LEG_TO_LEG;

  const initialDATTimeframe =
    (carrier.tooltipContent?.timeframe as DATQuoteTimeframe) || null;
  const initialDATLocationType =
    (carrier.tooltipContent?.originType as DATQuoteLocationType) || null;

  const [isChangeLookupExpanded, setIsChangeLookupExpanded] = useState(false);
  const [selectedDATTimeframe, setSelectedDATTimeframe] =
    useState<Maybe<DATQuoteTimeframe>>(initialDATTimeframe);
  const [selectedDATLocationType, setSelectedDATLocationType] = useState<
    Maybe<DATQuoteLocationType>
  >(initialDATLocationType);

  const handleTargetBuyToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedTargetBuy(!expandedTargetBuy);
  };

  const handlePriceRangeToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedPriceRange(!expandedPriceRange);
  };

  const isGSTriumph =
    carrier.type === SelectedQuoteType.GS_BUYPOWER ||
    carrier.type === SelectedQuoteType.GS_NETWORK;

  const getTargetBuyText = () => {
    if (!isGSTriumph) {
      return 'Target Buy';
    }

    if (carrier.type === SelectedQuoteType.GS_BUYPOWER) {
      return 'Verified Buy Rate';
    }

    return 'Market Rate';
  };

  const handleUpdateRateView = () => {
    if (selectedDATTimeframe && selectedDATLocationType) {
      onUpdateRateView?.(
        selectedDATTimeframe,
        selectedDATLocationType,
        setUpdateRateViewError
      );
    }
  };

  const popoverRootContainer = getDrumkitContainer();

  return (
    <TooltipProvider>
      <Flex
        key={carrier.type}
        direction='col'
        className={cn(
          'relative w-full border-2 border-brand-400 rounded-[4px] p-2 transition-transform',
          isSelected
            ? 'bg-brand-50'
            : 'cursor-pointer hover:scale-[1.01] bg-neutral-50'
        )}
        onClick={onClick}
      >
        <Flex justify='between' align='center' className='w-full mb-2'>
          <Flex direction='col'>
            <Flex align='center' gap='xs'>
              <Typography variant='body-sm' weight='semibold'>
                {carrier.title}
              </Typography>
              {carrier.tooltipContent && (
                <Tooltip delayDuration={10}>
                  <TooltipTrigger onClick={(e: any) => e.preventDefault()}>
                    <InfoIcon
                      className={cn(
                        'h-4 w-4 text-neutral-600',
                        carrier.tooltipContent.isMultiStop &&
                          String(carrier.type).includes('LANE_HISTORY') &&
                          'text-error-500'
                      )}
                    />
                  </TooltipTrigger>
                  <TooltipPortal container={popoverRootContainer}>
                    <TooltipContent className='ml-2 max-w-64 whitespace-pre-wrap z-50'>
                      {carrier.tooltipConstructor(carrier.tooltipContent)}
                    </TooltipContent>
                  </TooltipPortal>
                </Tooltip>
              )}
            </Flex>
            {carrier.subtitle && (
              <span className='text-xs text-neutral-600'>
                {carrier.subtitle}
              </span>
            )}
          </Flex>
          {carrier.icon}
        </Flex>
        <Flex direction='col' className='w-full'>
          <Flex justify='between' align='center' className='w-full mt-2'>
            <Flex align='center' className='gap-x-1.5'>
              <Truck className='h-4 w-4' />
              <Typography variant='body-sm'>Transport Type</Typography>
            </Flex>
            {/* If the transport type was proxied, then show the source data + tooltip */}
            {carrier.inputtedTransportType !== carrier.actualTransportType ? (
              <Flex align='baseline' gap='sm'>
                <Tooltip delayDuration={10}>
                  <TooltipTrigger className='border-b border-dashed border-neutral-900'>
                    <Typography variant='body-sm'>
                      {`${titleCase(carrier.actualTransportType)}`}
                    </Typography>
                  </TooltipTrigger>
                  <TooltipPortal container={popoverRootContainer}>
                    <TooltipContent className='mr-1 max-w-60 whitespace-pre-wrap'>
                      <Typography variant='body-xs'>
                        {`${carrier.quotingIntegrationName ? `${carrier.quotingIntegrationName}` : 'Quoting integration'}`}{' '}
                        does not support{' '}
                        {titleCase(carrier.inputtedTransportType)} quotes,
                        showing equivalent{' '}
                        {titleCase(carrier.actualTransportType)} quote.
                      </Typography>
                    </TooltipContent>
                  </TooltipPortal>
                </Tooltip>
              </Flex>
            ) : (
              <Typography variant='body-sm'>
                {`${titleCase(carrier.actualTransportType)}`}
              </Typography>
            )}
          </Flex>

          <Flex justify='between' align='center' className='w-full'>
            <Flex align='center' gap='sm'>
              <BadgeDollarSignIcon className='h-4 w-4' />
              <Typography variant='body-sm'>{getTargetBuyText()}</Typography>
            </Flex>
            <Flex align='center' className='gap-x-2'>
              <Flex align='baseline' className='gap-x-2'>
                <Typography variant='body-xs'>{`$${roundedCost}`}</Typography>
                {roundedCostPerMile && (
                  <Typography variant='body-xs'>
                    {`($${roundedCostPerMile}/mi)`}
                  </Typography>
                )}
              </Flex>
              {shouldShowExpansion && (
                <button
                  onClick={handleTargetBuyToggle}
                  type='button'
                  className='p-0.5 hover:bg-neutral-100 rounded'
                >
                  {expandedTargetBuy ? (
                    <ChevronUpIcon className='h-3 w-3' />
                  ) : (
                    <ChevronDownIcon className='h-3 w-3' />
                  )}
                </button>
              )}
            </Flex>
          </Flex>

          {/* Expanded Target Buy Details */}
          {expandedTargetBuy && shouldShowExpansion && carrier.legs && (
            <QuoteLegBreakdown legs={carrier.legs} type='target' />
          )}

          <Flex justify='between' align='center' className='w-full'>
            {roundedConfidence && (
              <>
                <Flex align='center' gap='sm'>
                  <ShieldCheckIcon className='h-4 w-4' />
                  <Typography variant='body-sm'>Confidence</Typography>
                </Flex>
                <QuoteConfidenceLevel
                  confidence={roundedConfidence}
                  lowConfidenceThreshold={lowConfidenceThreshold}
                  mediumConfidenceThreshold={mediumConfidenceThreshold}
                  hidePercentSign={isGSTriumph}
                />
              </>
            )}
          </Flex>

          <Flex justify='between' align='center' className='w-full'>
            {roundedPriceRange && (
              <>
                <Flex align='center' gap='sm'>
                  <CoinsIcon className='h-4 w-4' />
                  <Typography variant='body-sm'>Price Range</Typography>
                </Flex>
                <Flex align='center' className='gap-x-2'>
                  {roundedPriceRangePerMile ? (
                    <Tooltip delayDuration={10}>
                      <TooltipTrigger className='border-b border-dashed border-neutral-900'>
                        <Typography variant='body-xs'>
                          {`$${roundedPriceRange.lowEstimate} - $${roundedPriceRange.highEstimate}`}
                        </Typography>
                      </TooltipTrigger>
                      {/* TooltipPortal allows tooltip to be rendered outside the parent's container's DOM hierarchy
                      to fix the issue where tooltip is hidden by other quote cards, despite high z-index. */}
                      <TooltipPortal container={popoverRootContainer}>
                        <TooltipContent className='mr-2 max-w-60 whitespace-pre-wrap'>
                          <div className='pb-3'>
                            <Typography variant='body-xs'>
                              {`Price range per mile`}
                            </Typography>
                            <Typography variant='body-xs'>
                              {`$${roundedPriceRangePerMile.lowEstimate} - $${roundedPriceRangePerMile.highEstimate}/mi.`}
                            </Typography>
                          </div>

                          <Typography
                            variant='body-sm'
                            className='border-t pt-3'
                          >
                            All prices include fuel.
                          </Typography>
                        </TooltipContent>
                      </TooltipPortal>
                    </Tooltip>
                  ) : (
                    <Typography variant='body-sm'>
                      {`$${roundedPriceRange.lowEstimate} - $${roundedPriceRange.highEstimate}`}
                    </Typography>
                  )}
                  {shouldShowExpansion && (
                    <button
                      onClick={handlePriceRangeToggle}
                      type='button'
                      className='p-0.5 hover:bg-neutral-100 rounded'
                    >
                      {expandedPriceRange ? (
                        <ChevronUpIcon className='h-3 w-3' />
                      ) : (
                        <ChevronDownIcon className='h-3 w-3' />
                      )}
                    </button>
                  )}
                </Flex>
              </>
            )}
          </Flex>

          {/* Expanded Price Range Details */}
          {expandedPriceRange &&
            shouldShowExpansion &&
            carrier.legs &&
            roundedPriceRange && (
              <QuoteLegBreakdown legs={carrier.legs} type='range' />
            )}

          {/* Market Analytics */}
          {isDATRateView &&
            (isDATMarketConditionsEnabled ||
              isDATMarketConditionsForecastEnabled ||
              isDATLoadToTruckRatioEnabled) && (
              <Collapsible
                open={isMarketInformationExpanded}
                onOpenChange={handleMarketInformationToggle}
                className='w-full'
              >
                <CollapsibleTrigger className='flex items-center justify-between w-full cursor-pointer py-2 text-sm font-medium border-t border-neutral-200 mt-3 pt-3'>
                  <Typography variant='body-sm'>Market Analytics</Typography>
                  {isMarketInformationExpanded ? (
                    <ChevronUpIcon className='h-4 w-4' />
                  ) : (
                    <ChevronDownIcon className='h-4 w-4' />
                  )}
                </CollapsibleTrigger>

                <CollapsibleContent className='pt-3'>
                  {isMarketInformationLoading ? (
                    <div className='flex items-center justify-center py-4'>
                      <Loader2 className='h-4 w-4 animate-spin mr-2' />
                    </div>
                  ) : pickupMarketInformation && dropoffMarketInformation ? (
                    <Tabs defaultValue='pickup' className='w-full'>
                      <TabsList className='grid w-full grid-cols-2 mb-3 m-0'>
                        <TabsTrigger
                          value='pickup'
                          className='text-xs flex items-center gap-1 rounded-b-none'
                        >
                          <MapPin className='h-3 w-3' />
                          <Typography variant='body-xs'>Pickup</Typography>
                        </TabsTrigger>
                        <TabsTrigger
                          value='dropoff'
                          className='text-xs flex items-center gap-1 rounded-b-none'
                        >
                          <Navigation className='h-3 w-3' />
                          <Typography variant='body-xs'>Dropoff</Typography>
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value='pickup' className='mt-0'>
                        <div className='my-3'>
                          <Typography
                            variant='body-xs'
                            className='text-neutral-500 font-medium truncate'
                          >
                            Showing data for:{' '}
                            {formatStopLocation(
                              pickupMarketInformation.stopLocation
                            )}
                          </Typography>
                        </div>
                        {renderDATMarketAnalytics(pickupMarketInformation)}
                      </TabsContent>

                      <TabsContent value='dropoff' className='mt-0'>
                        <div className='my-3'>
                          <Typography
                            variant='body-xs'
                            className='text-neutral-500 font-medium truncate'
                          >
                            Showing data for:{' '}
                            {formatStopLocation(
                              dropoffMarketInformation.stopLocation
                            )}
                          </Typography>
                        </div>
                        {renderDATMarketAnalytics(dropoffMarketInformation)}
                      </TabsContent>
                    </Tabs>
                  ) : (
                    <div className='text-center py-4'>
                      <Typography
                        variant='body-sm'
                        className='text-neutral-500'
                      >
                        No market analytics available
                      </Typography>
                    </div>
                  )}
                </CollapsibleContent>
              </Collapsible>
            )}

          {/* Expanded Price Range Details */}
          {isDATRateView && isUpdateRateViewEnabled && (
            <>
              <Button
                buttonNamePosthog={ButtonNamePosthog.QuickQuoteAddStop}
                type='button'
                variant='ghost'
                onClick={() =>
                  setIsChangeLookupExpanded(!isChangeLookupExpanded)
                }
                className='h-8 flex gap-2 items-center text-xs text-neutral-800 px-4 mx-auto mt-4 mb-2 hover:border-neutral-700 hover:bg-neutral-100'
                size='sm'
              >
                Change lookup
                {isChangeLookupExpanded ? (
                  <ChevronUpIcon className='h-3 w-3' />
                ) : (
                  <ChevronDownIcon className='h-3 w-3' />
                )}
              </Button>

              {isChangeLookupExpanded && (
                <Flex direction='col' gap='md' className='w-full'>
                  <Flex direction='col' gap='sm' className='w-full'>
                    {selectedDATTimeframe && (
                      <Flex direction='col' gap='xs' className='w-full'>
                        <Typography variant='body-sm'>
                          Timeframe (days)
                        </Typography>
                        <DATTimeframeSlider
                          value={selectedDATTimeframe}
                          onChange={(value) =>
                            setSelectedDATTimeframe(value as DATQuoteTimeframe)
                          }
                          bestFitOption={initialDATTimeframe}
                        />
                      </Flex>
                    )}

                    {selectedDATLocationType && (
                      <Flex direction='col' gap='xs' className='w-full'>
                        <Typography variant='body-sm'>Area Type</Typography>
                        <DATAreaTypeSlider
                          value={selectedDATLocationType}
                          onChange={(value) =>
                            setSelectedDATLocationType(
                              value as DATQuoteLocationType
                            )
                          }
                          bestFitOption={initialDATLocationType}
                        />
                      </Flex>
                    )}
                  </Flex>

                  {updateRateViewError && (
                    <Typography
                      variant='body-xs'
                      align='center'
                      className='text-error-500'
                    >
                      {updateRateViewError}
                    </Typography>
                  )}

                  <Button
                    buttonNamePosthog={
                      ButtonNamePosthog.QuickQuoteUpdateRateView
                    }
                    variant='default'
                    className='mt-2 h-8 w-full'
                    type='button'
                    onClick={handleUpdateRateView}
                  >
                    Update RateView
                  </Button>
                </Flex>
              )}
            </>
          )}

          {isSelected && (
            <CircleCheckIcon className='absolute -top-3 -right-2.5 fill-neutral-50 !stroke-success-500 w-6 h-6' />
          )}
        </Flex>
      </Flex>
    </TooltipProvider>
  );
};

const parseDATLocationDetails = (
  type: DATQuoteLocationType,
  name: string
): string => {
  switch (type) {
    case DATQuoteLocationType['3_DIGIT_ZIP']:
      return `${name.toUpperCase()}`;
    case DATQuoteLocationType['MARKET_AREA']:
      return `${name.replace(' Mkt', '')} Market Area`;
    case DATQuoteLocationType['EXTENDED_MARKET_AREA']:
      return `${name.replace(' X-Mkt', '')} Extended Market Area`;
    case DATQuoteLocationType['STATE']:
      return `${name} State`;
    case DATQuoteLocationType['REGION']:
      return `${name} Region`;
    case DATQuoteLocationType['COUNTRY']:
      return `${name} Country`;
    default:
      return '';
  }
};

const parseDATTimeframe = (timeframe: DATQuoteTimeframe): string => {
  return timeframe
    .replace('_', ' ') // Remove underscores for readability
    .replace('DAYS', 'DAY') // Replace plural for conciseness (e.g. 30 day timeframe)
    .toLowerCase();
};

const formatStopLocation = (stopLocation: string): string => {
  // Split by comma and trim whitespace
  const parts = stopLocation.split(',').map((part) => part.trim());

  if (parts.length === 2) {
    const city = titleCase(parts[0]);
    const state = parts[1].toUpperCase();
    return `${city}, ${state}`;
  }

  // Fallback to titleCase if format is unexpected
  return titleCase(stopLocation);
};

export const GreenscreensTooltipConstructor = (
  isZipCodeLookup: boolean
): JSX.Element => {
  return (
    <>
      <Typography variant='body-sm'>
        {isZipCodeLookup
          ? 'This rate is based on your zip code lookup.'
          : 'This rate is based on your city and state lookup.'}
      </Typography>
    </>
  );
};

export const DATTooltipConstructor = (
  content: TooltipContentType
): JSX.Element => {
  return (
    <>
      {/* DAT multi-stop Longest Leg */}
      {content.typeInSource === QuoteTypeInSource.DAT_LONGEST_LEG && (
        <>
          <Typography variant='body-xs' className='mb-2'>
            Longest Leg quote calculated by taking the total distance across all
            legs (leg-to-leg) multiplied by the DAT rate per mile for the
            longest leg plus a{' '}
            {content.stopFeeUSDMedium ? (
              <b>{formatCurrency(content.stopFeeUSDMedium, 'USD', 0)} </b>
            ) : (
              ''
            )}
            fee per intermediate stop.
            <br />
            This approach is best for routes with short intermediate legs.
          </Typography>

          <Typography variant='body-xs'>
            First to last stop base rate based on a
            <b>{` ${parseDATTimeframe(content.timeframe as DATQuoteTimeframe)} `}</b>
            timeframe from
            <b>{` ${parseDATLocationDetails(content.originType as DATQuoteLocationType, content.originName || '')} `}</b>
            to
            <b>{` ${parseDATLocationDetails(content.destinationType as DATQuoteLocationType, content.destinationName || '')}`}</b>
          </Typography>
          {content.companies && content.reports && (
            <Typography variant='body-xs' className='mt-2'>
              Data calculated using <b>{content.reports} reports</b> from{' '}
              <b>{content.companies} companies.</b>
            </Typography>
          )}
        </>
      )}

      {/* DAT multi-stop Leg-to-Leg */}
      {content.typeInSource === QuoteTypeInSource.DAT_LEG_TO_LEG && (
        <>
          <Typography
            variant='body-xs'
            className='mb-2'
            style={{ padding: '6px' }}
          >
            Leg to leg quote calculated by summing each leg's DAT rate. This
            approach combines the point-to-point rates for each leg of the trip.
            The rate is the combined total of each leg summed together.
            <br />
          </Typography>

          {content.companies && content.reports && (
            <Typography
              variant='body-xs'
              className='mt-2'
              style={{ padding: '6px' }}
            >
              Data calculated using <b>{content.reports} reports</b> from{' '}
              <b>{content.companies} companies.</b>
            </Typography>
          )}
        </>
      )}

      {/* DAT 2-stop RateView */}
      {content.typeInSource === QuoteTypeInSource.DAT && (
        <>
          <Typography variant='body-xs'>
            <b>Broker Spot Rate</b> based on a
            <b>{` ${parseDATTimeframe(content.timeframe as DATQuoteTimeframe)} `}</b>
            timeframe from
            <b>{` ${parseDATLocationDetails(content.originType as DATQuoteLocationType, content.originName || '')} `}</b>
            to
            <b>{` ${parseDATLocationDetails(content.destinationType as DATQuoteLocationType, content.destinationName || '')}`}</b>
          </Typography>
          {content.companies && content.reports && (
            <Typography variant='body-xs' className='mt-2'>
              Data calculated using <b>{content.reports} reports</b> from{' '}
              <b>{content.companies} companies.</b>
            </Typography>
          )}
        </>
      )}
    </>
  );
};

export const LaneHistoryTooltipConstructor = (
  content: TooltipContentType
): JSX.Element => {
  return (
    <>
      <Typography>
        Rate based on a<b>{` ${content.timeframe} `}</b>
        from {`${laneTierMap[content.originType as LaneTier]}`} to
        {` ${laneTierMap[content.destinationType as LaneTier]}`}.
      </Typography>
      {content.isMultiStop && (
        <Typography className='text-xs text-neutral-600 mt-2'>
          <b>Note:</b> This quote is based on 2-stop data, from the first and
          last stop in the form. Multi-stop TMS lane history is coming soon.
        </Typography>
      )}
    </>
  );
};

const renderDATMarketAnalytics = (data: DATMarketInformation) => (
  <Flex direction='col' gap='md'>
    <Grid
      className='text-xs w-full'
      style={{
        gridTemplateColumns: 'calc(40% - 16px) calc(30% + 8px) calc(30% + 8px)',
      }}
      gap='sm'
    >
      <div />
      <Flex align='center' justify='center' gap='xs' className='-ml-6'>
        <ArrowRight className='h-3 w-3 !stroke-success' />
        <Typography variant='body-xs' weight='medium' align='center'>
          Inbound
        </Typography>
      </Flex>
      <Flex align='center' justify='center' gap='xs' className='-ml-8'>
        <ArrowLeft className='h-3 w-3 !stroke-info' />
        <Typography variant='body-xs' weight='medium' align='center'>
          Outbound
        </Typography>
      </Flex>
    </Grid>

    {data.inboundLoadToTruckRatio || data.outboundLoadToTruckRatio ? (
      <Grid
        className='text-xs w-full'
        style={{
          gridTemplateColumns:
            'calc(40% - 16px) calc(30% + 8px) calc(30% + 8px)',
        }}
        gap='sm'
      >
        <Typography variant='body-xs' textColor='muted'>
          L/T Ratio
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-4'
        >
          {data.inboundLoadToTruckRatio}
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-6'
        >
          {data.outboundLoadToTruckRatio}
        </Typography>
      </Grid>
    ) : null}

    {data.inboundMarketConditionsIndexPrevDay ||
    data.outboundMarketConditionsIndexPrevDay ? (
      <Grid
        className='text-xs w-full'
        style={{
          gridTemplateColumns:
            'calc(40% - 16px) calc(30% + 8px) calc(30% + 8px)',
        }}
        gap='sm'
      >
        <Typography variant='body-xs' textColor='muted'>
          MCI Yesterday
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-4'
        >
          {data.inboundMarketConditionsIndexPrevDay}
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-6'
        >
          {data.outboundMarketConditionsIndexPrevDay}
        </Typography>
      </Grid>
    ) : null}

    {data.inboundMarketConditionsIndexPrevWeek ||
    data.outboundMarketConditionsIndexPrevWeek ? (
      <Grid
        className='text-xs w-full'
        style={{
          gridTemplateColumns:
            'calc(40% - 16px) calc(30% + 8px) calc(30% + 8px)',
        }}
        gap='sm'
      >
        <Typography variant='body-xs' textColor='muted'>
          MCI Week Avg
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-4'
        >
          {data.inboundMarketConditionsIndexPrevWeek}
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-6'
        >
          {data.outboundMarketConditionsIndexPrevWeek}
        </Typography>
      </Grid>
    ) : null}

    {data.inboundMarketConditionsForecastNextDay ||
    data.outboundMarketConditionsForecastNextDay ? (
      <Grid
        className='text-xs w-full'
        style={{
          gridTemplateColumns:
            'calc(40% - 16px) calc(30% + 8px) calc(30% + 8px)',
        }}
        gap='sm'
      >
        <Typography variant='body-xs' textColor='muted'>
          MCI Tomorrow
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-4'
        >
          {data.inboundMarketConditionsForecastNextDay}
        </Typography>
        <Typography
          variant='body-xs'
          weight='medium'
          align='center'
          className='-ml-6'
        >
          {data.outboundMarketConditionsForecastNextDay}
        </Typography>
      </Grid>
    ) : null}
  </Flex>
);

// New component for expandable leg quote breakdown
export function QuoteLegBreakdown({
  legs,
  type,
  className = '',
  showMileage = false,
}: {
  legs: RouteSegment[];
  type: 'target' | 'range';
  className?: string;
  showMileage?: boolean;
}) {
  const popoverRootContainer = getDrumkitContainer();

  return (
    <Flex
      direction='col'
      className={`w-full pl-4 pr-6 mt-1 text-xs text-neutral-500 ${className}`}
    >
      {legs.map((leg, index) => {
        let label = `Leg ${leg.order + 1}`;
        if (showMileage) {
          label += ` (${_.round(leg.distanceMiles)} mi)`;
        }
        label += ':';
        if (type === 'target') {
          const legCost = leg.rates?.target ? _.round(leg.rates.target) : null;
          const legCostPerMile = leg.rates?.targetPerMile
            ? _.round(leg.rates.targetPerMile, 2)
            : null;
          return (
            <Flex
              key={index}
              justify='between'
              align='center'
              className='w-full mb-1'
            >
              <Flex align='center' gap='xs'>
                <Typography variant='body-xs' className='text-neutral-500'>
                  {label}
                </Typography>
                <Tooltip delayDuration={10}>
                  <TooltipTrigger
                    onClick={(e: React.MouseEvent<HTMLElement>) =>
                      e.preventDefault()
                    }
                  >
                    <InfoIcon className={'h-4 w-4 text-neutral-600'} />
                  </TooltipTrigger>
                  <TooltipPortal container={popoverRootContainer}>
                    <TooltipContent
                      className='ml-2 max-w-64 rounded-sm whitespace-pre-wrap z-50'
                      style={{ borderRadius: '6px' }}
                    >
                      <Typography variant='body-xs' style={{ padding: '6px' }}>
                        {leg.startCityState} - {leg.endCityState}
                      </Typography>
                    </TooltipContent>
                  </TooltipPortal>
                </Tooltip>
              </Flex>

              <Typography
                variant='body-xs'
                className='text-neutral-500 text-right min-w-[90px]'
              >
                {legCost ? (
                  <>
                    ${legCost}
                    {legCostPerMile && ` ($${legCostPerMile}/mi)`}
                  </>
                ) : (
                  'N/A'
                )}
              </Typography>
            </Flex>
          );
        } else {
          const legLow = leg.rates?.low ? _.round(leg.rates.low) : null;
          const legHigh = leg.rates?.high ? _.round(leg.rates.high) : null;
          return (
            <Flex
              key={index}
              justify='between'
              align='center'
              className='w-full mb-1'
            >
              <Flex align='center' gap='xs'>
                <Typography variant='body-xs' className='text-neutral-500'>
                  {label}
                </Typography>
                <Tooltip delayDuration={10}>
                  <TooltipTrigger
                    onClick={(e: React.MouseEvent<HTMLElement>) =>
                      e.preventDefault()
                    }
                  >
                    <InfoIcon className={'h-4 w-4 text-neutral-600'} />
                  </TooltipTrigger>
                  <TooltipPortal container={popoverRootContainer}>
                    <TooltipContent
                      className='ml-2 max-w-64 rounded-sm whitespace-pre-wrap z-50'
                      style={{ borderRadius: '6px' }}
                    >
                      <Typography variant='body-xs' style={{ padding: '6px' }}>
                        {leg.startCityState} - {leg.endCityState}
                      </Typography>
                    </TooltipContent>
                  </TooltipPortal>
                </Tooltip>
              </Flex>

              <Typography
                variant='body-xs'
                className='text-neutral-500 text-right min-w-[90px]'
              >
                {legLow && legHigh ? `$${legLow} - $${legHigh}` : 'N/A'}
              </Typography>
            </Flex>
          );
        }
      })}
    </Flex>
  );
}
