import { JSX } from 'react';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './Tooltip';

type QuoteConfidenceLevelProps = {
  confidence: number;
  lowConfidenceThreshold: number;
  mediumConfidenceThreshold: number;
  hidePercentSign?: boolean;
};

export default function QuoteConfidenceLevel({
  confidence,
  lowConfidenceThreshold,
  mediumConfidenceThreshold,
  hidePercentSign = false,
}: QuoteConfidenceLevelProps): JSX.Element {
  const lowConfidence = lowConfidenceThreshold ?? 70;
  const mediumConfidence = mediumConfidenceThreshold ?? 80;

  const confidenceText = hidePercentSign ? confidence : `${confidence}%`;
  return (
    <TooltipProvider>
      {confidence >= mediumConfidence ? (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className='text-success-main'>{confidenceText}</div>
          </TooltipTrigger>
          <TooltipContent>High Confidence</TooltipContent>
        </Tooltip>
      ) : confidence < mediumConfidence && confidence >= lowConfidence ? (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className='text-warning-main'>{confidenceText}</div>
          </TooltipTrigger>
          <TooltipContent>Medium Confidence</TooltipContent>
        </Tooltip>
      ) : (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className='text-error-main'>{confidenceText}</div>
          </TooltipTrigger>
          <TooltipContent>Low Confidence</TooltipContent>
        </Tooltip>
      )}
    </TooltipProvider>
  );
}
