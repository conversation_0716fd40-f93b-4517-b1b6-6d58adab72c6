import * as React from 'react';

import { type VariantProps, cva } from 'class-variance-authority';

import { cn } from 'utils/shadcn';

const badgeVariants = cva(
  'inline-flex items-center border border-neutral-500 px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'rounded-full border-transparent bg-neutral-900 text-neutral-50 hover:bg-neutral-900/80',
        secondary:
          'border-transparent bg-neutral-100 text-neutral-900 hover:bg-neutral-100/80',
        destructive:
          'border-transparent bg-error-500 text-neutral-50 hover:bg-error-500/80',
        outline: 'rounded-sm text-neutral-900',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  );
}

export { Badge, badgeVariants };
