import { PlusIcon } from 'lucide-react';

import { Button } from './Button';
import { Flex } from './layout';
import { Typography } from './typography';

interface AddActionButtonProps {
  onClick: () => void;
  buttonText: string;
  descriptionText: string;
  buttonNamePosthog: string;
  className?: string;
}

export function AddActionButton({
  onClick,
  buttonText,
  descriptionText,
  buttonNamePosthog,
  className = '',
}: AddActionButtonProps) {
  return (
    <Flex justify='between' align='center' className={className}>
      <Typography variant='body-xs' className='text-neutral-500'>
        {descriptionText}
      </Typography>
      <Button
        onClick={onClick}
        variant='outline'
        size='sm'
        className='h-7 px-2 text-xs'
        buttonNamePosthog={buttonNamePosthog}
        type='button'
      >
        <PlusIcon className='h-3 w-3 mr-1' />
        {buttonText}
      </Button>
    </Flex>
  );
}
