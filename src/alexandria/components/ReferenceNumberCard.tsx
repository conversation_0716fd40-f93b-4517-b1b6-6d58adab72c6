import { JSX } from 'react';

import { XCircleIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { AdditionalReference } from 'types/Load';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';

import { Grid } from './layout';

interface ReferenceNumberCardProps {
  reference: AdditionalReference;
  index: number;
  onRemove: (index: number) => void;
  onEdit: (index: number) => void;
}

export function ReferenceNumberCard({
  reference,
  index,
  onRemove,
  onEdit,
}: ReferenceNumberCardProps): JSX.Element {
  return (
    <div className='shrink-0 w-60 h-44 p-3 bg-neutral-50 rounded-md border relative flex flex-col transition-opacity duration-300'>
      {/* Remove button positioned at top-right */}
      <button
        type='button'
        onClick={() => onRemove(index)}
        className='absolute top-2 right-2 text-error-500 hover:text-error-700 hover:bg-error-50 rounded-full p-1 z-10'
        title='Remove reference'
      >
        <XCircleIcon className='h-4 w-4' />
      </button>

      {/* Reference content - grow to take available space */}
      <div className='grow space-y-2 text-sm pr-8'>
        <Grid cols='2' gap='sm' className='mx-0 w-full'>
          <div>
            <span className='font-bold text-neutral-600'>Qualifier:</span>
            <div className='text-neutral-900 font-medium truncate'>
              {reference.qualifier || 'N/A'}
            </div>
          </div>
          <div>
            <span className='font-bold text-neutral-600'>Number:</span>
            <div className='text-neutral-900 font-medium break-words line-clamp-2'>
              {reference.number || 'N/A'}
            </div>
          </div>
        </Grid>
        <Grid cols='2' gap='sm' className='mx-0 w-full'>
          <div>
            <span className='font-bold text-neutral-600 text-xs'>Weight:</span>
            <div className='text-neutral-900 font-medium'>
              {reference.weight || 0}
            </div>
          </div>
          <div>
            <span className='font-bold text-neutral-600 text-xs'>Pieces:</span>
            <div className='text-neutral-900 font-medium'>
              {reference.pieces || 0}
            </div>
          </div>
        </Grid>
      </div>

      {/* Bottom section - always at bottom */}
      <Grid
        cols='3'
        gap='xs'
        className='mt-auto pt-2 border-t border-neutral-200 mx-0 w-full'
      >
        {reference.shouldSendToDriver ? (
          <div className='col-span-2 flex items-center'>
            <span className='text-xs bg-info-100 text-info-800 px-2 py-1 rounded'>
              Send to driver
            </span>
          </div>
        ) : (
          <div className='col-span-2 flex items-center'>
            <span className='text-xs bg-error-100 text-error-800 px-2 py-1 rounded'>
              Don&apos;t send to driver
            </span>
          </div>
        )}
        <Button
          type='button'
          variant='ghost'
          size='sm'
          onClick={() => onEdit(index)}
          className='w-full h-7 px-2 text-xs hover:bg-neutral-100 col-span-1'
          buttonNamePosthog={
            ButtonNamePosthog.LoadBuildingEditMcleodReferenceNumber
          }
        >
          Edit
        </Button>
      </Grid>
    </div>
  );
}
