import { Typography } from './typography/Typography';

interface ProgressProps {
  logo: string;
  message: string;
  title: string;
}

export default function Progress({ logo, message, title }: ProgressProps) {
  return (
    <section className='ms-welcome__progress ms-u-fadeIn500'>
      <img width='90' height='90' src={logo} alt={title} title={title} />
      <Typography
        variant='h1'
        textColor='default'
        weight='light'
        align='center'
      >
        {title}
      </Typography>
      <Typography>{message}</Typography>
    </section>
  );
}
