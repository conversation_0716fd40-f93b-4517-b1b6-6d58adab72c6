import * as React from 'react';

import { cn } from 'utils/shadcn';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    const textareaClass = cn(
      'flex min-h-[80px] w-full rounded-[4px] border border-neutral-input-border bg-neutral-input-bg text-[13px] text-neutral-input-text',
      'px-3 py-2 ring-offset-neutral-50 scroll-mt-20',
      'file:border-0 file:bg-transparent file:text-[14px] file:font-medium',
      'placeholder:text-neutral-500/30 focus-visible:outline-brand focus-visible:read-only:outline-none',
      'disabled:cursor-not-allowed read-only:cursor-not-allowed read-only:bg-neutral-100 read-only:border-neutral-200 focus-visible:read-only:border-neutral-200 read-only:text-neutral-400',
      props.disabled ? 'resize-none' : '',
      className
    );

    return <textarea className={textareaClass} ref={ref} {...props} />;
  }
);
Textarea.displayName = 'Textarea';

export { Textarea };
