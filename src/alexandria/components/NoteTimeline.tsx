import React from 'react';

import dayjs from 'dayjs';
import { NotebookPenIcon } from 'lucide-react';

import useTMSContext from 'hooks/useTMSContext';
import { Note } from 'types/Load';
import { TMS } from 'types/enums/Integrations';

import { Typography } from './typography';

type NoteProps = Note;

type NoteTimelineProps = {
  events: NoteProps[];
};

const NoteTimeline: React.FC<NoteTimelineProps> = ({ events }) => {
  if (!events) {
    return null;
  }

  const isTZAgnostic = useTMSContext().tmsName === TMS.Aljex;
  const shortTZ = dayjs().tz().format('z');
  const normalizedTZ = isTZAgnostic ? '' : ` ${shortTZ}`;

  return (
    <ol className='relative border-s ms-4 border-info-main px-2'>
      {events
        .sort(
          (a, b) =>
            // Sort in descending order (most recent first)
            new Date(b.updatedBy).getTime() - new Date(a.updatedBy).getTime()
        )
        .map((event) => (
          <li className='mb-10 ms-3' key={null}>
            <span className='absolute flex items-center justify-center w-6 h-6 mr-2 bg-neutral-50 rounded-full -start-3 ring-1 ring-offset-4 ring-offset-neutral-50 ring-info-main'>
              <NotebookPenIcon size={'sm'} strokeWidth={1} />
            </span>
            <div className='flex items-start font-normal  text-neutral-500 text-xs leading-none mt-6 justify-between'>
              <span className='flex text-left text-sm'>{event.updatedBy}</span>
              <span>
                <time className='flex text-right text-xs font-normal leading-none text-neutral-500 pl-6'>
                  {(() => {
                    // Handle if somehow no timestamp provided
                    if (!event.createdAt) {
                      return <Typography>N/A</Typography>;
                    }

                    return (
                      <>
                        {dayjs(event.createdAt)
                          .tz(isTZAgnostic ? 'UTC' : undefined) // If valid TZ, undefined = display in user's locale
                          .format('M/D/YY')}
                        <br />
                        {`${dayjs(event.createdAt)
                          .tz(isTZAgnostic ? 'UTC' : undefined) // If valid TZ, undefined = display in user's locale
                          .format('H:mm')} ${normalizedTZ}`}
                      </>
                    );
                  })()}
                </time>
              </span>
            </div>

            {event.isException !== null && (
              <div>
                <span
                  className={`text-neutral-400 text-xs leading-5 inline ${event.isException ? 'bg-error-50 text-error-main' : ''}`}
                >
                  Issue:{' '}
                </span>
                <span
                  className={`text-neutral-500 text-xs leading-5 inline ${event.isException ? 'text-error-main bg-error-50 px-2' : ''}`}
                >
                  {`${event.isException}`}
                </span>
              </div>
            )}

            {event.isOnTime !== null && (
              <div>
                <span
                  className={`text-neutral-400 text-xs leading-5 inline ${!event.isOnTime ? 'text-error-main bg-error-50' : ''}`}
                >
                  On Time:{' '}
                </span>
                <span
                  className={`text-neutral-500 text-xs leading-5 inline ${!event.isOnTime ? 'text-error-main bg-error-50 px-2' : ''}`}
                >
                  {`${event.isOnTime}`}
                </span>
              </div>
            )}

            <div>
              <span className='text-neutral-400 text-xs leading-5 inline'>
                Notes:{' '}
              </span>
              <span className='text-neutral-500 text-xs leading-5 inline'>
                {event.note}
              </span>
            </div>
          </li>
        ))}
    </ol>
  );
};

export { NoteTimeline, NoteProps, NoteTimelineProps };
