import { ReactNode, useState } from 'react';

import {
  Service,
  ServiceContext,
  initialServiceValues,
} from 'contexts/serviceContext';

export default function ServiceProvider({ children }: { children: ReactNode }) {
  const [service, setService] = useState<Service>(initialServiceValues);
  const [isLoading, setIsLoading] = useState(true);

  return (
    <ServiceContext.Provider
      value={{
        ...service,
        setService,
        isLoading,
        setIsLoading,
      }}
    >
      {children}
    </ServiceContext.Provider>
  );
}
