import { useContext } from 'react';
import type { KeyboardEvent as ReactKeyboardEvent } from 'react';

import { AutoComplete } from 'antd';
import { SearchIcon } from 'lucide-react';

import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';

import { useLoadSearch } from '../contexts/loadSearchContext';
import AdvancedSearchPopover from './AdvancedSearchPopover';

export default function SearchBar() {
  const { proNumberInput, setProNumberInput, addTab } = useLoadSearch();

  const {
    currentState: { viewedLoads },
  } = useContext(SidebarStateContext);

  const {
    serviceFeaturesEnabled: { isAdvancedSearchEnabled, isOrderEnabled },
  } = useServiceFeatures();

  const handleSearch = async (value: string) => {
    if (value.trim() === '') return;

    // Add a load tab
    addTab(value, 'load');

    // If orders are enabled, also add an order tab
    if (isOrderEnabled) {
      addTab(value, 'order');
    }
  };

  const handleBlurSearchInput = async () => {
    await handleSearch(proNumberInput);
  };

  const handleKeyDownSearchInput = (
    ev: ReactKeyboardEvent<HTMLInputElement>
  ) => {
    if (ev.key === 'Enter') {
      handleBlurSearchInput();
    }
  };

  return (
    <div className='w-full'>
      <div className='search-container relative flex border border-neutral-400 rounded-[4px] bg-neutral-50 h-8 z-20'>
        <div className='flex-1 flex justify-between bg-neutral-50 rounded gap-2'>
          {isAdvancedSearchEnabled && (
            <div className='w-full relative flex' id='search-bar-container'>
              <AutoComplete
                options={viewedLoads.reverse().map((vlID) => ({
                  label: `${vlID}`,
                  value: vlID,
                }))}
                onSelect={handleSearch}
                value={proNumberInput}
                suffixIcon={null}
                className='w-full h-[30px]! mt-0! border-none!'
              >
                <input
                  type='text'
                  className='w-full pl-2 pr-20 h-[30px] bg-transparent focus-visible:outline-none text-[13px]'
                  value={proNumberInput}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setProNumberInput(e.target.value)
                  }
                  onBlur={handleBlurSearchInput}
                  onKeyDown={handleKeyDownSearchInput}
                  placeholder='Search by ID...'
                />
              </AutoComplete>
              <div className='absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2'>
                <SearchIcon className='h-3.5 w-3.5 stroke-neutral-500 cursor-pointer z-1' />
                <AdvancedSearchPopover />
              </div>
            </div>
          )}

          {!isAdvancedSearchEnabled && (
            <div className='w-full relative flex'>
              <input
                type='text'
                className='w-full pl-2 pr-8 h-[30px] bg-transparent focus-visible:outline-none text-[13px]'
                value={proNumberInput}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setProNumberInput(e.target.value)
                }
                onBlur={handleBlurSearchInput}
                onKeyDown={handleKeyDownSearchInput}
                placeholder='Search by ID...'
              />
              <SearchIcon className='h-3.5 w-3.5 stroke-neutral-500 absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer' />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
