import { Circle<PERSON>heck, Mail } from 'lucide-react';

import { Maybe } from 'types/UtilityTypes';
import { formatCurrency } from 'utils/formatCurrency';
import { cn } from 'utils/shadcn';

import { Tooltip, TooltipContent, TooltipTrigger } from './Tooltip';
import { Flex } from './layout';
import { Typography } from './typography';

type CarrierQuoteCardProps = {
  name?: string;
  locationName?: string;
  city?: string;
  state?: string;
  emails: string[];
  price?: number;
  available?: boolean;
  notes?: Maybe<string>;
  responded: boolean;
  bounced: boolean;
  selected?: boolean;
  onViewThread: () => void;
  onSelect?: () => void;
};

const MAX_NOTE_LENGTH = 80;

const trimString = (str: string, maxLength: number) => {
  return str.length > maxLength ? `${str.substring(0, maxLength)}...` : str;
};

export const CarrierQuoteCard = ({
  name,
  city,
  state,
  emails,
  price,
  available,
  notes,
  responded,
  bounced,
  selected,
  onViewThread,
  onSelect,
}: CarrierQuoteCardProps) => {
  const getStatusText = () => {
    if (bounced) return 'Email bounced';
    if (!responded) return 'No quote';
    if (available !== undefined && !available) return 'Not available';
    // Carrier may have not responded yet OR they responded without a quote which processor doesn't pickup
    // because regex looks for a quote number (e.g. '320'). So we say "No quote" instead of "No response" to capture both cases
    // TODO: ENG-3146
    if (!price) return 'No quote';

    return formatCurrency(price || 0, 'USD');
  };

  const getStatusColor = () => {
    if (bounced || !responded || !available) return 'text-error-500';
    return 'text-success-600';
  };

  return (
    <Flex
      direction='col'
      className={cn(
        'relative border rounded-lg p-2.5 transition-all cursor-pointer shadow-sm w-full',
        bounced || !responded || !available
          ? 'bg-error-50 border-error-200'
          : [
              'border-brand-400',
              selected
                ? 'bg-brand-50/90 border-brand-400'
                : 'hover:shadow-md bg-neutral-50',
            ]
      )}
      onClick={onSelect}
    >
      {/* Header with carrier name and email button */}
      <Flex align='center' justify='between' className='mb-0.5 w-full'>
        {/* Render Carrier name, or the first email in emails array */}
        <Typography
          variant='h6'
          weight='medium'
          className='text-neutral-800 truncate flex-1 mr-2'
        >
          {name || (Array.isArray(emails) ? emails[0] : emails)}
        </Typography>
        <Tooltip delayDuration={10}>
          <TooltipTrigger asChild>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onViewThread();
              }}
              className='p-1 text-accent hover:bg-brand-400 hover:text-neutral-50 rounded-[4px] transition-colors flex-shrink-0'
              aria-label='View email thread'
            >
              <Mail className='w-4 h-4' />
            </button>
          </TooltipTrigger>
          <TooltipContent className='mr-12'>
            <Typography variant='body-xs'>View thread</Typography>
          </TooltipContent>
        </Tooltip>
      </Flex>

      {/* Location and email info */}
      <Flex direction='col' gap='xs' className='w-full'>
        {city && state && (
          <Typography
            variant='body-xs'
            className='text-neutral-600 w-full text-left'
          >{`${city}, ${state}`}</Typography>
        )}

        {name && (
          <div className='w-full text-center'>
            <Typography
              variant='body-xs'
              className='text-neutral-600 text-ellipsis block'
            >
              {emails.join(', ')}
            </Typography>
          </div>
        )}

        {/* Status/Price section */}
        <div className='mt-2 border-t pt-2 border-neutral-300 w-full'>
          <Flex align='center' justify='between' className='w-full'>
            <Typography
              variant='body-xs'
              weight='medium'
              className='text-neutral-700'
            >
              {price ? 'Total cost:' : 'Status:'}
            </Typography>
            <Typography
              variant='body-xs'
              weight='medium'
              className={getStatusColor()}
            >
              {getStatusText()}
            </Typography>
          </Flex>

          {notes && responded && available && (
            <div className='w-full mt-2 p-2 bg-neutral-100 rounded-md'>
              <Typography
                variant='body-xs'
                weight='medium'
                className='text-neutral-700 mb-1'
                title={notes}
              >
                Note:
              </Typography>
              <Typography
                variant='body-xs'
                className='text-neutral-600 italic w-full'
              >
                {trimString(notes, MAX_NOTE_LENGTH)}
              </Typography>
            </div>
          )}
        </div>
      </Flex>

      {selected && (
        <div className='absolute top-[-6px] right-[-6px]'>
          <CircleCheck
            className='text-success-500 w-5 h-5 fill-neutral-50'
            strokeWidth={2}
          />
        </div>
      )}
    </Flex>
  );
};
