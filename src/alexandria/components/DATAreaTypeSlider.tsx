import type React from 'react';
import { useCallback, useRef, useState } from 'react';

import { cn } from 'utils/shadcn';

interface DATAreaTypeOption {
  value: string;
  label: string;
  specificity: number;
}

interface DATAreaTypeSliderProps {
  options?: DATAreaTypeOption[];
  value?: string;
  onChange?: (value: string) => void;
  bestFitOption?: string;
  className?: string;
}

const defaultOptions: DATAreaTypeOption[] = [
  { value: '3_DIGIT_ZIP', label: '3-digit ZIP', specificity: 1 },
  { value: 'MARKET_AREA', label: 'Mkt', specificity: 2 },
  { value: 'EXTENDED_MARKET_AREA', label: 'X-Mkt', specificity: 3 },
  { value: 'REGION', label: 'Region', specificity: 4 },
];

export function DATAreaTypeSlider({
  options = defaultOptions,
  value,
  onChange,
  bestFitOption,
  className,
}: DATAreaTypeSliderProps) {
  const [selectedValue, setSelectedValue] = useState(
    value || options[0]?.value || ''
  );

  const sliderMarkerIndexesToOffset = options.map((_, index) => {
    // No offset for first and last options, as those should be exactly at start and end of slider
    if (index < 1 || index >= options.length - 1) {
      return 0;
    }

    if (index === 1) {
      return 8;
    }

    return index * -1.5;
  });

  // Lock allowed range to the initial bestFit option INDEX and to the right.
  const initialMinIdx = (() => {
    const idx = bestFitOption
      ? options.findIndex((o) => o.value === bestFitOption)
      : -1;
    return idx >= 0 ? idx : 0;
  })();

  const minAllowedIndexRef = useRef<number>(initialMinIdx);

  const getEffectiveDisabledOptions = useCallback(() => {
    const disabled = new Set<string>();
    const minIdx = minAllowedIndexRef.current;
    for (let i = 0; i < Math.min(minIdx, options.length); i++) {
      disabled.add(options[i].value);
    }
    return disabled;
  }, [options]);

  const effectiveDisabledOptions = getEffectiveDisabledOptions();
  const currentValue = value || selectedValue;
  const currentIndex = options.findIndex((opt) => opt.value === currentValue);
  const safeIndex = Math.max(currentIndex, minAllowedIndexRef.current);

  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let index = Number.parseInt(event.target.value);
    index = Math.max(index, minAllowedIndexRef.current);

    const option = options[index];
    if (option) {
      setSelectedValue(option.value);
      onChange?.(option.value);
    }
  };

  const handleOptionClick = (optionValue: string) => {
    if (!effectiveDisabledOptions.has(optionValue)) {
      setSelectedValue(optionValue);
      onChange?.(optionValue);
    }
  };

  const getSliderWidth = (index: number) => {
    return (
      (index / (options.length - 1)) * 100 + sliderMarkerIndexesToOffset[index]
    );
  };

  return (
    <div className={cn('w-full max-w-md', className)}>
      <div className='relative'>
        <div className='relative h-2 bg-neutral-200 rounded-full'>
          <div
            className='absolute h-2 bg-brand rounded-full transition-all duration-200'
            style={{
              width: `${getSliderWidth(safeIndex)}%`,
            }}
          />
        </div>

        <input
          type='range'
          min='0'
          max={options.length - 1}
          value={safeIndex}
          onChange={handleSliderChange}
          className='absolute inset-0 w-full h-2 opacity-0 cursor-pointer'
          aria-label='Select timeframe'
        />

        <div
          className='absolute top-1/2 w-4 h-4 bg-brand border-2 border-brand rounded-full shadow-sm transform -translate-y-1/2 -translate-x-1/2 transition-all duration-200'
          style={{
            left: `${getSliderWidth(safeIndex)}%`,
          }}
        />
      </div>

      <div className='flex justify-between mt-3'>
        {options.map((option) => {
          const isDisabled = effectiveDisabledOptions.has(option.value);
          const isSelected = option.value === currentValue;

          return (
            <button
              key={option.value}
              onClick={() => handleOptionClick(option.value)}
              disabled={isDisabled}
              className={cn(
                'text-xs font-medium transition-colors duration-200 px-1 py-1 rounded',
                isSelected && !isDisabled && 'text-brand font-semibold',
                isDisabled &&
                  'text-neutral-500 cursor-not-allowed opacity-30 line-through',
                !isSelected &&
                  !isDisabled &&
                  'text-neutral-500 hover:text-neutral-600 cursor-pointer'
              )}
              aria-label={`Select ${option.label} timeframe`}
              type='button'
            >
              {option.label}
            </button>
          );
        })}
      </div>
    </div>
  );
}
