import { JSX, useState } from 'react';

import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';

import FormTextInput from 'components/input/FormTextInput';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import getPlaceholderForField from 'utils/getPlaceholderForField';
import getValidator<PERSON>or<PERSON>ield from 'utils/getValidatorForField';
import { cn } from 'utils/shadcn';

import { Button } from './Button';
import { Typography } from './typography/Typography';

export interface DataElement {
  field: string;
  label: string;
  value: any;
  className?: string;
  alwaysShown?: boolean;
}

type Props = {
  objectData: DataElement[];
  sectionPrefix: string;
  className?: string;
  collapsible?: boolean;
};

export default function Section({
  objectData,
  sectionPrefix,
  className,
  collapsible = false,
}: Props): JSX.Element {
  const [isExpanded, setIsExpanded] = useState(false);

  const alwaysShownFields = objectData.filter((item) => item.alwaysShown);
  const otherFields = objectData.filter((item) => !item.alwaysShown);

  const fieldsToShow =
    collapsible && !isExpanded ? alwaysShownFields : objectData;
  const hasHiddenFields = collapsible && otherFields.length > 0;

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      className={cn('grid gap-x-4 gap-y-3 grid-cols-2 mx-0 w-full', className)}
    >
      {fieldsToShow.map((item) => {
        const { field, label, className: itemClassName } = item;
        let value = item.value;

        const cleanedField = field.startsWith('first') ? field.slice(5) : field;
        const inputName = `${sectionPrefix}.${cleanedField}`;
        const labelID = field;

        if (
          value &&
          (field.endsWith('Start') ||
            field.endsWith('End') ||
            field.endsWith('Time'))
        ) {
          if (value !== '0001-01-01T00:00:00Z') {
            // NOTE: Aljex has no timezone data, so the backend defaults datetime conversions to UTC,
            // but the front-end preserves users' current experience by showing the timestamp
            // exactly as it is written in Aljex.
            value = new Date(value).toLocaleString('en-US', {
              timeZone: 'UTC',
            });
          } else {
            value = '';
          }
        }

        return (
          <div className={itemClassName}>
            <FormTextInput
              name={labelID}
              key={`${field}-${value ? value.toString() : ''}`}
              canEdit={false}
              label={label}
              labelID={inputName}
              value={value ? value.toString() : ''}
              validator={getValidatorForField(labelID)}
              placeholderText={getPlaceholderForField(labelID)}
            />
          </div>
        );
      })}

      {hasHiddenFields && (
        <div className='col-span-2 mt-1'>
          <Button
            type='button'
            variant='ghost'
            onClick={toggleExpanded}
            buttonNamePosthog={ButtonNamePosthog.ToggleExpandableContent}
            className={`w-40 mx-auto h-8 flex items-center justify-center gap-2 border border-transparent hover:border-neutral-600 hover:bg-neutral-200`}
          >
            <Typography variant='body-sm' className='text-neutral-600 ml-1'>
              {isExpanded ? 'Show less' : 'Show more'}
            </Typography>
            {isExpanded ? (
              <ChevronUpIcon className='w-4 h-4' />
            ) : (
              <ChevronDownIcon className='w-4 h-4' />
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
