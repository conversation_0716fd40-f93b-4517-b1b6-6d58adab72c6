name: Linter

on:
  pull_request:
    branches: ['main', 'release']

jobs:
  prettier:
    runs-on: ubuntu-latest
    name: Run Prettier
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0
          persist-credentials: false

      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'
      - run: corepack enable
      - run: yarn

      - name: Prettify code
        uses: creyD/prettier_action@v4.3
        with:
          prettier_plugins: "@trivago/prettier-plugin-sort-imports"
          prettier_options: --write **/*.{ts,tsx,js,md}
          github_token: ${{ secrets.BOT_RW_PAT }}
  lint:
    runs-on: ubuntu-latest
    name: Run ES Lint
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.BOT_RW_PAT }}
          submodules: 'true'

      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'
      - run: corepack enable
      - run: yarn
      - run: yarn lint
  tsc:
    runs-on: ubuntu-latest
    name: Check Typescript
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.BOT_RW_PAT }}
          submodules: 'true'
      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'
      - run: corepack enable
      - run: yarn
      - run: yarn tsc
