import { Flex, Grid } from 'components/layout';
import SidebarLoader from 'components/loading/SidebarLoader';
import { Typography } from 'components/typography';
import { useFetchOrdersForLoad } from 'hooks/useFetchOrdersForLoad';
import { Load } from 'types/Load';
import { Order } from 'types/Order';

interface OrdersTabProps {
  load: Load;
}

export default function OrdersTab({ load }: OrdersTabProps) {
  const { orders, isLoading, error } = useFetchOrdersForLoad(load.ID || 0);

  if (isLoading) {
    return <SidebarLoader />;
  }

  if (error) {
    return (
      <div className='p-4 text-error-main'>
        Error loading orders: {error.message}
      </div>
    );
  }

  return (
    <div className='p-4'>
      {orders && orders.length > 0 ? (
        <div className='space-y-6'>
          {orders.map((order: Order) => (
            <div
              key={order.ID}
              className='border rounded-lg p-4 bg-neutral-50 shadow-sm'
            >
              <Flex justify='between' align='start' className='mb-4'>
                <div>
                  <Typography variant='h4' weight='medium'>
                    Order #{order.externalOrderId}
                  </Typography>
                  <Typography variant='body-sm' className='text-neutral-500'>
                    Type: {order.type}
                  </Typography>
                </div>
                <div className='text-right'>
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-medium ${
                      order.status === 'COMPLETED'
                        ? 'bg-success-100 text-success-800'
                        : order.status === 'IN_PROGRESS'
                          ? 'bg-info-100 text-info-800'
                          : 'bg-neutral-100 text-neutral-800'
                    }`}
                  >
                    {order.status}
                  </span>
                  <Typography
                    variant='body-sm'
                    className='text-neutral-500 mt-1'
                  >
                    Mode: {order.mode}
                  </Typography>
                </div>
              </Flex>

              <Grid cols='2' gap='xl' className='mb-4 mx-0 w-full'>
                {/* <div className='space-y-4'>
                  <div>
                    <Typography variant="h6" weight="medium" className='text-neutral-700 mb-1'>
                      Pickup Location
                    </Typography>
                    <div className='text-sm'>
                      <Typography weight="medium">{order.pickup.name}</Typography>
                      <Typography>{order.pickup.addressLine1}</Typography>
                      {order.pickup.addressLine2 && (
                        <Typography>{order.pickup.addressLine2}</Typography>
                      )}
                      <Typography>{`${order.pickup.city}, ${order.pickup.state} ${order.pickup.zipcode}`}</Typography>
                    </div>
                  </div>
                  <div>
                    <Typography variant="h6" weight="medium" className='text-neutral-700 mb-1'>
                      Delivery Location
                    </Typography>
                    <div className='text-sm'>
                      <Typography weight="medium">{order.consignee.name}</Typography>
                      <Typography>{order.consignee.addressLine1}</Typography>
                      {order.consignee.addressLine2 && (
                        <Typography>{order.consignee.addressLine2}</Typography>
                      )}
                      <Typography>{`${order.consignee.city}, ${order.consignee.state} ${order.consignee.zipcode}`}</Typography>
                    </div>
                  </div>
                </div> */}

                <div className='space-y-4'>
                  <div>
                    <Typography
                      variant='h6'
                      weight='medium'
                      className='text-neutral-700 mb-1'
                    >
                      Specifications
                    </Typography>
                    <div className='text-sm space-y-1'>
                      <Typography>
                        Weight: {order.specifications.totalWeight.val}{' '}
                        {order.specifications.totalWeight.unit}
                      </Typography>
                      <Typography>Pieces: {order.pieceCount}</Typography>
                      {order.specifications.isRefrigerated && (
                        <Typography className='text-info-600'>
                          Temperature: {order.specifications.minTempFahrenheit}
                          °F - {order.specifications.maxTempFahrenheit}°F
                        </Typography>
                      )}
                      {order.isHazmat && (
                        <Typography className='text-error-600'>
                          Hazmat
                        </Typography>
                      )}
                    </div>
                  </div>
                  <div>
                    <Typography
                      variant='h6'
                      weight='medium'
                      className='text-neutral-700 mb-1'
                    >
                      Rate Information
                    </Typography>
                    <div className='text-sm space-y-1'>
                      {/* <Typography>Type: {order.rateData.customerRateType}</Typography>
                      <Typography>
                        Line Haul: {order.rateData.customerLineHaulCharge.val}{' '}
                        {order.rateData.customerLineHaulCharge.unit}
                      </Typography> */}
                      <Typography>
                        Total Charge: {order.rateData.customerTotalCharge.val}{' '}
                        {order.rateData.customerTotalCharge.unit}
                      </Typography>
                    </div>
                  </div>
                </div>
              </Grid>

              {order.poNums && (
                <div className='mt-4 pt-4 border-t'>
                  <Typography
                    variant='h6'
                    weight='medium'
                    className='text-neutral-700 mb-2'
                  >
                    References
                  </Typography>
                  <Flex gap='sm' wrap='wrap'>
                    <span className='text-xs bg-neutral-100 px-2 py-1 rounded'>
                      PO: {order.poNums}
                    </span>
                    <span className='text-xs bg-neutral-100 px-2 py-1 rounded'>
                      Order ID: {order.orderTrackingId}
                    </span>
                  </Flex>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <Typography variant='body-sm' className='text-neutral-500'>
          No orders associated with this load.
        </Typography>
      )}
    </div>
  );
}
