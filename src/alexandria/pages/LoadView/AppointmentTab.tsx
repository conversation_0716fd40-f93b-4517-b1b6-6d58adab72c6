import { useContext } from 'react';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { ServiceContext } from 'contexts/serviceContext';
import useLogPostHogPageView from 'hooks/useLogPostHogPageView';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import Pageview from 'types/enums/Pageview';

import { AppointmentEditor } from './AppointmentScheduling/AppointmentEditor';

dayjs.extend(utc);
dayjs.extend(timezone);

type AppointmentSchedulingSectionProps = {
  normalizedLoad: NormalizedLoad;
  loadPickupWarehouse: Maybe<Warehouse>;
  loadDropoffWarehouse: Maybe<Warehouse>;
};

export default function AppointmentSchedulingSection({
  normalizedLoad: load,
  loadPickupWarehouse,
  loadDropoffWarehouse,
}: AppointmentSchedulingSectionProps) {
  const { schedulerIntegrations } = useContext(ServiceContext);

  useLogPostHogPageView(Pageview.AppointmentScheduling, {
    service_id: load.serviceID,
    load_id: load.ID,
    freightTrackingID: load.freightTrackingID,
  });

  // Check if the warehouses associated with load are from a scheduler accessible by current user
  const isLoadPickupWarehouseAvailable = schedulerIntegrations.some(
    (integration) => integration.name === loadPickupWarehouse?.warehouseSource
  );

  const isLoadDropoffWarehouseAvailable = schedulerIntegrations.some(
    (integration) => integration.name === loadDropoffWarehouse?.warehouseSource
  );

  return (
    <div className='px-4'>
      <AppointmentEditor
        normalizedLoad={load}
        loadPickupWarehouse={
          isLoadPickupWarehouseAvailable ? loadPickupWarehouse : null
        }
        loadDropoffWarehouse={
          isLoadDropoffWarehouseAvailable ? loadDropoffWarehouse : null
        }
      />
    </div>
  );
}
