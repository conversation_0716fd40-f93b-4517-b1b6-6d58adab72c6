import { confirmSlotAppt } from 'lib/api/confirmSlotAppt';
import {
  UpdateLoadAppointmentRequest,
  updateLoadAppointment,
} from 'lib/api/updateLoadAppointment';
import { StopTypes } from 'types/Appointment';
import { ApiError } from 'types/api/ApiError';
import { ConfirmSlotApptResult } from 'types/api/ConfirmSlotAppt';
import captureException from 'utils/captureException';

import { isTMSUpdateLoadAppointmentSupported } from './helpers';

export type ConfirmApptAndUpdateTMSOptions = {
  confirmApptProps: Parameters<typeof confirmSlotAppt>[0];
  featureFlagEnabled: boolean;
  tmsName: string;
  timezone?: string;
  stopType: StopTypes;
  loadId: number;
  freightTrackingId: string;
  onTmsUpdateComplete?: (succeeded: boolean, errorMessage?: string) => void;
};

export type ConfirmApptAndUpdateTMSResult = {
  confirmResult: ConfirmSlotApptResult | null;
  confirmError?: ApiError;
  tmsUpdateSucceeded: boolean;
  tmsUpdateError?: string;
};

/**
 * confirmApptAndUpdateTMS is used to confirm appointments and optionally update the TMS with the appointment details.
 * If auto-update TMS is enabled (feature flag: isAppointmentTMSUpdateEnabled) then we make a call to
 * updateLoadAppointment automatically.
 *
 * Flow:
 * 1. Call confirmSlotAppt with skipTMSUpdate: true (always skips backend TMS update)
 * 2. If successful and feature flag is enabled, initiate TMS update in background
 * 3. Return appointment confirmation result immediately
 * 4. TMS update completes asynchronously and notifies via onTmsUpdateComplete callback
 */
export async function confirmApptAndUpdateTMS(
  options: ConfirmApptAndUpdateTMSOptions
): Promise<ConfirmApptAndUpdateTMSResult> {
  const {
    confirmApptProps,
    featureFlagEnabled,
    tmsName,
    stopType,
    loadId,
    freightTrackingId,
    onTmsUpdateComplete,
  } = options;

  const confirmRes = await confirmSlotAppt({
    ...confirmApptProps,
    skipTMSUpdate: true,
  });

  if (!confirmRes.isOk()) {
    return {
      confirmResult: null,
      confirmError: confirmRes.error,
      tmsUpdateSucceeded: false,
    };
  }

  const shouldUpdateTMS =
    featureFlagEnabled && isTMSUpdateLoadAppointmentSupported(tmsName);

  if (!shouldUpdateTMS) {
    return {
      confirmResult: confirmRes.value,
      tmsUpdateSucceeded: false,
    };
  }

  const start = confirmApptProps.start;
  const end = new Date(start.getTime() + 60 * 60 * 1000);

  // selectedSlot.startTime is already a Date representing the correct moment in time (UTC internally)
  // Just convert to ISO string without any timezone manipulation
  const apptStart = start.toISOString();
  const apptEnd = end.toISOString();

  const updateData: UpdateLoadAppointmentRequest = {
    load: {
      freightTrackingID: freightTrackingId,
      ...(stopType === StopTypes.Pickup && {
        pickup: {
          apptStartTime: apptStart,
          apptEndTime: apptEnd,
          apptType: 'By appointment',
        },
      }),
      ...(stopType === StopTypes.Dropoff && {
        consignee: {
          apptStartTime: apptStart,
          apptEndTime: apptEnd,
          apptType: 'By appointment',
        },
      }),
    },
  };

  // Fire-and-forget TMS update: execute asynchronously without blocking the return
  // This ensures the appointment confirmation response is returned immediately while
  // TMS update happens in the background. Results are communicated via onTmsUpdateComplete callback.
  (async () => {
    try {
      const updateRes = await updateLoadAppointment(loadId, updateData);
      if (!updateRes.isOk()) {
        captureException(
          new Error(
            updateRes.error?.message || 'Failed to update TMS with appointment'
          ),
          {
            functionName: 'confirmApptAndUpdateTMS:updateLoadAppointment',
            loadId,
            stopType,
          }
        );
        if (onTmsUpdateComplete) {
          onTmsUpdateComplete(
            false,
            updateRes.error?.message || 'Failed to update TMS with appointment'
          );
        }
      } else if (onTmsUpdateComplete) {
        onTmsUpdateComplete(true);
      }
    } catch (error) {
      captureException(error, {
        functionName: 'confirmApptAndUpdateTMS:updateLoadAppointment',
        loadId,
        stopType,
      });
      if (onTmsUpdateComplete) {
        onTmsUpdateComplete(
          false,
          (error as Error)?.message || 'Failed to update TMS with appointment'
        );
      }
    }
  })();

  // Return immediately with confirm result; TMS update runs in background as fire-and-forget
  // The tmsUpdateSucceeded flag remains false here because we don't wait for completion.
  // Actual TMS update status is communicated via the onTmsUpdateComplete callback.
  return {
    confirmResult: confirmRes.value,
    tmsUpdateSucceeded: false,
  };
}
