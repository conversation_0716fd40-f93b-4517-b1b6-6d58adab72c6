import { useMemo } from 'react';

import Section, { DataElement } from 'components/Section';
import { Typography } from 'components/typography';
import { useLoadContext } from 'hooks/useLoadContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import useTMSContext from 'hooks/useTMSContext';
import { StopTypes } from 'types/Appointment';
import { AdditionalReference, NormalizedLoad } from 'types/Load';
import { TMS } from 'types/enums/Integrations';

interface MiniLoadInfoProps {
  load: NormalizedLoad;
  type: StopTypes;
}

export function MiniLoadInfo({ load, type }: MiniLoadInfoProps) {
  const { loadAttrsObj: attrs } = useLoadContext();
  const { tmsName } = useTMSContext();
  const { serviceFeaturesEnabled } = useServiceFeatures();
  const { isLeanSolutionsCustomer } = serviceFeaturesEnabled;
  const pickup = load.pickup;
  const dropoff = load.consignee;
  const specs = load.specifications;
  const specsAttrs = attrs.specifications;

  const specsArray: DataElement[] = useMemo(
    () => [
      ...(type === StopTypes.Pickup
        ? [
            {
              field: 'pickup',
              label: `Pickup Address`,
              className: 'col-span-2',
              value: formatAddress(pickup),
              alwaysShown: true,
            },
            ...(isLeanSolutionsCustomer && pickup.additionalReferences?.length
              ? [
                  {
                    field: 'pickupAdditionalReferences',
                    label: 'Pickup Additional References',
                    className: 'col-span-2',
                    value: formatAdditionalReferences(
                      pickup.additionalReferences
                    ),
                    alwaysShown: false,
                  },
                ]
              : []),
          ]
        : []),
      ...(type === StopTypes.Dropoff
        ? [
            {
              field: 'dropoff',
              label: `Dropoff Address`,
              className: 'col-span-2',
              value: formatAddress(dropoff),
              alwaysShown: true,
            },
            ...(isLeanSolutionsCustomer && dropoff.additionalReferences?.length
              ? [
                  {
                    field: 'dropoffAdditionalReferences',
                    label: 'Consignee Additional References',
                    className: 'col-span-2',
                    value: formatAdditionalReferences(
                      dropoff.additionalReferences
                    ),
                    alwaysShown: false,
                  },
                ]
              : []),
          ]
        : []),
      ...(specsAttrs.commodities.isNotSupported
        ? []
        : [
            {
              field: 'commodities',
              label: 'Commodities',
              value: specs.commodities,
              alwaysShown: false, // Specification fields are hidden by default
            },
          ]),
      ...(specsAttrs.totalWeight.isNotSupported
        ? []
        : [
            {
              field: 'totalWeight',
              label: `Weight${specs.totalWeight?.unit ? ' (' + specs.totalWeight?.unit + ')' : ''}`,
              value: specs.totalWeight?.val ?? '',
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
      ...(specsAttrs.totalDistance.isNotSupported
        ? []
        : [
            {
              field: 'totalDistance',
              label: `Distance${specs.totalDistance?.unit ? ' (' + specs.totalDistance?.unit + ')' : ''}`,
              value: specs.totalDistance?.val ?? '',
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
      ...(specsAttrs.totalOutPalletCount.isNotSupported
        ? []
        : [
            {
              field: 'pallets',
              label: 'Pallets',
              value: specs.totalOutPalletCount,
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
      ...(specsAttrs.totalPieces.isNotSupported
        ? []
        : [
            {
              field: 'pieces',
              label: `Pieces${specs.totalPieces?.unit ? ' (' + specs.totalPieces?.unit + ')' : ''}`,
              value: specs.totalPieces?.val ?? '',
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
    ],
    [type, pickup, dropoff, specs, specsAttrs, tmsName, isLeanSolutionsCustomer]
  );

  return (
    <div className='my-4'>
      <Typography variant='h5' weight='medium' className='my-1.5'>
        Load Details
      </Typography>
      <Section
        sectionPrefix={'specs'}
        objectData={specsArray}
        collapsible={true}
      />
    </div>
  );
}

function formatAddress(address: {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zipCode?: string;
}): string {
  if (!address) {
    return '';
  }

  const parts: string[] = [];

  // Join address lines with comma
  if (address.addressLine1) {
    parts.push(address.addressLine1);
  }

  if (address.addressLine2) {
    parts.push(address.addressLine2);
  }

  // Join address lines with comma, then add dash before city
  const addressPart = parts.join(', ');
  const cityStateZip: string[] = [];

  if (address.city) {
    cityStateZip.push(address.city);
  }

  if (address.state) {
    cityStateZip.push(address.state.toUpperCase());
  }

  if (address.zipCode) {
    cityStateZip.push(address.zipCode);
  }

  const cityStateZipPart = cityStateZip.join(', ');

  // Combine address part with city/state/zip part
  if (addressPart && cityStateZipPart) {
    return `${addressPart} - ${cityStateZipPart}`;
  } else if (addressPart) {
    return addressPart;
  } else if (cityStateZipPart) {
    return cityStateZipPart;
  }

  return '';
}

function formatAdditionalReferences(
  additionalReferences: AdditionalReference[]
): string {
  if (!additionalReferences || additionalReferences.length === 0) {
    return '';
  }

  return additionalReferences
    .map((ref) => {
      const parts: string[] = [];

      // Show the reference number as the primary value
      if (ref.number) {
        parts.push(ref.number);
      }

      // Add qualifier if it exists and is different from the default "Assigned Branch"
      if (ref.qualifier && ref.qualifier !== 'Assigned Branch') {
        parts.push(`(${ref.qualifier})`);
      }

      // Add weight and pieces as additional context if available
      const additionalInfo: string[] = [];
      if (ref.weight && ref.weight > 0) {
        additionalInfo.push(`Weight: ${ref.weight}`);
      }
      if (ref.pieces && ref.pieces > 0) {
        additionalInfo.push(`Pieces: ${ref.pieces}`);
      }

      if (additionalInfo.length > 0) {
        parts.push(`[${additionalInfo.join(', ')}]`);
      }

      return parts.join(' ');
    })
    .join('; ');
}
