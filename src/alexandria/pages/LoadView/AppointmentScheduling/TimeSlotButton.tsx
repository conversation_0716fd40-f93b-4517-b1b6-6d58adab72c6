import React from 'react';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { GroupedSlot } from 'types/Appointment';
import { cn } from 'utils/shadcn';

dayjs.extend(utc);
dayjs.extend(timezone);

interface TimeSlotButtonProps {
  slot: GroupedSlot;
  isSelected: boolean;
  onSelect: (slot: GroupedSlot) => void;
  formatTime?: string;
  className?: string;
}

const TimeSlotButton: React.FC<TimeSlotButtonProps> = ({
  slot,
  isSelected,
  onSelect,
  formatTime = 'HH:mm',
  className,
}) => {
  const handleClick = () => {
    onSelect(slot);
  };

  const formattedTime = dayjs(slot.startTime)
    .tz(slot.timezone)
    .format(formatTime);

  const buttonClasses = cn(
    'text-neutral-900 bg-neutral-50 border border-neutral-400 p-1 py-1.5 rounded cursor-pointer text-sm transition-colors',
    isSelected
      ? 'bg-brand border-brand text-neutral-50'
      : 'bg-neutral-50 hover:bg-neutral-100',
    className
  );

  return (
    <button type='button' onClick={handleClick} className={buttonClasses}>
      {formattedTime}
    </button>
  );
};

export default TimeSlotButton;
