import { useEffect, useRef, useState } from 'react';

import { Input } from 'antd';
import type { InputRef } from 'antd';
import { PlusIcon, X } from 'lucide-react';

import { Button } from 'components/Button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography/Typography';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import { PurchaseOrder } from '../SchedulingPortalForms/RetalixForm';

type RetalixFormProps = {
  purchaseOrders: PurchaseOrder[];
  setPurchaseOrders: React.Dispatch<React.SetStateAction<PurchaseOrder[]>>;
};

export function RetalixPOList({
  purchaseOrders,
  setPurchaseOrders,
}: RetalixFormProps) {
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const [editInputValue, setEditInputValue] = useState('');

  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);

  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.input?.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    editInputRef.current?.input?.focus();
  }, [editInputValue]);

  const showInput = () => setInputVisible(true);

  const handleClose = (removedPONumber: string) =>
    setPurchaseOrders((prevPurchaseOrders) =>
      prevPurchaseOrders.filter((poNum) => poNum.number !== removedPONumber)
    );
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setInputValue(e.target.value);
  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setEditInputValue(e.target.value);

  const handleInputConfirm = () => {
    const newTag = inputValue.trim();
    if (inputValue && !purchaseOrders.map((po) => po.number).includes(newTag)) {
      setPurchaseOrders([
        ...purchaseOrders,
        { number: newTag, isValid: false, error: '' },
      ]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputConfirm = () => {
    const trimmedValue = editInputValue.trim();
    // Validate: trimmed value must be non-empty and not a duplicate (excluding current index)
    const existingPONumbers = purchaseOrders.map((po) => po.number);
    const isDuplicate = existingPONumbers.some(
      (poNumber, index) => poNumber === trimmedValue && index !== editInputIndex
    );

    if (trimmedValue && !isDuplicate) {
      const newTags = [...purchaseOrders];
      newTags[editInputIndex] = {
        number: trimmedValue,
        isValid: false,
        error: '',
      };
      setPurchaseOrders(newTags);
    }
    // Reset edit state regardless of validation result
    setEditInputIndex(-1);
    setEditInputValue('');
  };

  return (
    <TooltipProvider>
      <Flex justify='start' wrap='wrap' gap='sm' className='mt-2 w-full'>
        {purchaseOrders.map((order, index) => {
          if (editInputIndex === index) {
            return (
              <Input
                ref={editInputRef}
                key={order.number}
                className='h-9 me-2 align-center bg-neutral-50 text-[14px]'
                value={editInputValue}
                onChange={handleEditInputChange}
                onBlur={handleEditInputConfirm}
                onPressEnter={handleEditInputConfirm}
                maxLength={20}
              />
            );
          }

          const handleDeleteClick = (e: React.MouseEvent) => {
            e.stopPropagation();
            handleClose(order.number);
          };

          const poNumTag = (
            <Flex
              key={order.number}
              align='center'
              gap='sm'
              className={cn(
                'pl-3 pr-2 py-1.5 rounded-md select-none max-w-xs wrap-break-word border border-neutral-400 transition-all duration-200 group hover:shadow-sm',
                !order.isValid && order.error !== ''
                  ? 'bg-error-100 border-error-300'
                  : 'bg-neutral-50 text-neutral-600'
              )}
            >
              <span
                onDoubleClick={(e) => {
                  if (index !== 0) {
                    setEditInputIndex(index);
                    setEditInputValue(order.number);
                    e.preventDefault();
                  }
                }}
                className='text-sm flex-1 wrap-break-word cursor-text'
              >
                {order.number}
              </span>
              <button
                onClick={handleDeleteClick}
                className='shrink-0 p-0.5 rounded hover:bg-red-200 transition-colors duration-150 cursor-pointer opacity-60 group-hover:opacity-100'
                aria-label={`Remove PO ${order.number}`}
                tabIndex={0}
              >
                <X className='h-3.5 w-3.5 text-neutral-500' />
              </button>
            </Flex>
          );

          return order.isValid || order.error === '' ? (
            poNumTag
          ) : (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>{poNumTag}</TooltipTrigger>
              <TooltipContent className='mx-2 max-w-64 whitespace-pre-wrap'>
                <Typography variant='body-xs'>{order.error}</Typography>
              </TooltipContent>
            </Tooltip>
          );
        })}
      </Flex>

      <Flex className='mt-2 w-full'>
        {inputVisible ? (
          <Input
            ref={inputRef}
            type='text'
            placeholder='Enter PO # and press Enter to save'
            className='h-9 me-2 align-top'
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputConfirm}
            onPressEnter={handleInputConfirm}
            maxLength={20}
          />
        ) : (
          <Button
            type='button'
            variant='ghost'
            size='sm'
            onClick={showInput}
            className='text-xs pl-2 pr-3 py-1.5 tracking-wide text-neutral-600 border border-neutral-600 hover:border-neutral-700 hover:bg-neutral-200'
            buttonNamePosthog={ButtonNamePosthog.RetalixAddPO}
          >
            <Flex align='center' gap='xs'>
              <PlusIcon className='w-3.5 h-3.5' />
              Add new PO #
            </Flex>
          </Button>
        )}
      </Flex>
    </TooltipProvider>
  );
}
