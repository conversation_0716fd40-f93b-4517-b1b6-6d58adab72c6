// Company data for 3rd party requests
export const COMPANY_WISE_LOCATORS = {
  'dart container': {},
  'abbott nutrition': {},
  "america's logistics, llc": {},
  'american sugar refining, inc': {
    'ca appointment scheduling action profile': {},
    'us appointment scheduling default': {},
  },
  'associated wholesale grocers': {},
  'batory foods': {},
  'bay valley foods': {},
  'catania oils': {},
  'coca-cola beverages florida': {},
  'coca-cola united': {},
  'crown cork and seal': {},
  'ferrara candy': {},
  'flagstone foods llc': {},
  'heartland coca-cola': {},
  'heartland foods': {},
  hellofresh: {},
  'high liner foods inc': {},
  ingredion: {},
  "ken's foods": {},
  'lassonde industries inc': {},
  'mount franklin foods': {},
  'phillips pet': {},
  'reyes logistics solutions, llc': {
    'glccb/rccb scheduling': {},
    'reyes beer division': {},
  },
  'swire coca-cola': {},
  'toys r us': {},
  'ulta beauty': {},
  'winland foods': {
    'winland foods 3rd party appt sched': {},
    'winland foods 3rd party appt sched - int': {},
  },
};

// Companies/operations that require appointment date
export const APPOINTMENT_DATE_REQUIREMENTS = {
  // american sugar refining, inc > ca appointment scheduling action profile (both pickup and dropoff)
  'american sugar refining, inc': {
    'ca appointment scheduling action profile': {
      pickup: true,
      dropoff: true,
    },
  },
  // ken's foods (pickup only)
  "ken's foods": {
    pickup: true,
    dropoff: false,
  },
  'associated wholesale grocers': {
    pickup: false,
    dropoff: true,
  },
} as const;
