// Costco dropdown options
export const DEPOT_OPTIONS = [
  { value: '76', label: '76' },
  { value: '171', label: '171' },
  { value: '172', label: '172' },
  { value: '174', label: '174' },
  { value: '175', label: '175' },
  { value: '179', label: '179' },
  { value: '210', label: '210' },
  { value: '260', label: '260' },
  { value: '262', label: '262' },
  { value: '263', label: '263' },
  { value: '265', label: '265' },
  { value: '267', label: '267' },
  { value: '268', label: '268' },
  { value: '280', label: '280' },
  { value: '283', label: '283' },
  { value: '284', label: '284' },
  { value: '285', label: '285' },
  { value: '288', label: '288' },
  { value: '289', label: '289' },
  { value: '557', label: '557' },
  { value: '559', label: '559' },
  { value: '561', label: '561' },
  { value: '571', label: '571' },
  { value: '572', label: '572' },
  { value: '573', label: '573' },
  { value: '574', label: '574' },
  { value: '584', label: '584' },
  { value: '585', label: '585' },
  { value: '725', label: '725' },
  { value: '731', label: '731' },
  { value: '757', label: '757' },
  { value: '758', label: '758' },
  { value: '759', label: '759' },
  { value: '792', label: '792' },
  { value: '929', label: '929' },
  { value: '932', label: '932' },
  { value: '936', label: '936' },
  { value: '946', label: '946' },
  { value: '951', label: '951' },
  { value: '952', label: '952' },
  { value: '960', label: '960' },
  { value: '961', label: '961' },
  { value: '993', label: '993' },
  { value: '1034', label: '1034' },
  { value: '1035', label: '1035' },
  { value: '1052', label: '1052' },
  { value: '1053', label: '1053' },
  { value: '1179', label: '1179' },
  { value: '1203', label: '1203' },
  { value: '1204', label: '1204' },
  { value: '1239', label: '1239' },
  { value: '1250', label: '1250' },
  { value: '1251', label: '1251' },
  { value: '1252', label: '1252' },
  { value: '1253', label: '1253' },
  { value: '1254', label: '1254' },
  { value: '1255', label: '1255' },
  { value: '1256', label: '1256' },
  { value: '1257', label: '1257' },
  { value: '1258', label: '1258' },
  { value: '1259', label: '1259' },
  { value: '1260', label: '1260' },
  { value: '1321', label: '1321' },
  { value: '1354', label: '1354' },
  { value: '1355', label: '1355' },
  { value: '1376', label: '1376' },
  { value: '1377', label: '1377' },
  { value: '1386', label: '1386' },
  { value: '1387', label: '1387' },
  { value: '1397', label: '1397' },
  { value: '1398', label: '1398' },
  { value: '1399', label: '1399' },
  { value: '1400', label: '1400' },
  { value: '1401', label: '1401' },
  { value: '1402', label: '1402' },
  { value: '1404', label: '1404' },
  { value: '1405', label: '1405' },
  { value: '1406', label: '1406' },
  { value: '1407', label: '1407' },
  { value: '1410', label: '1410' },
  { value: '1412', label: '1412' },
  { value: '1413', label: '1413' },
];

export const UOM_OPTIONS = [
  { value: 'Q', label: 'QTY (cases)' },
  { value: 'P', label: 'PLT Count' },
];

export const DOOR_TYPE_OPTIONS = [
  { value: 'DOCK', label: 'DOCK' },
  { value: 'GROUND', label: 'GROUND' },
  { value: 'LIVE', label: 'LIVE' },
];

export const UNLOAD_TYPE_OPTIONS = [
  { value: 'DROP', label: 'DROP' },
  { value: 'LIVE', label: 'LIVE' },
  { value: 'STANDING', label: 'STANDING' },
];
