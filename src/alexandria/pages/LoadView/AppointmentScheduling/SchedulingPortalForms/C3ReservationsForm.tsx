import { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>ath,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { UpdateTMSLoadWithAppt } from 'components/UpdateTMSLoadWithAppt';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import { confirmApptAndUpdateTMS } from '../helpers/confirmApptAndUpdateTMS';
import { isTMSUpdateLoadAppointmentSupported } from '../helpers/helpers';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Extract tenant slug from URL (e.g., "dollartree", "kehe")
 * Returns lowercase for matching/config purposes
 */
const extractTenantSlug = (tenant?: string): string => {
  if (!tenant) return '';

  const trimmed = tenant.trim();
  if (!trimmed) return '';

  const lower = trimmed.toLowerCase();
  // Match pattern: c3reservations.com/{tenant}
  const match = lower.match(/c3reservations\.com\/([^/]+)/);
  if (match && match[1]) {
    return match[1];
  }

  try {
    const url = new URL(
      lower.startsWith('http') ? lower : `https://${lower.replace(/^\/+/, '')}`
    );
    const pathSegments = url.pathname
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pathSegments.length > 0) {
      return pathSegments[0];
    }
  } catch {
    // If URL parsing fails, try simple split
    const pieces = lower
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pieces.length > 0) {
      return pieces[0];
    }
  }

  return '';
};

/**
 * Extract tenant name from URL while preserving original casing (e.g., "DollarTree", "KeHE")
 * Used for display purposes
 */
const extractTenantName = (tenant?: string): string => {
  if (!tenant) return '';

  const trimmed = tenant.trim();
  if (!trimmed) return '';

  // Match pattern: c3reservations.com/{tenant} (case-sensitive)
  const match = trimmed.match(/c3reservations\.com\/([^/]+)/i);
  if (match && match[1]) {
    return match[1];
  }

  try {
    const url = new URL(
      trimmed.startsWith('http')
        ? trimmed
        : `https://${trimmed.replace(/^\/+/, '')}`
    );
    const pathSegments = url.pathname
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pathSegments.length > 0) {
      return pathSegments[0];
    }
  } catch {
    // If URL parsing fails, try simple split
    const pieces = trimmed
      .split('/')
      .filter(Boolean)
      .filter((segment) => segment !== 'app' && segment !== 'login');
    if (pieces.length > 0) {
      return pieces[0];
    }
  }

  return '';
};

/**
 * Tenant-specific field configuration
 * Maps tenant names to which fields should be shown and required
 */
type TenantFieldConfig = {
  showFields: {
    contactName: boolean;
    email: boolean;
    phone: boolean;
    unloadType: boolean;
    casesOrPalletQuantity: boolean;
    loadType: boolean;
    carrierCC: boolean;
    comment: boolean;
  };
  requiredFields: {
    contactName: boolean;
    email: boolean;
    unloadType: boolean;
    casesOrPalletQuantity: boolean;
    loadType: boolean;
  };
};

const TENANT_FIELD_CONFIG: Record<string, TenantFieldConfig> = {
  dollartree: {
    showFields: {
      contactName: true,
      email: true,
      phone: true,
      unloadType: true,
      casesOrPalletQuantity: true,
      loadType: false,
      carrierCC: false,
      comment: true,
    },
    requiredFields: {
      contactName: true,
      email: true,
      unloadType: true,
      casesOrPalletQuantity: true,
      loadType: false,
    },
  },
  kehe: {
    showFields: {
      contactName: false,
      email: false,
      phone: false,
      unloadType: false,
      casesOrPalletQuantity: false,
      loadType: true,
      carrierCC: true,
      comment: true,
    },
    requiredFields: {
      contactName: false,
      email: false,
      unloadType: false,
      casesOrPalletQuantity: false,
      loadType: true,
    },
  },
};

// Default config for unknown tenants (show common fields only)
const DEFAULT_FIELD_CONFIG: TenantFieldConfig = {
  showFields: {
    contactName: true,
    email: true,
    phone: true,
    unloadType: false,
    casesOrPalletQuantity: false,
    loadType: false,
    carrierCC: false,
    comment: true,
  },
  requiredFields: {
    contactName: false,
    email: false,
    unloadType: false,
    casesOrPalletQuantity: false,
    loadType: false,
  },
};

const getTenantFieldConfig = (tenantSlug: string): TenantFieldConfig => {
  return TENANT_FIELD_CONFIG[tenantSlug.toLowerCase()] || DEFAULT_FIELD_CONFIG;
};

interface C3ReservationsInputsWithoutLoad {
  proId: string;
  startDate: Date;
  endDate: Date;
  contactName: string;
  email: string;
  phone: string;
  unloadType: string;
  casesOrPalletQuantity: string;
  loadType: string;
  carrierCC: string;
  comment: string;
}

type C3ReservationsTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<C3ReservationsInputsWithoutLoad>;
};

const C3ReservationsTextInput = (props: C3ReservationsTextInputProps) => (
  <RHFTextInput {...props} />
);

interface C3ReservationsFormProps {
  type: StopTypes;
  load: NormalizedLoad;
  integrationID?: number;
  tmsName: string;
  isAppointmentTMSUpdateEnabled: boolean;
  tenant?: string;
}

export function C3ReservationsForm({
  type,
  load,
  integrationID,
  tmsName,
  isAppointmentTMSUpdateEnabled,
  tenant,
}: C3ReservationsFormProps) {
  const { toast } = useToast();
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [tmsUpdateSucceeded, setTMSUpdateSucceeded] = useState(false);
  const [showUpdateTMSLoadWithAppt, setShowUpdateTMSLoadWithAppt] =
    useState(false);

  const formMethods = useForm<C3ReservationsInputsWithoutLoad>({
    defaultValues: {
      proId: load.freightTrackingID || '',
      startDate: new Date(),
      endDate: dayjs().add(7, 'days').toDate(),
      contactName: '',
      email: '',
      phone: '',
      unloadType: '',
      casesOrPalletQuantity: '',
      loadType: '',
      carrierCC: '',
      comment: '',
    },
    mode: 'onChange',
  });

  const { control, handleSubmit, getValues, setValue, watch } = formMethods;

  const unloadTypeValue = watch('unloadType');
  const loadTypeValue = watch('loadType');

  const resolvedTenant = extractTenantSlug(tenant || '');
  const fieldConfig = getTenantFieldConfig(resolvedTenant);

  // Extract tenant name for display (preserve original casing)
  const tenantName = extractTenantName(tenant || '');

  const previousTenantSlugRef = useRef<string>(extractTenantSlug(tenant || ''));
  const previousIntegrationRef = useRef<number | undefined>(integrationID);

  useEffect(() => {
    const currentTenantSlug = extractTenantSlug(tenant || '');
    const tenantChanged = currentTenantSlug !== previousTenantSlugRef.current;
    const integrationChanged = integrationID !== previousIntegrationRef.current;

    if (!tenantChanged && !integrationChanged) {
      return;
    }

    previousTenantSlugRef.current = currentTenantSlug;
    previousIntegrationRef.current = integrationID;

    // Clear tenant-specific fields so stale values don't carry over
    const currentValues = getValues();
    if (currentValues.unloadType) {
      setValue('unloadType', '');
    }
    if (currentValues.casesOrPalletQuantity) {
      setValue('casesOrPalletQuantity', '');
    }
    if (currentValues.loadType) {
      setValue('loadType', '');
    }
    if (currentValues.carrierCC) {
      setValue('carrierCC', '');
    }

    // Clear cached slot data when switching integrations/tenants
    setOrderedSlots(null);
    setSelectedSlot(null);
    setApptConfirmationNumber('');
    setShowUpdateTMSLoadWithAppt(false);
  }, [tenant, integrationID, getValues, setValue]);

  /**
   * Build formData payload for API calls
   * Similar to cyclops/integrations/scheduling/c3reservations/models.py C3ReservationsFormData
   */
  const buildFormDataPayload = (
    values: C3ReservationsInputsWithoutLoad,
    tenantSlug: string
  ): { payload: Record<string, unknown>; error?: string } => {
    const payload: Record<string, unknown> = {};
    const config = getTenantFieldConfig(tenantSlug);

    // Contact fields
    if (config.showFields.contactName) {
      const trimmed = values.contactName?.trim();
      if (config.requiredFields.contactName && !trimmed) {
        return { payload: {}, error: 'Contact Name is required.' };
      }
      if (trimmed) {
        payload.contactName = trimmed;
      }
    }

    if (config.showFields.email) {
      const trimmed = values.email?.trim();
      if (config.requiredFields.email && !trimmed) {
        return { payload: {}, error: 'Email is required.' };
      }
      if (trimmed) {
        payload.email = trimmed;
      }
    }

    if (config.showFields.phone && values.phone?.trim()) {
      payload.phone = values.phone.trim();
    }

    // Dollar Tree specific fields
    if (config.showFields.unloadType) {
      if (config.requiredFields.unloadType && !values.unloadType) {
        return {
          payload: {},
          error: 'Unload Type is required for Dollar Tree.',
        };
      }
      if (values.unloadType) {
        payload.unloadType = values.unloadType;
      }
    }

    if (config.showFields.casesOrPalletQuantity) {
      const qty = Number(values.casesOrPalletQuantity);
      if (
        config.requiredFields.casesOrPalletQuantity &&
        (!values.casesOrPalletQuantity || Number.isNaN(qty) || qty <= 0)
      ) {
        return {
          payload: {},
          error: 'Cases or Pallet Quantity is required for Dollar Tree.',
        };
      }
      if (values.casesOrPalletQuantity && !Number.isNaN(qty) && qty > 0) {
        payload.casesOrPalletQuantity = qty;
      }
    }

    // KeHE specific fields
    if (config.showFields.loadType) {
      if (config.requiredFields.loadType && !values.loadType?.trim()) {
        return { payload: {}, error: 'Load Type is required for KeHE.' };
      }
      if (values.loadType?.trim()) {
        payload.loadType = values.loadType.trim();
      }
    }

    if (config.showFields.carrierCC && values.carrierCC?.trim()) {
      payload.carrierCC = values.carrierCC.trim();
    }

    // Comment (optional for all tenants)
    if (config.showFields.comment && values.comment?.trim()) {
      payload.comment = values.comment.trim();
    }

    return { payload };
  };

  const handleLoadAvailableSlots = async () => {
    const formValues = getValues();

    if (!formValues.proId?.trim()) {
      toast({
        description: 'Please enter a PRO / PO number.',
        variant: 'destructive',
      });
      return;
    }

    const start = dayjs(formValues.startDate);
    const end = dayjs(formValues.endDate);

    if (!start.isValid() || !end.isValid()) {
      toast({
        description: 'Please select a valid date range.',
        variant: 'destructive',
      });
      return;
    }

    if (end.isBefore(start)) {
      toast({
        description: 'End date must be after start date.',
        variant: 'destructive',
      });
      return;
    }

    const effectiveTenant = extractTenantSlug(tenant || '');
    const { payload: formDataPayload, error } = buildFormDataPayload(
      formValues,
      effectiveTenant
    );
    if (error) {
      toast({
        description: error,
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingSlots(true);
    setSelectedSlot(null);

    try {
      setApptConfirmationNumber('');
      setShowUpdateTMSLoadWithAppt(false);

      const res = await getOpenApptSlots({
        source: SchedulingPortals.C3Reservations,
        loadTypeID: formValues.proId.trim(),
        freightTrackingID: load.freightTrackingID || '',
        warehouseTimezoneStartDate: start,
        warehouseTimezoneEndDate: end,
        integrationID,
        formData: formDataPayload,
      });

      if (res.isOk()) {
        const slots = res.value;
        setOrderedSlots(slots);

        if (!slots?.slots || Object.keys(slots.slots).length === 0) {
          toast({
            description:
              'No available slots were found for the selected dates.',
            variant: 'default',
          });
          return;
        }

        scrollResultsIntoViewRef.current?.scrollIntoView({
          behavior: 'smooth',
        });
        toast({
          description: 'Available slots retrieved successfully.',
          variant: 'success',
        });
      } else {
        toast({
          description:
            res.error.message || 'Failed to load available appointments.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots) {
      toast({
        description:
          'Please select a time slot before requesting an appointment.',
        variant: 'destructive',
      });
      return;
    }

    const formValues = getValues();

    const effectiveTenant = extractTenantSlug(tenant || '');
    const { payload: formDataPayload, error } = buildFormDataPayload(
      formValues,
      effectiveTenant
    );
    if (error) {
      toast({
        description: error,
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingConfirm(true);

    const warehouseTimezone =
      orderedSlots.warehouseTimezone || selectedSlot.timezone || 'UTC';

    const details: string[] = [];
    if (formValues.unloadType) {
      details.push(`Unload Type: ${formValues.unloadType}`);
    }
    if (formValues.casesOrPalletQuantity) {
      details.push(`Quantity: ${formValues.casesOrPalletQuantity}`);
    }
    if (formValues.loadType) {
      details.push(`Load Type: ${formValues.loadType}`);
    }
    if (formValues.carrierCC) {
      details.push(`Carrier CC: ${formValues.carrierCC}`);
    }
    if (formValues.comment) {
      details.push(`Comment: ${formValues.comment}`);
    }

    const notes =
      details.length > 0 ? `${details.join(' | ')}` : formValues.comment || '';

    try {
      const res = await confirmApptAndUpdateTMS({
        confirmApptProps: {
          source: SchedulingPortals.C3Reservations,
          isTMSLoad: false,
          stopType: type,
          start: selectedSlot.startTime,
          loadTypeId: formValues.proId.trim(),
          warehouseID:
            orderedSlots.warehouse?.warehouseID ||
            selectedSlot.dock.warehouseId ||
            'c3reservations',
          warehouseTimezone,
          dockId: selectedSlot.dock.id || 'c3reservations',
          loadID: load.ID!,
          freightTrackingId: load.freightTrackingID || '',
          notes,
          poNums: formValues.proId.trim(),
          email: formValues.email,
          phone: formValues.phone,
          formData: formDataPayload,
        },
        featureFlagEnabled: isAppointmentTMSUpdateEnabled,
        tmsName,
        timezone: warehouseTimezone,
        stopType: type,
        loadId: load.ID!,
        freightTrackingId: load.freightTrackingID || '',
        onTmsUpdateComplete: (succeeded, errorMessage) => {
          setTMSUpdateSucceeded(!!succeeded);
          if (succeeded) {
            toast({
              description: 'Load updated in TMS.',
              variant: 'success',
            });
          } else if (errorMessage) {
            toast({
              description: errorMessage,
              variant: 'destructive',
            });
          }
        },
      });

      if (res.confirmResult) {
        setApptConfirmationNumber(res.confirmResult.ConfirmationNo);
        if (
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
          isAppointmentTMSUpdateEnabled
        ) {
          toast({
            description:
              'Appointment requested. The TMS update will run in the background.',
            variant: 'default',
          });
        } else {
          toast({
            description: 'Appointment requested successfully!',
            variant: 'success',
          });
        }
        setShowUpdateTMSLoadWithAppt(
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
            !isAppointmentTMSUpdateEnabled
        );
      } else {
        const message =
          res.confirmError?.message || 'Failed to create appointment.';
        toast({
          description: message,
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'An error occurred while creating the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  const onInvalid: SubmitErrorHandler<
    C3ReservationsInputsWithoutLoad
  > = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form
          onSubmit={handleSubmit(handleConfirmAppointment, onInvalid)}
          className='flex flex-col gap-4 mt-4 mx-0 w-full'
        >
          <div className='rounded-lg border border-neutral-400 p-4 bg-neutral-50 shadow-sm'>
            <div className='mb-4'>
              <Typography variant='h5' className='mb-2' weight='medium'>
                Appointment Scheduling
              </Typography>
              <Typography variant='body-xs' textColor='muted'>
                Enter the PRO / PO number, date range, and required reservation
                details to find and book available slots.
              </Typography>
              {tenantName && (
                <Typography
                  variant='body-xs'
                  textColor='muted'
                  className='mt-1'
                >
                  Tenant: {tenantName}
                </Typography>
              )}
            </div>

            <Flex direction='col' gap='md' className='w-full'>
              {/* PRO/PO Number */}
              <C3ReservationsTextInput
                name='proId'
                label='PRO / PO Number'
                required
              />

              {/* Date Range */}
              <Flex direction='row' gap='md' wrap='wrap'>
                <div className='flex-1 min-w-[220px]'>
                  <DateTimeInput
                    control={control}
                    name='startDate'
                    label='Start Date'
                    preventNormalizedLabelTZ={true}
                    hideAIHint={true}
                    hideTimePicker={true}
                  />
                </div>
                <div className='flex-1 min-w-[220px]'>
                  <DateTimeInput
                    control={control}
                    name='endDate'
                    label='End Date'
                    preventNormalizedLabelTZ={true}
                    hideAIHint={true}
                    hideTimePicker={true}
                  />
                </div>
              </Flex>

              {/* Contact Information */}
              {fieldConfig.showFields.contactName && (
                <C3ReservationsTextInput
                  name='contactName'
                  label='Contact Name'
                  required={fieldConfig.requiredFields.contactName}
                />
              )}

              {fieldConfig.showFields.email && (
                <C3ReservationsTextInput
                  name='email'
                  label='Email'
                  required={fieldConfig.requiredFields.email}
                  placeholder='<EMAIL>'
                />
              )}

              {fieldConfig.showFields.phone && (
                <C3ReservationsTextInput
                  name='phone'
                  label='Phone#'
                  placeholder='(*************'
                />
              )}

              {/* Dollar Tree Specific Fields */}
              {fieldConfig.showFields.unloadType && (
                <Flex direction='row' gap='md' wrap='wrap'>
                  <div className='flex-1 min-w-[220px]'>
                    <Label htmlFor='unloadType' name='unloadType'>
                      Unload Type
                      {fieldConfig.requiredFields.unloadType && (
                        <span className='text-error-500'> *</span>
                      )}
                    </Label>
                    <Select
                      value={unloadTypeValue}
                      onValueChange={(value: string) =>
                        setValue('unloadType', value, { shouldDirty: true })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select unload type' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Drop'>Drop</SelectItem>
                        <SelectItem value='Import'>Import</SelectItem>
                        <SelectItem value='Live'>Live</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {fieldConfig.showFields.casesOrPalletQuantity && (
                    <div className='flex-1 min-w-[220px]'>
                      <C3ReservationsTextInput
                        name='casesOrPalletQuantity'
                        label='Cases or Pallet Quantity (if pallets)'
                        inputType='number'
                        required={
                          fieldConfig.requiredFields.casesOrPalletQuantity
                        }
                      />
                    </div>
                  )}
                </Flex>
              )}

              {/* KeHE Specific Fields */}
              {fieldConfig.showFields.loadType && (
                <Flex direction='row' gap='md' wrap='wrap'>
                  <div className='flex-1 min-w-[220px]'>
                    <Label htmlFor='loadType' name='loadType'>
                      Load Type
                      {fieldConfig.requiredFields.loadType && (
                        <span className='text-error-500'> *</span>
                      )}
                    </Label>
                    <Select
                      value={loadTypeValue}
                      onValueChange={(value: string) =>
                        setValue('loadType', value, { shouldDirty: true })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select load type' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Cases Stacked on Floor'>
                          Cases Stacked on Floor
                        </SelectItem>
                        <SelectItem value='Cases Stacked on Pallets'>
                          Cases Stacked on Pallets
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {fieldConfig.showFields.carrierCC && (
                    <div className='flex-1 min-w-[220px]'>
                      <C3ReservationsTextInput
                        name='carrierCC'
                        label='Carrier CC'
                      />
                    </div>
                  )}
                </Flex>
              )}

              {/* Comment */}
              {fieldConfig.showFields.comment && (
                <C3ReservationsTextInput
                  name='comment'
                  label='Comment / Special Instructions'
                />
              )}

              {/* Get Open Slots Button */}
              <Button
                type='button'
                className='w-full'
                disabled={isLoadingSlots}
                buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                onClick={handleLoadAvailableSlots}
              >
                {isLoadingSlots ? (
                  <ButtonLoader />
                ) : (
                  ButtonText.GetOpenApptSlots
                )}
              </Button>
            </Flex>
          </div>

          {/* Slots Display and Appointment Confirmation */}
          {orderedSlots && (
            <div
              className='rounded-lg border border-neutral-400 p-4 bg-neutral-50 shadow-sm'
              ref={scrollResultsIntoViewRef}
            >
              <div className='mb-4'>
                <Typography variant='h5' className='mb-2' weight='medium'>
                  Available Slots
                </Typography>
              </div>

              {Object.entries(orderedSlots.slots).map(([date, slots]) => (
                <div key={date} className='mt-3'>
                  <Typography variant='body-sm' weight='medium'>
                    {date}
                  </Typography>
                  <Grid cols='3' gap='xs' className='mt-2 mx-0 w-full'>
                    {slots.map((slot, idx) => (
                      <button
                        type='button'
                        key={`${date}-${idx}`}
                        onClick={() =>
                          setSelectedSlot(selectedSlot === slot ? null : slot)
                        }
                        className={cn(
                          'text-neutral-900 bg-neutral-50 border border-neutral-400 p-2 rounded cursor-pointer text-sm',
                          selectedSlot === slot &&
                            'bg-brand border-brand-700 text-neutral-50'
                        )}
                      >
                        {dayjs(slot.startTime)
                          .tz(orderedSlots.warehouseTimezone || 'UTC')
                          .format('MMM D, HH:mm')}
                      </button>
                    ))}
                  </Grid>
                </div>
              ))}

              {selectedSlot && (
                <div className='mt-4 text-neutral-400 text-left text-sm'>
                  <Typography weight='bold' className='my-1'>
                    Selected Slot:
                  </Typography>
                  <Typography className='mb-2'>
                    {dayjs(selectedSlot.startTime)
                      .tz(orderedSlots.warehouseTimezone || 'UTC')
                      .format('MMM D, YYYY, HH:mm')}
                  </Typography>
                </div>
              )}

              {apptConfirmationNumber ? (
                <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                  <Typography className='mb-2'>
                    Appointment requested successfully! 🎉
                  </Typography>
                  <Typography variant='body-sm' className='mb-1'>
                    Confirmation #: {apptConfirmationNumber}
                  </Typography>
                  <Typography variant='body-sm'>
                    {isTMSUpdateLoadAppointmentSupported(tmsName) &&
                    isAppointmentTMSUpdateEnabled
                      ? tmsUpdateSucceeded
                        ? 'Your TMS was updated with the appointment details.'
                        : 'Your TMS update will run in the background.'
                      : 'Make sure to update your TMS with the scheduled appointment.'}
                  </Typography>
                </div>
              ) : (
                <Button
                  buttonNamePosthog={
                    ButtonNamePosthog.ConfirmSlotApptScheduling
                  }
                  className='mt-4 w-full'
                  type='submit'
                  disabled={isLoadingConfirm || !selectedSlot}
                >
                  {isLoadingConfirm ? <ButtonLoader /> : 'Request Appointment'}
                </Button>
              )}

              {apptConfirmationNumber &&
              showUpdateTMSLoadWithAppt &&
              selectedSlot ? (
                <UpdateTMSLoadWithAppt
                  load={load}
                  stopType={type}
                  appointmentStartTime={selectedSlot.startTime}
                  appointmentEndTime={
                    new Date(selectedSlot.startTime.getTime() + 60 * 60 * 1000)
                  }
                  freightTrackingId={load.freightTrackingID || ''}
                  onSuccess={() => setTMSUpdateSucceeded(true)}
                  onError={(error) =>
                    console.error('TMS update failed:', error)
                  }
                />
              ) : null}
            </div>
          )}
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
