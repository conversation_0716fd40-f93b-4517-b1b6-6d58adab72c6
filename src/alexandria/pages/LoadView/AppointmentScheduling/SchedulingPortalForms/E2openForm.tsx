import { useContext, useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>ath,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import { Divider } from 'antd';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { RefreshCw } from 'lucide-react';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { UpdateTMSLoadWithAppt } from 'components/UpdateTMSLoadWithAppt';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { ServiceContext } from 'contexts/serviceContext';
import { useToast } from 'hooks/useToaster';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import { submitAppt } from 'lib/api/submitAppt';
import {
  type CompanyInfo,
  type DateField,
  type OperationInfo,
  type ProIdField,
  validateAppt,
} from 'lib/api/validateAppt';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import { APPOINTMENT_DATE_REQUIREMENTS } from '../constants/e2openScheduling';
import { confirmApptAndUpdateTMS } from '../helpers/confirmApptAndUpdateTMS';
import { isTMSUpdateLoadAppointmentSupported } from '../helpers/helpers';

dayjs.extend(utc);

interface E2openInputsWithoutLoad {
  freightTrackingID: string;
  startDateTime: Date;
  endDateTime: Date;
  shouldRequestLumper: boolean;
  apptNote: string;
  requestedDateTime: Date;
  appointmentType: 'normal' | '3rd_party';
  company: string;
  operation: string;
  zipCode: string;
  city: string;
  state: string;
  country: string;
  scac: string;
  appointmentDate: Date; // Required for specific companies/operations
}

type E2openTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<E2openInputsWithoutLoad>;
};
const E2openTextInput = (props: E2openTextInputProps) => (
  <RHFTextInput {...props} />
);

interface E2openFormProps {
  type: StopTypes;
  load: NormalizedLoad;
  integrationID?: number;
  tmsName: string;
  isAppointmentTMSUpdateEnabled: boolean;
}

export function E2openForm({
  type,
  load,
  integrationID,
  tmsName,
  isAppointmentTMSUpdateEnabled,
}: E2openFormProps) {
  const { schedulerConfig } = useContext(ServiceContext);
  const { toast } = useToast();
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [isValidatingPRONum, setIsValidatingPRONum] = useState(false);
  const [isValidPRONum, setIsValidPRONum] = useState(false);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isFetchingWarehouseDetails, setIsFetchingWarehouseDetails] =
    useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [warehouseDetails, setWarehouseDetails] =
    useState<Maybe<OrderedSlots>>(null);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [liveAppointmentsAvailable, setLiveAppointmentsAvailable] =
    useState(true);
  const [_tmsUpdateSucceeded, setTMSUpdateSucceeded] = useState(false);
  const [showUpdateTMSLoadWithAppt, setShowUpdateTMSLoadWithAppt] =
    useState(false);

  // Dynamic company/operation selection states
  const [isFetchingCompanies, setIsFetchingCompanies] = useState(false);
  const [isFetchingOperations, setIsFetchingOperations] = useState(false);
  const [dynamicCompanies, setDynamicCompanies] = useState<CompanyInfo[]>([]);
  const [dynamicOperations, setDynamicOperations] = useState<OperationInfo[]>(
    []
  );
  const [requiresCompanySelection, setRequiresCompanySelection] =
    useState(false);
  const [requiresOperationSelection, setRequiresOperationSelection] =
    useState(false);
  const [canShowValidateButton, setCanShowValidateButton] = useState(false);
  const [operationsChecked, setOperationsChecked] = useState(false);

  // Dynamic form field selection states
  const [proIdFields, setProIdFields] = useState<ProIdField[]>([]);
  const [dateFields, setDateFields] = useState<DateField[]>([]);
  const [selectedProIdField, setSelectedProIdField] = useState<string>('');
  const [isFetchingFormFields, setIsFetchingFormFields] = useState(false);

  const formMethods = useForm<E2openInputsWithoutLoad>({
    defaultValues: {
      freightTrackingID: load.freightTrackingID || '',
      startDateTime: new Date(),
      endDateTime: dayjs().add(7, 'days').toDate(),
      shouldRequestLumper: false,
      apptNote: '',
      requestedDateTime: new Date(),
      appointmentType: 'normal',
      company: '',
      operation: '',
      zipCode: load.pickup.zipCode || '',
      city: load.pickup.city || '',
      state: load.pickup.state || '',
      country: load.pickup.country || '',
      scac: schedulerConfig?.scac || '',
      appointmentDate: new Date(),
    },
    mode: 'onChange',
  });

  const { control, handleSubmit, getValues, watch } = formMethods;

  // Watch for changes in form values
  const appointmentType = watch('appointmentType');
  const company = watch('company');
  const operation = watch('operation');

  // Check if appointment date is required for current company/operation/stop type
  const isAppointmentDateRequired = () => {
    if (appointmentType !== '3rd_party') {
      return false;
    }

    // Use dynamic date fields if available (new approach)
    if (dateFields.length > 0) {
      return dateFields.some((field) => field.exists);
    }

    // Fallback to hardcoded requirements (old approach for backward compatibility)
    if (!company) {
      return false;
    }

    const companyRequirements =
      APPOINTMENT_DATE_REQUIREMENTS[
        company as keyof typeof APPOINTMENT_DATE_REQUIREMENTS
      ];

    if (!companyRequirements) {
      return false;
    }

    // Check if it's a company-level requirement (like ken's foods)
    if ('pickup' in companyRequirements && 'dropoff' in companyRequirements) {
      const isRequired = companyRequirements[type];
      return isRequired;
    }

    // Check if it's an operation-level requirement (like american sugar refining)
    if (
      operation &&
      typeof companyRequirements === 'object' &&
      operation in companyRequirements
    ) {
      const operationRequirements =
        companyRequirements[operation as keyof typeof companyRequirements];
      if (
        typeof operationRequirements === 'object' &&
        'pickup' in operationRequirements &&
        'dropoff' in operationRequirements
      ) {
        const isRequired = operationRequirements[type];
        return isRequired;
      }
    }

    return false;
  };

  useEffect(() => {
    if (isValidPRONum) {
      scrollResultsIntoViewRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isValidPRONum]);

  // Reset form state when integrationID changes (user switching)
  useEffect(() => {
    // Reset all form-related state when user changes
    setIsValidatingPRONum(false);
    setIsValidPRONum(false);
    setIsLoadingSlots(false);
    setIsFetchingWarehouseDetails(false);
    setIsLoadingSubmit(false);
    setIsLoadingConfirm(false);
    setWarehouseDetails(null);
    setOrderedSlots(null);
    setSelectedSlot(null);
    setApptConfirmationNumber('');
    setLiveAppointmentsAvailable(true);
    setTMSUpdateSucceeded(false);
    setShowUpdateTMSLoadWithAppt(false);

    // Reset dynamic company/operation selection states
    setIsFetchingCompanies(false);
    setIsFetchingOperations(false);
    setDynamicCompanies([]);
    setDynamicOperations([]);
    setRequiresCompanySelection(false);
    setRequiresOperationSelection(false);
    setCanShowValidateButton(false);
    setOperationsChecked(false);

    // Reset dynamic form field selection states
    setIsFetchingFormFields(false);
    setProIdFields([]);
    setDateFields([]);
    setSelectedProIdField('');

    // Reset form values to defaults
    formMethods.reset({
      freightTrackingID: load?.freightTrackingID || '',
      appointmentType: 'normal',
      company: '',
      operation: '',
      zipCode: load?.pickup?.zipCode || '',
      city: load?.pickup?.city || '',
      state: load?.pickup?.state || '',
      country: load?.pickup?.country || '',
      appointmentDate: new Date(),
      startDateTime: new Date(),
      endDateTime: dayjs().add(7, 'days').toDate(),
      scac: '',
    });
  }, [integrationID]);

  // Reset dependent state when company changes
  useEffect(() => {
    if (appointmentType === '3rd_party') {
      // Reset operations and form fields when company changes
      setDynamicOperations([]);
      setRequiresOperationSelection(false);
      setOperationsChecked(false);
      setCanShowValidateButton(false);

      // Reset form fields
      setProIdFields([]);
      setDateFields([]);
      setSelectedProIdField('');

      // Reset validation state
      setIsValidPRONum(false);
      setWarehouseDetails(null);
      setOrderedSlots(null);
      setSelectedSlot(null);
      setApptConfirmationNumber('');
      setLiveAppointmentsAvailable(true);
    }
  }, [company, appointmentType]);

  // Reset dependent state when operation changes
  useEffect(() => {
    if (appointmentType === '3rd_party' && operation) {
      // Reset form fields when operation changes
      setProIdFields([]);
      setDateFields([]);
      setSelectedProIdField('');

      // Reset validation state
      setIsValidPRONum(false);
      setWarehouseDetails(null);
      setOrderedSlots(null);
      setSelectedSlot(null);
      setApptConfirmationNumber('');
      setLiveAppointmentsAvailable(true);

      // Auto-fetch form fields whenever operation changes
      if (requiresOperationSelection) {
        toast({
          description: 'Fetching form fields for selected operation...',
          variant: 'default',
        });
        handleFetchFormFields();
      }
    }
  }, [operation, appointmentType]);

  // Reset everything when stop type (type prop) changes
  useEffect(() => {
    if (appointmentType === '3rd_party') {
      // Reset all 3rd party related state
      setDynamicCompanies([]);
      setDynamicOperations([]);
      setRequiresCompanySelection(false);
      setRequiresOperationSelection(false);
      setCanShowValidateButton(false);
      setOperationsChecked(false);

      // Reset form fields
      setProIdFields([]);
      setDateFields([]);
      setSelectedProIdField('');

      // Reset validation state
      setIsValidPRONum(false);
      setWarehouseDetails(null);
      setOrderedSlots(null);
      setSelectedSlot(null);
      setApptConfirmationNumber('');
      setLiveAppointmentsAvailable(true);

      // Reset form values for company and operation
      formMethods.setValue('company', '');
      formMethods.setValue('operation', '');
    }
  }, [type, appointmentType, formMethods]);

  // Handler for hard refresh - clears cache and refetches all data
  const handleHardRefresh = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent form submission

    toast({
      description: 'Refreshing data from server...',
      variant: 'default',
    });

    // Reset all 3rd party state
    setDynamicCompanies([]);
    setDynamicOperations([]);
    setRequiresCompanySelection(false);
    setRequiresOperationSelection(false);
    setCanShowValidateButton(false);
    setOperationsChecked(false);
    setProIdFields([]);
    setDateFields([]);
    setSelectedProIdField('');
    formMethods.setValue('company', '');
    formMethods.setValue('operation', '');

    // Call search company with hardRefresh flag
    await handleSearchCompany(true);
  };

  // Handler to fetch companies for 3rd party flow
  const handleSearchCompany = async (hardRefresh: boolean = false) => {
    setIsFetchingCompanies(true);
    setDynamicCompanies([]);
    setDynamicOperations([]);
    setRequiresCompanySelection(false);
    setRequiresOperationSelection(false);
    setCanShowValidateButton(false);
    setOperationsChecked(false);

    try {
      const formValues = getValues();
      const validateRes = await validateAppt(
        'placeholder',
        SchedulingPortals.E2open,
        [formValues.freightTrackingID || 'temp'],
        {
          requestType: type,
          zipCode: formValues.zipCode || '',
          city: formValues.city || '',
          state: formValues.state || '',
          country: formValues.country || '',
          integrationID,
          appointmentType: '3rd_party',
          fetchCompanies: true,
          hardRefresh,
        }
      );

      if (validateRes.isOk()) {
        const response = validateRes.value;

        if (
          response.requiresCompanySelection &&
          response.companies &&
          response.companies.length > 0
        ) {
          // Show company dropdown
          setDynamicCompanies(response.companies);
          setRequiresCompanySelection(true);
          toast({
            description: `Found ${response.companies.length} companies`,
            variant: 'default',
          });
        } else {
          // Direct access - no company/operation selection needed
          setCanShowValidateButton(true);
          toast({
            description: 'Direct access available. Fetching form fields...',
            variant: 'default',
          });
          // Auto-fetch form fields for direct access
          await handleFetchFormFields();
        }
      } else {
        const errorMessage =
          validateRes.error?.message || 'Failed to fetch companies';
        toast({
          description: errorMessage,
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'Failed to fetch companies',
        variant: 'destructive',
      });
    } finally {
      setIsFetchingCompanies(false);
    }
  };

  // Handler to fetch operations when company is selected
  const handleCompanyChange = async (selectedCompany: string) => {
    formMethods.setValue('company', selectedCompany);
    formMethods.setValue('operation', '');
    setDynamicOperations([]);
    setRequiresOperationSelection(false);
    setCanShowValidateButton(false);
    setOperationsChecked(false);

    setIsFetchingOperations(true);

    try {
      const formValues = getValues();
      const validateRes = await validateAppt(
        'placeholder',
        SchedulingPortals.E2open,
        [formValues.freightTrackingID || 'temp'],
        {
          requestType: type,
          zipCode: formValues.zipCode,
          city: formValues.city,
          state: formValues.state,
          country: formValues.country,
          integrationID,
          appointmentType: '3rd_party',
          company: selectedCompany,
          fetchOperations: true,
        }
      );

      if (validateRes.isOk()) {
        const response = validateRes.value;

        if (response.requiresOperationSelection && response.operations) {
          // Show operation dropdown
          setDynamicOperations(response.operations);
          setRequiresOperationSelection(true);
          toast({
            description: `Found ${response.operations.length} operations. Select an operation to move further.`,
            variant: 'default',
          });
        } else {
          // No operations needed - automatically fetch form fields
          setCanShowValidateButton(true);
          toast({
            description: 'No operations required. Fetching form fields...',
            variant: 'default',
          });
          await handleFetchFormFields();
        }
      } else {
        const errorMessage =
          validateRes.error?.message || 'Failed to fetch operations';
        toast({
          description: errorMessage,
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'Failed to fetch operations',
        variant: 'destructive',
      });
    } finally {
      setIsFetchingOperations(false);
      setOperationsChecked(true);
    }
  };

  // Explicit handler for Search Operations button
  const handleSearchOperations = async () => {
    const selectedCompany = getValues('company');
    if (!selectedCompany) return;
    await handleCompanyChange(selectedCompany);
  };

  // Handler when operation is selected
  const handleOperationChange = (selectedOperation: string) => {
    formMethods.setValue('operation', selectedOperation);
    setCanShowValidateButton(true);
  };

  // Handler to fetch form fields (PRO ID and date fields)
  const handleFetchFormFields = async () => {
    setIsFetchingFormFields(true);
    setProIdFields([]);
    setDateFields([]);
    setSelectedProIdField('');

    try {
      const formValues = getValues();
      const validateRes = await validateAppt(
        'placeholder',
        SchedulingPortals.E2open,
        [formValues.freightTrackingID || 'temp'],
        {
          requestType: type,
          stopType: type,
          zipCode: formValues.zipCode,
          city: formValues.city,
          state: formValues.state,
          country: formValues.country,
          integrationID,
          appointmentType: '3rd_party',
          company: formValues.company,
          operation: formValues.operation,
          appointmentDate: formValues.appointmentDate,
          fetchFormFields: true,
        }
      );

      if (validateRes.isOk()) {
        const response = validateRes.value;

        if (response.proIdFields && response.proIdFields.length > 0) {
          setProIdFields(response.proIdFields);
          // Auto-select first field if only one available
          if (response.proIdFields.length === 1) {
            setSelectedProIdField(response.proIdFields[0].fieldName);
          }
          toast({
            description: `Found ${response.proIdFields.length} PRO ID field(s)`,
            variant: 'default',
          });
        }

        if (response.dateFields && response.dateFields.length > 0) {
          setDateFields(response.dateFields);
          toast({
            description: `Form fields fetched successfully.`,
            variant: 'default',
          });
        }
      } else {
        toast({
          description: `Failed to fetch form fields: ${validateRes.error?.message || 'Unknown error'}`,
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'Failed to fetch form fields',
        variant: 'destructive',
      });
    } finally {
      setIsFetchingFormFields(false);
    }
  };

  const validatePRONumber = async (proNumber: string) => {
    setIsValidatingPRONum(true);

    try {
      const formValues = getValues();
      const additionalParams: any = {};
      additionalParams.appointmentType = formValues.appointmentType;
      if (formValues.appointmentType === '3rd_party') {
        additionalParams.operation = formValues.operation;
        additionalParams.company = formValues.company;
        additionalParams.scac = formValues.scac;

        // Validate that PRO ID field is selected when available
        if (proIdFields.length > 0 && !selectedProIdField) {
          toast({
            description:
              'Please select a PRO ID field before validating appointment',
            variant: 'destructive',
          });
          setIsValidatingPRONum(false);
          return false;
        }

        // Include selected PRO ID field name if available
        if (selectedProIdField) {
          additionalParams.proIdFieldName = selectedProIdField;
        }

        // Include appointment date if required
        if (isAppointmentDateRequired() && formValues.appointmentDate) {
          additionalParams.appointmentDate =
            formValues.appointmentDate.toISOString();
        }
      }
      const validateRes = await validateAppt(
        'placeholder',
        SchedulingPortals.E2open,
        [proNumber],
        {
          requestType: type,
          zipCode: formValues.zipCode,
          city: formValues.city,
          state: formValues.state,
          country: formValues.country,
          integrationID,
          ...additionalParams,
        }
      );

      if (validateRes.isOk()) {
        const { validatedPONumbers: validatedPRONumbers } = validateRes.value;

        if (validatedPRONumbers.length === 0) {
          toast({
            description: 'No PRO number to validate.',
            variant: 'default',
          });
          setIsValidatingPRONum(false);
          return false;
        }

        const proValidation = validatedPRONumbers[0];

        if (!proValidation.isValid) {
          if (proValidation.error?.includes('FCFS scheduling')) {
            setLiveAppointmentsAvailable(false);
            setIsValidPRONum(true);
            toast({
              description:
                'Live appointments not available. Please use Option 1 to submit an appointment request.',
              variant: 'default',
            });
            return true;
          } else if (
            proValidation.error?.includes('No pickup/dropoff locations found')
          ) {
            toast({
              description: 'Invalid PRO number: No locations found.',
              variant: 'destructive',
            });
            setIsValidatingPRONum(false);
            return false;
          } else {
            toast({
              description: proValidation.error || 'PRO number is invalid.',
              variant: 'destructive',
            });
            setIsValidatingPRONum(false);
            return false;
          }
        } else {
          setIsValidPRONum(true);
          setLiveAppointmentsAvailable(true);
        }

        setIsValidatingPRONum(false);
        return true;
      } else {
        toast({
          description: 'Failed to validate PRO number.',
          variant: 'destructive',
        });
        setIsValidatingPRONum(false);
        return false;
      }
    } catch {
      toast({
        description: 'An error occurred during validation.',
        variant: 'destructive',
      });
      setIsValidatingPRONum(false);
      return false;
    }
  };

  const loadAvailableSlots = async () => {
    const formValues = getValues();
    const defaultStart = dayjs().startOf('day');
    const defaultEnd = dayjs().add(7, 'days').endOf('day');
    const start = formValues.startDateTime
      ? dayjs(formValues.startDateTime)
      : defaultStart;
    const end = formValues.endDateTime
      ? dayjs(formValues.endDateTime)
      : defaultEnd;
    const today = dayjs().startOf('day');

    if (start && !end) {
      toast({
        description: 'End date is required when start date is provided',
        variant: 'destructive',
      });
      return;
    }

    if (start && end && start.isAfter(end)) {
      toast({
        description: formValues.endDateTime
          ? 'Start date cannot be after end date'
          : 'Please select an end date',
        variant: 'destructive',
      });
      return;
    }

    if (start?.isBefore(today, 'day') || end?.isBefore(today, 'day')) {
      toast({
        description: 'Appointment dates cannot be in the past',
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingSlots(true);

    // Prepare additional parameters for 3rd party requests
    const additionalParams: any = {};
    additionalParams.appointmentType = formValues.appointmentType;
    if (formValues.appointmentType === '3rd_party') {
      additionalParams.operation = formValues.operation;
      additionalParams.company = formValues.company;
      additionalParams.scac = formValues.scac;

      // Validate that PRO ID field is selected when available
      if (proIdFields.length > 0 && !selectedProIdField) {
        toast({
          description: 'Please select a PRO ID field before getting open slots',
          variant: 'destructive',
        });
        setIsLoadingSlots(false);
        return;
      }

      // Include selected PRO ID field name if available
      if (selectedProIdField) {
        additionalParams.proIdFieldName = selectedProIdField;
      }

      // Include appointment date if required
      if (isAppointmentDateRequired() && formValues.appointmentDate) {
        additionalParams.appointmentDate =
          formValues.appointmentDate.toISOString();
      }
    }

    try {
      const res = await getOpenApptSlots({
        source: SchedulingPortals.E2open,
        freightTrackingID: formValues.freightTrackingID,
        loadTypeID: 'placeholder',
        requestType: type,
        startDateTime: start,
        endDateTime: end,
        zipCode: formValues.zipCode,
        city: formValues.city,
        state: formValues.state,
        country: formValues.country,
        integrationID,
        ...additionalParams,
      });

      if (res.isOk()) {
        setOrderedSlots(res.value);
        setLiveAppointmentsAvailable(true);

        if (!res.value?.slots || Object.keys(res.value.slots).length === 0) {
          toast({
            description: 'No available appointment times found.',
            variant: 'default',
          });
          return false;
        }
        return true;
      } else {
        if (
          res.error.message ===
          'Live appointments are not available at this time'
        ) {
          setLiveAppointmentsAvailable(false);
          toast({
            description:
              'Option 2 (Live Appointments) is not available for ' +
              'this warehouse. Please use Option 1 to submit an ' +
              'appointment request.',
            variant: 'default',
          });
        } else {
          toast({
            description:
              res.error.message || 'Failed to load available appointments.',
            variant: 'destructive',
          });
        }
        return false;
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleValidateAppointment = async () => {
    const { freightTrackingID } = getValues();
    await validatePRONumber(freightTrackingID);
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }

    const formValues = getValues();

    // Prepare additional parameters for 3rd party requests
    const additionalParams: any = {};

    additionalParams.appointmentType = formValues.appointmentType;
    if (formValues.appointmentType === '3rd_party') {
      additionalParams.operation = formValues.operation;
      additionalParams.company = formValues.company;
      additionalParams.scac = formValues.scac;

      // Validate that PRO ID field is selected when available
      if (proIdFields.length > 0 && !selectedProIdField) {
        toast({
          description:
            'Please select a PRO ID field before confirming appointment',
          variant: 'destructive',
        });
        return;
      }

      // Include selected PRO ID field name if available
      if (selectedProIdField) {
        additionalParams.proIdFieldName = selectedProIdField;
      }

      // Include appointment date if required
      if (isAppointmentDateRequired() && formValues.appointmentDate) {
        additionalParams.appointmentDate =
          formValues.appointmentDate.toISOString();
      }
    }

    setIsLoadingConfirm(true);

    try {
      let trailerType = '';
      if (load.specifications && load.specifications.transportType) {
        trailerType = load.specifications?.transportType
          ?.toLowerCase()
          .split(' ')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }

      const confirmApptProps = {
        source: SchedulingPortals.E2open,
        isTMSLoad: false,
        stopType: type,
        start: selectedSlot.startTime,
        loadTypeId: 'placeholder',
        warehouseID: '-',
        warehouseTimezone: 'UTC',
        dockId: 'placeholder',
        loadID: load.ID!,
        freightTrackingId: formValues.freightTrackingID,
        integrationID: integrationID,
        appointments: [
          {
            start: selectedSlot.startTime,
            freightTrackingId: formValues.freightTrackingID,
            zipCode: formValues.zipCode,
            city: formValues.city,
            state: formValues.state,
            country: formValues.country,
            scac: formValues.scac,
          },
        ],
        requestType: type,
        trailerType: trailerType,
        ...additionalParams,
      };

      const res = await confirmApptAndUpdateTMS({
        confirmApptProps,
        featureFlagEnabled: isAppointmentTMSUpdateEnabled,
        tmsName,
        timezone: undefined,
        stopType: type,
        loadId: load.ID!,
        freightTrackingId: formValues.freightTrackingID,
        onTmsUpdateComplete: (succeeded, errorMessage) => {
          setTMSUpdateSucceeded(!!succeeded);
          if (succeeded) {
            toast({ description: 'Load updated in TMS.', variant: 'success' });
          } else if (errorMessage) {
            toast({ description: errorMessage, variant: 'destructive' });
          }
        },
      });

      if (res.confirmResult) {
        setApptConfirmationNumber(res.confirmResult.ConfirmationNo);
        if (
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
          isAppointmentTMSUpdateEnabled
        ) {
          toast({
            description: 'Appointment scheduled. Updating load in TMS.',
            variant: 'default',
          });
        }
        setShowUpdateTMSLoadWithAppt(
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
            !isAppointmentTMSUpdateEnabled
        );
      } else {
        if (res.confirmError?.message === 'Conflicting Appointments') {
          toast({
            title: 'Conflicting Appointments',
            description:
              "Make sure you don't have an existing appointment for this load.",
            variant: 'destructive',
          });
        } else {
          toast({
            description:
              res.confirmError?.message || 'Failed to confirm appointment.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        description: 'An error occurred while confirming the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  const handleSubmitAppointment = async () => {
    setIsLoadingSubmit(true);

    const formValues = getValues();
    const defaultStart = dayjs().startOf('day');
    const requestedDateTime = formValues.requestedDateTime
      ? dayjs(formValues.requestedDateTime)
      : defaultStart;

    const request = {
      warehouseId: '',
      source: SchedulingPortals.E2open,
      // treat freightTrackingID aka PRO # as PO # here
      poNumbers: [formValues.freightTrackingID],
      lumperRequested: false,
      note: '',
      requestedDateTime: requestedDateTime,
      timePreference: undefined,
      integrationID,
      loadID: load.ID!,
    };

    try {
      const submitRes = await submitAppt(request);

      if (submitRes.isOk()) {
        toast({
          description: 'Your appointment has been submitted.',
          variant: 'success',
        });
      } else {
        toast({
          description:
            submitRes.error.message ||
            'An error occurred while submitting your appointment.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'An error occurred while submitting your appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingSubmit(false);
    }
  };

  const onInvalid: SubmitErrorHandler<E2openInputsWithoutLoad> = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form
          onSubmit={handleSubmit(handleSubmitAppointment, onInvalid)}
          className='flex flex-col gap-4 mt-4 mx-0 w-full'
        >
          <Typography
            variant='body-xs'
            className='text-neutral-400 mb-1 italic'
          >
            Note: This scheduling form only works for E2open actions (not
            compatible with EDI)
          </Typography>

          <Flex direction='col' gap='lg' className='w-full'>
            {/* Appointment Type Dropdown */}
            <div className='flex flex-col gap-2 w-full'>
              <Flex
                direction='row'
                align='center'
                justify='between'
                className='w-full'
              >
                <Label htmlFor='appointmentType' name='appointmentType'>
                  Appointment Type *
                </Label>
                {appointmentType === '3rd_party' && (
                  <TooltipProvider>
                    <Tooltip delayDuration={10}>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={handleHardRefresh}
                          disabled={isFetchingCompanies}
                          type='button'
                          variant='ghost'
                          size='xs'
                          buttonNamePosthog={
                            ButtonNamePosthog.RefreshE2openCache
                          }
                        >
                          {isFetchingCompanies ? (
                            <ButtonLoader />
                          ) : (
                            <RefreshCw className='h-4 w-4 text-brand-main hover:text-brand-600' />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className='mr-1'>
                        <Typography className='text-sm text-neutral-700'>
                          Use this to hard refresh all data
                        </Typography>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </Flex>
              <Select
                value={appointmentType}
                onValueChange={(value: 'normal' | '3rd_party') => {
                  formMethods.setValue('appointmentType', value);
                  // Reset company and operation when changing appointment type
                  if (value === 'normal') {
                    formMethods.setValue('company', '');
                    formMethods.setValue('operation', '');
                  } else if (value === '3rd_party') {
                    // Reset all 3rd party related state when switching to 3rd party
                    setDynamicCompanies([]);
                    setDynamicOperations([]);
                    setRequiresCompanySelection(false);
                    setRequiresOperationSelection(false);
                    setCanShowValidateButton(false);
                    setOperationsChecked(false);
                    setProIdFields([]);
                    setDateFields([]);
                    setSelectedProIdField('');
                    formMethods.setValue('company', '');
                    formMethods.setValue('operation', '');
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select appointment type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='normal'>NORMAL</SelectItem>
                  <SelectItem value='3rd_party'>3RD PARTY</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Dynamic Company Dropdown - only show when we have companies from API */}
            {appointmentType === '3rd_party' &&
              requiresCompanySelection &&
              dynamicCompanies.length > 0 && (
                <div className='flex flex-col gap-2 w-full'>
                  <Label htmlFor='company' name='company'>
                    Company Name *
                  </Label>
                  <Select
                    value={company}
                    onValueChange={handleCompanyChange}
                    disabled={isFetchingOperations}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select company' />
                    </SelectTrigger>
                    <SelectContent>
                      {dynamicCompanies.map((comp) => (
                        <SelectItem key={comp.id} value={comp.name}>
                          {comp.name.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

            {/* Search Operations Button - show when a company is selected and we may need operations */}
            {appointmentType === '3rd_party' &&
              dynamicCompanies.length > 0 &&
              !operationsChecked && (
                <Button
                  buttonNamePosthog={ButtonNamePosthog.SearchE2openOperations}
                  className='w-full'
                  type='button'
                  disabled={isFetchingOperations || !company}
                  onClick={() => handleSearchOperations()}
                >
                  {isFetchingOperations ? (
                    <ButtonLoader />
                  ) : (
                    'Search Operations'
                  )}
                </Button>
              )}

            {/* Dynamic Operation Dropdown - only show when we have operations from API */}
            {appointmentType === '3rd_party' &&
              requiresOperationSelection &&
              dynamicOperations.length > 0 && (
                <div className='flex flex-col gap-2 w-full'>
                  <Label htmlFor='operation' name='operation'>
                    Operation *
                  </Label>
                  <Select
                    value={watch('operation')}
                    onValueChange={handleOperationChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select operation' />
                    </SelectTrigger>
                    <SelectContent>
                      {dynamicOperations.map((op) => (
                        <SelectItem key={op.id} value={op.name}>
                          {op.name.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

            {/* Dynamic PRO ID Field Selection */}
            {appointmentType === '3rd_party' && proIdFields.length > 0 && (
              <div className='flex flex-col gap-2 w-full'>
                <Label htmlFor='proIdField' name='proIdField' required>
                  Select PRO ID field to search by
                </Label>
                <Select
                  value={selectedProIdField}
                  onValueChange={setSelectedProIdField}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select PRO ID field' />
                  </SelectTrigger>
                  <SelectContent>
                    {proIdFields.map((field) => (
                      <SelectItem key={field.fieldName} value={field.fieldName}>
                        {field.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Dynamic Date Field Selection */}
            {appointmentType === '3rd_party' &&
              dateFields.length > 0 &&
              dateFields.some((field) => field.exists) && (
                <div className='flex flex-col gap-2 w-full'>
                  <DateTimeInput
                    control={control}
                    name='appointmentDate'
                    label={
                      dateFields.find((field) => field.exists)?.label ||
                      'Appointment Date'
                    }
                    placeholder='Select appointment date'
                    preventNormalizedLabelTZ={true}
                    hideAIHint={true}
                    hideTimePicker={true}
                  />
                </div>
              )}

            {/* PRO ID Input - show for normal flow or after form fields are fetched for 3rd party */}
            {(appointmentType === 'normal' ||
              (appointmentType === '3rd_party' &&
                (proIdFields.length > 0 || dateFields.length > 0))) && (
              <E2openTextInput
                name='freightTrackingID'
                label='PRO ID'
                placeholder='Enter PRO ID'
                required
              />
            )}

            {/* Address Fields - show for normal flow or after form fields are fetched for 3rd party */}
            {(appointmentType === 'normal' ||
              (appointmentType === '3rd_party' &&
                (proIdFields.length > 0 || dateFields.length > 0))) && (
              <>
                <E2openTextInput
                  name='zipCode'
                  label='Zip Code'
                  placeholder='Enter zip code'
                  required
                />

                <E2openTextInput
                  name='city'
                  label='City'
                  placeholder='Enter city'
                />

                <E2openTextInput
                  name='state'
                  label='State'
                  placeholder='Enter state'
                />

                <E2openTextInput
                  name='country'
                  label='Country'
                  placeholder='Enter country'
                />
              </>
            )}

            {/* Search Company Button - show initially for 3rd party flow */}
            {appointmentType === '3rd_party' &&
              !requiresCompanySelection &&
              !canShowValidateButton &&
              dynamicCompanies.length === 0 && (
                <Button
                  buttonNamePosthog={ButtonNamePosthog.SearchE2openCompany}
                  className='w-full text-sm'
                  type='button'
                  disabled={isFetchingCompanies}
                  onClick={() => handleSearchCompany()}
                >
                  {isFetchingCompanies ? <ButtonLoader /> : 'Search Company'}
                </Button>
              )}

            {/* Fetch Form Fields Button - show when ready to fetch form fields */}
            {appointmentType === '3rd_party' &&
              proIdFields.length === 0 &&
              !(dynamicCompanies.length > 0 && !operationsChecked) && // Don't show if Search Operations button should be shown
              ((!requiresCompanySelection && canShowValidateButton) || // Direct access (company not required)
                (requiresCompanySelection &&
                  company &&
                  !requiresOperationSelection) || // Company selected, operation not required
                (requiresCompanySelection &&
                  company &&
                  requiresOperationSelection &&
                  watch('operation'))) && ( // Both selected
                <Button
                  buttonNamePosthog={ButtonNamePosthog.SearchE2openOperations}
                  className='w-full text-sm'
                  type='button'
                  disabled={isFetchingFormFields}
                  onClick={handleFetchFormFields}
                >
                  {isFetchingFormFields ? (
                    <ButtonLoader />
                  ) : (
                    'Fetch Form Fields'
                  )}
                </Button>
              )}

            {/* SCAC Input - only show after slots are loaded for 3rd party */}
            {appointmentType === '3rd_party' && orderedSlots && (
              <E2openTextInput
                name='scac'
                label='SCAC'
                placeholder='Enter SCAC'
                required
              />
            )}

            {/* Static Appointment Date field removed - now using dynamic date field above */}

            {/* Validate PRO ID Button - show for normal or when 3rd party form fields are fetched */}
            {(appointmentType === 'normal' ||
              (appointmentType === '3rd_party' &&
                (proIdFields.length > 0 || dateFields.length > 0))) && (
              <Button
                buttonNamePosthog={ButtonNamePosthog.ValidateE2openPRONumber}
                className='w-full'
                type='button'
                disabled={
                  isValidatingPRONum ||
                  (appointmentType === 'normal' &&
                    (!watch('zipCode') || watch('zipCode').trim() === '')) ||
                  (appointmentType === '3rd_party' &&
                    (!watch('freightTrackingID') ||
                      watch('freightTrackingID').trim() === '' ||
                      (proIdFields.length > 0 && !selectedProIdField) ||
                      (requiresCompanySelection && !company) ||
                      (requiresOperationSelection && !watch('operation')) ||
                      (isAppointmentDateRequired() &&
                        !watch('appointmentDate'))))
                }
                onClick={handleValidateAppointment}
              >
                {isValidatingPRONum ? <ButtonLoader /> : 'Validate PRO ID'}
              </Button>
            )}

            {isValidPRONum && (
              <Flex
                direction='col'
                gap='2xl'
                className='mt-4 w-full'
                ref={scrollResultsIntoViewRef}
              >
                {isFetchingWarehouseDetails && !warehouseDetails && (
                  <div className='p-4 text-left animate-slide-in'>
                    <div className='mb-6'>
                      <span className='text-sm font-medium text-neutral-500 block mb-2'>
                        Warehouse
                      </span>
                      <Typography className='text-neutral-500'>
                        Fetching warehouse details...
                      </Typography>
                    </div>
                    <Divider className='border border-neutral-400 my-2' />
                  </div>
                )}
                {warehouseDetails && (
                  <div className='p-4 text-left animate-slide-in'>
                    <div className='mb-6'>
                      <span className='text-sm font-medium text-neutral-500 block mb-2'>
                        Warehouse
                      </span>
                      <Typography
                        variant='h3'
                        className='text-neutral-900 mb-2'
                      >
                        {warehouseDetails.warehouse.warehouseID}
                        {warehouseDetails.warehouse.city &&
                          warehouseDetails.warehouse.state && (
                            <>
                              <br />
                              {warehouseDetails.warehouse.city},{' '}
                              {warehouseDetails.warehouse.state}
                            </>
                          )}
                      </Typography>
                      <Typography
                        variant='body-xs'
                        className='text-neutral-400 mt-1'
                      >
                        All times are displayed in the warehouse's timezone.
                      </Typography>
                    </div>
                    <Divider className='border border-neutral-400 my-2' />
                  </div>
                )}

                {/* Submit appointment request section - always visible */}
                <div className='rounded-lg border border-neutral-200 p-4 bg-neutral-50 shadow-sm'>
                  <div className='mb-4'>
                    <Typography
                      variant='body-xs'
                      textColor='muted'
                      className='mb-1'
                    >
                      Option 1
                    </Typography>
                    <Typography variant='h5' className='text-neutral-900 mb-2'>
                      Submit Appointment Request
                    </Typography>
                    <Typography
                      variant='body-sm'
                      className='text-neutral-500 mt-1'
                    >
                      Submit a request for review. You will be notified once
                      approved.
                    </Typography>
                  </div>

                  <div className='space-y-6 py-4'>
                    <div className='space-y-2'>
                      <DateTimeInput
                        control={control}
                        name='requestedDateTime'
                        label='Requested Date'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                      />
                    </div>
                  </div>

                  <Button
                    buttonNamePosthog={ButtonNamePosthog.SubmitE2openAppt}
                    type='submit'
                    className='mt-4 w-full'
                    disabled={isLoadingSubmit}
                  >
                    {isLoadingSubmit ? <ButtonLoader /> : 'Submit'}
                  </Button>
                </div>

                {/* Live Appointment Section */}
                <div
                  className={cn(
                    'rounded-lg border border-neutral-200 p-4 bg-neutral-50 shadow-sm',
                    !liveAppointmentsAvailable &&
                      'opacity-50 pointer-events-none'
                  )}
                >
                  <div className='mb-4'>
                    <Typography
                      variant='body-xs'
                      textColor='muted'
                      className='mb-1'
                    >
                      Option 2
                    </Typography>
                    <Typography variant='h5' className='text-neutral-900 mb-2'>
                      Book Live Appointment
                    </Typography>
                    <Typography variant='body-sm' className='text-neutral-500'>
                      {liveAppointmentsAvailable
                        ? 'Book an appointment immediately from available time slots.'
                        : 'Live appointments are not available for this PRO ID. Please use Option 1 to submit an appointment request.'}
                    </Typography>
                  </div>

                  <div className='space-y-4'>
                    <div>
                      <DateTimeInput
                        control={control}
                        name='startDateTime'
                        label='Search From'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                        hideTimePicker={true}
                      />
                    </div>

                    <div>
                      <DateTimeInput
                        control={control}
                        name='endDateTime'
                        label='Search To'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                        hideTimePicker={true}
                      />
                    </div>

                    <Button
                      type='button'
                      className='w-full mt-4'
                      disabled={
                        isLoadingSlots ||
                        !liveAppointmentsAvailable ||
                        (appointmentType === '3rd_party' &&
                          proIdFields.length > 0 &&
                          !selectedProIdField)
                      }
                      buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                      onClick={loadAvailableSlots}
                    >
                      {isLoadingSlots ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.GetOpenApptSlots
                      )}
                    </Button>
                  </div>

                  {orderedSlots && (
                    <div className='mt-4'>
                      {Object.entries(orderedSlots.slots).map(
                        ([date, slots]) => (
                          <div key={date}>
                            <Typography
                              variant='h6'
                              weight='bold'
                              className='text-neutral-400 uppercase mt-4'
                            >
                              {date}
                            </Typography>
                            <Grid
                              cols='3'
                              gap='xs'
                              className='mt-2 mx-0 w-full'
                            >
                              {slots.map((slot, idx) => (
                                <button
                                  type='button'
                                  key={idx}
                                  onClick={() =>
                                    setSelectedSlot(
                                      selectedSlot === slot ? null : slot
                                    )
                                  }
                                  className={cn(
                                    'text-neutral-900 bg-neutral-50 border border-neutral-400 p-1 py-2 rounded cursor-pointer text-sm',
                                    selectedSlot === slot &&
                                      'bg-brand border-brand-700 text-neutral-50'
                                  )}
                                >
                                  {dayjs.utc(slot.startTime).format('HH:mm')}
                                </button>
                              ))}
                            </Grid>
                          </div>
                        )
                      )}

                      {selectedSlot && (
                        <div className='mt-4 text-neutral-400 text-left text-sm'>
                          <Typography weight='bold' className='my-1'>
                            Selected Slot:
                          </Typography>
                          <Typography className='mb-2'>
                            {dayjs
                              .utc(selectedSlot.startTime)
                              .format('MMM D, YYYY, HH:mm')}
                          </Typography>

                          {warehouseDetails && (
                            <>
                              <Typography weight='bold' className='my-1'>
                                Warehouse Details:
                              </Typography>
                              <Typography className='mb-2'>
                                {warehouseDetails.warehouse.warehouseID}
                                {warehouseDetails.warehouse.city &&
                                  warehouseDetails.warehouse.state && (
                                    <>
                                      <br />
                                      {warehouseDetails.warehouse.city},{' '}
                                      {warehouseDetails.warehouse.state}
                                    </>
                                  )}
                              </Typography>
                            </>
                          )}

                          {apptConfirmationNumber ? (
                            <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                              <Typography className='mb-2'>
                                Appointment confirmed 🎉
                              </Typography>
                              <Typography variant='body-sm' className='mb-4'>
                                <Typography
                                  weight='bold'
                                  className='text-[14px]'
                                >
                                  E2open Confirmation #:{' '}
                                </Typography>
                                {apptConfirmationNumber}
                              </Typography>
                            </div>
                          ) : (
                            <Button
                              buttonNamePosthog={
                                ButtonNamePosthog.ConfirmSlotApptScheduling
                              }
                              className='mt-2 w-full'
                              onClick={handleConfirmAppointment}
                              disabled={
                                isLoadingConfirm ||
                                (appointmentType === '3rd_party' &&
                                  proIdFields.length > 0 &&
                                  !selectedProIdField)
                              }
                            >
                              {isLoadingConfirm ? (
                                <ButtonLoader />
                              ) : (
                                'Confirm appointment'
                              )}
                            </Button>
                          )}

                          {/* TMS Update Form - Visible after successful appointment confirmation and if TMS update is supported */}
                          {apptConfirmationNumber &&
                          showUpdateTMSLoadWithAppt ? (
                            <UpdateTMSLoadWithAppt
                              load={load}
                              stopType={type}
                              appointmentStartTime={selectedSlot.startTime}
                              appointmentEndTime={
                                new Date(
                                  selectedSlot.startTime.getTime() +
                                    60 * 60 * 1000 // +1 hour to start time
                                )
                              }
                              freightTrackingId={getValues('freightTrackingID')}
                              onSuccess={() => setTMSUpdateSucceeded(true)}
                              onError={(error) =>
                                console.error('TMS update failed:', error)
                              }
                            />
                          ) : null}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Flex>
            )}
          </Flex>
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
