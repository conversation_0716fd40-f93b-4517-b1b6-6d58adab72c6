import { useEffect, useRef, useState } from 'react';
import {
  Controller,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { DatePicker } from 'components/DatePicker';
import {
  DebounceSelect,
  GenericLocationOption,
} from 'components/DebounceSelect';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { Textarea } from 'components/Textarea';
import { UpdateTMSLoadWithAppt } from 'components/UpdateTMSLoadWithAppt';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { getWarehouseSearch } from 'lib/api/getWarehouseSearch';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import { submitAppt } from 'lib/api/submitAppt';
import { validateAppt } from 'lib/api/validateAppt';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import { RetalixPOList } from '../Retalix/RetalixPOList';
import TimeSlotButton from '../TimeSlotButton';
import WarehouseDisplay from '../WarehouseDisplay';
import { confirmApptAndUpdateTMS } from '../helpers/confirmApptAndUpdateTMS';
import { isTMSUpdateLoadAppointmentSupported } from '../helpers/helpers';

dayjs.extend(utc);
dayjs.extend(timezone);

// Calling object PurchaseOrders instead of PONumbers to avoid stuttering
export type PurchaseOrder = {
  number: string;
  isValid: boolean;
  error: string;
};

// Add time preference options
const timePreferences = [
  { value: 'Anytime', label: 'Anytime' },
  { value: 'Before Noon', label: 'Before Noon' },
  { value: 'Noon - 6pm', label: 'Noon - 6pm' },
  { value: 'After 6pm', label: 'After 6pm' },
];

interface RetalixInputsWithoutLoad {
  shouldRequestLumper: boolean;
  apptNote: string;
  poNumbers: string;
  requestedDate?: Date;
  timePreference?: string;
}

type RetalixFormProps = {
  type: StopTypes;
  load: NormalizedLoad;
  integrationID?: number;
  tmsName: string;
  isAppointmentTMSUpdateEnabled: boolean;
  recentWarehouses: Warehouse[];
  selectedWarehouse: Maybe<Warehouse>;
  setSelectedWarehouse: (warehouse: Maybe<Warehouse>) => void;
};

export function RetalixForm({
  type,
  load,
  integrationID,
  tmsName,
  isAppointmentTMSUpdateEnabled,
  recentWarehouses,
  selectedWarehouse,
  setSelectedWarehouse,
}: RetalixFormProps) {
  const { toast } = useToast();
  const formMethods = useForm<RetalixInputsWithoutLoad>();
  const { control, handleSubmit, getValues } = formMethods;
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [warehouses, setWarehouses] = useState<Warehouse[]>(
    recentWarehouses && recentWarehouses.length ? recentWarehouses : []
  );
  const [uniqueWarehouseSources, setUniqueWarehouseSources] = useState<
    string[]
  >([]);
  const [isInitialSearch, setIsInitialSearch] = useState(true);
  const [hasOverriddenSuggestedWarehouse, setHasOverriddenSuggestedWarehouse] =
    useState(false);

  const [isValidatingPOs, setIsValidatingPOs] = useState(false);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [liveAppointmentsAvailable, setLiveAppointmentsAvailable] =
    useState(true);
  const [_tmsUpdateSucceeded, setTMSUpdateSucceeded] = useState(false);
  const [showUpdateTMSLoadWithAppt, setShowUpdateTMSLoadWithAppt] =
    useState(false);

  useEffect(() => {
    if (warehouses) {
      setUniqueWarehouseSources([SchedulingPortals.Retalix]);
    }
  }, [warehouses]);

  useEffect(() => {
    if (
      selectedWarehouse &&
      selectedWarehouse.warehouseSource == SchedulingPortals.Retalix &&
      !hasOverriddenSuggestedWarehouse
    ) {
      // This effect is no longer needed since we're using external state
      // The warehouse is already set from the parent component
    }
  }, [selectedWarehouse, hasOverriddenSuggestedWarehouse]);

  useEffect(() => {
    setHasOverriddenSuggestedWarehouse(false);
  }, [type]);

  const handleResetWarehouseSearch = () => {
    setIsInitialSearch(true);
    setWarehouses(recentWarehouses);
  };

  const handleWarehouseSearch = async (search: string) => {
    if (search.length > 3) {
      setIsInitialSearch(false);
      const searchRes = await getWarehouseSearch(search);
      if (searchRes.isOk()) {
        const { warehouses: searchedWarehouses } = searchRes.value ?? {
          warehouses: [],
        };
        // Prevent filtering on null/undefined result from API
        const safeSearchedWarehouses = Array.isArray(searchedWarehouses)
          ? searchedWarehouses
          : [];

        const retalixWarehouses = safeSearchedWarehouses.filter(
          (w) => w && w.warehouseSource === SchedulingPortals.Retalix
        );
        setWarehouses(retalixWarehouses);
        return retalixWarehouses && retalixWarehouses.length
          ? mapWarehousesToOptions(retalixWarehouses)
          : [];
      }
    }
    handleResetWarehouseSearch();
    return mapWarehousesToOptions(recentWarehouses).filter((wh) =>
      wh.label.toLocaleLowerCase().includes(search.toLocaleLowerCase())
    );
  };

  const mapWarehousesToOptions = (warehouses: Warehouse[]) =>
    warehouses?.map((option: Warehouse) => ({
      ...option,
      value: option.warehouseID,
      name: option.warehouseName,
      mainAddress: option.warehouseAddressLine1,
      secondaryAddress: option.warehouseAddressLine2,
      source: option.warehouseSource,
      label: option.warehouseName,
    }));

  useEffect(() => {
    setOrderedSlots(null);
  }, [selectedWarehouse?.warehouseID]);

  useEffect(() => {
    if (load.poNums && load.poNums !== '') {
      setPurchaseOrders(
        load.poNums
          .split(load.poNums?.includes(',') ? ',' : '/')
          .map((poNum) => ({ number: poNum, isValid: false, error: '' }))
      );
    }
  }, [load]);

  const validatePONumbers = async (poNumbers: string[]) => {
    setIsValidatingPOs(true);
    // Clear previous validation errors before starting new validation
    setPurchaseOrders((prevPurchaseOrders) =>
      prevPurchaseOrders.map((po) => ({
        ...po,
        isValid: false,
        error: '',
      }))
    );
    try {
      const validateRes = await validateAppt(
        selectedWarehouse?.warehouseID ?? '',
        selectedWarehouse?.warehouseSource ?? SchedulingPortals.Retalix,
        poNumbers,
        { integrationID }
      );
      if (validateRes.isOk()) {
        const { validatedPONumbers } = validateRes.value;
        if (validatedPONumbers.length === 0) {
          toast({
            description: 'No PO numbers to validate.',
            variant: 'default',
          });
          return false;
        }
        // Update purchase orders with validation results, matching by PO number
        setPurchaseOrders((prevPurchaseOrders) => {
          const poMap = new Map(
            validatedPONumbers.map((poNum) => [
              poNum.poNumber,
              {
                number: poNum.poNumber,
                isValid: poNum.isValid,
                error: poNum.error,
              },
            ])
          );
          return prevPurchaseOrders.map((po) => {
            const validatedPo = poMap.get(po.number);
            return validatedPo || { ...po, isValid: false, error: '' };
          });
        });
        const allPOsValid = validatedPONumbers.every(
          (po) => po.isValid && !po.error
        );
        if (!allPOsValid) {
          toast({
            description: 'Some PO numbers are invalid.',
            variant: 'destructive',
          });
        }
        return allPOsValid;
      } else {
        toast({
          description: 'Failed to validate PO numbers.',
          variant: 'destructive',
        });
        return false;
      }
    } catch {
      toast({
        description: 'An error occurred during validation.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsValidatingPOs(false);
    }
  };

  const loadAvailableSlots = async (validatedPONumbers: string[]) => {
    setIsLoadingSlots(true);
    try {
      const res = await getOpenApptSlots({
        loadTypeID: 'placeholder-value',
        warehouseID: selectedWarehouse?.warehouseID ?? '',
        warehouseTimezoneStartDate: dayjs(),
        warehouseTimezoneEndDate: dayjs(),
        freightTrackingID: '',
        source: SchedulingPortals.Retalix,
        warehouse: {
          warehouseID: selectedWarehouse?.warehouseID ?? '',
          warehouseName: selectedWarehouse?.warehouseName ?? '',
        },
        poNumbers: validatedPONumbers,
        integrationID,
      });
      if (res.isOk()) {
        setOrderedSlots(res.value);
        setLiveAppointmentsAvailable(true);
        if (!res.value?.slots || Object.keys(res.value.slots).length === 0) {
          toast({
            description: 'No available appointment times found.',
            variant: 'default',
          });
          return false;
        }
        return true;
      } else {
        if (
          res.error.message ===
          'Live appointments are not available at this time'
        ) {
          setLiveAppointmentsAvailable(false);
          toast({
            description:
              'Option 2 (Live Appointments) is not available for this warehouse. Please use Option 1 to submit an appointment request.',
            variant: 'default',
          });
        } else {
          const errorMessage =
            res.error.message || 'Failed to load available appointments.';
          const isBookingLiveApptError = errorMessage.includes(
            'for booking a live appointment'
          );
          toast({
            description:
              errorMessage +
              '. You can still submit a request for an appointment.',
            variant: isBookingLiveApptError ? 'default' : 'destructive', // No need to show scary red toast if live appointment booking is not available
          });
        }
        return false;
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleValidateAppt = async () => {
    const poNumbers = purchaseOrders.map((po) => po.number);
    const isValid = await validatePONumbers(poNumbers);
    if (!isValid) return;
    await loadAvailableSlots(poNumbers);
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots || !selectedWarehouse) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }
    const validPoNumbers = purchaseOrders
      .filter((po) => po.isValid && !po.error)
      .map((po) => po.number);
    if (!validPoNumbers.length) {
      toast({
        description: 'No valid PO numbers found',
        variant: 'destructive',
      });
      return;
    }
    setIsLoadingConfirm(true);
    const confirmApptProps = {
      source: SchedulingPortals.Retalix,
      isTMSLoad: false,
      stopType: type,
      start: selectedSlot.startTime,
      loadTypeId: orderedSlots.loadType.id || 'placeholder',
      warehouseID: orderedSlots.warehouse.warehouseID,
      warehouseTimezone: selectedWarehouse?.warehouseTimezone || 'UTC',
      dockId: selectedSlot.dock.id || 'placeholder',
      loadID: load.ID!,
      freightTrackingId: load.freightTrackingID || 'placeholder',
      integrationID: integrationID,
      trailerType: orderedSlots.trailerType,
      subscribedEmail: '',
      notes: 'Booking appointment',
      poNums: validPoNumbers.join(','),
      requestType: type,
    };

    const res = await confirmApptAndUpdateTMS({
      confirmApptProps,
      featureFlagEnabled: isAppointmentTMSUpdateEnabled,
      tmsName,
      timezone: selectedWarehouse?.warehouseTimezone,
      stopType: type,
      loadId: load.ID!,
      freightTrackingId: load.freightTrackingID || 'placeholder',
      onTmsUpdateComplete: (succeeded, errorMessage) => {
        setTMSUpdateSucceeded(!!succeeded);
        if (succeeded) {
          toast({ description: 'Load updated in TMS.', variant: 'success' });
        } else if (errorMessage) {
          toast({ description: errorMessage, variant: 'destructive' });
        }
      },
    });
    if (res.confirmResult) {
      setApptConfirmationNumber(res.confirmResult.ConfirmationNo);
      if (
        isTMSUpdateLoadAppointmentSupported(tmsName) &&
        isAppointmentTMSUpdateEnabled
      ) {
        toast({
          description: 'Appointment scheduled. Updating load in TMS.',
          variant: 'default',
        });
      }
      setShowUpdateTMSLoadWithAppt(
        isTMSUpdateLoadAppointmentSupported(tmsName) &&
          !isAppointmentTMSUpdateEnabled
      );
    } else {
      if (res.confirmError?.message === 'Conflicting Appointments') {
        toast({
          title: 'Conflicting Appointments',
          description:
            "Make sure you don't have an existing appointment for this load.",
          variant: 'destructive',
        });
      } else {
        toast({
          description:
            res.confirmError?.message || 'Unable to book appointment',
          variant: 'destructive',
        });
      }
    }
    setIsLoadingConfirm(false);
  };

  const handleSubmitAppt = async () => {
    setIsLoadingSubmit(true);
    const formValues = getValues();
    const request = {
      warehouseId: selectedWarehouse?.warehouseID ?? '',
      source: selectedWarehouse?.warehouseSource ?? SchedulingPortals.Retalix,
      poNumbers: purchaseOrders.map((po) => po.number),
      lumperRequested: formValues.shouldRequestLumper,
      note: formValues.apptNote,
      requestedDate: formValues.requestedDate
        ? dayjs(formValues.requestedDate).format()
        : undefined,
      timePreference: formValues.timePreference || undefined,
      requestType: type,
      integrationID,
      loadID: load.ID!,
    };
    const submitRes = await submitAppt(request);
    if (submitRes.isOk()) {
      toast({
        description: 'Your appointment has been submitted.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'An error occurred while submitting your appointment.',
        variant: 'destructive',
      });
    }
    setIsLoadingSubmit(false);
  };

  const onInvalid: SubmitErrorHandler<any> = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  const canSubmit =
    purchaseOrders.length > 0 &&
    purchaseOrders.every((po) => po.isValid && po.error === '');

  useEffect(() => {
    if (canSubmit) {
      scrollResultsIntoViewRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [canSubmit]);

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <div className='col-span-6'>
          <form className='w-full flex flex-col mb-2'>
            <label className='text-neutral-600 text-sm'>Warehouse</label>
            <DebounceSelect
              showSearch
              placeholder='Choose'
              optionFilterProp='children'
              value={
                selectedWarehouse
                  ? mapWarehousesToOptions([selectedWarehouse])
                  : null
              }
              fetchOptions={handleWarehouseSearch}
              onFocus={handleResetWarehouseSearch}
              onSelect={({ value }) => {
                if (
                  !hasOverriddenSuggestedWarehouse &&
                  value !== selectedWarehouse?.warehouseName
                ) {
                  setHasOverriddenSuggestedWarehouse(true);
                }
                const foundWarehouse = warehouses.find(
                  (w) => w.warehouseID === value
                );
                setSelectedWarehouse(foundWarehouse || null);
              }}
              options={[
                {
                  label: (
                    <span>{`${isInitialSearch ? 'Recently used' : 'Searched'} warehouses`}</span>
                  ),
                  title: `${isInitialSearch ? 'Recently used' : 'Searched'} warehouses`,
                  options: mapWarehousesToOptions(warehouses),
                },
              ]}
              optionRender={(option) => (
                <GenericLocationOption
                  option={option.data}
                  optionFieldsToRender={[
                    'mainAddress',
                    'secondaryAddress',
                    ...(uniqueWarehouseSources ? ['source'] : []),
                  ]}
                />
              )}
              notFoundContent={
                isInitialSearch ? (
                  <Typography>
                    Start typing to search for a warehouse
                  </Typography>
                ) : (
                  <Typography>No results found</Typography>
                )
              }
            />
          </form>

          {selectedWarehouse && (
            <>
              <WarehouseDisplay warehouse={selectedWarehouse} />

              <div className='mt-2'>
                <form
                  onSubmit={handleSubmit(handleSubmitAppt, onInvalid)}
                  className='flex flex-col gap-4 mt-4 mx-0 w-full'
                >
                  <div>
                    <Label name='poNumbers'>Customer PO #</Label>
                    <RetalixPOList
                      purchaseOrders={purchaseOrders}
                      setPurchaseOrders={setPurchaseOrders}
                    />
                  </div>

                  <Button
                    buttonNamePosthog={
                      ButtonNamePosthog.ValidateRetalixPONumbers
                    }
                    className='w-full'
                    type='button'
                    disabled={!purchaseOrders.length || isValidatingPOs}
                    onClick={handleValidateAppt}
                  >
                    {isValidatingPOs ? (
                      <ButtonLoader />
                    ) : (
                      ButtonText.ValidatePONumbers
                    )}
                  </Button>

                  {canSubmit && (
                    <Flex
                      direction='col'
                      gap='2xl'
                      className='mt-4'
                      ref={scrollResultsIntoViewRef}
                    >
                      {/* Submit appointment request section - always visible */}
                      <div className='rounded-lg border border-neutral-200 p-4 bg-neutral-50 shadow-sm'>
                        <div className='mb-6'>
                          <span className='text-sm font-medium text-neutral-500 block mb-1.5'>
                            Option 1
                          </span>
                          <Typography
                            variant='h5'
                            className='text-neutral-900 mb-1'
                          >
                            Submit Appointment Request
                          </Typography>
                          <Typography variant='body-xs' textColor='muted'>
                            Submit a request for review. You will be notified
                            once approved.
                          </Typography>
                        </div>

                        <Controller
                          name='shouldRequestLumper'
                          control={control}
                          render={({ field }) => (
                            <Checkbox
                              label='Request Lumper?'
                              labelClassName='text-xs leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          )}
                        />

                        <Flex direction='col' gap='md' className='py-4 w-full'>
                          <Flex gap='xs' direction='col' className='w-full'>
                            <Label name='requestedDate'>
                              Requested Delivery Date
                            </Label>
                            <Controller
                              name='requestedDate'
                              control={control}
                              render={({ field }) => (
                                <DatePicker field={field} />
                              )}
                            />
                          </Flex>

                          <Flex gap='xs' direction='col' className='w-full'>
                            <Label name='timePreference'>Preferred Time</Label>
                            <Controller
                              name='timePreference'
                              control={control}
                              render={({ field }) => (
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select preferred time' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {timePreferences.map(({ value, label }) => (
                                      <SelectItem key={value} value={value}>
                                        {label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              )}
                            />
                          </Flex>
                        </Flex>

                        <Flex direction='col' gap='xs'>
                          <Label name='apptNote'>Appointment Note</Label>
                          <Controller
                            control={control}
                            name='apptNote'
                            render={({ field: { onChange, value } }) => (
                              <Textarea
                                className='p-2 h-16 whitespace-pre-wrap focus-visible:ring-transparent'
                                onChange={onChange}
                                value={value}
                              />
                            )}
                          />
                        </Flex>

                        <Button
                          buttonNamePosthog={
                            ButtonNamePosthog.SubmitRetalixAppt
                          }
                          type='submit'
                          className='mt-4 w-full'
                          disabled={!canSubmit || isLoadingSubmit}
                        >
                          {isLoadingSubmit ? (
                            <ButtonLoader />
                          ) : (
                            'Request Appointment'
                          )}
                        </Button>
                      </div>

                      {/* Live Appointment Section */}
                      <div
                        className={cn(
                          'rounded-lg border border-neutral-200 p-4 bg-neutral-50 shadow-sm',
                          !liveAppointmentsAvailable &&
                            'opacity-50 pointer-events-none'
                        )}
                      >
                        <div className='mb-6'>
                          <span className='text-sm font-medium text-neutral-500 block mb-1.5'>
                            Option 2
                          </span>
                          <Typography
                            variant='h5'
                            className='text-neutral-900 mb-1'
                          >
                            Book Live Appointment
                          </Typography>
                          <Typography variant='body-xs' textColor='muted'>
                            {liveAppointmentsAvailable
                              ? 'Book an appointment immediately from available time slots.'
                              : 'Live appointments are not available for this warehouse. Please use Option 1 to submit an appointment request.'}
                          </Typography>
                        </div>

                        {isValidatingPOs ? (
                          <div className='text-center'>
                            <Flex align='center' gap='sm' className='mt-4'>
                              <ButtonLoader />
                              <Typography variant='body-sm'>
                                Validating PO numbers...
                              </Typography>
                            </Flex>
                          </div>
                        ) : isLoadingSlots ? (
                          <div className='text-center'>
                            <Flex align='center' gap='sm' className='mt-4'>
                              <ButtonLoader />
                              <Typography variant='body-sm'>
                                Loading open slots...
                              </Typography>
                            </Flex>
                          </div>
                        ) : orderedSlots ? (
                          <div>
                            {Object.entries(orderedSlots.slots).map(
                              ([date, slots]) => (
                                <div key={date}>
                                  <Typography
                                    variant='h6'
                                    weight='bold'
                                    className='text-neutral-400 uppercase mt-4'
                                  >
                                    {date}
                                  </Typography>
                                  <Grid
                                    cols='3'
                                    gap='xs'
                                    className='mt-2 mx-0 w-full'
                                  >
                                    {slots.map((slot, idx) => (
                                      <TimeSlotButton
                                        key={idx}
                                        slot={slot}
                                        isSelected={selectedSlot === slot}
                                        onSelect={(clickedSlot) =>
                                          setSelectedSlot(
                                            selectedSlot === clickedSlot
                                              ? null
                                              : clickedSlot
                                          )
                                        }
                                      />
                                    ))}
                                  </Grid>
                                </div>
                              )
                            )}

                            {selectedSlot && (
                              <div className='mt-4 text-neutral-400 text-left text-sm'>
                                <Typography weight='bold' className='my-1'>
                                  Selected Slot:
                                </Typography>
                                <Typography className='mb-2'>
                                  {dayjs
                                    .utc(selectedSlot.startTime)
                                    .format('MMM D, YYYY, HH:mm')}
                                </Typography>
                                {apptConfirmationNumber ? (
                                  <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                                    <Typography className='mb-2'>
                                      Appointment confirmed 🎉
                                    </Typography>
                                    <Typography
                                      variant='body-sm'
                                      className='mb-4'
                                    >
                                      <Typography
                                        variant='body-sm'
                                        weight='bold'
                                      >
                                        Retalix Confirmation #:{' '}
                                      </Typography>
                                      {apptConfirmationNumber}
                                    </Typography>
                                  </div>
                                ) : (
                                  <Button
                                    onClick={handleConfirmAppointment}
                                    className='mt-4 w-full'
                                    disabled={isLoadingConfirm}
                                    buttonNamePosthog={
                                      ButtonNamePosthog.ConfirmSlotApptScheduling
                                    }
                                  >
                                    {isLoadingConfirm ? (
                                      <ButtonLoader />
                                    ) : (
                                      'Confirm Appointment'
                                    )}
                                  </Button>
                                )}

                                {/* TMS Update Form - Visible after successful appointment confirmation and if TMS update is supported */}
                                {apptConfirmationNumber &&
                                showUpdateTMSLoadWithAppt ? (
                                  <UpdateTMSLoadWithAppt
                                    load={load}
                                    stopType={type}
                                    appointmentStartTime={
                                      selectedSlot.startTime
                                    }
                                    appointmentEndTime={
                                      new Date(
                                        selectedSlot.startTime.getTime() +
                                          60 * 60 * 1000
                                      )
                                    }
                                    freightTrackingId={
                                      load.freightTrackingID || 'placeholder'
                                    }
                                    onSuccess={() =>
                                      setTMSUpdateSucceeded(true)
                                    }
                                    onError={(error) =>
                                      console.error('TMS update failed:', error)
                                    }
                                  />
                                ) : null}
                              </div>
                            )}
                          </div>
                        ) : (
                          <Button
                            buttonNamePosthog={null}
                            onClick={handleValidateAppt}
                            disabled={
                              !purchaseOrders.length ||
                              !liveAppointmentsAvailable
                            }
                            className='mt-4 w-full'
                          >
                            Load available times
                          </Button>
                        )}
                      </div>
                    </Flex>
                  )}
                </form>
              </div>
            </>
          )}
        </div>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
