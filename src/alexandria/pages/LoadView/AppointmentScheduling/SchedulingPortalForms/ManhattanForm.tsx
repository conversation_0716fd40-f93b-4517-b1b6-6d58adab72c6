import { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>ath,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import { Divider } from 'antd';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { UpdateTMSLoadWithAppt } from 'components/UpdateTMSLoadWithAppt';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import { submitAppt } from 'lib/api/submitAppt';
import { validateAppt } from 'lib/api/validateAppt';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import FlowType from 'types/enums/FlowType';

import TimeSlotButton from '../TimeSlotButton';
import { confirmApptAndUpdateTMS } from '../helpers/confirmApptAndUpdateTMS';
import { isTMSUpdateLoadAppointmentSupported } from '../helpers/helpers';

dayjs.extend(utc);

// Manhattan appointment types
const MANHATTAN_APPOINTMENT_TYPES = [
  'Drop Empty',
  'Drop Unload',
  'Live Load',
  'Live Unload',
  'Pickup Empty',
  'Pickup Unload',
] as const;

type ManhattanAppointmentType = (typeof MANHATTAN_APPOINTMENT_TYPES)[number];

// Manhattan appointment types
const MANHATTAN_FLOW_TYPES = [
  FlowType.WEB_TENDER,
  FlowType.APPOINTMENT,
] as const;

type ManhattanFlowType = (typeof MANHATTAN_FLOW_TYPES)[number];

interface ManhattanInputsWithoutLoad {
  freightTrackingID: string; // This will be PO ID for Manhattan
  startDateTime: Date;
  endDateTime: Date;
  shouldRequestLumper: boolean;
  apptNote: string;
  requestedDateTime: Date;
  city: string;
  state: string;
  facilityName: string;
  phone: string;
  email: string;
  contactPerson: string;
  facilityId: string;
  facilityText: string;
  appointmentType: ManhattanAppointmentType;
  flowType: ManhattanFlowType;
}

type ManhattanTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<ManhattanInputsWithoutLoad>;
};

function ManhattanTextInput(props: ManhattanTextInputProps) {
  return <RHFTextInput {...props} />;
}

interface ManhattanFormProps {
  type: StopTypes;
  load: NormalizedLoad;
  integrationID?: number;
  tmsName: string;
  isAppointmentTMSUpdateEnabled: boolean;
}

interface ManhattanFacility {
  facility_id: string;
  facility_text: string;
}

export function ManhattanForm({
  type,
  load,
  integrationID,
  tmsName,
  isAppointmentTMSUpdateEnabled,
}: ManhattanFormProps) {
  const { toast } = useToast();
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [isValidatingPRONum, setIsValidatingPRONum] = useState(false);
  const [isValidPRONum, setIsValidPRONum] = useState(false);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [facilities, setFacilities] = useState<ManhattanFacility[]>([]);
  const [_tmsUpdateSucceeded, setTMSUpdateSucceeded] = useState(false);
  const [showUpdateTMSLoadWithAppt, setShowUpdateTMSLoadWithAppt] =
    useState(false);

  const formMethods = useForm<ManhattanInputsWithoutLoad>({
    defaultValues: {
      freightTrackingID: load.freightTrackingID || '',
      startDateTime: new Date(),
      endDateTime: dayjs().add(7, 'days').toDate(),
      shouldRequestLumper: false,
      apptNote: '',
      requestedDateTime: new Date(),
      city: load.pickup.city || '',
      state: load.pickup.state || '',
      facilityName: '*',
      phone: '',
      email: '',
      contactPerson: '',
      facilityId: '',
      facilityText: '',
      appointmentType: 'Live Unload',
      flowType: FlowType.WEB_TENDER,
    },
    mode: 'onChange',
  });

  const { control, handleSubmit, getValues, watch, setValue } = formMethods;

  // Watch for changes in form values
  const appointmentType = watch('appointmentType');
  const facilityId = watch('facilityId');
  const flowType = watch('flowType');

  useEffect(() => {
    if (isValidPRONum) {
      scrollResultsIntoViewRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isValidPRONum]);

  const validatePRONumber = async (poNumber: string) => {
    setIsValidatingPRONum(true);

    try {
      const formValues = getValues();

      if (!formValues.flowType) {
        toast({
          description: 'Please select flow type.',
          variant: 'destructive',
        });
        setIsValidPRONum(false);
      }

      const validateRes = await validateAppt(
        'placeholder',
        SchedulingPortals.Manhattan,
        [poNumber],
        {
          requestType: type,
          city: formValues.city,
          state: formValues.state,
          facilityName: formValues.facilityName,
          integrationID,
          flowType: formValues.flowType,
        }
      );

      if (validateRes.isOk()) {
        const { validatedPONumbers } = validateRes.value;
        const isValid = validatedPONumbers.some(
          (pro) => pro.poNumber === poNumber && pro.isValid
        );

        if (isValid) {
          setIsValidPRONum(true);
          // Extract facilities from the response (only for Appointment flow)
          if (formValues.flowType === FlowType.APPOINTMENT) {
            const validatedPO = validatedPONumbers.find(
              (pro) => pro.poNumber === poNumber && pro.isValid
            );
            if (validatedPO && validatedPO.facilities) {
              const facilityList = Object.entries(validatedPO.facilities).map(
                ([facility_id, facility_text]) => ({
                  facility_id,
                  facility_text: facility_text as string,
                })
              );
              setFacilities(facilityList);
            }
          }
          toast({
            description: `${formValues.flowType === FlowType.WEB_TENDER ? 'Customer Service ID' : 'PO number'} validated successfully.`,
            variant: 'success',
          });
        } else {
          setIsValidPRONum(false);
          toast({
            description: `Invalid ${formValues.flowType === FlowType.WEB_TENDER ? 'Customer Service ID' : 'PO number'}. Please check and try again.`,
            variant: 'destructive',
          });
        }
      } else {
        setIsValidPRONum(false);
        toast({
          description:
            validateRes.error.message ||
            `Failed to validate ${formValues.flowType === FlowType.WEB_TENDER ? 'Customer Service ID' : 'PO number'}.`,
          variant: 'destructive',
        });
      }
    } catch {
      setIsValidPRONum(false);
      toast({
        description: `Failed to validate ${getValues().flowType === FlowType.WEB_TENDER ? 'Customer Service ID' : 'PO number'}.`,
        variant: 'destructive',
      });
    } finally {
      setIsValidatingPRONum(false);
    }
  };

  const loadAvailableSlots = async () => {
    const formValues = getValues();

    // For Web Tender flow, use default date range since date inputs are not shown
    let start: dayjs.Dayjs;
    let end: dayjs.Dayjs;

    if (formValues.flowType === FlowType.WEB_TENDER) {
      start = dayjs().startOf('day');
      end = dayjs().add(7, 'days').endOf('day');
      // Update form values with default dates
      setValue('startDateTime', start.toDate());
      setValue('endDateTime', end.toDate());
      setValue('facilityId', '');
      setValue('facilityText', '');
      setValue('appointmentType', 'Live Unload');
    } else {
      start = dayjs(formValues.startDateTime);
      end = dayjs(formValues.endDateTime);

      if (start.isAfter(end)) {
        toast({
          description: 'Start date must be before end date.',
          variant: 'destructive',
        });
        return false;
      }

      if (!formValues.facilityId || !formValues.appointmentType) {
        toast({
          description: 'Please select facility and appointment type.',
          variant: 'destructive',
        });
        return false;
      }
    }

    if (!formValues.flowType) {
      toast({
        description: 'Please select flow type.',
        variant: 'destructive',
      });
      return false;
    }

    setIsLoadingSlots(true);

    try {
      const res = await getOpenApptSlots({
        source: SchedulingPortals.Manhattan,
        freightTrackingID: formValues.freightTrackingID, // This is PO ID
        loadTypeID: formValues.freightTrackingID, // PO ID is used as loadTypeID
        requestType: type,
        startDateTime: start,
        endDateTime: end,
        city: formValues.city,
        state: formValues.state,
        facilityId: formValues.facilityId,
        facilityText: formValues.facilityText,
        appointmentType: formValues.appointmentType,
        integrationID,
        flowType: formValues.flowType,
      });

      if (res.isOk()) {
        setOrderedSlots(res.value);

        if (!res.value?.slots || Object.keys(res.value.slots).length === 0) {
          toast({
            description: 'No available appointment times found.',
            variant: 'default',
          });
          return false;
        }
        return true;
      } else {
        if (
          res.error.message ===
          'Live appointments are not available at this time'
        ) {
          toast({
            description:
              'Live appointments are not available for this warehouse. Please use the manual appointment request option.',
            variant: 'default',
          });
        } else {
          toast({
            description:
              res.error.message || 'Failed to load available appointments.',
            variant: 'destructive',
          });
        }
        return false;
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleValidateAppointment = async () => {
    const { freightTrackingID } = getValues();

    if (!freightTrackingID || freightTrackingID.trim() === '') {
      toast({
        description: 'Please enter a PO ID before validating.',
        variant: 'destructive',
      });
      return;
    }

    await validatePRONumber(freightTrackingID);
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }

    const formValues = getValues();

    if (!formValues.flowType) {
      toast({
        description: 'Please select flow type.',
        variant: 'destructive',
      });
      return;
    }

    // Validate that appointment type is selected
    if (
      formValues.flowType === FlowType.APPOINTMENT &&
      !formValues.appointmentType
    ) {
      toast({
        description: 'Please select an appointment type',
        variant: 'destructive',
      });
      return;
    }

    // Validate that facility is selected
    if (
      formValues.flowType === FlowType.APPOINTMENT &&
      !formValues.facilityId
    ) {
      toast({
        description: 'Please select a facility',
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingConfirm(true);

    try {
      let trailerType = '';
      if (load.specifications && load.specifications.transportType) {
        trailerType = load.specifications?.transportType
          ?.toLowerCase()
          .split(' ')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }

      const confirmApptProps = {
        source: SchedulingPortals.Manhattan,
        isTMSLoad: false,
        stopType: type,
        start: selectedSlot.startTime,
        loadTypeId: formValues.freightTrackingID,
        warehouseID: formValues.facilityId || '-',
        warehouseTimezone: 'UTC',
        dockId: 'placeholder',
        loadID: load.ID!,
        freightTrackingId: formValues.freightTrackingID,
        integrationID: integrationID,
        appointments: [
          {
            start: selectedSlot.startTime,
            freightTrackingId: formValues.freightTrackingID,
            facilityId: formValues.facilityId || '-',
            facilityText: formValues.facilityText || '-',
            appointmentType: formValues.appointmentType || '-',
          },
        ],
        requestType: type,
        trailerType: trailerType,
        phone: formValues.phone,
        email: formValues.email,
        facilityId: formValues.facilityId || '-',
        facilityText: formValues.facilityText || '-',
        appointmentType: formValues.appointmentType || '-',
        flowType: formValues.flowType,
      };

      const res = await confirmApptAndUpdateTMS({
        confirmApptProps,
        featureFlagEnabled: isAppointmentTMSUpdateEnabled,
        tmsName,
        timezone: undefined,
        stopType: type,
        loadId: load.ID!,
        freightTrackingId: formValues.freightTrackingID,
        onTmsUpdateComplete: (succeeded, errorMessage) => {
          setTMSUpdateSucceeded(!!succeeded);
          if (succeeded) {
            toast({ description: 'Load updated in TMS.', variant: 'success' });
          } else if (errorMessage) {
            toast({ description: errorMessage, variant: 'destructive' });
          }
        },
      });

      if (res.confirmResult) {
        if (res.confirmResult.ConfirmationNo) {
          setApptConfirmationNumber(res.confirmResult.ConfirmationNo);
        } else {
          setApptConfirmationNumber(res.confirmResult.ExternalID);
        }
        if (
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
          isAppointmentTMSUpdateEnabled
        ) {
          toast({
            description: 'Appointment scheduled. Updating load in TMS.',
            variant: 'default',
          });
        }
        setShowUpdateTMSLoadWithAppt(
          isTMSUpdateLoadAppointmentSupported(tmsName) &&
            !isAppointmentTMSUpdateEnabled
        );
      } else {
        if (res.confirmError?.message === 'Conflicting Appointments') {
          toast({
            title: 'Conflicting Appointments',
            description:
              "Make sure you don't have an existing appointment for this load.",
            variant: 'destructive',
          });
        } else {
          toast({
            description:
              res.confirmError?.message || 'Failed to confirm appointment.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        description: 'An error occurred while confirming the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  const handleSubmitAppointment = async () => {
    const formValues = getValues();
    const defaultStart = dayjs().startOf('day');
    const requestedDateTime = formValues.requestedDateTime
      ? dayjs(formValues.requestedDateTime)
      : defaultStart;

    const request = {
      warehouseId: formValues.facilityId || '-',
      source: SchedulingPortals.Manhattan,
      poNumbers: [formValues.freightTrackingID], // PO ID
      lumperRequested: formValues.shouldRequestLumper,
      note: formValues.apptNote,
      requestedDateTime: requestedDateTime,
      timePreference: undefined,
      integrationID,
      loadID: load.ID!,
      flowType: formValues.flowType,
    };

    try {
      const submitRes = await submitAppt(request);

      if (submitRes.isOk()) {
        toast({
          description: 'Your appointment has been submitted.',
          variant: 'success',
        });
      } else {
        toast({
          description:
            submitRes.error.message ||
            'An error occurred while submitting your appointment.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'An error occurred while submitting your appointment.',
        variant: 'destructive',
      });
    }
  };

  const onInvalid: SubmitErrorHandler<
    ManhattanInputsWithoutLoad
  > = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form
          onSubmit={handleSubmit(handleSubmitAppointment, onInvalid)}
          className='flex flex-col gap-4 mt-4 mx-0 w-full'
        >
          <Flex direction='col' gap='sm'>
            {/* Flow Type Dropdown */}
            <Label htmlFor='flowType' name='flowType'>
              Flow Type *
            </Label>
            <Select
              value={flowType}
              onValueChange={(value: ManhattanFlowType) => {
                setValue('flowType', value);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder='Select flow type' />
              </SelectTrigger>
              <SelectContent>
                {MANHATTAN_FLOW_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </Flex>
          <Flex direction='col' gap='lg'>
            <ManhattanTextInput
              name='freightTrackingID'
              label={
                flowType === FlowType.WEB_TENDER
                  ? 'Customer Service ID'
                  : 'PO ID'
              }
              placeholder={
                flowType === FlowType.WEB_TENDER
                  ? 'Enter Customer Service ID'
                  : 'Enter PO ID'
              }
              required
            />

            {/* For Appointment flow, show Validate button */}
            {flowType === FlowType.APPOINTMENT && (
              <>
                {/* Address Fields - only city and state for Manhattan */}
                <ManhattanTextInput
                  name='city'
                  label='City'
                  placeholder='Enter city'
                />

                <ManhattanTextInput
                  name='state'
                  label='State'
                  placeholder='Enter state'
                />

                {/* Facility Name - for normal sysco flow */}
                <ManhattanTextInput
                  name='facilityName'
                  label='Facility Name'
                  placeholder='Enter Facility Name'
                />
                <Button
                  buttonNamePosthog={
                    ButtonNamePosthog.ValidateManhattanPONumber
                  }
                  className='w-full'
                  type='button'
                  disabled={
                    isValidatingPRONum ||
                    !watch('freightTrackingID') ||
                    watch('freightTrackingID').trim() === ''
                  }
                  onClick={handleValidateAppointment}
                >
                  {isValidatingPRONum ? <ButtonLoader /> : 'Validate PO ID'}
                </Button>
              </>
            )}

            {/* For Web Tender flow, show fields directly. For Appointment flow, show only after validation */}
            {(flowType === FlowType.WEB_TENDER || isValidPRONum) && (
              <div
                className='flex flex-col gap-8 mt-4 w-full'
                ref={scrollResultsIntoViewRef}
              >
                {/* Facility Selection - show after validation for Appointment flow, or directly for Web Tender */}
                <Flex direction='col' gap='lg'>
                  {/* For Appointment flow, show facility and appointment type selection */}
                  {flowType === FlowType.APPOINTMENT && (
                    <>
                      <Typography variant='h5' weight='medium'>
                        Facility Selection
                      </Typography>
                      <Flex direction='col' gap='sm'>
                        <Label htmlFor='facilityId' name='facilityId'>
                          Facility *
                        </Label>
                        <Select
                          value={facilityId}
                          onValueChange={(value: string) => {
                            const facility = facilities.find(
                              (f) => f.facility_id === value
                            );
                            setValue('facilityId', value);
                            setValue(
                              'facilityText',
                              facility?.facility_text || ''
                            );
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select facility' />
                          </SelectTrigger>
                          <SelectContent>
                            {facilities.map((facility) => (
                              <SelectItem
                                key={facility.facility_id}
                                value={facility.facility_id}
                              >
                                {facility.facility_text}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </Flex>

                      {/* Appointment Type Dropdown */}
                      <Flex direction='col' gap='sm'>
                        <Label htmlFor='appointmentType' name='appointmentType'>
                          Appointment Type *
                        </Label>
                        <Select
                          value={appointmentType}
                          onValueChange={(value: ManhattanAppointmentType) => {
                            setValue('appointmentType', value);
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select appointment type' />
                          </SelectTrigger>
                          <SelectContent>
                            {MANHATTAN_APPOINTMENT_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </Flex>

                      {/* Date Range - only show for Appointment flow */}
                      <DateTimeInput
                        control={control}
                        name='startDateTime'
                        label='Search From'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                        hideTimePicker={true}
                      />

                      <DateTimeInput
                        control={control}
                        name='endDateTime'
                        label='Search To'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                        hideTimePicker={true}
                      />

                      <Button
                        type='button'
                        className='w-full'
                        disabled={
                          isLoadingSlots || !facilityId || !appointmentType
                        }
                        buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                        onClick={loadAvailableSlots}
                      >
                        {isLoadingSlots ? (
                          <ButtonLoader />
                        ) : (
                          ButtonText.GetOpenApptSlots
                        )}
                      </Button>
                    </>
                  )}

                  {/* For Web Tender flow, show Get Open Slots button directly */}
                  {flowType === FlowType.WEB_TENDER && (
                    <Button
                      type='button'
                      className='w-full'
                      disabled={
                        isLoadingSlots ||
                        !watch('freightTrackingID') ||
                        watch('freightTrackingID').trim() === '' ||
                        !appointmentType
                      }
                      buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                      onClick={loadAvailableSlots}
                    >
                      {isLoadingSlots ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.GetOpenApptSlots
                      )}
                    </Button>
                  )}
                </Flex>

                {/* Available Slots */}
                {orderedSlots && (
                  <div className='space-y-4'>
                    <Divider />
                    <h3 className='text-lg font-semibold'>
                      Available Time Slots
                    </h3>
                    {Object.entries(orderedSlots.slots).map(([date, slots]) => (
                      <div key={date}>
                        <Typography
                          variant='body-sm'
                          weight='bold'
                          className='text-neutral-600 uppercase mt-3'
                        >
                          {date}
                        </Typography>
                        <Grid cols='3' gap='xs' className='mt-2'>
                          {slots.map((slot: GroupedSlot, idx: number) => (
                            <TimeSlotButton
                              key={idx}
                              slot={slot}
                              isSelected={selectedSlot === slot}
                              onSelect={(clickedSlot) => {
                                setSelectedSlot(
                                  selectedSlot === clickedSlot
                                    ? null
                                    : clickedSlot
                                );
                              }}
                            />
                          ))}
                        </Grid>
                      </div>
                    ))}

                    {selectedSlot && (
                      <div className='mt-4 space-y-4'>
                        <Button
                          type='button'
                          className='w-full'
                          disabled={isLoadingConfirm}
                          onClick={handleConfirmAppointment}
                          buttonNamePosthog={
                            ButtonNamePosthog.ConfirmSlotApptScheduling
                          }
                        >
                          {isLoadingConfirm ? (
                            <ButtonLoader />
                          ) : (
                            ButtonText.ConfirmSlotApptScheduling
                          )}
                        </Button>

                        {apptConfirmationNumber && (
                          <div className='p-4 bg-success-50 border border-success-200 rounded-lg'>
                            <Typography
                              variant='body-sm'
                              weight='medium'
                              className='text-success-800'
                            >
                              Appointment Confirmed!
                            </Typography>
                            <Typography
                              variant='body-sm'
                              className='text-success-600'
                            >
                              Confirmation Number: {apptConfirmationNumber}
                            </Typography>
                          </div>
                        )}

                        {/* TMS Update Form - Visible after successful appointment confirmation and if TMS update is supported */}
                        {apptConfirmationNumber && showUpdateTMSLoadWithAppt ? (
                          <UpdateTMSLoadWithAppt
                            load={load}
                            stopType={type}
                            appointmentStartTime={selectedSlot.startTime}
                            appointmentEndTime={
                              new Date(
                                selectedSlot.startTime.getTime() +
                                  60 * 60 * 1000
                              )
                            }
                            freightTrackingId={getValues('freightTrackingID')}
                            onSuccess={() => setTMSUpdateSucceeded(true)}
                            onError={(error) =>
                              console.error('TMS update failed:', error)
                            }
                          />
                        ) : null}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </Flex>
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
