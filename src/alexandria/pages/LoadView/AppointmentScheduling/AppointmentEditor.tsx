import { useContext, useEffect, useState } from 'react';

import { ToggleGroup } from 'components/ToggleGroup';
import { ToggleGroupItem } from 'components/ToggleGroup';
import { Flex } from 'components/layout';
import { ServiceContext } from 'contexts/serviceContext';
import useFetchRecentWarehouses from 'hooks/useFetchRecentWarehouses';
import { useToast } from 'hooks/useToaster';
import { SchedulingPortals, StopTypes } from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import { cn } from 'utils/shadcn';

import AppointmentScheduling from './AppointmentScheduling';
import { MiniLoadInfo } from './MiniLoadInfo';

type AppointmentEditorProps = {
  normalizedLoad: NormalizedLoad;
  loadPickupWarehouse: Maybe<Warehouse>;
  loadDropoffWarehouse: Maybe<Warehouse>;
};

export function AppointmentEditor({
  normalizedLoad: load,
  loadPickupWarehouse,
  loadDropoffWarehouse,
}: AppointmentEditorProps) {
  const { serviceID } = useContext(ServiceContext);
  const { toast } = useToast();
  const {
    warehouses: recentWarehouses,
    isLoading,
    error,
  } = useFetchRecentWarehouses();

  const [selectedPickupWarehouse, setSelectedPickupWarehouse] =
    useState<Maybe<Warehouse>>(loadPickupWarehouse);
  const [selectedDropoffWarehouse, setSelectedDropoffWarehouse] =
    useState<Maybe<Warehouse>>(loadDropoffWarehouse);

  // NFI uses YardView exclusively for dropoff appointments so we can default to dropoff tab.
  const isYardViewDropoff =
    loadDropoffWarehouse?.warehouseSource === SchedulingPortals.YardView;
  const isNFIService = serviceID === 2;

  const defaultAppointmentType =
    (isYardViewDropoff && isNFIService) || // Default to dropoff if YardView for NFI
    (!loadPickupWarehouse && loadDropoffWarehouse) // or if load only has a dropoff warehouse
      ? StopTypes.Dropoff
      : StopTypes.Pickup;

  const [type, setType] = useState<StopTypes>(defaultAppointmentType);

  // Get the current warehouse and setter based on the selected type
  const getCurrentWarehouse = () => {
    return type === StopTypes.Pickup
      ? selectedPickupWarehouse
      : selectedDropoffWarehouse;
  };

  const getCurrentWarehouseSetter = () => {
    return (warehouse: Maybe<Warehouse>) => {
      if (type === StopTypes.Pickup) {
        setSelectedPickupWarehouse(warehouse);
      } else {
        setSelectedDropoffWarehouse(warehouse);
      }
    };
  };

  useEffect(() => {
    if (isLoading) {
      return;
    }
    if (error) {
      toast({
        description: 'Error fetching warehouses.',
        variant: 'destructive',
      });
    }
  }, [isLoading, error]);

  return (
    <div className='flex-1 shrink-0 mt-6'>
      <Flex justify='between' gap='sm' align='center' className='mb-4'>
        <ToggleGroup
          type='single'
          value={type === StopTypes.Pickup ? 'Pickup' : 'Dropoff'}
          onValueChange={(value: string) =>
            setType(value === 'Dropoff' ? StopTypes.Dropoff : StopTypes.Pickup)
          }
          className='relative bg-neutral-100 rounded-md p-1 grid w-full grid-cols-2 gap-0 m-0'
        >
          {/* Sliding background */}
          <div
            className={cn(
              'absolute top-1 bottom-1 w-[calc(50%-4px)] rounded-md bg-brand-main transition-all duration-200 ease-in-out',
              {
                'left-1': type === StopTypes.Pickup,
                'left-[calc(50%+1px)]': type === StopTypes.Dropoff,
              }
            )}
          />

          <ToggleGroupItem
            value='Pickup'
            aria-label='Pickup'
            className='relative z-10 flex-1 bg-transparent border-0 shadow-none data-[state=on]:text-white data-[state=off]:text-neutral-600'
          >
            Pickup
          </ToggleGroupItem>
          <ToggleGroupItem
            value='Dropoff'
            aria-label='Dropoff'
            className='relative z-10 flex-1 bg-transparent border-0 shadow-none data-[state=on]:text-white data-[state=off]:text-neutral-600'
          >
            Dropoff
          </ToggleGroupItem>
        </ToggleGroup>
      </Flex>

      <MiniLoadInfo load={load} type={type} />

      <AppointmentScheduling
        type={type}
        load={load}
        recentWarehouses={recentWarehouses}
        selectedWarehouse={getCurrentWarehouse()}
        setSelectedWarehouse={getCurrentWarehouseSetter()}
      />
    </div>
  );
}
