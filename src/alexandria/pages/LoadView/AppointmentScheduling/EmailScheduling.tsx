import { useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore library installed on parent module, overriding tsc check
import DOMPurify from 'dompurify';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore react-quill is in the parent dir
import JoditEditor from 'jodit-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { IJodit } from 'jodit/esm/types';
import { ArrowLeft, CheckIcon, CopyIcon } from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import AIHintLabel from 'components/AIHint';
import { Button } from 'components/Button';
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from 'components/Tooltip';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import useFetchEmailTemplates from 'hooks/useFetchEmailTemplates';
import { useToast } from 'hooks/useToaster';
import { saveApptEmailRequest } from 'lib/api/saveApptEmailRequest';
import { StopTypes } from 'types/Appointment';
import { TemplateType } from 'types/EmailTemplates';
import { NormalizedLoad } from 'types/Load';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import {
  CopyToClipboardOptions,
  copyToClipboard,
  createRichTextContent,
  isHTMLContent,
} from 'utils/copyToClipboard';
import { getJoditEditorConfig, nl2br } from 'utils/getJoditEditorHelpers';

interface EmailSchedulingProps {
  type: StopTypes;
  load: NormalizedLoad;
  onBack: () => void;
}

const EmailScheduling = ({ type, load, onBack }: EmailSchedulingProps) => {
  const { toast } = useToast();
  const joditRef = useRef<IJodit | null>(null);
  const [hasCopiedEmail, setHasCopiedEmail] = useState(false);
  const copyEmailTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const initialDefaultEmail =
    type === StopTypes.Pickup
      ? load?.pickup?.email || ''
      : load?.consignee?.email || '';

  const formMethods = useForm<{ email: string }>({
    mode: 'onChange',
    defaultValues: {
      email: initialDefaultEmail,
    },
  });
  const { setValue, getValues, formState, watch } = formMethods;
  const lastAutoEmailRef = useRef<string>(initialDefaultEmail);
  const emailValue = watch('email');

  const { emailTemplates } = useFetchEmailTemplates(
    load.ID,
    TemplateType.APPOINTMENT,
    type
  );

  const [templateText, setTemplateText] = useState('');

  useEffect(() => {
    if (emailTemplates?.appointmentRequestTemplateHTML?.body) {
      setTemplateText(
        nl2br(emailTemplates.appointmentRequestTemplateHTML.body)
      );
    }
  }, [emailTemplates]);

  // Auto-populate email based on pickup/dropoff selection, without interfering with user edits
  useEffect(() => {
    const newAutoEmail =
      type === StopTypes.Pickup
        ? load?.pickup?.email || ''
        : load?.consignee?.email || '';

    const currentEmail = getValues('email');
    const isDirty = !!formState?.dirtyFields?.email;
    const isCurrentlyAuto =
      currentEmail === '' || currentEmail === lastAutoEmailRef.current;

    if (!isDirty || isCurrentlyAuto) {
      setValue('email', newAutoEmail, {
        shouldDirty: false,
        shouldTouch: false,
        shouldValidate: false,
      });
      lastAutoEmailRef.current = newAutoEmail;
    }
  }, [type, load, getValues, setValue, formState?.dirtyFields?.email]);

  const renderEmailTemplate = () => {
    if (!templateText) {
      return '<Typography>No template available</Typography>';
    }

    const sanitizedHTML = DOMPurify.sanitize(templateText);
    return sanitizedHTML;
  };

  const handleCopyTemplate = async () => {
    if (!emailTemplates?.appointmentRequestTemplateHTML) {
      toast({
        description: 'No template available to copy.',
        variant: 'destructive',
      });
      return;
    }

    await saveApptEmailRequest(
      templateText,
      emailTemplates,
      load.freightTrackingID
    );

    const copyToClipboardOptions: CopyToClipboardOptions = {
      toastMessage: 'Template copied to clipboard',
      toastVariant: 'default',
      wrapInHTML: true,
      joditEditorRef: joditRef,
      toastDuration: 3000,
    };

    await copyToClipboard(
      isHTMLContent(templateText)
        ? createRichTextContent(templateText)
        : templateText,
      copyToClipboardOptions
    );
  };

  const handleCopyEmail = async () => {
    const emailToCopy = getValues('email');
    if (!emailToCopy) {
      toast({
        description: 'No email address to copy.',
        variant: 'destructive',
      });
      return;
    }

    const copyToClipboardOptions: CopyToClipboardOptions = {
      toastMessage: 'Email copied to clipboard!',
      toastVariant: 'default',
      toastDuration: 500,
    };

    const success = await copyToClipboard(emailToCopy, copyToClipboardOptions);
    if (success) {
      setHasCopiedEmail(true);
      // Clear any existing timeout
      if (copyEmailTimeoutRef.current) {
        clearTimeout(copyEmailTimeoutRef.current);
      }
      copyEmailTimeoutRef.current = setTimeout(() => {
        setHasCopiedEmail(false);
        copyEmailTimeoutRef.current = null;
      }, 1000);
    }
  };

  useEffect(() => {
    return () => {
      if (copyEmailTimeoutRef.current) {
        clearTimeout(copyEmailTimeoutRef.current);
      }
    };
  }, []);

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <div className='w-full mx-auto py-4'>
          <Flex align='center' gap='sm'>
            <Flex align='center' gap='xs'>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={onBack}
                      className='p-1 hover:border-opacity-0'
                      buttonNamePosthog={null}
                    >
                      <ArrowLeft className='h-5 w-5' />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side='top' className='rounded-md ml-2'>
                    Schedule with a supported website
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <Typography variant='h5' weight='medium'>
                Email Request
              </Typography>
            </Flex>
          </Flex>

          <Flex direction='col' gap='sm'>
            <div className='w-full'>
              <Flex align='center' justify='between' className='mb-1'>
                <Flex align='center' gap='xs'>
                  <Typography variant='body-sm' className='text-neutral-800'>
                    {`${type === StopTypes.Pickup ? 'Pickup' : 'Dropoff'} Email`}
                  </Typography>
                  <AIHintLabel name='email' />
                </Flex>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={handleCopyEmail}
                        disabled={!emailValue}
                        className='p-1 rounded-md transition-colors hover:bg-neutral-200 disabled:opacity-50 disabled:cursor-not-allowed shrink-0'
                        type='button'
                        aria-label='Copy email address'
                      >
                        {hasCopiedEmail ? (
                          <CheckIcon className='h-4 w-4 text-success-600' />
                        ) : (
                          <CopyIcon className='h-4 w-4 text-neutral-600' />
                        )}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side='top' className='rounded-md'>
                      {hasCopiedEmail ? 'Copied!' : 'Copy email address'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Flex>
              <RHFTextInput
                name='email'
                label=''
                placeholder='Enter email'
                hideAIHint={true}
              />
            </div>

            <Typography variant='body-sm' textColor='muted'>
              Copy this template and send it via email
            </Typography>

            <JoditEditor
              value={renderEmailTemplate()}
              config={getJoditEditorConfig({ refToUpdateAfterInit: joditRef })}
              onBlur={(e: any) => setTemplateText(e)}
            />

            {/* TODO: Handle direct email sending */}
            <Button
              onClick={handleCopyTemplate}
              className='w-full mt-2'
              buttonNamePosthog={
                ButtonNamePosthog.EmailSchedulingCopyEmailDraft
              }
              disabled={!emailTemplates?.appointmentRequestTemplateHTML}
            >
              Copy Template
            </Button>
          </Flex>
        </div>
      </FormProvider>
    </ExtendedFormProvider>
  );
};

export default EmailScheduling;
