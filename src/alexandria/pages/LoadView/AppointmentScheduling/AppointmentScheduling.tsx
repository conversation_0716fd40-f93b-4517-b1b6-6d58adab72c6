import { useContext, useEffect, useState } from 'react';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { Mail } from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { Card, CardContent } from 'components/Card';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { ServiceContext } from 'contexts/serviceContext';
import { TMSContext } from 'contexts/tms';
import { useServiceFeatures } from 'hooks/useServiceContext';
import C3ReservationsLogo from 'icons/C3ReservationsLogo';
import CostcoLogo from 'icons/CostcoLogo';
import E2openLogo from 'icons/E2OpenLogo';
import ManhattanLogo from 'icons/ManhattanLogo';
import OneNetworkLogo from 'icons/OneNetworkLogo';
import OpendockLogo from 'icons/OpendockLogo';
import RetalixLogo from 'icons/RetalixLogo';
import <PERSON><PERSON><PERSON>ogo from 'icons/YardviewLogo';
import { getSuggestedSchedulingIntegrations } from 'lib/api/getSuggestedSchedulingIntegrations';
import { SchedulingPortals, StopTypes } from 'types/Appointment';
import { IntegrationCore } from 'types/Integration';
import { NormalizedLoad } from 'types/Load';
import { IntegrationWithUsage } from 'types/SchedulingAssociations';
import { Maybe, Undef } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';

import EmailScheduling from './EmailScheduling';
import PortalSchedulingHeader from './PortalSchedulingHeader';
import { C3ReservationsForm } from './SchedulingPortalForms/C3ReservationsForm';
import { CostcoForm } from './SchedulingPortalForms/CostcoForm';
import { E2openForm } from './SchedulingPortalForms/E2openForm';
import { ManhattanForm } from './SchedulingPortalForms/ManhattanForm';
import { OneNetworkForm } from './SchedulingPortalForms/OneNetworkForm';
import { OpendockForm } from './SchedulingPortalForms/OpendockForm';
import { RetalixForm } from './SchedulingPortalForms/RetalixForm';
import { YardViewForm } from './SchedulingPortalForms/YardViewForm';

type Portal = {
  id: string;
  name: string;
  icon: any;
  color: string;
  requiresCustomerInfo?: boolean;
  category?: 'standard' | 'beta';
  integrationID?: number;
  username?: string;
};

type GroupedPortal = {
  id: string;
  name: string;
  icon: any;
  color: string;
  requiresCustomerInfo?: boolean;
  category?: 'standard' | 'beta';
  primaryIntegrationId: number;
  primaryUsername?: string;
  allUsers: Array<{
    integrationID: number;
    username?: string;
  }>;
  additionalUserCount: number;
};

const getPortalConfig = (
  integrationName: string,
  id: number,
  username?: string
): Undef<Portal> => {
  const configMap: Partial<Record<SchedulingPortals, Portal>> = {
    [SchedulingPortals.Costco]: {
      id: SchedulingPortals.Costco,
      name: 'Costco',
      icon: CostcoLogo,
      color: 'blue',
      category: 'beta',
      integrationID: id,
      username: username,
    },
    [SchedulingPortals.C3Reservations]: {
      id: SchedulingPortals.C3Reservations,
      name: 'C3 Reservations',
      icon: C3ReservationsLogo,
      color: 'blue',
      category: 'beta',
      integrationID: id,
      username: username,
    },
    [SchedulingPortals.E2open]: {
      id: SchedulingPortals.E2open,
      name: 'E2open',
      icon: E2openLogo,
      color: 'blue',
      category: 'beta',
      integrationID: id,
      username: username,
    },
    [SchedulingPortals.Manhattan]: {
      id: SchedulingPortals.Manhattan,
      name: 'Manhattan',
      icon: ManhattanLogo,
      color: 'blue',
      category: 'beta',
      integrationID: id,
      username: username,
    },
    [SchedulingPortals.OneNetwork]: {
      id: SchedulingPortals.OneNetwork,
      name: 'OneNetwork',
      icon: OneNetworkLogo,
      color: 'red',
      category: 'beta',
      integrationID: id,
      username: username,
    },
    [SchedulingPortals.Opendock]: {
      id: SchedulingPortals.Opendock,
      name: 'Opendock',
      icon: OpendockLogo,
      color: 'green',
      category: 'standard',
      integrationID: id,
      username: username,
    },
    [SchedulingPortals.Retalix]: {
      id: SchedulingPortals.Retalix,
      name: 'Retalix NCR',
      icon: RetalixLogo,
      color: 'green',
      category: 'beta',
      integrationID: id,
      username: username,
    },
    [SchedulingPortals.YardView]: {
      id: SchedulingPortals.YardView,
      name: 'Yardview',
      icon: YardviewLogo,
      color: 'blue',
      category: 'standard',
      integrationID: id,
      username: username,
    },
  };
  return Object.values(SchedulingPortals).includes(
    integrationName as SchedulingPortals
  )
    ? configMap[integrationName as SchedulingPortals]
    : undefined;
};

// Helper function to get color classes - this is the only place we use tailwind's root colors
const getColorClasses = (color: string) => {
  const colorMap: Record<string, { bg: string; text: string }> = {
    blue: { bg: 'bg-info-100', text: 'text-info-600' },
    green: { bg: 'bg-success-100', text: 'text-success-600' },
    red: { bg: 'bg-error-100', text: 'text-error-600' },
    yellow: { bg: 'bg-warning-100', text: 'text-warning-600' },
    orange: { bg: 'bg-brand-100', text: 'text-brand-600' },
  };
  return colorMap[color] || { bg: 'bg-neutral-100', text: 'text-neutral-600' };
};

dayjs.extend(utc);
dayjs.extend(timezone);

type AppointmentSchedulingProps = {
  type: StopTypes;
  load: NormalizedLoad;
  recentWarehouses: Warehouse[];
  selectedWarehouse: Maybe<Warehouse>;
  setSelectedWarehouse: (warehouse: Maybe<Warehouse>) => void;
};

// Helper function to get the integration ID for the selected warehouse
const getPortalIntegrationId = (
  selectedWarehouse: Maybe<Warehouse>,
  schedulerIntegrations: IntegrationCore[]
) => {
  return selectedWarehouse?.warehouseSource
    ? (schedulerIntegrations.find(
        (integration) => integration.name === selectedWarehouse.warehouseSource
      )?.id ?? 0)
    : 0;
};

export default function AppointmentScheduling({
  type,
  load,
  recentWarehouses,
  selectedWarehouse,
  setSelectedWarehouse,
}: AppointmentSchedulingProps) {
  const {
    serviceFeaturesEnabled: { isAppointmentEmailingEnabled },
    schedulerIntegrations,
  } = useContext(ServiceContext);

  const { tmsName } = useContext(TMSContext);
  const {
    serviceFeaturesEnabled: { isAppointmentTMSUpdateEnabled },
  } = useServiceFeatures();

  const [selectedPortal, setSelectedPortal] = useState<Maybe<string>>(
    selectedWarehouse?.warehouseSource ?? null
  );
  const [selectedPortalIntegrationId, setSelectedPortalIntegrationId] =
    useState<number>(
      getPortalIntegrationId(selectedWarehouse, schedulerIntegrations)
    );
  const [isManualPortalSelection, setIsManualPortalSelection] = useState(false);

  // New state for tracking available users for the selected portal
  const [selectedPortalUsers, setSelectedPortalUsers] = useState<
    Array<{
      integrationID: number;
      username?: string;
    }>
  >([]);
  const [selectedUsername, setSelectedUsername] = useState<string | undefined>(
    undefined
  );
  const [isManualUsernameSelection, setIsManualUsernameSelection] =
    useState(false);

  const [showEmail, setShowEmail] = useState(false);

  const [suggestedSchedulingIntegrations, setSuggestedSchedulingIntegrations] =
    useState<IntegrationWithUsage[]>([]);

  const portals: Portal[] = schedulerIntegrations
    .map((integration) =>
      getPortalConfig(integration.name, integration.id, integration.username)
    )
    .filter((portal): portal is Portal => portal !== undefined);

  // Group portals by name to merge duplicates with different usernames
  const groupPortalsByName = (portals: Portal[]): GroupedPortal[] => {
    const portalGroups = new Map<string, Portal[]>();

    // Group portals by their name
    portals.forEach((portal) => {
      const existing = portalGroups.get(portal.name) || [];
      existing.push(portal);
      portalGroups.set(portal.name, existing);
    });

    // Convert groups to GroupedPortal objects
    return Array.from(portalGroups.entries()).map(([_name, portalList]) => {
      // Sort by priority (could be based on customer preference in the future)
      // For now, just use the first one as primary
      const primaryPortal = portalList[0];
      const allUsers = portalList.map((p) => ({
        integrationID: p.integrationID!,
        username: p.username,
      }));

      return {
        id: primaryPortal.id,
        name: primaryPortal.name,
        icon: primaryPortal.icon,
        color: primaryPortal.color,
        requiresCustomerInfo: primaryPortal.requiresCustomerInfo,
        category: primaryPortal.category,
        primaryIntegrationId: primaryPortal.integrationID!,
        primaryUsername: primaryPortal.username,
        allUsers,
        additionalUserCount: portalList.length - 1,
      };
    });
  };

  const groupedPortals = groupPortalsByName(portals);

  const getPortalsByCategory = () => {
    const standard = groupedPortals.filter((p) => p.category === 'standard');
    const beta = groupedPortals.filter((p) => p.category === 'beta');

    return { standard, beta };
  };

  const handlePortalSelect = (
    portalId: string,
    portalIntegrationId: number = 0,
    allUsers: Array<{ integrationID: number; username?: string }> = []
  ) => {
    setSelectedPortal(portalId);
    setSelectedPortalIntegrationId(portalIntegrationId);
    setSelectedPortalUsers(allUsers);

    // Set the initial username to the primary user (first in the list)
    const primaryUser = allUsers.find(
      (user) => user.integrationID === portalIntegrationId
    );
    setSelectedUsername(primaryUser?.username);

    setIsManualPortalSelection(true);
    setIsManualUsernameSelection(false); // Reset manual username selection when portal changes
  };

  const handleBack = () => {
    if (showEmail) {
      setShowEmail(false);
    } else {
      setSelectedPortal(null);
      setSelectedPortalIntegrationId(0);
      setIsManualPortalSelection(true); // Mark as manual so useEffect doesn't override
      setIsManualUsernameSelection(false); // Reset manual username selection when going back
    }
  };

  // Fetch customer scheduling associations for the current stop's warehouse
  useEffect(() => {
    const warehouseId = selectedWarehouse?.id;
    if (!warehouseId || !load?.tmsID) {
      setSuggestedSchedulingIntegrations([]);
      return;
    }

    // Prevent setState after unmount: mark cancelled in cleanup and check before updating state
    // Mount: kick off async load below. Unmount: flip flag so pending async won't set state
    let isCancelled = false;

    const loadSuggestedSchedulingIntegrations = async () => {
      try {
        const suggestedIntegrations = await getSuggestedSchedulingIntegrations({
          tmsIntegrationId: load.tmsID,
          warehouseId,
          tmsCustomerExternalID: load.customer?.externalTMSID,
          customerName: load.customer?.name || '',
        });
        if (isCancelled) {
          return;
        }
        setSuggestedSchedulingIntegrations(suggestedIntegrations || []);
      } catch {
        if (!isCancelled) {
          setSuggestedSchedulingIntegrations([]);
        }
      }
    };

    // Cleanup below prevents state updates after unmount
    loadSuggestedSchedulingIntegrations();

    return () => {
      // Unmount/dep change: signal any in-flight async work to no-op state updates
      isCancelled = true;
    };
  }, [
    selectedWarehouse?.id,
    load?.tmsID,
    load?.customer?.name,
    load?.customer?.externalTMSID,
  ]);

  // Prefer the most-used association when not manually overridden
  useEffect(() => {
    if (
      isManualPortalSelection ||
      isManualUsernameSelection ||
      !suggestedSchedulingIntegrations?.length
    ) {
      return;
    }

    const accessibleByUser = (name: string) =>
      schedulerIntegrations.some((i) => i.name === name);

    const mostUsedIntegration = [...suggestedSchedulingIntegrations]
      .filter((a) => a.name && accessibleByUser(a.name as string))
      .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))[0];

    if (!mostUsedIntegration) {
      return;
    }

    const mostUsedPortalName = mostUsedIntegration.name as string;
    const mostUsedIntegrationId = mostUsedIntegration.id;

    if (selectedPortal !== mostUsedPortalName) {
      setSelectedPortal(mostUsedPortalName);
    }

    if (selectedPortalIntegrationId !== mostUsedIntegrationId) {
      setSelectedPortalIntegrationId(mostUsedIntegrationId);
    }

    const users = schedulerIntegrations
      .filter((i) => i.name === mostUsedPortalName)
      .map((i) => ({ integrationID: i.id, username: i.username }));
    setSelectedPortalUsers(users);

    // Pick the username that corresponds to the chosen integration ID
    const matchedUser = users.find(
      (u) => u.integrationID === mostUsedIntegrationId
    );
    setSelectedUsername(matchedUser?.username || users?.[0]?.username);
    setIsManualUsernameSelection(false);
  }, [
    suggestedSchedulingIntegrations,
    isManualPortalSelection,
    isManualUsernameSelection,
    schedulerIntegrations,
  ]);

  useEffect(() => {
    const warehouseSource = selectedWarehouse?.warehouseSource ?? null;

    // Only auto-sync with warehouse source if not manually overridden
    if (!isManualPortalSelection && selectedPortal !== warehouseSource) {
      setSelectedPortal(warehouseSource);
    }

    // Update selectedPortalIntegrationId based on the current portal
    // Only if user hasn't manually selected a username
    if (selectedPortal && !isManualUsernameSelection) {
      // Find all integrations for the current portal
      const portalIntegrations = schedulerIntegrations.filter(
        (integration) => integration.name === selectedPortal
      );

      // Update selectedPortalUsers to match the current portal
      const portalUsers = portalIntegrations.map((i) => ({
        integrationID: i.id,
        username: i.username,
      }));
      setSelectedPortalUsers(portalUsers);

      // Check if the current integration ID belongs to the selected portal
      const currentIntegrationBelongsToPortal = portalIntegrations.some(
        (integration) => integration.id === selectedPortalIntegrationId
      );

      // If the integration ID doesn't match the portal, update it to the first available integration for that portal
      if (!currentIntegrationBelongsToPortal && portalIntegrations.length > 0) {
        const newIntegrationId = portalIntegrations[0].id;
        if (newIntegrationId) {
          setSelectedPortalIntegrationId(newIntegrationId);
          // Update username to match the new integration ID
          const matchedUser = portalUsers.find(
            (u) => u.integrationID === newIntegrationId
          );
          setSelectedUsername(
            matchedUser?.username || portalUsers[0]?.username
          );
        }
      } else if (
        selectedPortalIntegrationId === 0 &&
        portalIntegrations.length > 0
      ) {
        // If no integration ID is set, use the first available one
        const newIntegrationId = portalIntegrations[0].id;
        if (newIntegrationId) {
          setSelectedPortalIntegrationId(newIntegrationId);
          // Update username to match the new integration ID
          const matchedUser = portalUsers.find(
            (u) => u.integrationID === newIntegrationId
          );
          setSelectedUsername(
            matchedUser?.username || portalUsers[0]?.username
          );
        }
      }
    } else if (
      selectedPortalIntegrationId !== 0 &&
      !isManualUsernameSelection
    ) {
      setSelectedPortalIntegrationId(0);
    }
  }, [
    selectedWarehouse,
    selectedPortal,
    schedulerIntegrations,
    selectedPortalIntegrationId,
    isManualPortalSelection,
    isManualUsernameSelection,
  ]);

  // Reset manual selection flag when selectedWarehouse changes
  useEffect(() => {
    setIsManualPortalSelection(false);
  }, [selectedWarehouse]);

  // Filter selected warehouse so we don't select warehouses from different portals inside forms
  const filterSelectedWarehouse = (portal: SchedulingPortals) => {
    return selectedWarehouse?.warehouseSource === portal
      ? selectedWarehouse
      : null;
  };

  // Email Scheduling UI
  if (showEmail) {
    return <EmailScheduling type={type} load={load} onBack={handleBack} />;
  }

  // Portal Scheduling UI
  if (selectedPortal) {
    // Find the portal with the current integration ID
    let portal = portals.find(
      (p) =>
        p.id === selectedPortal &&
        p.integrationID === selectedPortalIntegrationId
    );

    // If not found (which can happen after username change), find any portal with the same name
    if (!portal) {
      portal = portals.find((p) => p.id === selectedPortal);
    }

    if (!portal) {
      return null;
    }

    // Handle username selection for portals with multiple users
    const handleUsernameChange = (newUsername: string) => {
      const selectedUser = selectedPortalUsers.find(
        (user) => user.username === newUsername
      );
      if (selectedUser) {
        setSelectedUsername(newUsername);
        setSelectedPortalIntegrationId(selectedUser.integrationID);
        setIsManualUsernameSelection(true); // Mark as manual selection
      }
    };

    const selectedIntegration = schedulerIntegrations.find(
      (integration) => integration.id === selectedPortalIntegrationId
    );

    const commonProps = {
      type,
      load,
      integrationID: selectedPortalIntegrationId, // Use the current integration ID, not portal.integrationID
      tmsName,
      isAppointmentTMSUpdateEnabled,
    };

    const isCurrentPortalSuggested = suggestedSchedulingIntegrations.some(
      (s) => s.id === selectedPortalIntegrationId || s.name === selectedPortal
    );

    return (
      <div className='w-full mx-auto py-4 space-y-4'>
        {/* Username/account selection and option to go back/select a different scheduling portal F*/}
        <PortalSchedulingHeader
          onBack={handleBack}
          portalName={portal?.name}
          selectedPortalUsers={selectedPortalUsers}
          selectedUsername={selectedUsername}
          portalUsername={portal.username}
          onUsernameChange={handleUsernameChange}
          showAISparkle={isCurrentPortalSuggested}
        />

        {/* Render scheduling portal form based on the selected portal */}
        <div className='space-y-4'>
          {portal.id === SchedulingPortals.Costco && (
            <CostcoForm key={portal.id} {...commonProps} />
          )}
          {portal.id === SchedulingPortals.E2open && (
            <E2openForm key={portal.id} {...commonProps} />
          )}
          {portal.id === SchedulingPortals.Manhattan && (
            <ManhattanForm {...commonProps} />
          )}
          {portal.id === SchedulingPortals.OneNetwork && (
            <OneNetworkForm
              key={portal.id}
              {...commonProps}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.OneNetwork
              )}
            />
          )}
          {portal.id === SchedulingPortals.Opendock && (
            <OpendockForm
              key={portal.id}
              {...commonProps}
              recentWarehouses={(recentWarehouses || []).filter(
                (w) => w && w.warehouseSource === SchedulingPortals.Opendock
              )}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.Opendock
              )}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
          {portal.id === SchedulingPortals.Retalix && (
            <RetalixForm
              key={portal.id}
              {...commonProps}
              recentWarehouses={(recentWarehouses || []).filter(
                (w) => w && w.warehouseSource === SchedulingPortals.Retalix
              )}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.Retalix
              )}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
          {portal.id === SchedulingPortals.C3Reservations && (
            <C3ReservationsForm
              key={portal.id}
              {...commonProps}
              tenant={selectedIntegration?.tenant}
            />
          )}
          {portal.id === SchedulingPortals.YardView && (
            <YardViewForm
              key={portal.id}
              {...commonProps}
              recentWarehouses={(recentWarehouses || []).filter(
                (w) => w && w.warehouseSource === SchedulingPortals.YardView
              )}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.YardView
              )}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
        </div>
      </div>
    );
  }

  const { standard, beta } = getPortalsByCategory();

  const renderPortalCard = (portal: GroupedPortal, isBeta: boolean = false) => {
    const IconComponent = portal.icon;
    const colorClasses = getColorClasses(portal.color);

    return (
      <Card
        key={portal.id + portal.primaryIntegrationId}
        className='w-full cursor-pointer transition-all hover:shadow-md hover:bg-neutral-50'
        onClick={() =>
          handlePortalSelect(
            portal.id,
            portal.primaryIntegrationId,
            portal.allUsers
          )
        }
      >
        <CardContent className='p-2'>
          <Flex align='center' gap='md'>
            <div className={`p-2 ${colorClasses.bg} rounded-lg shrink-0`}>
              <IconComponent
                className={`h-4 w-4 object-contain ${colorClasses.text}`}
              />
            </div>
            <div className='min-w-0 flex-1'>
              <Flex direction='col' gap='none' className='w-full'>
                <Flex
                  justify='between'
                  align='center'
                  gap='sm'
                  className='w-full'
                >
                  <Typography variant='h6' weight='medium'>
                    {portal.name}
                  </Typography>
                  {isBeta && (
                    <span className='text-[8px] text-neutral-50 bg-brand-main/90 rounded-full px-2 py-0.5 -mr-0.5 -mt-0.5'>
                      Beta
                    </span>
                  )}
                </Flex>

                {portal.primaryUsername && (
                  <Flex direction='row' gap='xs' align='end'>
                    <Typography variant='body-xxs' textColor='moreMuted'>
                      Username:
                    </Typography>
                    <Typography variant='body-xs' textColor='muted'>
                      {portal.primaryUsername.length > 15
                        ? portal.primaryUsername.slice(0, 15) + '...'
                        : portal.primaryUsername}
                    </Typography>
                    {portal.additionalUserCount > 0 && (
                      <Typography
                        variant='body-xxs'
                        className='text-neutral-600 bg-neutral-200 rounded-full px-1.5 py-0.25'
                      >
                        +{portal.additionalUserCount}
                      </Typography>
                    )}
                  </Flex>
                )}
              </Flex>
            </div>
          </Flex>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className='w-full mx-auto mb-4'>
      <Flex align='center' gap='xs' className='mt-2'>
        <Typography variant='h5' weight='medium'>
          Book an Appointment
        </Typography>
      </Flex>

      {/* Portals Section */}
      <div className='mt-1'>
        <Flex align='center' gap='sm'>
          <Typography variant='body-sm' textColor='muted'>
            Select a scheduling portal
          </Typography>
        </Flex>

        <Flex direction='col' gap='sm' className='mt-2'>
          {standard.map((portal) => renderPortalCard(portal))}
          {beta.map((portal) => renderPortalCard(portal, true))}
        </Flex>
      </div>

      {/* Email Request Section */}
      {isAppointmentEmailingEnabled && (
        <div className='pt-4 border-t mt-4'>
          <Card
            className='w-full cursor-pointer transition-all hover:shadow-md hover:bg-neutral-50'
            onClick={() => setShowEmail(true)}
          >
            <CardContent className='p-3'>
              <Flex align='center' gap='md'>
                <div className='p-2 bg-brand-100 rounded-lg shrink-0'>
                  <Mail className='h-4 w-4 text-brand-600' />
                </div>
                <div className='min-w-0'>
                  <Typography variant='h6' weight='medium'>
                    Email Request
                  </Typography>
                  <Typography
                    variant='body-xs'
                    className='text-neutral-400 mt-1'
                  >
                    Get a template to send via email
                  </Typography>
                </div>
              </Flex>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
