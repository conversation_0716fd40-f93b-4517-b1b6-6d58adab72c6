import { ArrowLeft, SparklesIcon } from 'lucide-react';

import { Button } from 'components/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';

type UserOption = {
  integrationID: number;
  username?: string;
};

type PortalSchedulingHeaderProps = {
  onBack: () => void;
  portalName?: string;
  selectedPortalUsers: UserOption[];
  selectedUsername?: string;
  portalUsername?: string;
  onUsernameChange: (newUsername: string) => void;
  showAISparkle?: boolean;
};

export default function PortalSchedulingHeader({
  onBack,
  portalName,
  selectedPortalUsers,
  selectedUsername,
  portalUsername,
  onUsernameChange,
  showAISparkle,
}: PortalSchedulingHeaderProps) {
  return (
    <TooltipProvider>
      <Flex direction='col' gap='none' className='mb-4'>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant='ghost'
              size='sm'
              onClick={onBack}
              className='p-0.5 hover:border-opacity-0 gap-1 group mb-2'
              buttonNamePosthog={null}
            >
              <ArrowLeft className='h-5 w-5' />
              <Typography
                variant='body-xs'
                weight='medium'
                className='group-hover:text-neutral-600 hover:underline'
              >
                Schedule another way
              </Typography>
            </Button>
          </TooltipTrigger>
          <TooltipContent side='top' className='rounded-md ml-2 max-w-[260px]'>
            <Typography variant='body-xs'>
              Select a different platform or send an email
            </Typography>
          </TooltipContent>
        </Tooltip>

        <Flex align='center' gap='xs' className='mb-2'>
          <Typography variant='h5' weight='semibold'>
            {portalName}
          </Typography>

          {showAISparkle && (
            <Tooltip>
              <TooltipTrigger asChild>
                <span
                  className='inline-flex items-center ml-1 gap-1'
                  role='img'
                  aria-label='AI suggested'
                >
                  <SparklesIcon
                    className='h-3 w-3 !stroke-accent'
                    strokeWidth={2}
                  />
                  <Typography variant='body-xs' className='text-accent-main'>
                    AI-suggested
                  </Typography>
                </span>
              </TooltipTrigger>
              <TooltipContent
                side='top'
                className='rounded-md mr-2 max-w-[240px]'
              >
                <Typography variant='body-xs'>
                  You've scheduled loads with this warehouse and customer using
                  this <strong>{portalName}</strong> account before.
                </Typography>
              </TooltipContent>
            </Tooltip>
          )}
        </Flex>

        {selectedPortalUsers.length > 1 ? (
          <Flex direction='col' gap='xs' className='w-full'>
            <Typography variant='body-xs' textColor='muted'>
              You will be scheduling using the selected account below. Make sure
              all info is correct.
            </Typography>

            <Typography variant='body-xs' textColor='muted'>
              Username:
            </Typography>
            <Select
              value={selectedUsername || ''}
              onValueChange={onUsernameChange}
            >
              <SelectTrigger className='w-full max-w-xs'>
                <SelectValue placeholder='Select username' />
              </SelectTrigger>
              <SelectContent>
                {selectedPortalUsers.map((user) => (
                  <SelectItem
                    key={user.integrationID}
                    value={user.username || `User ${user.integrationID}`}
                  >
                    {user.username || `User ${user.integrationID}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </Flex>
        ) : (
          (selectedUsername || portalUsername) && (
            <Flex direction='row' gap='xs'>
              <Typography variant='body-xs' textColor='muted'>
                Username:
              </Typography>
              <Typography variant='body-xs'>
                {selectedUsername || portalUsername}
              </Typography>
            </Flex>
          )
        )}
      </Flex>
    </TooltipProvider>
  );
}
