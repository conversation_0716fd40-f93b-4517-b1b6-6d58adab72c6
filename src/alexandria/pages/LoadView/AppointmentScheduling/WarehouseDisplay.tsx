import React from 'react';

import { Info } from 'lucide-react';

import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { Warehouse } from 'types/Warehouse';
import { cn } from 'utils/shadcn';

interface WarehouseDisplayProps {
  warehouse: Warehouse;
  className?: string;
  showSource?: boolean;
}

const WarehouseDisplay: React.FC<WarehouseDisplayProps> = ({
  warehouse,
  className = '',
  showSource = true,
}) => {
  const capitalizeFirst = (str: string) => {
    if (!str) return '';
    const trimmed = str.trim();
    return trimmed.charAt(0).toUpperCase() + trimmed.slice(1);
  };

  return (
    <div
      className={cn(
        'border border-info-300 bg-info-200 rounded-[4px] py-1.5 px-2.5 mt-2 mb-4 shadow-sm',
        className
      )}
    >
      <Flex direction='col' gap='none'>
        <Flex direction='row' gap='xs' align='center'>
          <Info className='h-3.5 w-3.5' color='#666666' strokeWidth={2.5} />
          <Typography variant='body-sm' weight='semibold'>
            Selected Warehouse:
          </Typography>
        </Flex>

        <Flex direction='col' gap='none' className='ml-0.5'>
          <Typography
            variant='body-xs'
            weight='medium'
            className='text-neutral-600'
          >
            {warehouse.warehouseName.trim()}
          </Typography>

          {warehouse.warehouseAddressLine1 && (
            <Typography
              variant='body-xs'
              weight='medium'
              className='text-neutral-600'
            >
              {warehouse.warehouseAddressLine1.trim()}
            </Typography>
          )}

          {warehouse.warehouseAddressLine2 && (
            <Typography
              variant='body-xs'
              weight='medium'
              className='text-neutral-600'
            >
              {warehouse.warehouseAddressLine2.trim()}
            </Typography>
          )}

          {showSource && warehouse.warehouseSource && (
            <Typography variant='body-xxs' textColor='muted' className='mt-0.5'>
              {capitalizeFirst(warehouse.warehouseSource)} Warehouse
            </Typography>
          )}
        </Flex>
      </Flex>
    </div>
  );
};

export default WarehouseDisplay;
