import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { Order } from 'types/Order';

interface OrderTabProps {
  order: Order;
}

export default function OrderTab({ order }: OrderTabProps) {
  return (
    <div className='p-4'>
      <div className='border rounded-lg p-4 bg-neutral-50 shadow-sm'>
        <div className='text-xs text-neutral-500 mb-2'>
          Debug: OrderTab received order with ID: {order.ID}, externalOrderId:{' '}
          {order.externalOrderId}
        </div>

        <Flex justify='between' align='start' className='mb-4'>
          <div>
            <Typography variant='h3' className='font-medium text-lg'>
              Order #{order.externalOrderId}
            </Typography>
            <Typography variant='body-sm' className='text-neutral-500'>
              Type: {order.type}
            </Typography>
          </div>
          <div className='text-right'>
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                order.status === 'COMPLETED'
                  ? 'bg-success-100 text-success-800'
                  : order.status === 'IN_PROGRESS'
                    ? 'bg-info-100 text-info-800'
                    : 'bg-neutral-100 text-neutral-800'
              }`}
            >
              {order.status}
            </span>
            <Typography variant='body-sm' className='text-neutral-500 mt-1'>
              Mode: {order.mode}
            </Typography>
          </div>
        </Flex>

        <Grid cols='2' gap='xl' className='mb-4 mx-0 w-full'>
          <div className='space-y-4'>
            <div>
              <Typography
                variant='h6'
                weight='medium'
                className='text-neutral-700 mb-1'
              >
                Pickup Location
              </Typography>
              <div className='text-sm'>
                <Typography weight='medium'>{order.pickup.name}</Typography>
                <Typography>{order.pickup.addressLine1}</Typography>
                {order.pickup.addressLine2 && (
                  <Typography>{order.pickup.addressLine2}</Typography>
                )}
                <Typography>{`${order.pickup.city}, ${order.pickup.state} ${order.pickup.zipcode}`}</Typography>
              </div>
            </div>
            <div>
              <Typography
                variant='h6'
                weight='medium'
                className='text-neutral-700 mb-1'
              >
                Delivery Location
              </Typography>
              <div className='text-sm'>
                <Typography weight='medium'>{order.consignee.name}</Typography>
                <Typography>{order.consignee.addressLine1}</Typography>
                {order.consignee.addressLine2 && (
                  <Typography>{order.consignee.addressLine2}</Typography>
                )}
                <Typography>{`${order.consignee.city}, ${order.consignee.state} ${order.consignee.zipcode}`}</Typography>
              </div>
            </div>
          </div>

          <div className='space-y-4'>
            <div>
              <Typography
                variant='h6'
                weight='medium'
                className='text-neutral-700 mb-1'
              >
                Specifications
              </Typography>
              <div className='text-sm space-y-1'>
                <Typography>
                  Weight: {order.specifications.totalWeight.val}{' '}
                  {order.specifications.totalWeight.unit}
                </Typography>
                <Typography>Pieces: {order.pieceCount}</Typography>
                {order.specifications.isRefrigerated && (
                  <Typography className='text-info-600'>
                    Temperature: {order.specifications.minTempFahrenheit}
                    °F - {order.specifications.maxTempFahrenheit}°F
                  </Typography>
                )}
                {order.isHazmat && (
                  <Typography className='text-error-600'>Hazmat</Typography>
                )}
              </div>
            </div>
            <div>
              <Typography
                variant='h6'
                weight='medium'
                className='text-neutral-700 mb-1'
              >
                Rate Information
              </Typography>
              <div className='text-sm space-y-1'>
                <Typography>
                  Total Charge: {order.rateData.customerTotalCharge.val}{' '}
                  {order.rateData.customerTotalCharge.unit}
                </Typography>
              </div>
            </div>
          </div>
        </Grid>

        {order.poNums && (
          <div className='mt-4 pt-4 border-t'>
            <Typography
              variant='h6'
              weight='medium'
              className='text-neutral-700 mb-2'
            >
              References
            </Typography>
            <Flex gap='sm' wrap='wrap'>
              <span className='text-xs bg-neutral-100 px-2 py-1 rounded'>
                PO: {order.poNums}
              </span>
              <span className='text-xs bg-neutral-100 px-2 py-1 rounded'>
                Order ID: {order.orderTrackingId}
              </span>
            </Flex>
          </div>
        )}

        {order.loadId && (
          <div className='mt-4 pt-4 border-t'>
            <Typography
              variant='h6'
              weight='medium'
              className='text-neutral-700 mb-2'
            >
              Load Association
            </Typography>
            <Typography variant='body-sm' className='text-neutral-600'>
              This order is associated with load ID: {order.loadId}
            </Typography>
          </div>
        )}
      </div>
    </div>
  );
}
