import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { isAxiosError } from 'axios';
import dayjs from 'dayjs';
import {
  CalendarIcon,
  MailsIcon,
  PackageIcon,
  RouteIcon,
  XCircleIcon,
} from 'lucide-react';

import SuggestionsCarousel from 'components/AISuggestions/SuggestionsCarousel';
import CarrierVerificationCard from 'components/CarrierVerificationCard';
import ErrorBoundary from 'components/ErrorBoundary';
import LoadContextProvider from 'components/LoadContextProvider';
import StarredLoadList from 'components/StarredLoadList';
import StarredLoadToggle from 'components/StarredLoadToggle';
import TMSProvider from 'components/TMSProvider';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  TabsTriggerVariants,
} from 'components/Tabs';
import { Toaster } from 'components/ToasterProvider';
import { Flex } from 'components/layout';
import SidebarLoader from 'components/loading/SidebarLoader';
import { Typography } from 'components/typography';
import { AvailableTabs } from 'constants/SidebarTabs';
import {
  SidebarStateContext,
  isEmailPlatform,
} from 'contexts/sidebarStateContext';
import { TMSContextType } from 'contexts/tms';
import useFetchLoadByFreightTrackingID from 'hooks/useFetchLoadByFreightTrackingID';
import { fetchStarredLoads } from 'hooks/useFetchStarredLoads';
import useFetchSuggestions from 'hooks/useFetchSuggestions';
import { fetchViewedLoads } from 'hooks/useFetchViewedLoads';
import { useServiceFeatures } from 'hooks/useServiceContext';
import InfoCircleIcon from 'icons/InfoCircle';
import { createHostInstance } from 'lib/hosts/interface';
import AppointmentSchedulingSection from 'pages/LoadView/AppointmentTab';
import LoadInformationTab from 'pages/LoadView/LoadInformationTab';
import OrderTab from 'pages/LoadView/OrderTab';
import OrdersTab from 'pages/LoadView/OrdersTab';
import EmailsSection from 'pages/LoadView/OutboxTab';
import CarrierCheckInSection from 'pages/LoadView/TrackAndTrace/CarrierCheckIn';
import CheckCallSection from 'pages/LoadView/TrackAndTrace/CheckCalls';
import ShipperCheckInSection from 'pages/LoadView/TrackAndTrace/ShipperCheckIn';
import UnifiedRevenovaLoadForm from 'pages/QuoteView/LoadBuilding/RevenovaSectionForms/UnifiedRevenovaLoadForm';
import TurvoLoadForm from 'pages/QuoteView/LoadBuilding/TurvoLoadForm';
import { Email } from 'types/Email';
import { ExternalLinks } from 'types/ExternalLinks';
import { Load, normalizeLoad } from 'types/Load';
import { LoadAttributes } from 'types/LoadAttributes';
import { PendingOutboxEmails } from 'types/PendingOutboxEmail';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import CheckCallSource from 'types/enums/CheckCallSource';
import { TMS } from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import {
  CheckCallSuggestion,
  SuggestedLoadChange,
} from 'types/suggestions/LoadSuggestions';
import captureException from 'utils/captureException';
import { cn } from 'utils/shadcn';

import {
  TabItem,
  getTabKey,
  useLoadSearch,
} from '../../contexts/loadSearchContext';
import useFetchOrderByTrackingId from '../../hooks/useFetchOrderByTrackingId';
import OverviewSection from './OverviewSection';
import ExceptionSection from './TrackAndTrace/Exceptions';
import NoteSection from './TrackAndTrace/Notes';

interface LoadsSidebarProps {
  email: Maybe<Email>;
}

export default function LoadsSidebar({ email }: LoadsSidebarProps) {
  const { addTab, tabs, setTabs, activeTabKey, setActiveTabKey } =
    useLoadSearch();

  const {
    currentState: { starredLoads },
    setCurrentState,
  } = useContext(SidebarStateContext);

  const {
    serviceFeaturesEnabled: {
      isAdvancedSearchEnabled,
      isOrderEnabled,
      isCarrierVerificationEnabled,
      isLoadViewEnabled,
      isQuoteViewEnabled,
      isAppointmentSchedulingEnabled,
      isTrackAndTraceEnabled,
      isQuickQuoteEnabled,
      isCarrierNetworkQuotingEnabled,
      isLoadBuildingEnabled,
      isTruckListEnabled,
    },
    carrierVerificationIntegrations,
  } = useServiceFeatures();

  // Check if any major features are enabled
  const hasAnyFeaturesEnabled =
    isAdvancedSearchEnabled ||
    isLoadViewEnabled ||
    isQuoteViewEnabled ||
    isAppointmentSchedulingEnabled ||
    isTrackAndTraceEnabled ||
    isQuickQuoteEnabled ||
    isCarrierNetworkQuotingEnabled ||
    isLoadBuildingEnabled ||
    isTruckListEnabled ||
    isCarrierVerificationEnabled;

  // Get the first enabled carrier verification integration
  const enabledCarrierVerificationIntegration =
    carrierVerificationIntegrations?.[0] ?? null;

  // Fetch starred and viewed loads only when advanced search is enabled
  useEffect(() => {
    if (isAdvancedSearchEnabled) {
      fetchStarredLoads(setCurrentState);
      fetchViewedLoads(setCurrentState);
    }
  }, [isAdvancedSearchEnabled, setCurrentState]);

  const closeTab = useCallback(
    (tabToClose: TabItem) => {
      setTabs((prevTabs) => {
        const tabToCloseIndex = prevTabs.findIndex(
          (tab) => getTabKey(tab) === getTabKey(tabToClose)
        );

        if (tabToCloseIndex === -1) {
          return prevTabs;
        }

        const newTabs = prevTabs.filter((_, i) => i !== tabToCloseIndex);

        if (getTabKey(tabToClose) !== activeTabKey) {
          return newTabs;
        }

        let newActiveTab: TabItem | undefined;
        if (newTabs.length > 0) {
          const newIndex = Math.min(tabToCloseIndex, newTabs.length - 1);
          newActiveTab = newTabs[newIndex];
        }

        setActiveTabKey(newActiveTab ? getTabKey(newActiveTab) : '');
        return newTabs;
      });
    },
    [activeTabKey, setActiveTabKey, setTabs]
  );

  const activeTab = tabs.find((tab) => getTabKey(tab) === activeTabKey);

  return (
    <div className='flex-1 w-full'>
      <Flex direction='col'>
        <Flex direction='col' className='w-full flex-1 shrink-0'>
          <Tabs
            value={activeTabKey}
            onValueChange={setActiveTabKey}
            className='w-full flex-1 shrink-0 flex flex-col mt-2'
          >
            <TabsList className='w-full gap-[12px] relative flex items-center justify-between pr-4'>
              <Flex gap='md' align='center' className='flex-1 overflow-x-auto'>
                {tabs?.length > 0 &&
                  tabs.map((tab) => (
                    <>
                      <TabsTrigger
                        value={getTabKey(tab)}
                        key={getTabKey(tab)}
                        className={cn(
                          'text-sm rounded-none! inline-block w-auto shrink-1 text-ellipsis overflow-hidden',
                          activeTabKey === getTabKey(tab) && 'shrink-0'
                        )}
                      >
                        <Flex align='center' gap='xs'>
                          {isOrderEnabled && tab.type === 'order' && (
                            <PackageIcon className='h-3 w-3' />
                          )}
                          {tab.id}
                        </Flex>
                      </TabsTrigger>
                      {activeTabKey === getTabKey(tab) && (
                        <XCircleIcon
                          className='h-4 w-4 shrink-0 -ml-1 stroke-brand-main cursor-pointer'
                          onClick={() => closeTab(tab)}
                        />
                      )}
                    </>
                  ))}
              </Flex>

              {isAdvancedSearchEnabled && activeTab && (
                <Flex align='center' gap='md' className='ml-4 shrink-0 py-2'>
                  <StarredLoadToggle
                    tab={activeTab.id}
                    starredLoads={starredLoads}
                  />
                  <StarredLoadList
                    tab={activeTab.id}
                    starredLoads={starredLoads}
                    addLoadTab={(id) => addTab(id, 'load')}
                  />
                </Flex>
              )}
            </TabsList>

            <div className='flex-1 shrink-0'>
              {!hasAnyFeaturesEnabled ? (
                <div className='flex-1 flex flex-col items-center justify-center p-8'>
                  <div className='text-center mb-6 max-w-sm'>
                    <Typography
                      variant='h4'
                      weight='semibold'
                      className='mb-3'
                      align='center'
                    >
                      Welcome to Drumkit!
                    </Typography>
                    <Typography
                      variant='body-sm'
                      textColor='muted'
                      className='mb-4'
                      align='center'
                    >
                      Your Drumkit features are not yet configured. Contact your
                      Drumkit representative to get started with load
                      management, quoting, and more.
                    </Typography>
                    <Typography
                      variant='body-xs'
                      textColor='muted'
                      align='center'
                    >
                      Need help? Reach out to your account manager or support
                      team.
                    </Typography>
                  </div>
                </div>
              ) : tabs.length === 0 ? (
                <div className='flex-1 flex flex-col items-center justify-center p-8'>
                  <div className='text-center mb-4'>
                    <Typography
                      variant='body-sm'
                      weight='medium'
                      textColor='muted'
                      className='mb-1'
                      align='center'
                    >
                      No loads or orders found
                    </Typography>
                    <Typography
                      variant='body-xs'
                      textColor='muted'
                      align='center'
                    >
                      Search for a load or order ID to get started
                    </Typography>
                  </div>
                  {email?.from && isCarrierVerificationEnabled && (
                    <CarrierVerificationCard
                      fromEmail={email.from}
                      enabledIntegration={enabledCarrierVerificationIntegration}
                    />
                  )}
                </div>
              ) : (
                tabs.map((tab) => (
                  <TabsContent
                    value={getTabKey(tab)}
                    key={getTabKey(tab)}
                    className='pt-4'
                  >
                    {tab.type === 'load' ? (
                      <LoadsSidebarLoadTab
                        email={email}
                        freightTrackingID={tab.id}
                        closeTab={() => closeTab(tab)}
                      />
                    ) : isOrderEnabled ? (
                      <LoadsSidebarOrderTab
                        orderId={tab.id}
                        closeTab={() => closeTab(tab)}
                      />
                    ) : (
                      <div className='px-4 text-error-main'>
                        Order functionality is not enabled.
                      </div>
                    )}
                  </TabsContent>
                ))
              )}
            </div>
          </Tabs>
        </Flex>
      </Flex>
      <Toaster />
    </div>
  );
}

type LoadsSidebarOrderTabProps = {
  orderId: string;
  closeTab: () => void;
};

function LoadsSidebarOrderTab({
  orderId,
  closeTab,
}: LoadsSidebarOrderTabProps) {
  const { order, isLoading, error } = useFetchOrderByTrackingId(orderId);
  const { setTabs, setActiveTabKey } = useLoadSearch();

  const orderNotFound =
    !isLoading &&
    error &&
    isAxiosError(error) &&
    error.response?.status === 404;

  // If order not found, close the tab
  useEffect(() => {
    if (orderNotFound) {
      closeTab();
    }
  }, [orderNotFound, closeTab]);

  // If order has a load associated with it, redirect to show the load
  useEffect(() => {
    if (order && order.loadId && order.externalLoadId) {
      setTabs((prevTabs) => {
        const associatedLoadTab: TabItem = {
          id: order.externalLoadId!,
          type: 'load',
        };

        const loadTabExists = prevTabs.some(
          (tab) =>
            tab.id === associatedLoadTab.id &&
            tab.type === associatedLoadTab.type
        );

        if (loadTabExists) {
          // The load tab already exists, so just close this order tab
          // and switch to the load tab.
          setActiveTabKey(getTabKey(associatedLoadTab));
          return prevTabs.filter(
            (tab) => !(tab.id === orderId && tab.type === 'order')
          );
        } else {
          // The load tab doesn't exist, so replace this order tab with a new load tab.
          const newTabs = prevTabs.map((tab) =>
            tab.id === orderId && tab.type === 'order' ? associatedLoadTab : tab
          );
          setActiveTabKey(getTabKey(associatedLoadTab));
          return newTabs;
        }
      });
    }
  }, [order, orderId, setTabs, setActiveTabKey]);

  // If order has a load with externalLoadId, don't render the order view
  if (order && order.loadId && order.externalLoadId) {
    return (
      <div className='flex-1 shrink-0'>
        <SidebarLoader />
        <div className='px-4 text-neutral-600'>
          Redirecting to associated load...
        </div>
      </div>
    );
  }

  // If order not found, render nothing while tab is closing
  if (orderNotFound) {
    return null;
  }

  return (
    <div className='flex-1 shrink-0'>
      {!order && isLoading && <SidebarLoader />}

      {order && !isLoading && !order.loadId && (
        <ErrorBoundary>
          <OrderTab order={order} />
        </ErrorBoundary>
      )}

      {orderNotFound && (
        <div className='px-4 text-error-main'>
          Order not found for #{orderId}.
        </div>
      )}

      {!orderNotFound && orderId && !order && !isLoading && (
        <div className='px-4 text-error-main'>Error fetching order.</div>
      )}
    </div>
  );
}

type LoadsSidebarLoadTabProps = {
  email: MaybeUndef<Email>;
  freightTrackingID: string;
  closeTab: () => void;
};

function LoadsSidebarLoadTab({
  email,
  freightTrackingID,
  closeTab,
}: LoadsSidebarLoadTabProps) {
  const {
    tmsID,
    tmsName,
    load,
    loadAttributes,
    externalLinks,
    invalidate,
    invalidateKey,
    isLoading,
    pendingOutboxEmails,
    pickupWarehouse: loadPickupWarehouse,
    dropoffWarehouse: loadDropoffWarehouse,
    error: fetchError,
  } = useFetchLoadByFreightTrackingID(freightTrackingID);

  const { tmsIntegrations } = useServiceFeatures();

  // A service may have multiple TMSes, so we need to get the specific one for this load
  const tmsTenant =
    tmsIntegrations?.find((tms) => tms.id === tmsID)?.tenant ?? '';

  const loadNotFound =
    !isLoading &&
    fetchError &&
    isAxiosError(fetchError) &&
    fetchError.response?.status === 404;

  // If load not found, close the tab
  useEffect(() => {
    if (loadNotFound) {
      closeTab();
    }
  }, [loadNotFound, closeTab]);

  const invalidateLoad = async (
    load?: Maybe<Load>,
    attrs?: Maybe<LoadAttributes>
  ) => {
    if (load && attrs) {
      await invalidate({
        tmsID,
        tmsName,
        externalLinks,
        load,
        loadAttributes: attrs,
        pendingOutboxEmails,
        pickupWarehouse: loadPickupWarehouse,
        dropoffWarehouse: loadDropoffWarehouse,
      });
    } else {
      invalidate();
    }
  };

  // If load not found, render nothing while tab is closing
  if (loadNotFound) {
    return null;
  }

  return (
    <div className='flex-1 shrink-0'>
      {!load && isLoading && <SidebarLoader />}

      {/* {!isLoading && load && load.moreThanTwoStops && (
        <div className='px-4 text-error-main'>
          {'We will soon support multi-stop loads!'}
        </div>
      )} */}

      {load && !isLoading && !load.isPlaceholder && (
        <LoadTabContent
          tmsID={tmsID}
          tmsName={tmsName}
          tenant={tmsTenant}
          email={email!}
          load={load!}
          loadAttributes={loadAttributes!}
          externalLinks={externalLinks}
          loadPickupWarehouse={loadPickupWarehouse!}
          loadDropoffWarehouse={loadDropoffWarehouse!}
          invalidate={invalidateLoad}
          invalidateKey={invalidateKey}
          pendingOutboxEmails={pendingOutboxEmails!}
        />
      )}

      {loadNotFound && (
        <div className='px-4 text-error-main'>
          Load not found for #{freightTrackingID}.
        </div>
      )}

      {/* We live lookup a load from TMS when a user opens email/searches for it. So if it's still a placeholder at this point,
  something went wrong with the live lookup (429, network timeout, etc) */}
      {(!loadNotFound && freightTrackingID && !load && !isLoading) ||
        (load?.isPlaceholder && (
          <div className='px-4 text-error-main'>Error fetching load.</div>
        ))}
    </div>
  );
}

type LoadTabContentProps = TMSContextType & {
  email: Email;
  load: Load;
  loadAttributes: LoadAttributes;
  externalLinks: MaybeUndef<ExternalLinks>;
  loadPickupWarehouse: MaybeUndef<Warehouse>;
  loadDropoffWarehouse: MaybeUndef<Warehouse>;
  invalidate: (load?: Load, loadAttributes?: LoadAttributes) => Promise<void>;
  invalidateKey: number;
  pendingOutboxEmails: MaybeUndef<PendingOutboxEmails>;
};

export function LoadTabContent({
  tmsID,
  tmsName,
  email,
  load,
  loadAttributes,
  externalLinks,
  loadPickupWarehouse,
  loadDropoffWarehouse,
  invalidate,
  invalidateKey,
  pendingOutboxEmails,
}: LoadTabContentProps) {
  const {
    serviceFeaturesEnabled: {
      isAppointmentSchedulingEnabled,
      isTrackAndTraceEnabled,
      isExceptionsEnabled,
      isCheckCallCarrierSOPEnabled,
      isCheckCallShipperSOPEnabled,
      isCheckCallNotesEnabled,
      isCarrierEmailOutboxEnabled,
      isOrderEnabled,
    },
    tmsIntegrations,
  } = useServiceFeatures();

  const tmsTenant =
    tmsIntegrations?.find((tms) => tms.id === tmsID)?.tenant ?? '';

  const {
    suggestions,
    isLoading,
    invalidate: suggestionsInvalidate,
  } = useFetchSuggestions(email?.threadID, load.ID);

  const [tab, setTab] = useState(AvailableTabs.LoadInformation);

  const [modifiedSuggestions, setModifiedSuggestions] = useState<
    SuggestedLoadChange[]
  >([]);

  useEffect(() => {
    if (suggestions) {
      const updatedSuggestions = suggestions
        .filter((sug) => sug.status === 'pending')
        .map((sug) => {
          if (sug.pipeline === SuggestionPipelines.CarrierInfo) {
            return {
              ...sug,
              suggested: {
                ...sug.suggested,
                dispatchedTime: sug.suggested.dispatchedTime
                  ? sug.suggested.dispatchedTime
                  : dayjs().second(0).millisecond(0).toDate(),
                dispatchSource:
                  sug.suggested.dispatchSource ?? CheckCallSource.Dispatcher,
              },
            };
          }
          return sug;
        });

      setModifiedSuggestions(updatedSuggestions);
    }
  }, [suggestions]);

  const {
    currentState: { drumkitPlatform },
  } = useContext(SidebarStateContext);

  useEffect(() => {
    if (!isEmailPlatform(drumkitPlatform) && tmsName) {
      setTab(
        createHostInstance(tmsName as TMS)?.determineDefaultLoadTab() ??
          AvailableTabs.LoadInformation
      );
    }
  }, [tmsName]);

  const normalizedLoad = normalizeLoad(tmsName, load);

  // Reformat load attributes for context provider
  const fieldAttrs = useMemo(() => {
    const customerFieldAttrs = loadAttributes.customer
      ? Object.entries(loadAttributes.customer).map(([key, value]) => ({
          [`customer.${key}`]: value,
        }))
      : [];
    const billToFieldAttrs = loadAttributes.billTo
      ? Object.entries(loadAttributes.billTo).map(([key, value]) => ({
          [`billTo.${key}`]: value,
        }))
      : [];
    const specificationsFieldAttrs = loadAttributes.specifications
      ? Object.entries(loadAttributes.specifications).map(([key, value]) => ({
          [`specifications.${key}`]: value,
        }))
      : [];
    const pickupFieldAttrs = loadAttributes.pickup
      ? Object.entries(loadAttributes.pickup).map(([key, value]) => ({
          [`pickup.${key}`]: value,
        }))
      : [];
    const consigneeFieldAttrs = loadAttributes.consignee
      ? Object.entries(loadAttributes.consignee).map(([key, value]) => ({
          [`consignee.${key}`]: value,
        }))
      : [];
    const stopsFieldAttrs = loadAttributes.stops
      ? Object.entries(loadAttributes.stops).flatMap(([key, value]) => {
          if (key === 'address') {
            return Object.entries(value).map(([addressKey, addressValue]) => ({
              [`stops.address.${addressKey}`]: addressValue,
            }));
          }
          return [{ [`stops.${key}`]: value }];
        })
      : [];
    const carrierFieldAttrs = loadAttributes.carrier
      ? Object.entries(loadAttributes.carrier).map(([key, value]) => ({
          [`carrier.${key}`]: value,
        }))
      : [];
    const rateDataFieldAttrs = loadAttributes.rateData
      ? Object.entries(loadAttributes.rateData).map(([key, value]) => ({
          [`rateData.${key}`]: value,
        }))
      : [];

    return [
      { poNums: loadAttributes.poNums },
      { mode: loadAttributes.mode },
      ...customerFieldAttrs,
      ...billToFieldAttrs,
      ...specificationsFieldAttrs,
      ...pickupFieldAttrs,
      ...consigneeFieldAttrs,
      ...stopsFieldAttrs,
      ...carrierFieldAttrs,
      ...rateDataFieldAttrs,
    ];
  }, [loadAttributes]);

  const {
    currentState: { clickedSuggestion },
  } = useContext(SidebarStateContext);

  // Redirecting to correct tab after Email AI card is clicked
  useEffect(() => {
    if (!clickedSuggestion) return;

    switch (clickedSuggestion.pipeline) {
      case SuggestionPipelines.CarrierInfo:
      case SuggestionPipelines.ApptConfirmation:
        setTab(AvailableTabs.LoadInformation);
        break;
      case SuggestionPipelines.CheckCall:
        setTab(AvailableTabs.TrackAndTrace);
        break;
      default:
        captureException(
          new Error('Invalid suggestion pipeline: ' + clickedSuggestion)
        );
    }
  }, [clickedSuggestion]);

  const checkCallSuggestions = useMemo(() => {
    if (!suggestions) return [];

    return suggestions.filter(
      (s): s is CheckCallSuggestion =>
        s.status === 'pending' && s.pipeline == SuggestionPipelines.CheckCall
    );
  }, [suggestions]);

  // Initialize tab refs
  const tabRefs = Object.fromEntries(
    Object.values(AvailableTabs).map((tab) => [
      tab,
      useRef<HTMLButtonElement>(null),
    ])
  ) as Record<AvailableTabs, React.RefObject<HTMLButtonElement>>;

  return (
    <>
      {isLoading && <SidebarLoader />}

      {!isLoading && load && modifiedSuggestions?.length > 0 && (
        <SuggestionsCarousel suggestions={modifiedSuggestions} />
      )}

      {!isLoading && load && !load.isPlaceholder && (
        <OverviewSection
          tmsName={tmsName as TMS}
          load={load}
          fromEmail={email?.from}
        />
      )}

      {!isLoading && load && (
        <TMSProvider tmsName={tmsName} tmsID={tmsID} tenant={tmsTenant}>
          <LoadContextProvider
            fieldAttributes={fieldAttrs}
            loadAttrsObj={loadAttributes}
            invalidateLoad={invalidate}
          >
            <Tabs
              value={tab}
              onValueChange={(tab) => setTab(tab as AvailableTabs)}
              className='w-full h-full flex-1 shrink-0 flex flex-col'
            >
              <TabsList className='w-full overflow-x-auto flex gap-0'>
                <TabsTrigger
                  value={AvailableTabs.LoadInformation}
                  ref={tabRefs.loadInformation}
                  variant={TabsTriggerVariants.LoadTabs}
                >
                  <InfoCircleIcon
                    className='min-h-[18px] max-h-[18px] min-w-[18px] max-w-[18px]'
                    strokeWidth={1}
                  />
                  <Typography className='overflow-x-hidden text-brand'>
                    Load Info
                  </Typography>
                </TabsTrigger>
                {isOrderEnabled &&
                  (tmsName === TMS.FreightFlow || tmsName === TMS.ThreeG) && (
                    <TabsTrigger
                      value={AvailableTabs.Orders}
                      ref={tabRefs.orders}
                      variant={TabsTriggerVariants.LoadTabs}
                    >
                      <PackageIcon
                        className='min-h-[18px] max-h-[18px] min-w-[18px] max-w-[18px]'
                        strokeWidth={1}
                      />
                      <Typography className='overflow-x-hidden text-brand'>
                        Orders
                      </Typography>
                    </TabsTrigger>
                  )}
                {/* Temporarily disable appointment scheduling for 3G */}
                {isAppointmentSchedulingEnabled && tmsName !== TMS.ThreeG && (
                  <TabsTrigger
                    value={AvailableTabs.AppointmentScheduling}
                    ref={tabRefs.appointmentScheduling}
                    variant={TabsTriggerVariants.LoadTabs}
                  >
                    <CalendarIcon
                      className='min-h-[18px] max-h-[18px] min-w-[18px] max-w-[18px]'
                      strokeWidth={1}
                    />
                    <Typography className='overflow-x-hidden text-brand'>
                      Appointments
                    </Typography>
                  </TabsTrigger>
                )}
                {/* Temporarily disable track and trace for 3G */}
                {isTrackAndTraceEnabled && tmsName !== TMS.ThreeG && (
                  <TabsTrigger
                    value={AvailableTabs.TrackAndTrace}
                    ref={tabRefs.trackAndTrace}
                    variant={TabsTriggerVariants.LoadTabs}
                  >
                    <RouteIcon
                      className='min-h-[18px] max-h-[18px] min-w-[18px] max-w-[18px]'
                      strokeWidth={1}
                    />
                    <Typography className='overflow-x-hidden text-brand'>
                      Track & Trace
                    </Typography>
                  </TabsTrigger>
                )}

                {/* Temporarily disable outbox for 3G */}
                {isCarrierEmailOutboxEnabled && tmsName !== TMS.ThreeG && (
                  <TabsTrigger
                    value={AvailableTabs.Outbox}
                    ref={tabRefs.outbox}
                    variant={TabsTriggerVariants.LoadTabs}
                  >
                    <MailsIcon
                      className='min-h-[18px] max-h-[18px] min-w-[18px] max-w-[18px]'
                      strokeWidth={1}
                    />
                    <Typography className='overflow-x-hidden'>
                      Outbox
                    </Typography>
                  </TabsTrigger>
                )}
              </TabsList>
              <div className='flex-1 shrink-0'>
                <TabsContent
                  value={AvailableTabs.LoadInformation}
                  className='px-5'
                >
                  <ErrorBoundary>
                    {tmsName === TMS.Turvo ? (
                      <TurvoLoadForm
                        isCreateMode={false}
                        load={load}
                        externalLinks={externalLinks}
                        key={`${load.ID}-${invalidateKey}`}
                      />
                    ) : tmsName === TMS.Revenova ? (
                      <UnifiedRevenovaLoadForm
                        isCreateMode={false}
                        load={load}
                        externalLinks={externalLinks || undefined}
                        key={`${load.ID}-${invalidateKey}`}
                      />
                    ) : (
                      <LoadInformationTab
                        load={load}
                        externalLinks={externalLinks}
                        key={`${load.ID}-${invalidateKey}`}
                      />
                    )}
                  </ErrorBoundary>
                </TabsContent>
                {isOrderEnabled &&
                  (tmsName === TMS.FreightFlow || tmsName === TMS.ThreeG) && (
                    <TabsContent value={AvailableTabs.Orders}>
                      <ErrorBoundary>
                        <OrdersTab load={load} />
                      </ErrorBoundary>
                    </TabsContent>
                  )}
                {isAppointmentSchedulingEnabled && (
                  <TabsContent value={AvailableTabs.AppointmentScheduling}>
                    <ErrorBoundary>
                      <AppointmentSchedulingSection
                        normalizedLoad={normalizedLoad}
                        key={load.freightTrackingID}
                        loadPickupWarehouse={
                          loadPickupWarehouse?.warehouseID
                            ? loadPickupWarehouse
                            : null
                        }
                        loadDropoffWarehouse={
                          loadDropoffWarehouse?.warehouseID
                            ? loadDropoffWarehouse
                            : null
                        }
                      />
                    </ErrorBoundary>
                  </TabsContent>
                )}
                {isTrackAndTraceEnabled && (
                  <TabsContent
                    value={AvailableTabs.TrackAndTrace}
                    className='px-5'
                  >
                    {isCheckCallCarrierSOPEnabled && (
                      <ErrorBoundary>
                        <CarrierCheckInSection
                          normalizedLoad={normalizedLoad}
                          key={load.freightTrackingID}
                          setTab={setTab}
                        />
                      </ErrorBoundary>
                    )}
                    <ErrorBoundary>
                      <CheckCallSection
                        normalizedLoad={normalizedLoad}
                        key={normalizedLoad.freightTrackingID}
                        suggestions={checkCallSuggestions}
                        invalidateSuggestions={suggestionsInvalidate}
                      />
                    </ErrorBoundary>
                    {isExceptionsEnabled && (
                      <ErrorBoundary>
                        <ExceptionSection
                          normalizedLoad={normalizedLoad}
                          key={normalizedLoad.freightTrackingID}
                        />
                      </ErrorBoundary>
                    )}
                    {isCheckCallShipperSOPEnabled && (
                      <ErrorBoundary>
                        <ShipperCheckInSection
                          normalizedLoad={normalizedLoad}
                          key={normalizedLoad.freightTrackingID}
                        />
                      </ErrorBoundary>
                    )}
                    {isCheckCallNotesEnabled && (
                      <ErrorBoundary>
                        <NoteSection
                          normalizedLoad={normalizedLoad}
                          key={normalizedLoad.freightTrackingID}
                        />
                      </ErrorBoundary>
                    )}
                  </TabsContent>
                )}

                {isCarrierEmailOutboxEnabled &&
                  pendingOutboxEmails?.carrierEmails && (
                    <TabsContent value={AvailableTabs.Outbox}>
                      <ErrorBoundary>
                        <EmailsSection
                          normalizedLoad={normalizedLoad}
                          carrierEmails={pendingOutboxEmails?.carrierEmails}
                          setTab={setTab}
                        />
                      </ErrorBoundary>
                    </TabsContent>
                  )}
              </div>
            </Tabs>
          </LoadContextProvider>
        </TMSProvider>
      )}
    </>
  );
}
