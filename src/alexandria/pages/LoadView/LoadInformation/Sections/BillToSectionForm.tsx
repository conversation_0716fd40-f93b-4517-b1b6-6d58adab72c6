import { InputValue } from 'components/input/RHFTextInput';
import { LoadTextInput } from 'pages/LoadView/LoadInformation/Components';
import { emailFieldOptions } from 'utils/formValidators';

export function BillToSectionForm() {
  return (
    <>
      <LoadTextInput name='billTo.name' label='Name' />
      <LoadTextInput name='billTo.addressLine1' label='Address Line 1' />
      <LoadTextInput name='billTo.addressLine2' label='Address Line 2' />
      <LoadTextInput name='billTo.city' label='City' />
      <LoadTextInput name='billTo.state' label='State' />
      <LoadTextInput
        name='billTo.zipCode'
        label='Zip Code'
        placeholder='12345'
        // options={zipCodeFieldOptions}
      />
      <LoadTextInput name='billTo.contact' label='Contact' />
      <LoadTextInput
        name='billTo.phone'
        label='Phone'
        placeholder='(*************'
        // options={phoneFieldOptions}
        inputValue={InputValue.PHONE_NUMBER}
      />
      <LoadTextInput
        name='billTo.email'
        label='Email'
        placeholder='<EMAIL>'
        options={emailFieldOptions}
      />
    </>
  );
}
