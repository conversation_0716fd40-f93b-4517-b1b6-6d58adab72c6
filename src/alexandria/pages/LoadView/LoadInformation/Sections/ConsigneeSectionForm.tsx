import { useEffect, useState } from 'react';
import { Controller, FieldPath, UseFormReturn } from 'react-hook-form';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import { Checkbox } from 'components/Checkbox';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { InputValue } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import { useFieldAttributes } from 'hooks/useLoadContext';
import useTMSContext from 'hooks/useTMSContext';
import {
  LoadDateTimeInput,
  LoadSelectInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import { NormalizedLoad, TMSLocation } from 'types/Load';
import { getFieldAttribute } from 'types/LoadAttributes';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import { datetimeFieldOptions } from 'utils/formValidators';
import {
  GenericCompanySearchableFields,
  locationSearchHandler,
  mapLocationsToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { ThreeGTwoStopFields } from '../Components/ThreeG/ThreeG';

export function ConsigneeSectionForm({
  suggestedFieldsPrevValues,
  formMethods,
  isLoadingLocations,
  locations,
  handleRefreshLocations,
  setLocations,
}: {
  suggestedFieldsPrevValues: any;
  formMethods: UseFormReturn<NormalizedLoad>;
  isLoadingLocations: boolean;
  locations: Maybe<TMSLocation[]>;
  handleRefreshLocations: () => void;
  setLocations: React.Dispatch<React.SetStateAction<Maybe<TMSLocation[]>>>;
}) {
  const stop = 'consignee';
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = formMethods;

  const { tmsName, tmsID } = useTMSContext();
  const fieldAttrs = useFieldAttributes();
  const apptRequiredAttr = getFieldAttribute(
    fieldAttrs,
    'consignee.apptRequired'
  );
  const [isMcleodTMS, setIsMcleodTMS] = useState<boolean>(
    tmsName === TMS.McleodEnterprise
  );

  useEffect(() => {
    if (isMcleodTMS) {
      setIsMcleodTMS(true);
    }
  }, [tmsName]);

  const watchedLocationID = watch(`${stop}.externalTMSID`);
  const watchedLocationObj = watch(stop);

  useEffect(() => {
    if (watchedLocationID) {
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );
      if (!selectedLoc) {
        return;
      }

      Object.entries(selectedLoc).forEach(([key, value]) => {
        setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true,
        });
      });
    }
  }, [watchedLocationID]);

  const handleLocationSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return locationSearchHandler({
      tmsID,
      locations,
      setLocations,
      field,
      value,
    });
  };

  const currentLoad = formMethods.watch(); // Get current form values

  const isApptRequirementSupported =
    apptRequiredAttr && !apptRequiredAttr?.isNotSupported;
  const isApptRequired = watchedLocationObj?.apptRequired;
  // Show appt required field if it's supported and either required or from GlobalTranzTMS
  const shouldShowApptRequired =
    isApptRequirementSupported && (isApptRequired || TMS.GlobalTranzTMS);

  const isApptConfirmed = watchedLocationObj?.apptConfirmed;

  return (
    <>
      {!isMcleodTMS ? (
        <LoadTextInput name='consignee.name' label='Name' />
      ) : (
        <RHFDebounceSelect
          required={true}
          name={`${stop}.externalTMSID`}
          label='Name'
          control={control}
          errors={errors}
          data={locations}
          isLoading={isLoadingLocations}
          refreshHandler={handleRefreshLocations}
          fetchOptions={handleLocationSearch}
          mapOptions={mapLocationsToAntdOptions}
        />
      )}

      <Accordion type='multiple' className='w-full'>
        <AccordionItem value='consignee-address'>
          <AccordionTrigger className='text-sm font-medium py-2'>
            Address & Contact Info
          </AccordionTrigger>
          <AccordionContent className='space-y-3 pt-2'>
            <LoadTextInput
              name='consignee.addressLine1'
              label='Address Line 1'
            />
            <LoadTextInput
              name='consignee.addressLine2'
              label='Address Line 2'
            />
            <LoadTextInput name='consignee.city' label='City' />
            <LoadTextInput name='consignee.state' label='State' />
            <LoadTextInput
              name='consignee.zipCode'
              label='Zip Code'
              placeholder='12345'
              // options={zipCodeFieldOptions}
            />
            <LoadTextInput name='consignee.country' label='Country' />
            <LoadTextInput name='consignee.contact' label='Contact' />
            <LoadTextInput
              name='consignee.phone'
              label='Phone'
              placeholder='(*************'
              // FIXME handling of read-only phone fields not working as expected
              // options={phoneFieldOptions}
              inputValue={InputValue.PHONE_NUMBER}
            />
            <LoadTextInput
              name='consignee.email'
              label='Email'
              placeholder='<EMAIL>'
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Accordion
        type='multiple'
        className='w-full'
        defaultValue={['consignee-appointment']}
      >
        <AccordionItem value='consignee-appointment'>
          <AccordionTrigger className='text-sm font-medium py-2'>
            Appointment Info
          </AccordionTrigger>
          <AccordionContent className='pt-2'>
            <Flex direction='col' gap='md'>
              {tmsName === TMS.ThreeG ? (
                <ThreeGTwoStopFields
                  isPickup={false}
                  currentLoad={currentLoad}
                />
              ) : (
                <>
                  <LoadTextInput
                    name='consignee.businessHours'
                    label='Business Hours'
                  />
                  <LoadTextInput name='consignee.refNumber' label='Ref #' />
                  <LoadTextInput
                    name='consignee.mustDeliver'
                    label='Must Deliver By'
                  />

                  {/*
                    GlobalTranzTMS doesn't allow changes to appointment type directly,
                    but it's rather inferred through confirmation numbers and
                    whether it's required or not.
                  */}
                  {tmsName === TMS.GlobalTranzTMS && (
                    <LoadTextInput
                      name='consignee.apptType'
                      label='Appointment Status'
                      options={{ disabled: true }}
                    />
                  )}

                  {shouldShowApptRequired && (
                    <Controller
                      name={`${stop}.apptRequired`}
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          label='Appointment Required?'
                          labelClassName='leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                          checked={field.value ?? false}
                          onCheckedChange={(checked) => field.onChange(checked)}
                          disabled={apptRequiredAttr?.isReadOnly}
                        />
                      )}
                    />
                  )}

                  <Controller
                    name={`${stop}.apptConfirmed`}
                    control={control}
                    render={({ field }) => (
                      <Checkbox
                        label='Confirm Appointment'
                        labelClassName='leading-none text-neutral-600 peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                        checked={field.value ?? false}
                        onCheckedChange={(checked) => field.onChange(checked)}
                      />
                    )}
                  />

                  {/* Allow GlobalTranzTMS users to set appointment number, if appt is confirmed */}
                  {tmsName === TMS.GlobalTranzTMS && isApptConfirmed && (
                    <LoadTextInput
                      name='consignee.apptNum'
                      label='Appointment Number'
                      placeholder='APPT1234'
                    />
                  )}

                  <LoadDateTimeInput
                    name='consignee.apptStartTime'
                    label='Appointment Start Time'
                    options={datetimeFieldOptions}
                    prevValue={suggestedFieldsPrevValues?.dropoffApptTime}
                    load={currentLoad}
                  />
                  <LoadDateTimeInput
                    name='consignee.apptEndTime'
                    label='Appointment End Time'
                    options={datetimeFieldOptions}
                    load={currentLoad}
                  />

                  {tmsName === TMS.Turvo && (
                    <LoadSelectInput
                      name='consignee.apptType'
                      label='Appointment Type'
                      options={[
                        { value: 'FCFS', label: 'FCFS' },
                        { value: 'By appointment', label: 'By appointment' },
                      ]}
                    />
                  )}

                  <LoadTextInput
                    name='consignee.apptNote'
                    label='Appointment Note'
                  />
                </>
              )}
            </Flex>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  );
}
