import { useMemo } from 'react';

import dayjs from 'dayjs';

import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { Note } from 'types/Load';

export function NotesSectionForm({ notes }: { notes: Note[] }) {
  const filteredNotes = useMemo(() => {
    if (!notes || !Array.isArray(notes)) {
      return [];
    }

    return notes.filter((note) => !note.isDeleted);
  }, [notes]);

  if (!filteredNotes.length) {
    return (
      <Typography variant='body-sm' className='text-neutral-400'>
        No notes available for this load.
      </Typography>
    );
  }

  return (
    <>
      {filteredNotes.map((note, index) => (
        <Flex
          direction='col'
          gap='sm'
          key={index}
          className='p-3 rounded-[4px] border border-input-border bg-neutral-50'
        >
          <Flex justify='between' align='start'>
            <Typography
              variant='body-sm'
              weight='medium'
              className='text-input-text'
            >
              {note.userName}
            </Typography>

            {note.timeStamp && (
              <Typography
                variant='body-xs'
                className='text-neutral-400 whitespace-nowrap ml-2'
              >
                {dayjs(note.timeStamp).format('MMM D, YYYY [at] h:mm A')}
              </Typography>
            )}
          </Flex>

          <Flex align='center' gap='sm' className='w-full'>
            {note.noteType && (
              <Typography
                variant='body-xs'
                weight='medium'
                className='inline-flex items-center px-2 py-0.5 rounded bg-info-100 text-info-700 border border-info-200'
              >
                {note.noteType}
              </Typography>
            )}

            {note.isResolved && (
              <Typography
                variant='body-xs'
                weight='medium'
                className='inline-flex items-center px-2 py-0.5 rounded bg-success-50 text-success-700 border border-success-200'
              >
                ✓ Resolved
              </Typography>
            )}
          </Flex>

          <Typography className='text-[13px] text-input-text leading-relaxed'>
            {note.text}
          </Typography>
        </Flex>
      ))}
    </>
  );
}
