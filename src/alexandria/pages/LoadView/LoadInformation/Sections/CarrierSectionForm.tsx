import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Controller } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';

import InputPreviousValue from 'components/InputPreviousValue';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { InputValue } from 'components/input/RHFTextInput';
import { Typography } from 'components/typography';
import useTMSContext from 'hooks/useTMSContext';
import { useToast } from 'hooks/useToaster';
import { getCarrierQualification } from 'lib/api/getCarrierQualification';
import { getCarriers } from 'lib/api/getCarriers';
import {
  LoadDateTimeInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import { relaySourceEnums } from 'pages/LoadView/TrackAndTrace/CheckCalls';
import { Load, NormalizedLoad, TMSCarrier } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import { datetimeFieldOptions } from 'utils/formValidators';
import { emailFieldOptions } from 'utils/formValidators';
import { titleCase } from 'utils/formatStrings';
import { GenericCompanySearchableFields } from 'utils/loadInfoAndBuilding';
import { carrierSearchHandler } from 'utils/loadInfoAndBuilding';
import { mapCarriersToAntdOptions } from 'utils/loadInfoAndBuilding';

export function CarrierSectionForm({
  suggestedFieldsPrevValues,
  load,
  formMethods,
  setIsLoadingCarriers,
  isLoadingCarriers,
  carriers,
  setCarriers,
  setIsCarrierQualified,
  hideCarrierTimeInputs,
}: {
  suggestedFieldsPrevValues: any;
  load: Load;
  formMethods: UseFormReturn<NormalizedLoad>;
  isLoadingCarriers: boolean;
  carriers: Maybe<TMSCarrier[]>;
  setIsLoadingCarriers: React.Dispatch<React.SetStateAction<boolean>>;
  setCarriers: React.Dispatch<React.SetStateAction<Maybe<TMSCarrier[]>>>;
  setIsCarrierQualified: React.Dispatch<React.SetStateAction<boolean>>;
  hideCarrierTimeInputs: boolean;
}) {
  const {
    control,
    formState: { errors },
    watch,
    setValue,
    resetField,
    setError,
    clearErrors,
    getValues,
  } = formMethods;
  const { tmsName, tmsID } = useTMSContext();
  const { toast } = useToast();
  const isMcleodTMS = tmsName === TMS.McleodEnterprise;
  const isThreeG = tmsName === TMS.ThreeG;

  const handleRefreshCarriers = async () => {
    setIsLoadingCarriers(true);

    const res = await getCarriers(tmsID, true);
    if (res.isOk()) {
      setCarriers(res.value.carrierList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCarriers(false);
  };

  const handleCarrierSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return carrierSearchHandler({
      tmsID: tmsID,
      carriers,
      setCarriers,
      field,
      value,
    });
  };

  const handleGetCarrierQualification = async (
    carrierName: string,
    carrierExternalTMSID: string
  ) => {
    const res = await getCarrierQualification(
      tmsID,
      load.freightTrackingID,
      carrierExternalTMSID
    );

    if (res.isOk()) {
      if (!res.value.isQualified) {
        setIsCarrierQualified(false);
        toast({
          description: carrierName + ' is not qualified for this load.',
          variant: 'destructive',
        });
        setError('carrier.externalTMSID', {
          type: 'manual',
          message: 'Not qualified for this load.',
        });
      } else {
        // Clear because it's qualified
        setIsCarrierQualified(true);
        clearErrors('carrier.externalTMSID');
      }
    } else {
      // Clear because it's undefined
      setIsCarrierQualified(true);
      clearErrors('carrier.externalTMSID');
      toast({
        description: 'Unable to get carrier qualification from TMS.',
        variant: 'destructive',
      });
    }
  };
  const watchedCarrierID = watch('carrier.externalTMSID');

  useEffect(() => {
    if (watchedCarrierID) {
      const selectedCarrier = carriers?.find(
        (c) => c.externalTMSID === watchedCarrierID
      );
      if (!selectedCarrier) {
        return;
      }

      setValue('carrier.dotNumber', selectedCarrier.dotNumber, {
        shouldDirty: true,
      });
      setValue('carrier.name', selectedCarrier.name, {
        shouldDirty: true,
      });

      if (watchedCarrierID != load.carrier.externalTMSID) {
        // If user switches carriers, reset dependent to blank
        // For Mcleod, driver object is attached to carrier object
        setValue('carrier.dispatcher', '', { shouldDirty: true });
        setValue('carrier.phone', '', { shouldDirty: true });
        setValue('carrier.email', '', { shouldDirty: true });
        setValue('carrier.mcNumber', '', { shouldDirty: true });
        setValue('carrier.firstDriverName', '', { shouldDirty: true });
        setValue('carrier.firstDriverPhone', '', { shouldDirty: true });
        setValue('carrier.secondDriverName', '', { shouldDirty: true });
        setValue('carrier.secondDriverPhone', '', { shouldDirty: true });
      } else {
        // If user switches back to original carrier, reset fields to original values
        resetField('carrier.dispatcher', {
          defaultValue: load.carrier.dispatcher,
        });
        resetField('carrier.phone', { defaultValue: load.carrier.phone });
        resetField('carrier.email', { defaultValue: load.carrier.email });
        resetField('carrier.mcNumber', { defaultValue: load.carrier.mcNumber });
        resetField('carrier.dotNumber', {
          defaultValue: load.carrier.dotNumber,
        });

        resetField('carrier.firstDriverName', {
          defaultValue: load.carrier.firstDriverName,
        });
        resetField('carrier.firstDriverPhone', {
          defaultValue: load.carrier.firstDriverPhone,
        });
        resetField('carrier.secondDriverName', {
          defaultValue: load.carrier.secondDriverName,
        });
        resetField('carrier.secondDriverPhone', {
          defaultValue: load.carrier.secondDriverPhone,
        });
      }

      handleGetCarrierQualification(
        selectedCarrier.name,
        selectedCarrier.externalTMSID
      );
    }
  }, [watchedCarrierID]);

  const currentLoad = formMethods.watch(); // Get current form values

  return (
    <>
      {!isMcleodTMS ? (
        <LoadTextInput name='carrier.name' label='Name' />
      ) : (
        <RHFDebounceSelect
          required={true}
          name={`carrier.externalTMSID`}
          label='Name'
          control={control}
          errors={errors}
          data={carriers}
          isLoading={isLoadingCarriers}
          refreshHandler={handleRefreshCarriers}
          fetchOptions={handleCarrierSearch}
          mapOptions={mapCarriersToAntdOptions}
          searchParamOptions={[
            { key: 'name', label: 'Name' },
            { key: 'addressLine1', label: 'Street' },
            { key: 'dotNumber', label: 'DOT #' },
          ]}
        />
      )}
      <LoadTextInput name='mode' label='Mode' />
      <LoadTextInput name='carrier.equipmentName' label='Equipment Name' />

      <LoadTextInput name='carrier.dotNumber' label='DOT #' />
      <LoadTextInput name='carrier.mcNumber' label='MC #' />
      <LoadTextInput name='carrier.sealNumber' label='Seal #' />
      <LoadTextInput name='carrier.scac' label='SCAC' />
      <LoadTextInput
        name='carrier.phone'
        label='Phone'
        placeholder='(*************'
        // options={phoneFieldOptions}
        inputValue={InputValue.PHONE_NUMBER}
      />
      <LoadTextInput
        name='carrier.email'
        label='Email'
        placeholder='<EMAIL>'
        options={emailFieldOptions}
      />
      <LoadTextInput name='carrier.dispatcher' label='Dispatcher' />

      <LoadTextInput
        name='carrier.rateConfirmationSent'
        label='Rate Confirmation Sent'
      />
      <LoadTextInput
        name='carrier.firstDriverName'
        label='First Driver Name'
        prevValue={suggestedFieldsPrevValues?.firstDriverName}
      />
      <LoadTextInput
        name='carrier.firstDriverPhone'
        label='First Driver Phone'
        // options={phoneFieldOptions}
        inputValue={InputValue.PHONE_NUMBER}
        prevValue={suggestedFieldsPrevValues?.firstDriverPhone}
      />
      <LoadTextInput
        name='carrier.secondDriverName'
        label='Second Driver Name'
        prevValue={suggestedFieldsPrevValues?.secondDriverName}
      />
      <LoadTextInput
        name='carrier.secondDriverPhone'
        label='Second Driver Phone'
        // options={phoneFieldOptions}
        inputValue={InputValue.PHONE_NUMBER}
        prevValue={suggestedFieldsPrevValues?.secondDriverPhone}
      />

      {/* TODO: Fetch TMS form schema from BE and use zod validation for more complex validations */}
      {tmsName === TMS.Relay && (
        <div>
          <Label name='carrier.dispatchSource'>Dispatch Source</Label>
          <Controller
            name='carrier.dispatchSource'
            control={control}
            rules={{
              validate: () => {
                const source = getValues('carrier.dispatchSource');

                const dispTime = getValues('carrier.dispatchedTime');
                const eta = getValues('carrier.expectedPickupTime');
                const location =
                  getValues('carrier.dispatchCity') ??
                  getValues('carrier.dispatchState');

                if ((dispTime || eta || location) && !source) {
                  return 'Source required when dispatching';
                }

                return true;
              },
            }}
            render={({ field }) => {
              return (
                <Select
                  onValueChange={field.onChange}
                  value={(field.value as string) ?? ''}
                >
                  <SelectTrigger className='w-full mt-2'>
                    <SelectValue placeholder='Choose' />
                  </SelectTrigger>
                  <SelectContent>
                    {relaySourceEnums.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {titleCase(option.value)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              );
            }}
          />
          {suggestedFieldsPrevValues?.dispatchSource && (
            <InputPreviousValue
              prevValue={suggestedFieldsPrevValues?.dispatchSource}
            />
          )}
          <ErrorMessage
            errors={errors}
            name={`carrier.dispatchSource`}
            render={({ message }: { message: string }) => (
              <Typography variant='body-xs' className='text-error-500'>
                {message}
              </Typography>
            )}
          />
        </div>
      )}

      <LoadTextInput
        name='carrier.dispatchCity'
        label='Dispatch City'
        prevValue={suggestedFieldsPrevValues?.dispatchCity}
      />
      <LoadTextInput
        name='carrier.dispatchState'
        label='Dispatch State'
        prevValue={suggestedFieldsPrevValues?.dispatchState}
      />
      <LoadTextInput
        name='carrier.truckNumber'
        label='Truck #'
        prevValue={suggestedFieldsPrevValues?.truckNumber}
      />
      <LoadTextInput
        name='carrier.trailerNumber'
        label='Trailer #'
        prevValue={suggestedFieldsPrevValues?.trailerNumber}
      />

      <LoadTextInput name='carrier.notes' label='Notes' />

      {!hideCarrierTimeInputs && !isThreeG && (
        <>
          <LoadDateTimeInput
            name='carrier.confirmationSentTime'
            label='Confirmation Sent Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.confirmationReceivedTime'
            label='Confirmation Received Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.dispatchedTime'
            label='Dispatched Time'
            options={datetimeFieldOptions}
            prevValue={suggestedFieldsPrevValues?.dispatchedTime}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.expectedPickupTime'
            label='Expected Pickup Time'
            rules={{
              validate: () => {
                if (tmsName !== TMS.Relay) {
                  return true;
                }

                const source = getValues('carrier.dispatchSource');
                const dispTime = getValues('carrier.dispatchedTime');
                const eta = getValues('carrier.expectedPickupTime');
                const location =
                  getValues('carrier.dispatchCity') ??
                  getValues('carrier.dispatchState');

                if ((dispTime || source || location) && !eta) {
                  return 'ETA required when dispatching';
                }
                return true;
              },
            }}
            prevValue={suggestedFieldsPrevValues?.expectedPickupTime}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.pickupStart'
            label='Pickup Start Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.pickupEnd'
            label='Pickup End Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.expectedDeliveryTime'
            label='Expected Delivery Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.deliveryStart'
            label='Delivery Start Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.deliveryEnd'
            label='Delivery End Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
        </>
      )}
      <LoadTextInput name='carrier.signedBy' label='Signed By' />
    </>
  );
}
