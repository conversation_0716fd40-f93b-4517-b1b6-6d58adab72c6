import { useEffect, useState } from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { InputValue } from 'components/input/RHFTextInput';
import useTMSContext from 'hooks/useTMSContext';
import { useToast } from 'hooks/useToaster';
import { getCustomers } from 'lib/api/getCustomers';
import { LoadTextInput } from 'pages/LoadView/LoadInformation/Components';
import {
  NormalizedLoad,
  TMSCustomer,
  createInitCompanyCoreInfo,
} from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import { emailFieldOptions } from 'utils/formValidators';
import { GenericCompanySearchableFields } from 'utils/loadInfoAndBuilding';
import { customerSearchHandler } from 'utils/loadInfoAndBuilding';
import { mapCustomerToAntdOptions } from 'utils/loadInfoAndBuilding';

type CustomerSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  customers: Maybe<TMSCustomer[]>;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  isLoadingCustomers: boolean;
  setIsLoadingCustomers: React.Dispatch<React.SetStateAction<boolean>>;
};

export function CustomerSectionForm({
  formMethods,
  customers,
  setCustomers,
  isLoadingCustomers,
  setIsLoadingCustomers,
}: CustomerSectionFormProps) {
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = formMethods;

  const { toast } = useToast();
  const { tmsName, tmsID } = useTMSContext();
  const [isMcleodTMS, setIsMcleodTMS] = useState<boolean>(
    tmsName === TMS.McleodEnterprise
  );

  const watchedCustomerID = watch('customer.externalTMSID');

  useEffect(() => {
    if (isMcleodTMS) {
      setIsMcleodTMS(true);
    }
  }, [tmsName]);

  const handleRefreshCustomers = async () => {
    setIsLoadingCustomers(true);

    const res = await getCustomers(tmsID, true);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return customerSearchHandler({
      tmsID: tmsID,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  useEffect(() => {
    if (watchedCustomerID) {
      const selectedCustomer = customers?.find(
        (c) => c.externalTMSID === watchedCustomerID
      );
      if (!selectedCustomer) {
        return;
      }

      // Autofill customer object
      Object.entries(selectedCustomer).forEach(([key, value]) => {
        if (key in createInitCompanyCoreInfo()) {
          setValue(`customer.${key}` as FieldPath<NormalizedLoad>, value, {
            shouldDirty: true,
          });
        }
      });
    }
  }, [watchedCustomerID]);

  return (
    <>
      {!isMcleodTMS ? (
        <LoadTextInput name='customer.name' label='Name' />
      ) : (
        <RHFDebounceSelect
          required={true}
          name={`customer.externalTMSID`}
          label='Name'
          control={control}
          errors={errors}
          data={customers}
          isLoading={isLoadingCustomers}
          refreshHandler={handleRefreshCustomers}
          fetchOptions={handleCustomerSearch}
          mapOptions={mapCustomerToAntdOptions}
        />
      )}

      <LoadTextInput name='customer.addressLine1' label='Address Line 1' />

      <LoadTextInput name='customer.addressLine2' label='Address Line 2' />
      <LoadTextInput name='customer.city' label='City' />
      <LoadTextInput name='customer.state' label='State' />
      <LoadTextInput
        name='customer.zipCode'
        label='Zip Code'
        placeholder='12345'
        // Zipcode validation disabled to prevent false positives due to Canada postal and read-only values from TMS,
        // options={zipCodeFieldOptions}
      />
      <LoadTextInput name='customer.country' label='Country' />
      <LoadTextInput name='customer.contact' label='Contact' />
      <LoadTextInput
        name='customer.phone'
        label='Phone'
        placeholder='(*************'
        // options={phoneFieldOptions}
        inputValue={InputValue.PHONE_NUMBER}
      />
      <LoadTextInput
        name='customer.email'
        label='Email'
        placeholder='<EMAIL>'
        options={emailFieldOptions}
      />
      <LoadTextInput name='customer.refNumber' label='Ref # / BOL' />

      <LoadTextInput
        name={'poNums'}
        label='PO #'
        placeholder='Comma-separated list (e.g. 123,456,789)'
      />
    </>
  );
}
