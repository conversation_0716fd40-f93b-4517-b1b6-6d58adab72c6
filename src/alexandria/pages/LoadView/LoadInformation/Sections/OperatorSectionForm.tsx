import { useEffect, useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { Info } from 'lucide-react';

import { ComboboxDropdownMenu } from 'components/Combobox';
import { Label } from 'components/Label';
import { useExtendedFormContext } from 'contexts/extendedFormContext';
import useTMSContext from 'hooks/useTMSContext';
import { NormalizedLoad } from 'types/Load';
import { TMS } from 'types/enums/Integrations';
import joinClasses from 'utils/joinClasses';

type OperatorSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  operators: Array<string>;
  canEdit: boolean;
};

export function OperatorSectionForm({
  formMethods,
  operators,
  canEdit,
}: OperatorSectionFormProps) {
  const inputName = 'operator';
  const { tmsName, tenant } = useTMSContext();
  const [highlightDirtyField, setHighlightDirtyField] = useState(false);

  const {
    control,
    watch,
    getFieldState,
    formState: { dirtyFields },
  } = formMethods;
  const { highlightDirtyFields } = useExtendedFormContext();
  const watchedOperator = watch('operator');

  useEffect(() => {
    const highlightDirtyField = highlightDirtyFields ?? false;
    const fieldState = getFieldState(inputName);
    const isDirty = fieldState.isDirty || (dirtyFields[inputName] ?? false);

    setHighlightDirtyField(isDirty && highlightDirtyField);
  }, [watchedOperator, highlightDirtyFields]);

  return (
    <div className={joinClasses('flex flex-col gap-2.5')}>
      <Label name='operator'>Assigned Operator:</Label>
      <Controller
        name='operator'
        control={control}
        render={({ field }) => (
          <ComboboxDropdownMenu
            canEdit={canEdit && (tenant ? !tenant.includes('fcfm') : true)}
            selectedOperator={field.value}
            operators={operators}
            setSelectedOperator={field.onChange}
            highlightDirtyField={highlightDirtyField}
          />
        )}
      />

      {!canEdit && tmsName === TMS.Aljex && (
        <span className='flex flex-row bg-info-200 rounded-lg space-x-1 py-1 mt-0'>
          <div>
            <Info className='h-4 w-4 pl-1' color='#969696' strokeWidth={3} />
          </div>
          <span className='text-xs text-neutral-500 font-medium'>
            {`In order to assign an operator, the load's status must be OPEN and pickup and dropoff appointment times must first be set.`}
          </span>
        </span>
      )}
    </div>
  );
}
