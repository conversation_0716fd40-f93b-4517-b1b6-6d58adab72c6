import { useEffect, useState } from 'react';
import { Controller, FieldPath, useFormContext } from 'react-hook-form';
import { RegisterOptions } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import dayjs from 'dayjs';
import { XCircleIcon } from 'lucide-react';

import { DatePicker } from 'components/DatePicker';
import InputPreviousValue from 'components/InputPreviousValue';
import { Label } from 'components/Label';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { useExtendedFormContext } from 'contexts/extendedFormContext';
import { useFieldAttributes } from 'hooks/useLoadContext';
import { NormalizedLoad } from 'types/Load';
import { getFieldAttribute } from 'types/LoadAttributes';
import { initFieldAttributes } from 'types/LoadAttributes';
import { FieldAttributes } from 'types/LoadAttributes';
import { Undef } from 'types/UtilityTypes';

type LoadDateInputProps = Omit<
  React.ComponentPropsWithoutRef<typeof RHFTextInput>,
  'prevValue'
> & {
  name: FieldPath<NormalizedLoad>;
  showAIHint?: boolean;
  prevValue?: Undef<Date>;
  load?: NormalizedLoad;
  rules?: Omit<
    RegisterOptions<NormalizedLoad, FieldPath<NormalizedLoad>>,
    'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'
  >;
};

export const LoadDateInput = ({
  name,
  label,
  prevValue,
  required,
  rules,
}: LoadDateInputProps) => {
  const [highlightDirtyField, setHighlightDirtyField] = useState(false);

  const extendedFormContext = useExtendedFormContext();
  const formContext = useFormContext<NormalizedLoad>();
  const {
    control: formControl,
    getFieldState,
    formState: { errors },
  } = formContext;

  // When selecting a date, format it as YYYY-MM-DD string
  const handleDateSelect = (date: Date | null, field: any) => {
    if (typeof field.onChange === 'function') {
      const formattedDate = date ? dayjs(date).format('YYYY-MM-DD') : null;
      field.onChange(formattedDate);
    }
  };

  useEffect(() => {
    if (formContext && extendedFormContext && name) {
      const highlightDirtyFields =
        extendedFormContext.highlightDirtyFields ?? false;
      const fieldState = getFieldState(name);
      setHighlightDirtyField(fieldState.isDirty && highlightDirtyFields);
    } else {
      setHighlightDirtyField(false);
    }
  }, [extendedFormContext, formContext, name, getFieldState]);

  const allFieldAttrs = useFieldAttributes();

  const getFieldAttrForName = (fieldName: string): FieldAttributes => {
    let fieldAttr = getFieldAttribute(allFieldAttrs, fieldName);

    // Check if field is a stops field with index
    if (fieldName.startsWith('stops.')) {
      const parts = fieldName.split('.');

      if (parts.length >= 3 && !isNaN(Number(parts[1]))) {
        // Remove the index part (e.g., stops.1.pickup -> stops.pickup)
        const fieldNameWithoutIndex = `${parts[0]}.${parts.slice(2).join('.')}`;
        fieldAttr = getFieldAttribute(allFieldAttrs, fieldNameWithoutIndex);
      }
    }

    return fieldAttr ?? initFieldAttributes;
  };

  const thisFieldAttr: FieldAttributes = getFieldAttrForName(name);

  const prevValueStr = prevValue ? dayjs(prevValue).format('M/DD/YYYY') : '';

  return thisFieldAttr.isNotSupported ? null : (
    <div className='w-full'>
      <Label name={name} required={required}>
        {label}
      </Label>

      <Controller
        name={name}
        control={formControl}
        rules={required ? { ...rules, required: 'Required' } : rules}
        render={({ field }) => {
          // Convert various date formats to Date for DatePicker
          let dateValue: Date | null = null;

          if (field.value) {
            if (field.value instanceof Date) {
              const dateOnly = field.value.toISOString().split('T')[0];
              dateValue = dayjs(dateOnly).toDate();
            } else if (typeof field.value === 'string') {
              // Handle both ISO datetime strings and YYYY-MM-DD strings
              // For ISO strings, use UTC to avoid timezone conversion
              if (field.value.includes('T')) {
                // Extract just the date part and parse as local date
                const dateOnly = field.value.split('T')[0];
                dateValue = dayjs(dateOnly).toDate();
              } else {
                // YYYY-MM-DD string
                dateValue = dayjs(field.value).toDate();
              }
            }
          }

          return (
            <TooltipProvider>
              <Flex gap='sm' className='mt-1' data-name={name}>
                <DatePicker
                  logProperties={{ dateLabel: label }}
                  field={{
                    ...field,
                    value: dateValue,
                    onChange: (date: Date | null) =>
                      handleDateSelect(date, field),
                  }}
                  thisFieldAttr={thisFieldAttr}
                  highlightDirtyField={highlightDirtyField}
                />
                {field.value && !thisFieldAttr.isReadOnly && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        title='Clear date'
                        onClick={() => field.onChange(null)}
                        className='h-9 flex items-center justify-center'
                      >
                        <XCircleIcon className='w-4 h-4' />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>Clear date</TooltipContent>
                  </Tooltip>
                )}
              </Flex>
            </TooltipProvider>
          );
        }}
      />

      {prevValue ? <InputPreviousValue prevValue={prevValueStr} /> : null}

      <ErrorMessage
        errors={errors}
        name={name}
        render={({ message }: { message: string }) => (
          <Typography variant='body-xs' className='text-error-500'>
            {message}
          </Typography>
        )}
      />
    </div>
  );
};
