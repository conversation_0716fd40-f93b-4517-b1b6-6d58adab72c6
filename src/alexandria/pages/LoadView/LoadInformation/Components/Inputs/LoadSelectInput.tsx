import { FieldPath, useFormContext } from 'react-hook-form';
import { Controller } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { XCircleIcon } from 'lucide-react';

import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { useFieldAttributes } from 'hooks/useLoadContext';
import { NormalizedLoad } from 'types/Load';
import {
  FieldAttributes,
  getFieldAttribute,
  initFieldAttributes,
} from 'types/LoadAttributes';

type LoadSelectInputProps = {
  name: FieldPath<NormalizedLoad>;
  label: string;
  options: { value: string; label: string }[];
  required?: boolean;
  placeholder?: string;
};

export const LoadSelectInput = ({
  name,
  label,
  options,
  required = false,
  placeholder = 'Choose',
}: LoadSelectInputProps) => {
  const formContext = useFormContext<NormalizedLoad>();
  const {
    control,
    formState: { errors },
  } = formContext;

  const allFieldAttrs = useFieldAttributes();

  // Handle stops fields with indices (e.g., stops.1.pickup -> stops.pickup)
  const getFieldAttrForName = (fieldName: string): FieldAttributes => {
    // First try exact match
    let fieldAttr = getFieldAttribute(allFieldAttrs, fieldName);

    // If no exact match and it's a stops field with index, try without index
    if (
      !fieldAttr &&
      fieldName.startsWith('stops.') &&
      fieldName.includes('.')
    ) {
      const parts = fieldName.split('.');
      if (parts.length >= 3 && !isNaN(Number(parts[1]))) {
        // Remove the index part (e.g., stops.1.pickup -> stops.pickup)
        const fieldNameWithoutIndex = `${parts[0]}.${parts.slice(2).join('.')}`;
        fieldAttr = getFieldAttribute(allFieldAttrs, fieldNameWithoutIndex);
      }
    }

    return fieldAttr ?? initFieldAttributes;
  };

  const thisFieldAttr: FieldAttributes = getFieldAttrForName(name);

  return thisFieldAttr.isNotSupported ? null : (
    <div>
      <Label name={name} required={required}>
        {label}
      </Label>

      <Controller
        name={name}
        control={control}
        rules={required ? { required: 'Required' } : undefined}
        render={({ field }) => (
          <TooltipProvider>
            <Flex gap='sm' className='items-center'>
              <Select
                onValueChange={field.onChange}
                value={(field.value as string) || ''}
                disabled={thisFieldAttr.isReadOnly}
              >
                <SelectTrigger className='w-full mt-1'>
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
                <SelectContent>
                  {options.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {field.value && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      title='Clear selection'
                      onClick={() => field.onChange('')}
                      className='h-9 flex items-center justify-center'
                    >
                      <XCircleIcon className='w-4 h-4' />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>Clear selection</TooltipContent>
                </Tooltip>
              )}
            </Flex>
          </TooltipProvider>
        )}
      />

      <ErrorMessage
        errors={errors}
        name={name}
        render={({ message }: { message: string }) => (
          <Typography variant='body-xs' className='text-error-500'>
            {message}
          </Typography>
        )}
      />
    </div>
  );
};
