import { useEffect, useRef, useState } from 'react';
import { Controller, FieldPath, useFormContext } from 'react-hook-form';
import { RegisterOptions } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import dayjs from 'dayjs';
import { XCircleIcon } from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

import { DatePicker } from 'components/DatePicker';
import InputPreviousValue from 'components/InputPreviousValue';
import { Label } from 'components/Label';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Input } from 'components/input';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { useExtendedFormContext } from 'contexts/extendedFormContext';
import { useFieldAttributes } from 'hooks/useLoadContext';
import useTMSContext from 'hooks/useTMSContext';
import { NormalizedLoad } from 'types/Load';
import { getFieldAttribute } from 'types/LoadAttributes';
import { initFieldAttributes } from 'types/LoadAttributes';
import { FieldAttributes } from 'types/LoadAttributes';
import { MaybeUndef, Undef } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import captureException from 'utils/captureException';
import { processTimeAndUpdateDate } from 'utils/processTimeAndUpdateDate';
import { cn } from 'utils/shadcn';

type LoadDateTimeInputProps = Omit<
  React.ComponentPropsWithoutRef<typeof RHFTextInput>,
  'prevValue'
> & {
  name: FieldPath<NormalizedLoad>;
  showAIHint?: boolean;
  prevValue?: Undef<Date>;
  load?: NormalizedLoad;
  rules?: Omit<
    RegisterOptions<NormalizedLoad, FieldPath<NormalizedLoad>>,
    'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'
  >;
  timeCanBeEmpty?: boolean;
  useUTCWhenNoTimezone?: boolean;
};

export const LoadDateTimeInput = ({
  name,
  label,
  prevValue,
  required,
  load,
  rules,
  timeCanBeEmpty = false,
  useUTCWhenNoTimezone = false,
}: LoadDateTimeInputProps) => {
  const { tmsName } = useTMSContext();

  // Get timezone from the appropriate location based on field name
  const getTimezoneForField = (): MaybeUndef<string> => {
    if (!load) return undefined;

    // For Aljex and Relay, don't apply location timezones - display dates as is from BE (UTC)
    if (tmsName === TMS.Aljex || tmsName === TMS.Relay) {
      return undefined;
    }

    // Extract the section name from the field path (e.g., 'pickup' from 'pickup.apptStartTime')
    const section = name.split('.')[0];

    if (section === 'pickup' && load.pickup?.timezone) {
      return load.pickup.timezone;
    } else if (section === 'consignee' && load.consignee?.timezone) {
      return load.consignee.timezone;
    } else if (section === 'carrier') {
      // For carrier fields, use pickup timezone as default
      return load.pickup?.timezone;
    }

    return undefined;
  };

  // Get the timezone string (e.g. "America/Chicago")
  const timezone = getTimezoneForField();

  // Get short timezone display (e.g. "CST")
  const getShortTimezone = (timezone?: MaybeUndef<string>): string => {
    if (tmsName === TMS.Aljex || !timezone) {
      return '';
    }

    try {
      // Get current date to account for daylight saving time
      const date = new Date();
      const shortTZ =
        new Intl.DateTimeFormat('en-US', {
          timeZone: timezone,
          timeZoneName: 'short',
        })
          .formatToParts(date)
          .find((part) => part.type === 'timeZoneName')?.value || '';
      return shortTZ;
    } catch (error) {
      captureException(error);
      return '';
    }
  };

  const shortTZ = getShortTimezone(timezone);
  const normalizedLabel = label + (shortTZ ? ` (${shortTZ})` : '');

  const [highlightDirtyField, setHighlightDirtyField] = useState(false);
  const [timeInput, setTimeInput] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const timeInputRef = useRef<HTMLInputElement>(null);

  const extendedFormContext = useExtendedFormContext();
  const formContext = useFormContext<NormalizedLoad>();
  const {
    control: formControl,
    getFieldState,
    clearErrors,
    setError,
    formState: { errors },
  } = formContext;
  const posthog = usePostHog();

  // Format a date using the appropriate timezone
  const formatDateWithTimezone = (dateValue: any): string => {
    if (!dateValue) return '';

    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    if (isNaN(date.getTime())) return '';

    if (timezone) {
      return dayjs(date).tz(timezone).format('HH:mm');
    }

    let formattedDate = dayjs(date).format('HH:mm');
    if (useUTCWhenNoTimezone) {
      formattedDate = dayjs(date).utc().format('HH:mm');
    }

    return formattedDate;
  };

  // When selecting a date, ensure it's in the correct timezone
  const handleDateSelect = (date: Date | null, field: any) => {
    if (typeof field.onChange === 'function') {
      field.onChange(date);

      // Focus on time input after date selection
      if (date && timeInputRef.current) {
        setTimeout(() => timeInputRef.current?.focus());
      }
    }
  };

  const handleTimeInputBlur = (field: any) => {
    // Only process if the user has entered something
    if (timeInput.trim()) {
      // Use the section's timezone when processing the time
      const newDate = processTimeAndUpdateDate(
        timeInput,
        field.value,
        timezone ? String(timezone) : undefined
      );

      if (newDate) {
        clearErrors(name);
        if (typeof field.onChange === 'function') {
          field.onChange(newDate);
        }
      } else {
        setError(name, {
          type: 'manual',
          message: 'Invalid time format. Please use HH:mm',
        });
      }
    } else if (timeCanBeEmpty) {
      if (!field.value) return;

      field.onChange(dayjs(field.value).format('YYYY-MM-DD'));
    } else {
      // If they didn't enter anything, don't show an error
      clearErrors(name);
    }

    setTimeInput('');
  };

  useEffect(() => {
    if (formContext && extendedFormContext && name) {
      const highlightDirtyFields =
        extendedFormContext.highlightDirtyFields ?? false;
      const fieldState = getFieldState(name);
      setHighlightDirtyField(fieldState.isDirty && highlightDirtyFields);
    } else {
      setHighlightDirtyField(false);
    }
  }, [extendedFormContext, formContext, name, getFieldState]);

  const allFieldAttrs = useFieldAttributes();

  const getFieldAttrForName = (fieldName: string): FieldAttributes => {
    let fieldAttr = getFieldAttribute(allFieldAttrs, fieldName);

    // Check if field is a stops field with index
    if (fieldName.startsWith('stops.')) {
      const parts = fieldName.split('.');

      if (parts.length >= 3 && !isNaN(Number(parts[1]))) {
        // Remove the index part (e.g., stops.1.pickup -> stops.pickup)
        const fieldNameWithoutIndex = `${parts[0]}.${parts.slice(2).join('.')}`;
        fieldAttr = getFieldAttribute(allFieldAttrs, fieldNameWithoutIndex);
      }
    }

    return fieldAttr ?? initFieldAttributes;
  };

  const thisFieldAttr: FieldAttributes = getFieldAttrForName(name);

  const prevValueStr = prevValue
    ? dayjs(prevValue).format('M/DD/YYYY HH:mm')
    : '';

  // Disable dates before today
  const disabledDates = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  return thisFieldAttr.isNotSupported ? null : (
    <div className='w-full'>
      <Label name={name} required={required}>
        {normalizedLabel}
      </Label>

      <Controller
        name={name}
        control={formControl}
        rules={required ? { ...rules, required: 'Required' } : rules}
        render={({ field }) => {
          // Format display time with the appropriate timezone
          const displayTime = field.value
            ? formatDateWithTimezone(field.value)
            : '';

          return (
            <TooltipProvider>
              <Flex gap='sm' className='mt-1' data-name={name}>
                <Flex gap='lg' className='flex-1'>
                  <DatePicker
                    logProperties={{ dateLabel: label }}
                    field={{
                      ...field,
                      onChange: (date: Date | null) =>
                        handleDateSelect(date, field),
                    }}
                    thisFieldAttr={thisFieldAttr}
                    highlightDirtyField={highlightDirtyField}
                    disabled={disabledDates}
                    timezone={timezone}
                  />

                  <Input
                    ref={timeInputRef}
                    type='text'
                    className={cn(
                      'pr-1! pl-3 w-1/2 h-8 rounded-[4px] border border-neutral-input-border bg-neutral-input-bg text-neutral-input-text',
                      highlightDirtyField && 'bg-warning-50'
                    )}
                    placeholder='HH:mm'
                    value={isEditing ? timeInput : timeInput || displayTime}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setTimeInput(e.target.value);
                      setIsEditing(true);
                      posthog.capture('load_building_time_input_change', {
                        timeInput: e.target.value,
                        field: name,
                      });
                    }}
                    onFocus={() => setIsEditing(true)}
                    onBlur={() => {
                      handleTimeInputBlur(field);
                      setIsEditing(false);
                    }}
                    readOnly={thisFieldAttr.isReadOnly} // Hides placeholders
                    disabled={thisFieldAttr.isReadOnly} // Allows selecting the value
                  />
                </Flex>
                {field.value && !thisFieldAttr.isReadOnly && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        title='Clear date'
                        onClick={() => field.onChange(null)}
                        className='h-9 flex items-center justify-center'
                      >
                        <XCircleIcon className='w-4 h-4' />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>Clear date</TooltipContent>
                  </Tooltip>
                )}
              </Flex>
            </TooltipProvider>
          );
        }}
      />

      {prevValue ? <InputPreviousValue prevValue={prevValueStr} /> : null}

      <ErrorMessage
        errors={errors}
        name={name}
        render={({ message }: { message: string }) => (
          <Typography variant='body-xs' className='text-error-500'>
            {message}
          </Typography>
        )}
      />
    </div>
  );
};
