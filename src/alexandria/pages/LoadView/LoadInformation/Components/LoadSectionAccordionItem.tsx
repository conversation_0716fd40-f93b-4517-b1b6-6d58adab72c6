import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';

type LoadSectionAccordionItemProps = {
  icon: React.ReactNode;
  children: React.ReactNode;
  label: string;
  name: string;
  activeTabs: string[];
};

export function LoadSectionAccordionItem({
  icon,
  children,
  name,
  label,
  activeTabs,
}: LoadSectionAccordionItemProps) {
  return (
    <AccordionItem value={name}>
      <AccordionTrigger icon={icon}>{label}</AccordionTrigger>
      <AccordionContent
        forceMount={true}
        hidden={!activeTabs.includes(name)}
        className='grid grid-cols-1 gap-3 w-full m-0'
      >
        {children}
      </AccordionContent>
    </AccordionItem>
  );
}
