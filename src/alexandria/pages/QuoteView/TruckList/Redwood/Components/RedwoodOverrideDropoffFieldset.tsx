import { Button } from 'components/Button';
import { Grid } from 'components/layout';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';

import { TruckTextInput } from '../../TruckListFormFields';

type RedwoodOverrideDropoffFieldsetProps = {
  handleOverrideDropoff: () => void;
};

export const RedwoodOverrideDropoffFieldset = ({
  handleOverrideDropoff,
}: RedwoodOverrideDropoffFieldsetProps) => (
  <>
    <Grid cols='3' gap='lg' className='mt-2 mx-0 w-full'>
      <div className='col-span-2'>
        <TruckTextInput name={`override.city`} label='City' />
      </div>

      <div className='col-span-1'>
        <TruckTextInput
          name={`override.state`}
          label='State'
          aiIconOnly={true}
        />
      </div>
    </Grid>

    <Button
      buttonNamePosthog={ButtonNamePosthog.TruckListToggleOverrideDropoff}
      className='w-full h-8 text-[14px] mt-2'
      onClick={handleOverrideDropoff}
      type='button'
    >
      Apply to all Trucks
    </Button>
  </>
);
