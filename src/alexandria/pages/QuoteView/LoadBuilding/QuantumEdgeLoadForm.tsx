import { useEffect, useMemo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Form<PERSON>rovider,
  SubmitHandler,
  useForm,
} from 'react-hook-form';

import { Accordion } from '@radix-ui/react-accordion';
import {
  BoxIcon,
  Building2,
  DollarSignIcon,
  LetterTextIcon,
  MapPinnedIcon,
  WarehouseIcon,
  Weight,
} from 'lucide-react';

import { Button } from 'components/Button';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { LoadSectionAccordionItem } from 'pages/LoadView/LoadInformation/Components';
import {
  Load,
  NormalizedLoad,
  createInitLoad,
  createInitRateData,
  createInitSpecs,
  normalizeLoad,
} from 'types/Load';
import ButtonText from 'types/enums/ButtonText';
import { But<PERSON><PERSON>amePosthog } from 'types/enums/CustomPosthogEvents';

import { TMS } from '../../../types/enums/Integrations';
import AdditionalReferencesForm from './QuantumEdgeSectionForms/AdditionalReferences';
import CustomerSectionForm from './QuantumEdgeSectionForms/Customer';
import { QuantumEdgeRatesForm } from './QuantumEdgeSectionForms/Rates';
import SpecificationsSectionForm from './QuantumEdgeSectionForms/Specifications';
import StopForm from './QuantumEdgeSectionForms/Stop';
import { QuantumEdgeStopsTimeline } from './QuantumEdgeSectionForms/components/QuantumEdgeStopsTimeline';

enum AvailableTabs {
  customer = 'customer',
  rates = 'rates',
  additionalReferences = 'additionalReferences',
  specifications = 'specifications',
  pickup = 'pickup',
  consignee = 'consignee',
  route = 'route',
}

type LoadBuildingTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & { name: FieldPath<NormalizedLoad> };

export const LoadBuildingTextInput = (props: LoadBuildingTextInputProps) => (
  <RHFTextInput {...props} />
);

const initQuantumEdgeLoadBuildingForm = (tmsName: string): NormalizedLoad =>
  normalizeLoad(tmsName, {
    ...createInitLoad(),
    mode: 'OTR',
    specifications: {
      ...createInitSpecs(),
      transportType: "53VR - Van or Reefer - 53'",
    },
    rateData: {
      ...createInitRateData(),
      customerRateType: 'Flat Rate',
      carrierRateType: 'Flat Rate',
    },
  });

interface QuantumEdgeLoadFormProps {
  isCreateMode: boolean;
  load?: Load;
  onSubmit?: (data: NormalizedLoad) => void | Promise<void>;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
}

export default function QuantumEdgeLoadForm({
  isCreateMode,
  load,
  onSubmit,
  onSuccess: _onSuccess,
  onError: _onError,
}: QuantumEdgeLoadFormProps) {
  const tmsName = TMS.QuantumEdge;
  const [isLoading, setIsLoading] = useState(false);
  const [activeTabs, setActiveTabs] = useState<string[]>(
    Object.values(AvailableTabs)
  );

  const memoizedDefaultValues: NormalizedLoad = useMemo(() => {
    if (!isCreateMode && load) {
      return normalizeLoad(tmsName, load);
    }

    if (isCreateMode) {
      return initQuantumEdgeLoadBuildingForm(tmsName);
    }

    return initQuantumEdgeLoadBuildingForm(tmsName);
  }, [tmsName, isCreateMode, load]);

  const formMethods = useForm<NormalizedLoad>({
    defaultValues: memoizedDefaultValues,
  });

  const { handleSubmit } = formMethods;

  useEffect(() => {
    formMethods.reset(memoizedDefaultValues, {
      keepDefaultValues: false,
      keepTouched: false,
      keepDirtyValues: false,
      keepDirty: false,
    });
  }, [memoizedDefaultValues]);

  const handleFormSubmit: SubmitHandler<NormalizedLoad> = async (data) => {
    if (onSubmit) {
      setIsLoading(true);
      try {
        await onSubmit(data);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const buttonText = isCreateMode ? ButtonText.BuildLoad : ButtonText.UpdateTMS;
  const buttonNamePosthog = isCreateMode
    ? ButtonNamePosthog.BuildLoad
    : ButtonNamePosthog.UpdateTMS;

  return (
    <Flex direction='col' className='mb-5 w-full'>
      <ExtendedFormProvider
        aiDefaultValues={false}
        aiIconOnly={true}
        highlightDirtyFields={!isCreateMode}
      >
        <FormProvider {...formMethods}>
          <form onSubmit={handleSubmit(handleFormSubmit)} className='w-full'>
            <Accordion
              type='multiple'
              value={activeTabs}
              onValueChange={setActiveTabs}
            >
              <LoadSectionAccordionItem
                label='Customer'
                icon={<Building2 className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.customer}
                activeTabs={activeTabs}
              >
                <CustomerSectionForm
                  formMethods={formMethods}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='References'
                icon={<LetterTextIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.additionalReferences}
                activeTabs={activeTabs}
              >
                <AdditionalReferencesForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Specifications'
                icon={<Weight className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.specifications}
                activeTabs={activeTabs}
              >
                <SpecificationsSectionForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Rates'
                icon={<DollarSignIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.rates}
                activeTabs={activeTabs}
              >
                <QuantumEdgeRatesForm
                  formMethods={formMethods}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              {!isCreateMode &&
              load?.moreThanTwoStops &&
              load?.stops?.length ? (
                <>
                  <LoadSectionAccordionItem
                    label='Route'
                    icon={<MapPinnedIcon className='h-6 w-6' strokeWidth={1} />}
                    name={AvailableTabs.route}
                    activeTabs={activeTabs}
                  >
                    <QuantumEdgeStopsTimeline formMethods={formMethods} />
                  </LoadSectionAccordionItem>
                </>
              ) : (
                <>
                  <LoadSectionAccordionItem
                    label='Pickup'
                    icon={<BoxIcon className='h-6 w-6' strokeWidth={1} />}
                    name={AvailableTabs.pickup}
                    activeTabs={activeTabs}
                  >
                    <StopForm
                      stop='pickup'
                      formMethods={formMethods}
                      isCreateMode={isCreateMode}
                    />
                  </LoadSectionAccordionItem>

                  <LoadSectionAccordionItem
                    label='Consignee'
                    icon={<WarehouseIcon className='h-6 w-6' strokeWidth={1} />}
                    name={AvailableTabs.consignee}
                    activeTabs={activeTabs}
                  >
                    <StopForm
                      stop='consignee'
                      formMethods={formMethods}
                      isCreateMode={isCreateMode}
                    />
                  </LoadSectionAccordionItem>
                </>
              )}
            </Accordion>

            <Flex direction='col' className='w-full mt-4'>
              <Button
                buttonNamePosthog={buttonNamePosthog}
                type='submit'
                className='w-full'
                disabled={isLoading}
                logProperties={
                  !isCreateMode && load
                    ? {
                        loadID: load.ID,
                        freightTrackingID: load.freightTrackingID,
                        serviceID: load.serviceID,
                      }
                    : undefined
                }
              >
                {isLoading ? <ButtonLoader /> : buttonText}
              </Button>

              {isCreateMode && formMethods.formState.isDirty && (
                <Flex direction='row' justify='center' align='center'>
                  <Button
                    buttonNamePosthog={ButtonNamePosthog.ClearForm}
                    type='button'
                    className='w-50% mt-4 h-8 text-sm text-neutral-700'
                    disabled={isLoading}
                    variant='outline'
                    onClick={() => formMethods.reset()}
                  >
                    {ButtonText.ClearForm}
                  </Button>
                </Flex>
              )}
            </Flex>
          </form>
        </FormProvider>
      </ExtendedFormProvider>
    </Flex>
  );
}
