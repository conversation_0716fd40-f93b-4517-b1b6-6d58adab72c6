import { useEffect } from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFTextInput } from 'components/input/RHFTextInput';
import { ReferenceNumberInput } from 'components/input/ReferenceNumberInput';
import { NormalizedLoad, TMSCustomer } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';

type LoadBuildingTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & { name: FieldPath<NormalizedLoad> };

export const LoadBuildingTextInput = (props: LoadBuildingTextInputProps) => (
  <RHFTextInput {...props} />
);

export type CustomerSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  customers: Maybe<TMSCustomer[]>;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  isLoadingCustomers: boolean;
  setIsLoadingCustomers: React.Dispatch<React.SetStateAction<boolean>>;
  originalSuggestionData?: NormalizedLoad;
  isCreateMode?: boolean;
};

export function CustomerSectionForm({
  formMethods,
  customers,
  setCustomers,
  originalSuggestionData,
  isCreateMode = false,
}: CustomerSectionFormProps) {
  const {
    control,
    watch,
    setValue,
    getValues,
  } = formMethods;
  const watchedCustomer = watch('customer');

  useEffect(() => {
    // TODO: Support update the customer
    // Note: Add comment why we are commenting this for different email

    // if (watchedCustomer?.externalTMSID) {
    //   console.log('watchedCustomer', watchedCustomer);
    //   const selectedCustomer = customers?.find(
    //     (c) => c.externalTMSID === watchedCustomer.externalTMSID
    //   );
    //   if (!selectedCustomer) return;
    //   Object.entries(selectedCustomer).forEach(([key, value]) => {
    //     setValue(`customer.${key}` as FieldPath<NormalizedLoad>, value, {
    //       shouldDirty: true,
    //     });
    //   });
    // }
  }, [watchedCustomer?.externalTMSID, customers, setValue]);

  // In edit mode, if externalTMSID is empty but we have customer data, create a virtual customer option
  useEffect(() => {
    if (
      !isCreateMode &&
      watchedCustomer &&
      !watchedCustomer.externalTMSID &&
      watchedCustomer.name
    ) {
      const virtualCustomer: TMSCustomer = {
        externalTMSID: '', // Empty but valid
        name: watchedCustomer.name,
        addressLine1: watchedCustomer.addressLine1 || '',
        addressLine2: watchedCustomer.addressLine2 || '',
        city: watchedCustomer.city || '',
        state: watchedCustomer.state || '',
        zipCode: watchedCustomer.zipCode || '',
        country: watchedCustomer.country || '',
        contact: watchedCustomer.contact || '',
        phone: watchedCustomer.phone || '',
        email: watchedCustomer.email || '',
        refNumber: watchedCustomer.refNumber || '',
      };

      // Add virtual customer to the list if it doesn't exist
      if (
        customers &&
        !customers.find(
          (c) => c.name === virtualCustomer.name && c.externalTMSID === ''
        )
      ) {
        setCustomers([virtualCustomer, ...customers]);
      }
    }
  }, [isCreateMode, watchedCustomer, customers, setCustomers]);

  return (
    <>
      <LoadBuildingTextInput
        name='customer.name'
        label='Name'
        readOnly={!isCreateMode}
        required={isCreateMode}
      />

      <ReferenceNumberInput
        name='customer.refNumber'
        control={control}
        label='Ref #/ BOL'
        placeholder='LD12345'
        load={originalSuggestionData || getValues()}
      />

      <RHFTextInput name={'poNums'} label='PO #' placeholder='123456' />
    </>
  );
}
