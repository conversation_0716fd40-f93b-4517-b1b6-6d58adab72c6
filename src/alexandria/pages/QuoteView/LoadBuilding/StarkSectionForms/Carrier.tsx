import { UseFormReturn } from 'react-hook-form';
import { Controller } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';

import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { InputValue } from 'components/input/RHFTextInput';
import { Typography } from 'components/typography';
import {
  LoadDateTimeInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import { relaySourceEnums } from 'pages/LoadView/TrackAndTrace/CheckCalls';
import { NormalizedLoad } from 'types/Load';
import { datetimeFieldOptions } from 'utils/formValidators';
import { emailFieldOptions } from 'utils/formValidators';
import { titleCase } from 'utils/formatStrings';
import { LoadBuildingTextInput } from './Customer';

export function CarrierForm({
  formMethods,
  hideCarrierTimeInputs = false,
  isCreateMode = false
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
  hideCarrierTimeInputs?: boolean;
  isCreateMode?: boolean;
}) {
  const {
    control,
    formState: { errors },
    watch,
    getValues,
  } = formMethods;

  const currentLoad = watch(); // Get current form values

  return (
    <>
      <LoadBuildingTextInput
        name='carrier.externalTMSID'
        label='Name'
        readOnly={!isCreateMode}
        required={isCreateMode}
      />
      <LoadTextInput name='mode' label='Mode' />
      <LoadTextInput name='carrier.equipmentName' label='Equipment Name' />

      <LoadTextInput name='carrier.dotNumber' label='DOT #' />
      <LoadTextInput name='carrier.mcNumber' label='MC #' />
      <LoadTextInput name='carrier.sealNumber' label='Seal #' />
      <LoadTextInput name='carrier.scac' label='SCAC' />
      <LoadTextInput
        name='carrier.phone'
        label='Phone'
        placeholder='(*************'
        // options={phoneFieldOptions}
        inputValue={InputValue.PHONE_NUMBER}
        readOnly={false}
      />
      <LoadTextInput
        name='carrier.email'
        label='Email'
        placeholder='<EMAIL>'
        options={emailFieldOptions}
      />
      <LoadTextInput name='carrier.dispatcher' label='Dispatcher' />

      <LoadTextInput
        name='carrier.rateConfirmationSent'
        label='Rate Confirmation Sent'
      />
      <LoadTextInput
        name='carrier.firstDriverName'
        label='First Driver Name'
      />
      <LoadTextInput
        name='carrier.firstDriverPhone'
        label='First Driver Phone'
        inputValue={InputValue.PHONE_NUMBER}
      />
      <LoadTextInput
        name='carrier.secondDriverName'
        label='Second Driver Name'
      />
      <LoadTextInput
        name='carrier.secondDriverPhone'
        label='Second Driver Phone'
        inputValue={InputValue.PHONE_NUMBER}
      />

      {/* TODO: Fetch TMS form schema from BE and use zod validation for more complex validations */}
      <div>
        <Label name='carrier.dispatchSource'>Dispatch Source</Label>
        <Controller
          name='carrier.dispatchSource'
          control={control}
          rules={{
            validate: () => {
              const source = getValues('carrier.dispatchSource');

              const dispTime = getValues('carrier.dispatchedTime');
              const eta = getValues('carrier.expectedPickupTime');
              const location =
                getValues('carrier.dispatchCity') ??
                getValues('carrier.dispatchState');

              if ((dispTime || eta || location) && !source) {
                return 'Source required when dispatching';
              }

              return true;
            },
          }}
          render={({ field }) => {
            return (
              <Select
                onValueChange={field.onChange}
                value={(field.value as string) ?? ''}
              >
                <SelectTrigger className='w-full mt-2'>
                  <SelectValue placeholder='Choose' />
                </SelectTrigger>
                <SelectContent>
                  {relaySourceEnums.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {titleCase(option.value)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            );
          }}
        />
        <ErrorMessage
          errors={errors}
          name={`carrier.dispatchSource`}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>

      <LoadTextInput
        name='carrier.dispatchCity'
        label='Dispatch City'
      />
      <LoadTextInput
        name='carrier.dispatchState'
        label='Dispatch State'
      />
      <LoadTextInput
        name='carrier.truckNumber'
        label='Truck #'
      />
      <LoadTextInput
        name='carrier.trailerNumber'
        label='Trailer #'
      />

      <LoadTextInput name='carrier.notes' label='Notes' />

      {!hideCarrierTimeInputs && (
        <>
          <LoadDateTimeInput
            name='carrier.confirmationSentTime'
            label='Confirmation Sent Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.confirmationReceivedTime'
            label='Confirmation Received Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.dispatchedTime'
            label='Dispatched Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.expectedPickupTime'
            label='Expected Pickup Time'
            rules={{
              validate: () => {
                const source = getValues('carrier.dispatchSource');
                const dispTime = getValues('carrier.dispatchedTime');
                const eta = getValues('carrier.expectedPickupTime');
                const location =
                  getValues('carrier.dispatchCity') ??
                  getValues('carrier.dispatchState');

                if ((dispTime || source || location) && !eta) {
                  return 'ETA required when dispatching';
                }
                return true;
              },
            }}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.pickupStart'
            label='Pickup Start Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.pickupEnd'
            label='Pickup End Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.expectedDeliveryTime'
            label='Expected Delivery Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.deliveryStart'
            label='Delivery Start Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
          <LoadDateTimeInput
            name='carrier.deliveryEnd'
            label='Delivery End Time'
            options={datetimeFieldOptions}
            load={currentLoad}
          />
        </>
      )}
      <LoadTextInput name='carrier.signedBy' label='Signed By' />
    </>
  );
}
