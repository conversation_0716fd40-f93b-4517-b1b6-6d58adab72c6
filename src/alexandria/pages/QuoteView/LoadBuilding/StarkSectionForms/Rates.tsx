import { UseFormReturn } from 'react-hook-form';

import { Grid } from 'components/layout';
import { NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from './Customer';

interface RatesFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
}

export function RatesForm({ formMethods: _formMethods }: RatesFormProps) {
  return (
    <Grid cols='1' gap='md' className='w-full mx-0'>
      <div className='space-y-1 text-md text-neutral-800 font-semibold mb-1'>
        Customer
      </div>
      <Grid cols='2' gap='sm' className='mx-0 w-full col-span-2'>
        <LoadBuildingTextInput
          name='rateData.customerLineHaulCharge.val'
          label='Line Haul Charge'
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
        />
        <LoadBuildingTextInput
          name='rateData.customerLineHaulCharge.unit'
          label='unit'
          required={false}
        />
      </Grid>

      <div className='space-y-1 text-md text-neutral-800 font-semibold mb-1'>
        Carrier
      </div>
      <Grid cols='2' gap='sm' className='mx-0 w-full col-span-2'>
        <LoadBuildingTextInput
          name='rateData.carrierLineHaulCharge.val'
          label='Line Haul Charge'
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
        />
        <LoadBuildingTextInput
          name='rateData.carrierLineHaulCharge.unit'
          label='unit'
          required={false}
        />
      </Grid>
    </Grid>
  );
}

export default RatesForm;
