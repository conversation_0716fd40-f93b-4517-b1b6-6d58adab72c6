import { useContext, useEffect, useMemo, useState } from 'react';
import {
  <PERSON>Path,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import { Accordion } from '@radix-ui/react-accordion';
import { get, set } from 'lodash';
import {
  BoxIcon,
  Building2,
  CircleDollarSignIcon,
  CircleUserRound,
  ExternalLinkIcon,
  ReceiptIcon,
  TruckIcon,
  WarehouseIcon,
  Weight,
} from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import { Button } from 'components/Button';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { CreateLoadRequest, createLoad } from 'lib/api/createLoad';
import { getCustomers } from 'lib/api/getCustomers';
import { getLocations } from 'lib/api/getLocations';
import { getOperators } from 'lib/api/getOperators';
import { updateTMS } from 'lib/api/updateTMS';
import { FormStorageService } from 'lib/services/FormStorage/service';
import { LoadSectionAccordionItem } from 'pages/LoadView/LoadInformation/Components';
import { BillToSectionForm } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/BillTo';
import { CustomerSectionForm } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/Customer';
import { OperatorSectionForm } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/Operator';
import { RatesForm } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/Rates';
import { SpecificationsForm } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/Specifications';
import { StopForm } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/Stop';
import {
  FuelSurchargeCostType,
  LinehaulCostType,
} from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/constants';
import {
  CostLineItem,
  Load,
  NormalizedLoad,
  RateData,
  Specifications,
  TMSCustomer,
  TMSLocation,
  Unit,
  createInitLoad,
  createInitRateData,
  createInitSpecs,
  normalizeLoad,
} from 'types/Load';
import { Maybe, Undef } from 'types/UtilityTypes';
import { TMSOperation } from 'types/api/UpdateTMS';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import { SuggestedLoad } from 'types/suggestions/LoadBuildingSuggestions';
import { captureResponseInPosthog } from 'utils/captureResponseInPosthog';
import { titleCase } from 'utils/formatStrings';
import { injectSelectedObject } from 'utils/loadInfoAndBuilding';
import { normalizeToStringArray } from 'utils/loadInfoAndBuilding';
import { denormalizeDatesForTMSForm } from 'utils/parseDatesForTMSForm';

enum AvailableTabs {
  customer = 'customer',
  billTo = 'billTo',
  specifications = 'specifications',
  pickup = 'pickup',
  consignee = 'consignee',
  carrier = 'carrier',
  operator = 'operator',
  rates = 'rates',
}

export type KeyValuePair = {
  key: string;
  value: string;
};

export const devDisableRequiredFields = !isProd() && false;

type LoadBuildingTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & { name: FieldPath<NormalizedLoad> };

export const LoadBuildingTextInput = (props: LoadBuildingTextInputProps) => (
  <RHFTextInput {...props} />
);

const initLoadBuildingForm = (tmsName: string): NormalizedLoad =>
  normalizeLoad(tmsName, {
    ...createInitLoad(),
    mode: 'TL',
    specifications: {
      ...createInitSpecs(),
      transportType: 'Van',
      serviceType: 'Any',
    },
    rateData: {
      ...createInitRateData(),
    },
  });

const initLoadEditForm = (load: Load): NormalizedLoad =>
  normalizeLoad(TMS.Turvo, load);

interface TurvoLoadFormProps {
  isCreateMode: boolean;
  load?: Load | null; // null for create mode, Load object for edit mode
  onSubmit?: (data: NormalizedLoad) => void; // Optional custom submit handler
  onInvalid?: SubmitErrorHandler<NormalizedLoad>;
  externalLinks?: any; // External links for edit mode
}

export default function TurvoLoadForm({
  isCreateMode,
  load,
  onSubmit,
  onInvalid,
  externalLinks,
}: TurvoLoadFormProps) {
  const { serviceID } = useServiceFeatures();
  const { tmsIntegrations } = useServiceFeatures();
  const { serviceFeaturesEnabled } = useServiceFeatures();
  const { toast } = useToast();
  const tmsName = TMS.Turvo;
  const [isLoading, setIsLoading] = useState(false);
  const [activeTabs, setActiveTabs] = useState<string[]>(
    Object.values(AvailableTabs)
  );
  const [isLoadBuildingSuggestionClicked, setIsLoadBuildingSuggestionClicked] =
    useState(false);
  const [showAIHints, setShowAIHints] = useState(false);

  const [isLoadingLocations, setIsLoadingLocations] = useState(true);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(true);
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [locations, setLocations] = useState<Maybe<TMSLocation[]>>(null);
  const [operators, setOperators] = useState<Array<string>>([]);
  const [tmsTenant, setTMSTenant] = useState<Maybe<string>>();
  const [builtLoad, setBuiltLoad] = useState<Maybe<Load>>(null);

  const posthog = usePostHog();

  const {
    currentState: {
      clickedSuggestion,
      threadId,
      goToSuggestionInCarousel,
      openExternalUrl,
    },
    setCurrentState,
  } = useContext(SidebarStateContext);

  // Initialize form with appropriate default values
  const defaultValues = useMemo(() => {
    if (isCreateMode) {
      return initLoadBuildingForm(tmsName);
    } else if (load) {
      return initLoadEditForm(load);
    }
    return initLoadBuildingForm(tmsName);
  }, [isCreateMode, load, tmsName]);

  const formMethods = useForm<NormalizedLoad>({
    defaultValues,
  });

  const fetchCustomers = async () => {
    setIsLoadingCustomers(true);

    const res = await getCustomers(tmsIntegrations[0]?.id);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
      setTMSTenant(res.value.tmsTenant);
    } else {
      toast({
        description: res.error.message || 'Error while fetching customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  const fetchLocations = async (forceRefresh = false) => {
    setIsLoadingLocations(true);

    const res = await getLocations(tmsIntegrations[0]?.id, forceRefresh);
    if (res.isOk()) {
      setLocations(res.value.locationList);
    } else {
      const errorMessage = forceRefresh
        ? 'Error while refreshing location list.'
        : 'Error while fetching location list.';
      toast({
        description: res.error.message || errorMessage,
        variant: 'destructive',
      });
    }
    setIsLoadingLocations(false);
  };

  const fetchOperators = async () => {
    if (!serviceFeaturesEnabled.isOperatorEnabled) {
      return;
    }

    const res = await getOperators(tmsIntegrations?.[0]?.id);
    if (res.isOk()) {
      setOperators(res.value);
    } else {
      toast({
        description: res.error.message || 'Error while fetching operator list.',
        variant: 'destructive',
      });
    }
  };

  const handleRefreshLocations = async () => {
    await fetchLocations(true);
  };

  // Initialize data on mount
  useEffect(() => {
    if (!tmsIntegrations?.length) {
      return;
    }

    fetchCustomers();
    fetchLocations();
    fetchOperators();
  }, [tmsIntegrations]);

  // Set flag for suggestion presence
  useEffect(() => {
    if (
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      setIsLoadBuildingSuggestionClicked(true);
    } else {
      setIsLoadBuildingSuggestionClicked(false);
    }
  }, [clickedSuggestion]);

  const normalizeTransportType = (transportType: string): string => {
    if (!transportType) {
      return 'Van';
    }

    const normalizedTransportType = transportType.toLowerCase();

    const turvoTransportTypeMap: Record<string, string> = {
      reefer: 'Reefer',
      flatbed: 'Flatbed',
      van: 'Van',
      hotshot: 'Hotshot',
      'box truck': 'Box Truck',
      'dry van': 'Van - dry',
      'straight truck': 'Straight truck',
      sprinter: 'Van - sprinter',
      'cargo van': 'Cargo van',
    };

    return turvoTransportTypeMap[normalizedTransportType] || 'Van';
  };

  // Memoized default values with suggestion support for create mode
  const memoizedDefaultValues: NormalizedLoad = useMemo(() => {
    if (
      isCreateMode &&
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      const castedClickedSuggestion =
        clickedSuggestion.suggested as SuggestedLoad;
      const transportType =
        castedClickedSuggestion.specifications?.transportType?.toLowerCase();

      let linehaulCharge: Maybe<CostLineItem> = null;
      let fscLineItem: Maybe<CostLineItem> = null;

      if (
        castedClickedSuggestion.rateData.customerLineHaulCharge &&
        castedClickedSuggestion.rateData.customerLineHaulCharge.val > 0
      ) {
        linehaulCharge = {
          label: LinehaulCostType,
          unitBasis: 'Flat',
          quantity: 1,
          ratePerUnitUSD:
            castedClickedSuggestion.rateData.customerLineHaulCharge.val,
          totalChargeUSD:
            castedClickedSuggestion.rateData.customerLineHaulCharge.val,
          note: '',
        };

        // Reset legacy charge field so it's not double-charged
        castedClickedSuggestion.rateData.customerLineHaulCharge.val = 0;
        castedClickedSuggestion.rateData.customerLineHaulRate = null;
      }

      if (
        castedClickedSuggestion.rateData.fscFlatRate &&
        castedClickedSuggestion.rateData.fscFlatRate > 0
      ) {
        fscLineItem = {
          label: FuelSurchargeCostType,
          unitBasis: 'Flat',
          quantity: 1,
          ratePerUnitUSD: castedClickedSuggestion.rateData.fscFlatRate,
          totalChargeUSD: castedClickedSuggestion.rateData.fscFlatRate,
          note: '',
        };

        // Reset legacy charge field so it's not double-charged
        castedClickedSuggestion.rateData.fscFlatRate = 0;
      }

      const suggestedFields = {
        ...castedClickedSuggestion,
        mode: 'TL',
        // Preserve refNumberCandidates fields
        customer: {
          ...castedClickedSuggestion.customer,
          refNumberCandidates: normalizeToStringArray(
            castedClickedSuggestion.customer?.refNumberCandidates
          ),
        },
        pickup: {
          ...castedClickedSuggestion.pickup,
          refNumberCandidates: normalizeToStringArray(
            castedClickedSuggestion.pickup?.refNumberCandidates
          ),
        },
        consignee: {
          ...castedClickedSuggestion.consignee,
          refNumberCandidates: normalizeToStringArray(
            castedClickedSuggestion.consignee?.refNumberCandidates
          ),
        },
        specifications: {
          ...createInitSpecs(),
          ...castedClickedSuggestion.specifications,
          transportType: normalizeTransportType(transportType),
        } as Specifications,
        rateData: {
          ...createInitRateData(),
          ...castedClickedSuggestion.rateData,
          customerTotalCharge: {
            ...castedClickedSuggestion.rateData.customerTotalCharge,
            unit:
              castedClickedSuggestion.rateData.customerTotalCharge?.unit ??
              Unit.USD,
          },
          carrierTotalCost: {
            ...castedClickedSuggestion.rateData.carrierTotalCost,
            unit:
              castedClickedSuggestion.rateData.carrierTotalCost?.unit ??
              Unit.USD,
          },
          customerLineItems: [linehaulCharge, fscLineItem].filter(
            Boolean
          ) as CostLineItem[],
        } as RateData,
      };
      setShowAIHints(true);

      const res = {
        ...normalizeLoad(tmsName, createInitLoad()),
        ...suggestedFields,
        mode: 'TL',
        rateData: {
          ...suggestedFields.rateData,
          customerTotalCharge: {
            ...suggestedFields.rateData.customerTotalCharge,
            unit: Unit.USD,
          },
        },
      } as NormalizedLoad;

      return res;
    }

    if (builtLoad) {
      return normalizeLoad(tmsName, builtLoad);
    }

    // Handle edit mode with existing load data
    if (!isCreateMode && load) {
      const normalizedLoad = normalizeLoad(tmsName, load);

      // Backend now provides pickup date directly, no need to map from appointment start time
      // The pickup.readyTime field should now be populated directly from the backend

      return normalizedLoad;
    }

    setShowAIHints(false);

    return initLoadBuildingForm(tmsName);
  }, [
    isCreateMode ? threadId : null,
    isCreateMode ? clickedSuggestion?.id : null, // Use ID instead of full object to prevent unnecessary recalculations
    builtLoad,
    isCreateMode,
    load,
  ]);

  useEffect(() => {
    if (!memoizedDefaultValues) {
      return;
    }
    formMethods.reset(memoizedDefaultValues, {
      keepDefaultValues: false,
      keepTouched: false,
      keepDirtyValues: false,
      keepDirty: false,
    });

    // Clear validation errors for suggestions
    if (
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      setTimeout(() => {
        formMethods.clearErrors();
      }, 200);
    }
  }, [memoizedDefaultValues]);

  useEffect(() => {
    if (
      !isLoadingCustomers &&
      customers &&
      memoizedDefaultValues?.customer?.externalTMSID
    ) {
      setCustomers((prevCustomers) =>
        injectSelectedObject(
          memoizedDefaultValues.customer,
          prevCustomers ?? []
        )
      );
    }
  }, [isLoadingCustomers, memoizedDefaultValues?.customer]);

  useEffect(() => {
    if (!isLoadingLocations && locations && memoizedDefaultValues) {
      const additionalLocations: TMSLocation[] = [];
      if (memoizedDefaultValues.pickup?.externalTMSID) {
        additionalLocations.push(memoizedDefaultValues.pickup);
      }
      if (memoizedDefaultValues.consignee?.externalTMSID) {
        additionalLocations.push(memoizedDefaultValues.consignee);
      }

      if (additionalLocations.length > 0) {
        setLocations((prevLocations) => {
          let updatedLocations = prevLocations ?? [];
          additionalLocations.forEach((loc) => {
            updatedLocations = injectSelectedObject(loc, updatedLocations);
          });
          return updatedLocations;
        });
      }
    }
  }, [
    isLoadingLocations,
    memoizedDefaultValues?.pickup,
    memoizedDefaultValues?.consignee,
  ]);

  const { formState } = formMethods;

  // Clear form functionality
  function handleClearForm() {
    setShowAIHints(false);
    setBuiltLoad(null);

    FormStorageService.clearFormState(`${tmsName}_${threadId}`);

    // Deselect the suggestion in the carousel
    setCurrentState((prevState) => ({
      ...prevState,
      clickedSuggestion: null,
    }));

    // Reset form to empty values directly - bypass all memoization
    const emptyForm = initLoadBuildingForm(tmsName);
    formMethods.reset(emptyForm, {
      keepDefaultValues: false,
      keepDirty: false,
      keepDirtyValues: false,
      keepTouched: false,
    });

    // Clear any validation errors
    formMethods.clearErrors();
  }

  function flattenValues(
    values: any,
    parentKey = '',
    result: string[] = []
  ): string[] {
    for (const key in values) {
      const value = values[key];
      const newKey = parentKey ? `${parentKey}.${key}` : key;

      if (value && typeof value === 'object' && !Array.isArray(value)) {
        flattenValues(value, newKey, result);
      } else {
        result.push(newKey);
      }
    }
    return result;
  }

  function formatErrors(
    errors: any,
    parentPath = ''
  ): Array<{ path: string; message?: string }> {
    const out: Array<{ path: string; message?: string }> = [];

    if (!errors || typeof errors !== 'object') return out;

    for (const key of Object.keys(errors)) {
      const val = errors[key];
      const path = parentPath ? `${parentPath}.${key}` : key;
      if (Array.isArray(val)) {
        val.forEach((item, idx) => {
          out.push(...formatErrors(item, `${path}.${idx}`));
        });
        continue;
      }

      const hasMessage = val && typeof val === 'object' && 'message' in val;
      if (hasMessage) {
        out.push({ path, message: val.message });
      }

      if (val && typeof val === 'object') {
        out.push(...formatErrors(val, path));
      }
    }

    return out;
  }

  function formatLabel(path: string): string {
    // Customer charge items are required but under a different structure
    // from the rest of the form so it's custom mapped
    const m = path.match(/^rateData\.customerLineItems\.(\d+)\.(\w+)/);
    if (m) {
      const idx = m[1];
      const field = m[2];
      if (field === 'label') {
        return `Rate Charge ${parseInt(idx) + 1} Quantity`;
      }
    }

    const prefixMap: Record<string, string> = {
      customer: 'Customer',
      billTo: 'Bill To',
      pickup: 'Pickup',
      consignee: 'Consignee',
      specifications: 'Specs',
      rateData: 'Rates',
    };

    const replacements: Record<string, string> = {
      externalTMSID: 'Name',
      refNumber: 'Ref #',
      poNums: 'PO #',
      zipCode: 'Zip Code',
      readyTime: 'Pickup Date',
      mustDeliver: 'Delivery Date',
      apptStartTime: 'Appointment Start Time',
      apptEndTime: 'Appointment End Time',
      apptType: 'Appointment Type',
      unit: 'Units',
    };

    const parts = path
      .split('.')
      .filter((p) => p && !/^\d+$/.test(p) && p !== 'val');

    const formattedLabel = parts
      .map((part, idx) => {
        if (idx === 0 && prefixMap[part]) {
          return prefixMap[part];
        }
        if (replacements[part] != null) {
          return replacements[part];
        }
        return titleCase(part);
      })
      .filter(Boolean)
      .join(' ');

    return formattedLabel || titleCase(path);
  }

  function scrollAndFocusField(path: string) {
    setTimeout(() => {
      const trySelectors = [`input[name="${path}"]`, `select[name="${path}"]`];

      let el: HTMLElement | null = document.querySelector(
        trySelectors.join(', ')
      ) as HTMLElement | null;

      // Date-time inputs expose a container with data-name
      if (!el) {
        el =
          (document.querySelector(
            `[data-name="${path}"]`
          ) as HTMLElement | null) ||
          (document.querySelector(
            `label[name="${path}"]`
          ) as HTMLElement | null);
      }

      if (!el) return;

      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 150);
  }

  // Form state persistence for create mode
  useEffect(() => {
    if (isCreateMode && threadId) {
      const savedState = FormStorageService.getFormState<
        NormalizedLoad,
        NormalizedLoad
      >(`${tmsName}_${threadId}`);

      if (
        savedState &&
        savedState.threadID === threadId &&
        !(
          clickedSuggestion &&
          clickedSuggestion.pipeline == SuggestionPipelines.LoadBuilding
        )
      ) {
        setShowAIHints(true);
        goToSuggestionInCarousel({
          suggestionID: savedState.clickedSuggestion?.id,
        });

        // Flatten savedState.values to get all field names
        const fieldNames = flattenValues(savedState.values);
        const cleanFields = normalizeLoad(tmsName, createInitLoad());

        // For each field, set the value and mark as dirty if necessary
        fieldNames.forEach((fieldName) => {
          const isDirty: Undef<boolean> = get(
            savedState.dirtyFields,
            fieldName
          );
          if (isDirty === undefined || !isDirty) {
            set(cleanFields, fieldName, get(savedState.values, fieldName));
          }
        });

        formMethods.reset(cleanFields as NormalizedLoad, {
          keepDefaultValues: false,
          keepTouched: false,
          keepDirtyValues: false,
          keepDirty: false,
        });

        setTimeout(() => {
          fieldNames.forEach((fieldName) => {
            const isDirty: Undef<boolean> = get(
              savedState.dirtyFields,
              fieldName
            );

            if (isDirty) {
              formMethods.setValue(
                fieldName as FieldPath<NormalizedLoad>,
                get(savedState.values, fieldName),
                {
                  shouldDirty: isDirty,
                  shouldTouch: true,
                }
              );
            }
          });

          // After resetting the form, ensure that options arrays include the necessary data
          const customerID = savedState.values.customer?.externalTMSID;
          if (customerID) {
            setCustomers((prevCustomers) =>
              injectSelectedObject(
                savedState.values.customer,
                prevCustomers ?? []
              )
            );
          }

          const additionalLocations: TMSLocation[] = [];
          if (savedState.values.pickup?.externalTMSID) {
            additionalLocations.push(savedState.values.pickup);
          }

          if (savedState.values.consignee?.externalTMSID) {
            additionalLocations.push(savedState.values.consignee);
          }

          setLocations((prevLocations) => {
            let updatedLocations = prevLocations ?? [];

            additionalLocations.forEach((loc) => {
              updatedLocations = injectSelectedObject(loc, updatedLocations);
            });

            return updatedLocations;
          });
        }, 100);
      }

      // Wait for RHF to finish rendering before caching initial form state
      setTimeout(() => {
        if (!savedState || threadId === savedState.threadID) {
          FormStorageService.saveFormState(`${tmsName}_${threadId}`, {
            threadID: threadId,
            clickedSuggestion: isLoadBuildingSuggestionClicked
              ? clickedSuggestion
              : null,
            values: formMethods.getValues(),
            dirtyFields: formMethods.formState.dirtyFields,
          });
        }
      }, 100);

      // Subscribe to form to cache state on changes
      const subscription = formMethods.watch((value) => {
        if (!savedState || threadId === savedState.threadID) {
          FormStorageService.saveFormState(`${tmsName}_${threadId}`, {
            threadID: threadId,
            values: value,
            clickedSuggestion: isLoadBuildingSuggestionClicked
              ? clickedSuggestion
              : null,
            dirtyFields: formMethods.formState.dirtyFields,
          });
        }
      });

      return () => {
        subscription.unsubscribe();
      };
    }
    return undefined;
  }, [
    formMethods,
    threadId,
    isLoadBuildingSuggestionClicked,
    clickedSuggestion,
    tmsName,
    isCreateMode,
  ]);

  const validateAppointmentTimes = (
    sectionName: string,
    apptStartTime: Date,
    apptEndTime: Date
  ): boolean => {
    if (apptStartTime && apptEndTime && apptStartTime > apptEndTime) {
      toast({
        description: `${sectionName} Section: Appointment Start Time cannot be later than Appointment End Time`,
        variant: 'destructive',
      });
      return false;
    }
    return true;
  };

  // Handle form submission
  const handleFormSubmit = async (data: NormalizedLoad) => {
    if (data.pickup?.apptStartTime && data.pickup?.apptEndTime) {
      if (
        !validateAppointmentTimes(
          'Pickup',
          data.pickup.apptStartTime,
          data.pickup.apptEndTime
        )
      ) {
        setIsLoading(false);
        return;
      }
    }
    if (data.consignee?.apptStartTime && data.consignee?.apptEndTime) {
      if (
        !validateAppointmentTimes(
          'Consignee',
          data.consignee.apptStartTime,
          data.consignee.apptEndTime
        )
      ) {
        setIsLoading(false);
        return;
      }
    }

    if (isCreateMode) {
      // Create mode - build new load
      setIsLoading(true);
      try {
        const reqData: CreateLoadRequest = {
          suggestionId: clickedSuggestion?.id,
          load: {
            tmsID: tmsIntegrations[0]?.id,
            mode: data.mode,
            poNums: data.poNums,
            customer: denormalizeDatesForTMSForm(tmsName, data.customer),
            specifications: data.specifications,
            pickup: {
              ...denormalizeDatesForTMSForm(tmsName, data.pickup),
            },
            consignee: {
              ...denormalizeDatesForTMSForm(tmsName, data.consignee),
            },
            rateData: denormalizeDatesForTMSForm(tmsName, data.rateData),
            commodities: data.commodities,
          } as unknown as Load,
        };

        const readyTimeString = data.pickup.readyTime as unknown as string;
        const mustDeliverString = data.consignee
          .mustDeliver as unknown as string;

        reqData.metadata = {
          isPickupDateOnly:
            typeof readyTimeString === 'string' &&
            !readyTimeString.includes('T'),
          isDropoffDateOnly:
            typeof mustDeliverString === 'string' &&
            !mustDeliverString.includes('T'),
        };

        const res = await createLoad(reqData);

        if (res.isOk()) {
          setBuiltLoad(res.value.load);
          FormStorageService.clearFormState(`${tmsName}_${threadId}`);

          if (clickedSuggestion) {
            setCurrentState((prevState) => {
              const filteredList = prevState.curSuggestionList.filter(
                ({ id }) => id !== clickedSuggestion.id
              );
              return {
                ...prevState,
                clickedSuggestion: null,
                curSuggestionList: filteredList,
              };
            });
          }
          toast({
            description: 'Load built successfully!',
            variant: 'success',
          });
        } else {
          toast({
            description: res.error.message || 'Error building load.',
            variant: 'destructive',
          });
        }
        captureResponseInPosthog(posthog, !res.isOk(), {
          serviceID,
          tmsName,
          submissionType: isCreateMode
            ? ButtonNamePosthog.BuildLoad
            : ButtonNamePosthog.UpdateTMS,
        });
      } catch {
        toast({
          description: 'Error building load.',
          variant: 'destructive',
        });
      }
      setIsLoading(false);
    } else {
      // Edit mode - update existing load
      setIsLoading(true);
      try {
        const denormalizedData = denormalizeDatesForTMSForm(tmsName, data);

        // Define proper interface for update payload
        interface UpdatePayload
          extends Pick<
            Load,
            | 'customer'
            | 'billTo'
            | 'pickup'
            | 'consignee'
            | 'carrier'
            | 'rateData'
          > {
          operator: string;
          freightTrackingID: string;
        }

        // Create update data without carrier to avoid time field validation issues
        const updateData: UpdatePayload = {
          ...denormalizedData,
          operator: data.operator || '',
          freightTrackingID: data.freightTrackingID || '',
        } as UpdatePayload;

        // Remove carrier from update data to avoid validation issues
        if ('carrier' in updateData) {
          delete (updateData as any).carrier;
        }

        if (!load?.ID) {
          toast({
            description: 'Load ID is required for updates.',
            variant: 'destructive',
          });
          return;
        }

        const res = await updateTMS(load.ID, {
          operation: TMSOperation.UpdateLoad,
          load: updateData,
        });

        if (res.isOk()) {
          toast({
            description: 'Load updated successfully!',
            variant: 'success',
          });
        } else {
          toast({
            description: res.error.message || 'Error updating load.',
            variant: 'destructive',
          });
        }
      } catch {
        toast({
          description: 'Error updating load.',
          variant: 'destructive',
        });
      }
      setIsLoading(false);
    }

    // Call custom submit handler if provided
    if (onSubmit) {
      onSubmit(data);
    }
  };

  // Default onInvalid handler if none provided
  const defaultOnInvalid: SubmitErrorHandler<NormalizedLoad> = async (
    errors
  ) => {
    const invalids = formatErrors(errors);
    if (!invalids.length) {
      toast({
        description: 'Some fields are invalid.',
        variant: 'destructive',
      });
      return;
    }

    scrollAndFocusField(invalids[0].path);

    const maxLabelsToShow = 8;
    const items = invalids
      .slice(0, maxLabelsToShow)
      .map(({ path, message }) => {
        const label = formatLabel(path);
        return message ? `${label} (${message})` : label;
      });
    const moreCount = invalids.length - items.length;

    const description =
      `Invalid fields: ` +
      items.join(', ') +
      (moreCount > 0 ? `, and ${moreCount} more.` : '');

    toast({ description, variant: 'destructive' });
  };

  const handleFormSubmitWrapper = formMethods.handleSubmit(
    handleFormSubmit,
    onInvalid || defaultOnInvalid
  );

  // Determine which sections to show based on mode
  const showCarrierSection =
    !isCreateMode && serviceFeaturesEnabled.isCarrierVerificationEnabled;
  const showOperatorSection =
    !isCreateMode && serviceFeaturesEnabled.isOperatorEnabled;
  const showBillToSection = !isCreateMode; // Bill To is typically only shown in edit mode

  return (
    <div className='mb-5'>
      <ExtendedFormProvider aiDefaultValues={showAIHints} aiIconOnly={true}>
        <FormProvider {...formMethods}>
          <form onSubmit={handleFormSubmitWrapper}>
            <Accordion
              type='multiple'
              value={activeTabs}
              onValueChange={setActiveTabs}
            >
              <LoadSectionAccordionItem
                label='Customer'
                icon={<Building2 className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.customer}
                activeTabs={activeTabs}
              >
                <CustomerSectionForm
                  formMethods={formMethods}
                  customers={customers}
                  setCustomers={setCustomers}
                  isLoadingCustomers={isLoadingCustomers}
                  setIsLoadingCustomers={setIsLoadingCustomers}
                  originalSuggestionData={
                    clickedSuggestion?.suggested as NormalizedLoad
                  }
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Specs'
                icon={<Weight className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.specifications}
                activeTabs={activeTabs}
              >
                <SpecificationsForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Rates'
                icon={
                  <CircleDollarSignIcon className='h-6 w-6' strokeWidth={1} />
                }
                name={AvailableTabs.rates}
                activeTabs={activeTabs}
              >
                <RatesForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Pickup'
                icon={<BoxIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.pickup}
                activeTabs={activeTabs}
              >
                <StopForm
                  stop='pickup'
                  formMethods={formMethods}
                  isLoadingLocations={isLoadingLocations}
                  locations={locations}
                  handleRefreshLocations={handleRefreshLocations}
                  originalSuggestionData={
                    clickedSuggestion?.suggested as NormalizedLoad
                  }
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Consignee'
                icon={<WarehouseIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.consignee}
                activeTabs={activeTabs}
              >
                <StopForm
                  stop='consignee'
                  formMethods={formMethods}
                  isLoadingLocations={isLoadingLocations}
                  locations={locations}
                  handleRefreshLocations={handleRefreshLocations}
                  originalSuggestionData={
                    clickedSuggestion?.suggested as NormalizedLoad
                  }
                />
              </LoadSectionAccordionItem>

              {showCarrierSection && (
                <LoadSectionAccordionItem
                  label='Carrier'
                  icon={<TruckIcon className='h-6 w-6' strokeWidth={1} />}
                  name={AvailableTabs.carrier}
                  activeTabs={activeTabs}
                >
                  {externalLinks && externalLinks.viewCarrier && (
                    <Flex
                      justify='between'
                      align='center'
                      className='p-4 bg-neutral-200 rounded'
                    >
                      <a
                        href={externalLinks.viewCarrier}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-accent-600 hover:text-accent-800 underline'
                      >
                        Edit carrier details on Turvo.
                      </a>
                      <ExternalLinkIcon className='w-3 h-3' />
                    </Flex>
                  )}
                </LoadSectionAccordionItem>
              )}

              {showOperatorSection && (
                <LoadSectionAccordionItem
                  label='Operator'
                  icon={<CircleUserRound className='h-6 w-6' strokeWidth={1} />}
                  name={AvailableTabs.operator}
                  activeTabs={activeTabs}
                >
                  <OperatorSectionForm
                    formMethods={formMethods}
                    operators={operators}
                  />
                </LoadSectionAccordionItem>
              )}

              {showBillToSection && (
                <LoadSectionAccordionItem
                  label='Bill To'
                  icon={<ReceiptIcon className='h-6 w-6' strokeWidth={1} />}
                  name={AvailableTabs.billTo}
                  activeTabs={activeTabs}
                >
                  <BillToSectionForm />
                </LoadSectionAccordionItem>
              )}
            </Accordion>

            <section className='w-full mt-4'>
              <Button
                buttonNamePosthog={
                  isCreateMode
                    ? ButtonNamePosthog.BuildLoad
                    : ButtonNamePosthog.UpdateTMS
                }
                type='submit'
                className='w-full'
                disabled={isLoading}
                logProperties={{ serviceID, tmsName }}
              >
                {isLoading ? (
                  <ButtonLoader />
                ) : isCreateMode ? (
                  ButtonText.BuildLoad
                ) : (
                  ButtonText.UpdateTMS
                )}
              </Button>

              {isCreateMode && formState.isDirty && (
                <Flex justify='center' align='center'>
                  <Button
                    buttonNamePosthog={ButtonNamePosthog.ClearForm}
                    type='button'
                    className='w-50% mt-4 h-8 text-sm text-neutral-500'
                    disabled={isLoading}
                    variant='outline'
                    onClick={handleClearForm}
                    logProperties={{ serviceID }}
                  >
                    {ButtonText.ClearForm}
                  </Button>
                </Flex>
              )}

              {isCreateMode && builtLoad && (
                <>
                  <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-800 px-4 bg-success-50'>
                    <Typography className='mb-2'>Load Created 🎉</Typography>
                    <Typography variant='body-sm' className='mb-2'>
                      <Typography variant='body-sm' weight='bold'>
                        Turvo Load ID:{' '}
                      </Typography>
                      {builtLoad.externalTMSID}
                    </Typography>
                    <Typography variant='body-sm' className='mb-2'>
                      <Typography variant='body-sm' weight='bold'>
                        Shipment ID:{' '}
                      </Typography>
                      {builtLoad.freightTrackingID}
                    </Typography>
                    <Typography
                      variant='body-sm'
                      className='mb-1 cursor-pointer underline'
                      onClick={() =>
                        openExternalUrl(
                          `https://app.turvo.com/#/${tmsTenant}/shipments/${builtLoad.externalTMSID}/details`
                        )
                      }
                    >
                      Access the created load for more details
                    </Typography>
                  </div>
                </>
              )}
            </section>
          </form>
        </FormProvider>
      </ExtendedFormProvider>
    </div>
  );
}

// Placeholder components that will be implemented
