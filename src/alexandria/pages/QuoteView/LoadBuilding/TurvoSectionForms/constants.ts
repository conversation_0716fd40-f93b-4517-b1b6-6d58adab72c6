export const currencyList: string[] = ['USD'];

export const serviceTypes: string[] = ['Any', 'Expedite', 'Standard'];

export const unitsList: string[] = [
  'Metric tons',
  'Bottles',
  'Packages',
  'Racks',
  'Bin',
  'Piece',
  'Ream',
  'Spack',
  'Spool',
  'Bushels',
  'Containers',
  'Gallons',
  'Tons',
  'Kegs',
  'Cases',
  'Truckload',
  'Blocks',
  'Bulk',
  'Barrels',
  'Units',
  'Each',
  'Pallets',
  'Base units',
  'Boxes',
  'Loose',
  'Bags',
  'Bales',
  'Bundles',
  'Cans',
  'Carboys',
  'Carpets',
  'Cartons',
  'Coils',
  'Crates',
  'Cylinders',
  'Drums',
  'Pails',
  'Reels',
  'Lbs',
  'Rolls',
  'Kgs',
  'Skids',
  'Liters',
  'Totes',
  'Tubes/pipes',
  'Vehicles',
  'Other',
  'Kit',
  'Pack',
  'Pair',
  'Feet',
  'Inner Pack',
  'Grams',
  'Layer',
  'Items',
  'Buckets',
  'Combinations',
  'Hundredweight on Net',
  'Pouches',
  'Trays',
  'Tubs',
];

export const weightUnitsList: string[] = ['t', 'oz', 'ton', 'g', 'lb', 'kg'];

export const equipmentList: string[] = [
  'Chassis',
  'Bobtail',
  'Automotive racks',
  'Boxcars',
  'Centerbeams',
  'Covered hoppers',
  'Coil cars',
  'Flatcars',
  'Gondolas',
  'Refrigerated boxcar',
  'Open top hoppers',
  'Specialized rail equipment',
  'Bulk carriers',
  'Container ships',
  'Cargo ship',
  'Refrigerated ships',
  'Roll on/Roll off Ships',
  'Tank barge',
  'Deck barge',
  'Hopper barge',
  'Car float barge',
  'Single-axle',
  'Liquid cargo barge',
  'Double-axle',
  'Barrack barge',
  'Tri-axle',
  'Split Hopper barge',
  'Electric heat',
  'Power barge',
  'Straight truck',
  'Royal Barge',
  'Pickup truck',
  'Van - dry',
  'Cargo van',
  'Flatbed - standard',
  'Mini float',
  'Flatbed - legal',
  'Moffett',
  'Flatbed - stretch/extendable',
  'Winch truck',
  'Flatbed - covered wagon',
  'Crane',
  'Flatbed - curtain side',
  'Flatbed - auto',
  'Hotshot',
  'Bulk commodity',
  'Dump - end',
  'Driveaway',
  'Logging',
  'Dump - side',
  'Container - Bulk',
  'Tow - light duty',
  'Enclosed',
  'Container - Power pack',
  'Hopper',
  'Container - Upgraded',
  'Tanker - chemical',
  'Tanker - food',
  'Tanker - fuel',
  'Tow',
  'Pneumatic',
  'Food trailer - frozen',
  'Truck, van',
  'Container - temperature controlled',
  'Container - high cube',
  'Container - open top',
  'Container - flat rack',
  'Container - pallet wide',
  'Container - refrigerated',
  'Container - tanker',
  'Power unit - tractor',
  'Tow - medium duty',
  'Tow - heavy duty',
  'Tow - landoll',
  'Van',
  'Van - vented',
  'Van - curtain side',
  'Flatbed',
  "Flatbed - 4' tarp",
  "Flatbed - 6' tarp",
  "Flatbed - 8' tarp",
  'Refrigerated',
  'Auto transport',
  'Conestoga',
  'Container - standard',
  'Double drop (low boy)',
  'Removable goose neck',
  'Step deck',
  'Tanker',
  'Other',
  'Hot oil truck',
  'Hydro excavation truck',
  'Pump truck',
  'Vacuum truck',
  'Insulated Van or Reefer',
  'Reefer or Vented Van',
  'Containers',
  'Reefer',
  'Dry - Standard',
  'Dry - HC',
  'Reefer- Standard',
  'Reefer - HC',
  'Hotshot Flatbed',
  'Van - sprinter',
  'Isotank',
  'Flatbed - Quad-axle',
  'Flatbed - Quad-axle rolltite',
  'Flatbed - Super B',
  'Van - Tri-axle dry',
  'Auger feed truck',
  'Ag hopper',
  'Walking floor',
  'Roll Tite',
  'Reefer – Triaxle',
  'Reefer – Quadaxle',
  'Stepdeck Conestoga',
  'Box Truck',
  'Van - open top',
];

export const equipmentSizeList: string[] = [
  'Other',
  '8 ft',
  '10 ft',
  '12 ft',
  '14 ft',
  '16 ft',
  '20 ft',
  '24 ft',
  '26 ft',
  '28 ft',
  '40 ft',
  '42 ft',
  '45 ft',
  '48 ft',
  '50 ft',
  '53 ft',
  '70 bbl',
  '75 bbl',
  '82 bbl',
  '90 bbl',
  '92 bbl',
  '100 bbl',
  '110 bbl',
  '115 bbl',
  '125 bbl',
  '130 bbl',
  '150 bbl',
  '160 bbl',
  '170 bbl',
  '180 bbl',
  '190 bbl',
  '200 bbl',
  '210 bbl',
  '5000 psi',
  '6000 psi',
  '7000 psi',
  '8000 psi',
  '1000 psi',
  '2000 psi',
  '3000 psi',
  '4000 psi',
  '1 car',
  '2 car',
  '3 car',
  '4 car',
];

export const LinehaulCostType = 'Freight - Linehaul';
export const FuelSurchargeCostType = 'Fuel - flat';

// Cost Types, e.g. Freight-Flat, Accessorial - Detention, etc.
export const costTypesList: string[] = [
  'Accessorial - cross dock',
  'Accessorial - detention',
  'Accessorial - drayage',
  'Accessorial - extra miles',
  'Accessorial - layover',
  'Accessorial - loading',
  'Accessorial - lumper',
  'Accessorial - pallets',
  'Accessorial - stops',
  'Accessorial - storage',
  'Accessorial - tarp',
  'Accessorial - team services',
  'Accessorial - tolls',
  'Accessorial - trailer cleaning',
  'Accessorial - TWIC services',
  'Accessorial - unloading',
  'Accessorial - restack',
  'Accessorial - origin terminal handling charge',
  'Accessorial - destination terminal handling charge',
  'Accessorial - demurrage and detention',
  'Accessorial - origin reefer plug-in',
  'Accessorial - destination reefer plug-in',
  'Accessorial - Accessorial Charge',
  'Accessorial - Additional charge',
  'Accessorial - Bulkhead',
  'Accessorial - CARB Compliance Fee',
  'Accessorial - Charged as Weight',
  'Accessorial - Deficit Charge',
  'Accessorial - Excessive Length',
  'Accessorial - Forklift',
  'Accessorial - High Cost Delivery',
  'Accessorial - Redelivery Fee',
  'Accessorial - Refrigerate',
  'Accessorial - Requires Signature',
  'Accessorial - Unknown Charge',
  'Accessorial - Weekend Delivery',
  'Accessorial - Weekend Pickup',
  'Accessorial - Weight Fee - reweigh',
  'Accessorial - White Glove Service',
  'Location - Airport Delivery',
  'Location - Airport Pickup',
  'Location - Camp Delivery',
  'Location - Camp Pickup',
  'Location - Church Delivery',
  'Location - Church Pickup',
  'Location - Commercial Delivery',
  'Location - Commercial Pickup',
  'Location - Construction Site Delivery',
  'Location - Construction Site Pickup',
  'Location - Convention/Tradeshow Delivery',
  'Location - Convention/Tradeshow Pickup',
  'Location - Country Club Delivery',
  'Location - Country Club Pickup',
  'Location - Fair/Amusement/Park Delivery',
  'Location - Fair/Amusement/Park Pickup',
  'Location - Farm Delivery',
  'Location - Farm Pickup',
  'Location - Government Site Delivery',
  'Location - Government Site Pickup',
  'Location - Grocery Warehouse Delivery',
  'Location - Grocery Warehouse Pickup',
  'Location - Hospital Delivery',
  'Location - Hospital Pickup',
  'Location - Hotel Delivery',
  'Location - Hotel Pickup',
  'Location - Mall Delivery',
  'Location - Mall Pickup',
  'Location - Military Installation Delivery',
  'Location - Military Installation Pickup',
  'Harmonized Sales Tax',
  'Mine Site Delivery',
  'Mine Site Pickup',
  'Native American Reservation Delivery',
  'Native American Reservation Pickup',
  'Nursing Home Delivery',
  'Nursing Home Pickup',
  'Prison Delivery',
  'Prison Pickup',
  'Resort Delivery',
  'Resort Pickup',
  'School Delivery',
  'School Pickup',
  'accessorial.other	Accessorial - Other',
  'Accessorial - white glove',
  'Accessorial - bonded shipment',
  'Accessorial - border crossing',
  'Accessorial - chassis',
  'Accessorial - crane',
  'Accessorial - delivery charge',
  'Accessorial - demurrage',
  'Accessorial - driver assistance',
  'Accessorial - drop trailer',
  'Accessorial - escort',
  'Accessorial - expedited',
  'Accessorial - farm',
  'Accessorial - freeze protect',
  'Accessorial - hook up',
  'Accessorial - insurance',
  'Accessorial - liftgate',
  'Accessorial - limited access',
  'Accessorial - oversized',
  'Accessorial - overweight',
  'Accessorial - permit',
  'Accessorial - chassis split',
  'Accessorial - rebill',
  'Accessorial - detention (loading)',
  'Accessorial - reclass',
  'Accessorial - detention (unloading)',
  'Accessorial - reconsignment',
  'Accessorial - per diem',
  'Accessorial - redelivery',
  'Accessorial - tri-axle',
  'Accessorial - repossession',
  'Accessorial - residential',
  'Accessorial - reweigh',
  'Accessorial - scale',
  'Accessorial - school',
  'Accessorial - service area surcharge',
  'Accessorial - shunting',
  'Accessorial - single shipment',
  'Accessorial - tow',
  'Accessorial - tracking',
  'Accessorial - transload',
  'Accessorial - weekend',
  'Accessorial - pre-pull',
  'Accessorial - capacity surcharge',
  'Accessorial - shipment restraints',
  'Accessorial - delivery appointment',
  'Accessorial - hazmat',
  'Accessorial - Freight Under Management',
  'Accessorial - Freight Audit Pay',
  'Accessorial - notify before arrival',
  'Accessorial - temperature controlled',
  'Accessorial - after hours',
  'Accessorial - driver count',
  'Accessorial - holiday',
  'Accessorial - pickup appointment',
  'Accessorial - tradeshow',
  'Accessorial - bobtail',
  'Accessorial - empty return',
  'Accessorial - inside delivery',
  'Accessorial - small truck',
  'Accessorial - deadhead',
  'Accessorial - guaranteed',
  'Accessorial - inside pickup',
  'Accessorial - sort and segregate',
  'Accessorial - Customs or In-bond freight charge',
  'Accessorial - Port customs paperwork processing',
  'Accessorial - truck ordered not used',
  'Accessorial - Goods and Services Tax',
  'Accessorial - Heat',
  'Freight - per ton',
  'Freight - per tote',
  'Freight - per unit',
  'Freight - per hour',
  'Freight - per kg',
  'Freight - per kilometer',
  'Freight - Line Haul Surcharge',
  LinehaulCostType,
  'Freight - Minimum Charge',
  'Freight - Item',
  'Freight - per pallet',
  'Freight - per case',
  'Freight - per mile',
  'Freight - per bag',
  'Freight - flat',
  'Freight - per box',
  'Freight - per 50 lbs',
  'Freight - per 100 lbs',
  FuelSurchargeCostType,
  'Fuel - per mile',
  'Fuel - bunker adjustment',
  'Fuel - per kilometer',
  'Fuel - percent of freight',
  'Fuel - percentage',
  'Other - advance fee',
  'Other - canceled shipment',
  'Other - claim',
  'Other - credit card fee',
  'Other - documentation',
  'Other - gate fee',
  'Other - late truck',
  'Other - payment adjustment',
  'Other - product issue',
  'Other',
  'Other - food grade',
  'Other - documentation fees',
  'Other - pre-carriage',
  'Other - on-carriage',
  'Other - IMO surcharge',
  'Other - discount',
  'Total planned cost',
  'Other - payment fee',
  'Other - processing fee',
  'Other - tax',
  'Other - Claim Expense',
  'Service - per kg',
  'Service - per kilometer',
  'Service - flat',
  'Service - per mile',
  'Service - per 100 lbs',
  'Service - per stop',
  'Tax - VAT',
];

// List of unit bases aka in Turvo as Rate Qualifiers, e.g. Per Mile, Per Pound, Per Stop, etc.
export const unitBasisList: string[] = [
  'Flat',
  'Per Mile',
  'Per Hundredweight',
  'Per Ton',
  'Per Pound',
  'Per Hour',
  'Per Stop',
  'Percent',
  'Per Each',
  'Per Piece',
  'Per Pallet',
  'Per Unit',
  'Per Handling Unit',
  'Per Kilogram',
  'Per Kilometer',
  'Minimum',
  'Maximum',
  'Percentage of Charges',
  'Per Litre',
  'Per Gallon',
  'Per Barrel',
  'Per Container',
  'Per Package Charge',
  'Per Vehicle',
  'Per Foot',
  'Per Metric Ton (Tonne)',
];
