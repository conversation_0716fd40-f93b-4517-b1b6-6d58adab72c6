import { JSX } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { ComboboxDropdownMenu } from 'components/Combobox';
import { Label } from 'components/Label';
import { NormalizedLoad } from 'types/Load';
import { cn } from 'utils/shadcn';

interface OperatorSectionFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
  operators: Array<string>;
}

export function OperatorSectionForm({
  formMethods,
  operators,
}: OperatorSectionFormProps): JSX.Element {
  const { watch } = formMethods;
  const watchedOperator = watch('operator');

  return (
    <div className={cn('flex flex-col gap-2.5')}>
      <Label name='operator'>Assigned Operator:</Label>
      <ComboboxDropdownMenu
        selectedOperator={watchedOperator || ''}
        operators={operators}
        setSelectedOperator={(value: string) =>
          formMethods.setValue('operator', value)
        }
        canEdit={true}
      />
    </div>
  );
}
