import { JS<PERSON>, useEffect, useState } from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore framer-motion is in the parent dir
import { AnimatePresence, motion } from 'framer-motion';
import { CheckIcon, EditIcon, TrashIcon, XIcon } from 'lucide-react';

import { ActionButton } from 'components/ActionButton';
import { Label } from 'components/Label';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import {
  costTypesList,
  unitBasisList,
} from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/constants';
import { CostLineItem, NormalizedLoad } from 'types/Load';
import { formatCurrency } from 'utils/formatCurrency';
import { cn } from 'utils/shadcn';

interface ChargeInputCardProps {
  index: number;
  formMethods: UseFormReturn<NormalizedLoad>;
  onRemove: (index: number) => void;
  fieldPath: 'rateData.customerLineItems' | 'rateData.carrierLineItems';
}

const ANIMATIONS = {
  card: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  toggle: {
    initial: { opacity: 0, height: 0 },
    animate: { opacity: 1, height: 'auto' },
    exit: { opacity: 0, height: 0 },
    transition: { duration: 0.15 },
  },
  actions: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
    transition: { duration: 0.1 },
  },
};

export function ChargeInputCard({
  index,
  formMethods,
  onRemove,
  fieldPath,
}: ChargeInputCardProps): JSX.Element {
  const {
    control,
    watch,
    setValue,
    formState: { errors },
    trigger,
  } = formMethods;
  const [isEditing, setIsEditing] = useState(false);
  const [originalValues, setOriginalValues] = useState<CostLineItem | null>(
    null
  );

  const watchedCharge = watch(`${fieldPath}.${index}`) as CostLineItem;
  const watchedQuantity = watch(`${fieldPath}.${index}.quantity`);
  const watchedRatePerUnit = watch(`${fieldPath}.${index}.ratePerUnitUSD`);

  // Watch the Type field to determine if Rate Qualifier should be shown
  const watchedType = useWatch({
    control,
    name: `${fieldPath}.${index}.label`,
  });

  // Define keywords that indicate built-in rate qualifiers
  const perUnitKeywords = [
    'per mile',
    'per 100 lbs',
    'per kilometer',
    'per ton',
    'per tote',
    'per unit',
    'per hour',
    'per kg',
    'per pallet',
    'per case',
    'per bag',
    'per box',
    'per 50 lbs',
  ];

  // Determine if Rate Qualifier field should be shown
  // Hide Rate Qualifier for types that already have built-in rate qualifiers
  const shouldShowRateQualifier = !perUnitKeywords.some((keyword) =>
    watchedType?.toLowerCase().includes(keyword)
  );

  // Calculate total charge when quantity or rate changes
  useEffect(() => {
    const quantity = watchedQuantity || 0;
    const ratePerUnit = watchedRatePerUnit || 0;
    const calculatedTotal = quantity * ratePerUnit;

    if (calculatedTotal !== watchedCharge?.totalChargeUSD) {
      setValue(`${fieldPath}.${index}.totalChargeUSD`, calculatedTotal, {
        shouldDirty: true,
        shouldValidate: true,
      });
    }
  }, [
    watchedQuantity,
    watchedRatePerUnit,
    watchedCharge?.totalChargeUSD,
    setValue,
    index,
  ]);

  // Clear Rate Qualifier field when it should be hidden
  useEffect(() => {
    if (!shouldShowRateQualifier && watchedCharge?.unitBasis) {
      setValue(`${fieldPath}.${index}.unitBasis`, '', {
        shouldDirty: true,
      });
    }
  }, [
    shouldShowRateQualifier,
    watchedCharge?.unitBasis,
    setValue,
    fieldPath,
    index,
  ]);

  const isEmptyCharge = !watchedCharge?.label && !watchedCharge?.unitBasis;

  // Start in editing mode if this is an empty charge
  useEffect(() => {
    if (isEmptyCharge && !isEditing) {
      setIsEditing(true);
    }
  }, [isEmptyCharge, isEditing]);

  // Use effect to default quantity to 1 if type contains "flat"
  useEffect(() => {
    if (watchedCharge?.label?.toLowerCase().includes('flat')) {
      setValue(`${fieldPath}.${index}.quantity`, 1);
      setValue(`${fieldPath}.${index}.unitBasis`, 'Flat');
    }
  }, [watchedCharge?.label, setValue, index, fieldPath]);

  // Handlers for the edit, save, discard, and remove charge actions
  const handlers = {
    edit: () => {
      setOriginalValues({ ...watchedCharge });
      setIsEditing(true);
    },
    save: async () => {
      const isValid = await trigger(`${fieldPath}.${index}`);
      if (!isValid) {
        // Show error, don't collapse
        return;
      }
      setIsEditing(false);
      setOriginalValues(null);
    },
    discard: () => {
      if (originalValues) {
        setValue(`${fieldPath}.${index}`, originalValues);
      }
      setIsEditing(false);
      setOriginalValues(null);
    },
    remove: () => onRemove(index),
  };

  // Render the actions for the charge input card
  // If the charge is being edited, show the save, and remove buttons
  // If the charge is not being edited, show the edit and remove buttons
  const renderActions = () => (
    <AnimatePresence mode='wait'>
      {isEditing ? (
        <motion.div
          key='editing-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1.5'
        >
          {!isEmptyCharge ? (
            <>
              <ActionButton
                onClick={handlers.save}
                className='hover:bg-info-200 text-info-600'
                icon={CheckIcon}
                aria-label='Save changes'
              />
              <ActionButton
                onClick={handlers.discard}
                className='hover:bg-error-200 text-error-600'
                icon={XIcon}
                aria-label='Discard changes'
              />
            </>
          ) : (
            <ActionButton
              onClick={handlers.remove}
              className='hover:bg-error-300 text-error-600'
              icon={TrashIcon}
              aria-label='Remove charge'
            />
          )}
        </motion.div>
      ) : (
        <motion.div
          key='view-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1.5'
        >
          <ActionButton
            onClick={handlers.edit}
            className='hover:bg-info-300 text-info-600'
            icon={EditIcon}
            aria-label='Edit charge'
          />
          <ActionButton
            onClick={handlers.remove}
            className='hover:bg-error-400 text-error-600'
            icon={TrashIcon}
            aria-label='Remove charge'
          />
        </motion.div>
      )}
    </AnimatePresence>
  );

  // Handle Enter key contextually
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      const target = e.target as HTMLElement;

      // 1. If in Antd Select, let it handle Enter (for option selection)
      if (
        target.closest('.ant-select') ||
        target.closest('.ant-select-dropdown') ||
        target.classList.contains('ant-select-search__field') ||
        target.closest('.ant-select-selection-search')
      ) {
        // Let Antd Select handle Enter for option selection
        return;
      }

      // 2. If hitting enter in the editing form, save changes and collapse form
      if (isEditing) {
        e.preventDefault();
        e.stopPropagation();
        handlers.save();
      } else {
        return;
      }
    }
  };

  // Render the editing form for the charge input card
  const renderEditingForm = () => (
    <motion.div
      key='editing-form'
      {...ANIMATIONS.toggle}
      className='overflow-hidden'
    >
      {/* Top header with charge number and actions */}
      <Flex justify='between' align='center' className='mb-2'>
        <Flex align='center' gap='md'>
          <span className='text-xs font-semibold text-neutral-600'>
            Charge {index + 1}
          </span>
        </Flex>

        {/* Action buttons */}
        <div onClick={(e) => e.stopPropagation()}>{renderActions()}</div>
      </Flex>

      <Grid cols='2' gap='sm' className='mx-0 w-full'>
        <div className='col-span-2'>
          <Label name={`${fieldPath}.${index}.label`} className='text-xs'>
            Type
          </Label>
          <Controller
            name={`${fieldPath}.${index}.label`}
            control={control}
            rules={{ required: true }}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={costTypesList?.map((type) => ({
                  value: type,
                  label: type,
                }))}
              />
            )}
          />
          <ErrorMessage
            errors={errors}
            name={`${fieldPath}.${index}.label`}
            render={({ message }: { message: string }) => (
              <Typography variant='body-xs' className='text-error-500'>
                {message}
              </Typography>
            )}
          />
        </div>

        {shouldShowRateQualifier && (
          <div className='col-span-1'>
            <Label name={`${fieldPath}.${index}.unitBasis`} className='text-xs'>
              Rate Qualifier
            </Label>
            <Controller
              name={`${fieldPath}.${index}.unitBasis`}
              control={control}
              render={({ field }) => (
                <AntdSelect
                  showSearch
                  className='h-9 text-neutral-500'
                  placeholder={'Choose'}
                  optionFilterProp='children'
                  filterOption={(
                    input: string,
                    option: BaseOptionType | undefined
                  ) =>
                    (option?.label.toLocaleLowerCase() ?? '').includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(
                    optionA: BaseOptionType,
                    optionB: BaseOptionType
                  ) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  onChange={field.onChange}
                  value={field.value}
                  options={unitBasisList?.map((basis) => ({
                    value: basis,
                    label: basis,
                  }))}
                />
              )}
            />
            <ErrorMessage
              errors={errors}
              name={`${fieldPath}.${index}.unitBasis`}
              render={({ message }: { message: string }) => (
                <Typography variant='body-xs' className='text-error-500'>
                  {message}
                </Typography>
              )}
            />
          </div>
        )}

        {/* NOTE: Based on Turvo UI and real load data, 0 is a valid value for quantity, ratePerUnitUSD and totalChargeUSD */}
        <div className='col-span-2'>
          <Flex align='end' gap='sm' className='mb-1'>
            <div className='flex-1'>
              <RHFTextInput
                name={`${fieldPath}.${index}.quantity`}
                labelClassName='text-xs'
                label='Quantity'
                inputType='number'
                options={{ valueAsNumber: true, min: 0 }}
                required={true}
              />
            </div>

            <Flex align='center' className='mb-6'>
              <Typography variant='h6' className='text-neutral-400 font-bold'>
                ×
              </Typography>
            </Flex>

            <div className='flex-1'>
              <RHFTextInput
                name={`${fieldPath}.${index}.ratePerUnitUSD`}
                labelClassName='text-xs'
                label='Rate per Unit (USD)'
                inputType='number'
                options={{ valueAsNumber: true, min: 0 }}
                required={true}
              />
            </div>
          </Flex>
        </div>

        <div className='col-span-1'>
          <Flex align='center' gap='xs' className='mb-1'>
            <Typography
              variant='body-xs'
              className='text-neutral-600 font-medium'
            >
              Total Charge (USD)
            </Typography>
            <Typography variant='body-xs' className='text-neutral-400'>
              =
            </Typography>
          </Flex>
          <RHFTextInput
            name={`${fieldPath}.${index}.totalChargeUSD`}
            labelClassName='text-xs'
            label=''
            inputType='number'
            options={{ valueAsNumber: true, min: 0 }}
            required={false}
            readOnly={true}
            inputClassName='no-spin-buttons'
          />
        </div>

        <div className='col-span-2'>
          <RHFTextInput
            name={`${fieldPath}.${index}.note`}
            labelClassName='text-xs'
            label='Note'
            placeholder='Additional details...'
            required={false}
          />
        </div>
      </Grid>
    </motion.div>
  );

  // Render the view mode/collapsed mode for the charge input card which
  const renderViewMode = () => {
    if (!watchedCharge?.label) return null;

    return (
      <motion.div
        key='view-mode'
        {...ANIMATIONS.toggle}
        className='overflow-hidden cursor-pointer'
        onClick={handlers.edit}
        onKeyDown={(e: React.KeyboardEvent) =>
          e.key === 'Enter' && handlers.edit()
        }
        role='button'
        tabIndex={0}
        aria-label='Click to edit charge'
      >
        <Flex justify='between' align='center' className='min-w-0'>
          <Flex align='center' gap='md' className='flex-1 min-w-0 mr-2'>
            <Typography
              variant='caption'
              className='text-neutral-600 font-medium truncate'
              title={watchedCharge.label}
              aria-label={watchedCharge.label}
            >
              {watchedCharge.label}
            </Typography>
          </Flex>

          {/* Action buttons */}
          <Flex onClick={(e) => e.stopPropagation()} className='flex-shrink-0'>
            {renderActions()}
          </Flex>
        </Flex>

        <Flex align='center' gap='sm' className='text-sm text-neutral-900'>
          <Typography variant='body-xs' className='text-neutral-500'>
            Qty:
          </Typography>
          <Typography variant='body-xs' className='text-neutral-600'>
            {watchedCharge.quantity}
          </Typography>
          <Typography variant='body-xs' className='text-neutral-500'>
            •
          </Typography>
          <Typography
            variant='body-xs'
            className='font-semibold text-brand-main'
          >
            {formatCurrency(watchedCharge.totalChargeUSD, 'USD')}
          </Typography>
        </Flex>
        {watchedCharge.note && (
          <Typography variant='body-xs' className='text-neutral-500 mt-1'>
            {watchedCharge.note}
          </Typography>
        )}
      </motion.div>
    );
  };

  return (
    <motion.div
      layout
      transition={{ type: 'spring', duration: 0.2, bounce: 0 }}
      {...ANIMATIONS.card}
      className={cn(
        'overflow-hidden rounded-[4px] border bg-linear-to-br shadow-sm relative transition-all duration-200 p-2.5',
        isEmptyCharge
          ? 'border-neutral-400 bg-linear-to-br from-neutral-50 to-neutral-100/50'
          : 'border-neutral-500 bg-linear-to-br from-neutral-50 to-info-50/50'
      )}
      onKeyDown={handleKeyDown}
    >
      <AnimatePresence mode='wait'>
        {isEditing ? renderEditingForm() : renderViewMode()}
      </AnimatePresence>
    </motion.div>
  );
}
