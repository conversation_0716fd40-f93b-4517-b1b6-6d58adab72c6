import { JSX, useEffect } from 'react';
import { Controller, FieldArrayWithId, UseFormReturn } from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { AddActionButton } from 'components/AddActionButton';
import { Label } from 'components/Label';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { LoadBuildingTextInput } from 'pages/QuoteView/LoadBuilding/McleodLoadBuildingForm';
import { ChargeInputCard } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/components/CostItemInputCard';
import { currencyList } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/constants';
import { CostLineItem, NormalizedLoad } from 'types/Load';
import { cn } from 'utils/shadcn';

interface LineItemSectionProps {
  title: string;
  formMethods: UseFormReturn<NormalizedLoad>;
  lineItemsFieldPath:
    | 'rateData.customerLineItems'
    | 'rateData.carrierLineItems';
  totalFieldPath: 'rateData.customerTotalCharge' | 'rateData.carrierTotalCost';
  fields: FieldArrayWithId<NormalizedLoad, any, 'id'>[];
  onAddCharge: () => void;
  onRemoveCharge: (index: number) => void;
  watchedLineItems: CostLineItem[];
  watchedTotal: number;
}

export function LineItemSection({
  title,
  formMethods,
  lineItemsFieldPath,
  totalFieldPath,
  fields,
  onAddCharge,
  onRemoveCharge,
  watchedLineItems,
  watchedTotal,
}: LineItemSectionProps): JSX.Element {
  const { control, setValue } = formMethods;

  // Calculate total line items charge
  const totalLineItems =
    watchedLineItems?.reduce(
      (sum, charge) => sum + (charge.totalChargeUSD || 0),
      0
    ) || 0;

  // Auto-calculate the total charge
  useEffect(() => {
    if (totalLineItems !== watchedTotal) {
      setValue(`${totalFieldPath}.val`, totalLineItems, {
        shouldDirty: true,
        shouldValidate: true,
      });
    }
  }, [totalLineItems, watchedTotal, setValue, totalFieldPath]);

  return (
    <>
      {/* Section Title */}
      <Typography
        variant='h6'
        className='col-span-1 text-md text-neutral-800 font-semibold mb-1'
      >
        {title}
      </Typography>

      {/* Line Items Section */}
      <div
        className={cn(
          fields.length > 0 && 'space-y-2',
          fields.length === 0 && 'hidden'
        )}
      >
        {fields.map((field, index) => (
          <ChargeInputCard
            key={field.id}
            index={index}
            formMethods={formMethods}
            onRemove={onRemoveCharge}
            fieldPath={lineItemsFieldPath}
          />
        ))}
      </div>

      <AddActionButton
        onClick={onAddCharge}
        buttonText='Add Charge'
        descriptionText='Add charges like freight, fuel, and accessorials.'
        buttonNamePosthog='add_line_item'
      />

      <Grid cols='2' gap='sm' className='mx-0 w-full'>
        <LoadBuildingTextInput
          name={`${totalFieldPath}.val`}
          label={`Total Amount`}
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
          readOnly={true}
          inputClassName='no-spin-buttons'
        />

        <Flex direction='col' className='col-span-1'>
          <Label name={`${totalFieldPath}.unit`} required={false}>
            Currency
          </Label>
          <Controller
            name={`${totalFieldPath}.unit`}
            control={control}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={currencyList?.map((currency) => ({
                  value: currency,
                  label: currency,
                }))}
              />
            )}
          />
        </Flex>
      </Grid>
    </>
  );
}
