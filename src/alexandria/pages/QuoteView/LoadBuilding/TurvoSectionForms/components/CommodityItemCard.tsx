import { JSX, useEffect, useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore framer-motion is in the parent dir
import { AnimatePresence, motion } from 'framer-motion';
import { CheckIcon, EditIcon, TrashIcon, XIcon } from 'lucide-react';
import { Box } from 'lucide-react';

import { ActionButton } from 'components/ActionButton';
import { Label } from 'components/Label';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import {
  unitsList,
  weightUnitsList,
} from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/constants';
import { NormalizedLoad } from 'types/Load';
import { cn } from 'utils/shadcn';

interface CommodityItemCardProps {
  index: number;
  formMethods: UseFormReturn<NormalizedLoad>;
  onRemove: (index: number) => void;
  fieldPath?: 'commodities';
}

const ANIMATIONS = {
  card: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
  },
  toggle: {
    initial: { opacity: 0, height: 0 },
    animate: { opacity: 1, height: 'auto' },
    exit: { opacity: 0, height: 0 },
    transition: { duration: 0.15 },
  },
  actions: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
    transition: { duration: 0.1 },
  },
};

export function CommodityItemCard({
  index,
  formMethods,
  onRemove,
  fieldPath = 'commodities',
}: CommodityItemCardProps): JSX.Element {
  const { control, watch } = formMethods;
  const [isEditing, setIsEditing] = useState(false);
  const [originalValues, setOriginalValues] = useState<Partial<
    NormalizedLoad['commodities'][0]
  > | null>(null);

  const watchedItem = watch(`${fieldPath}.${index}`);
  const isEmptyItem = !watchedItem?.description;

  // Start in editing mode if this is an empty item
  useEffect(() => {
    if (isEmptyItem && !isEditing) {
      setIsEditing(true);
    }
  }, [isEmptyItem, isEditing]);

  // Handlers for the edit, save, discard, and remove item actions
  const handlers = {
    edit: () => {
      setOriginalValues({ ...watchedItem });
      setIsEditing(true);
    },
    save: () => {
      setIsEditing(false);
      setOriginalValues(null);
    },
    discard: () => {
      if (originalValues) {
        Object.keys(originalValues).forEach((key) => {
          const fieldName = `${fieldPath}.${index}.${key}` as any;
          const value = originalValues[key as keyof typeof originalValues];
          if (value !== undefined) {
            formMethods.setValue(fieldName, value, { shouldValidate: true });
          }
        });
      }
      setIsEditing(false);
      setOriginalValues(null);
    },
    remove: () => onRemove(index),
  };

  // Render the actions for the commodity input card
  const renderActions = () => (
    <AnimatePresence mode='wait'>
      {isEditing ? (
        <motion.div
          key='editing-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1.5'
        >
          {!isEmptyItem ? (
            <>
              <ActionButton
                onClick={handlers.save}
                className='hover:bg-info-200 text-info-600'
                icon={CheckIcon}
                aria-label='Save changes'
              />
              <ActionButton
                onClick={handlers.discard}
                className='hover:bg-error-200 text-error-600'
                icon={XIcon}
                aria-label='Discard changes'
              />
            </>
          ) : (
            <ActionButton
              onClick={handlers.remove}
              className='hover:bg-error-300 text-error-600'
              icon={TrashIcon}
              aria-label='Remove item'
            />
          )}
        </motion.div>
      ) : (
        <motion.div
          key='view-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1.5'
        >
          <ActionButton
            onClick={handlers.edit}
            className='hover:bg-info-300 text-info-600'
            icon={EditIcon}
            aria-label='Edit item'
          />
          <ActionButton
            onClick={handlers.remove}
            className='hover:bg-error-400 text-error-600'
            icon={TrashIcon}
            aria-label='Remove item'
          />
        </motion.div>
      )}
    </AnimatePresence>
  );

  // Handle Enter key contextually
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      // If hitting enter in the editing form, save changes and collapse form
      if (isEditing) {
        e.preventDefault();
        e.stopPropagation();
        handlers.save();
      } else {
        return;
      }
    }
  };

  // Render the editing form for the commodity input card
  const renderEditingForm = () => (
    <motion.div
      key='editing-form'
      {...ANIMATIONS.toggle}
      className='overflow-hidden'
    >
      {/* Top header with item number and actions */}
      <Flex justify='between' align='center' className='mb-2'>
        <Flex align='center' gap='sm'>
          <Box className='h-6 w-6' />
          <Typography
            variant='caption'
            className='text-neutral-600 font-semibold'
          >
            {index + 1}
          </Typography>
        </Flex>

        {/* Action buttons */}
        <div onClick={(e) => e.stopPropagation()}>{renderActions()}</div>
      </Flex>

      <div className='mx-0 w-full mb-2'>
        <RHFTextInput
          name={`${fieldPath}.${index}.description`}
          label='Item Name'
          required={false}
        />
      </div>

      <Grid cols='2' gap='sm' className='mx-0 w-full'>
        <div className='col-span-1'>
          <RHFTextInput
            name={`${fieldPath}.${index}.totalPieces.val`}
            label='Quantity'
            inputType='number'
            options={{ valueAsNumber: true, min: 0 }}
            required={false}
          />
        </div>

        <div className='col-span-1'>
          <Label
            name={`${fieldPath}.${index}.totalPieces.unit`}
            required={false}
          >
            Units
          </Label>
          <Controller
            name={`${fieldPath}.${index}.totalPieces.unit`}
            control={control}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={unitsList?.map((unit) => ({
                  value: unit,
                  label: unit,
                }))}
              />
            )}
          />
        </div>
      </Grid>

      <Grid cols='2' gap='sm' className='mx-0 w-full mt-2'>
        <div className='col-span-1'>
          <RHFTextInput
            name={`${fieldPath}.${index}.grossWeight.val`}
            label='Gross Weight'
            inputType='number'
            options={{ valueAsNumber: true, min: 0 }}
            required={false}
          />
        </div>

        <div className='col-span-1'>
          <Label
            name={`${fieldPath}.${index}.grossWeight.unit`}
            required={false}
          >
            Units
          </Label>
          <Controller
            name={`${fieldPath}.${index}.grossWeight.unit`}
            control={control}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={weightUnitsList?.map((unit) => ({
                  value: unit,
                  label: unit,
                }))}
              />
            )}
          />
        </div>
      </Grid>

      <Grid cols='2' gap='sm' className='mx-0 w-full mt-2'>
        <div className='col-span-1'>
          <RHFTextInput
            name={`${fieldPath}.${index}.netWeight.val`}
            label='Net Weight'
            inputType='number'
            options={{ valueAsNumber: true, min: 0 }}
            required={false}
          />
        </div>

        <div className='col-span-1'>
          <Label name={`${fieldPath}.${index}.netWeight.unit`} required={false}>
            Units
          </Label>
          <Controller
            name={`${fieldPath}.${index}.netWeight.unit`}
            control={control}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={weightUnitsList?.map((unit) => ({
                  value: unit,
                  label: unit,
                }))}
              />
            )}
          />
        </div>
      </Grid>
    </motion.div>
  );

  // Render the view mode/collapsed mode for the commodity input card
  const renderViewMode = () => {
    if (!watchedItem?.description) return null;

    return (
      <motion.div
        key='view-mode'
        {...ANIMATIONS.toggle}
        className='overflow-hidden cursor-pointer'
        onClick={handlers.edit}
        onKeyDown={(e: React.KeyboardEvent) =>
          e.key === 'Enter' && handlers.edit()
        }
        role='button'
        tabIndex={0}
        aria-label='Click to edit item'
      >
        <Flex justify='between' align='center' className='min-w-0'>
          <Flex align='center' gap='md' className='flex-1 min-w-0 mr-2'>
            <Box className='h-6 w-6' />
            <Typography
              variant='caption'
              className='text-neutral-600 font-medium truncate'
              title={watchedItem.description}
              aria-label={watchedItem.description}
            >
              {watchedItem.description}
            </Typography>
          </Flex>

          {/* Action buttons */}
          <Flex onClick={(e) => e.stopPropagation()} className='flex-shrink-0'>
            {renderActions()}
          </Flex>
        </Flex>

        <Flex align='center' gap='sm' className='text-sm text-neutral-900'>
          <Typography variant='body-xs' className='text-neutral-500'>
            Qty:
          </Typography>
          <Typography variant='body-xs' className='text-neutral-600'>
            {watchedItem.totalPieces?.val || 0}
          </Typography>
          {watchedItem.totalPieces?.unit && (
            <>
              <Typography variant='body-xs' className='text-neutral-500'>
                •
              </Typography>
              <Typography variant='body-xs' className='text-neutral-500'>
                {watchedItem.totalPieces.unit}
              </Typography>
            </>
          )}
          {(watchedItem.grossWeight?.val || watchedItem.netWeight?.val) && (
            <>
              <Typography variant='body-xs' className='text-neutral-500'>
                •
              </Typography>
              <Typography variant='body-xs' className='text-neutral-500'>
                Weight:{' '}
                {watchedItem.grossWeight?.val || watchedItem.netWeight?.val}{' '}
                {watchedItem.grossWeight?.unit ||
                  watchedItem.netWeight?.unit ||
                  'lbs'}
              </Typography>
            </>
          )}
        </Flex>
      </motion.div>
    );
  };

  return (
    <motion.div
      layout
      transition={{ type: 'spring', duration: 0.2, bounce: 0 }}
      {...ANIMATIONS.card}
      className={cn(
        'overflow-hidden rounded-[4px] border bg-linear-to-br shadow-sm relative transition-all duration-200 p-2.5',
        isEmptyItem
          ? 'border-neutral-400 bg-linear-to-br from-neutral-50 to-neutral-100/50'
          : 'border-neutral-500 bg-linear-to-br from-neutral-50 to-info-50/50'
      )}
      onKeyDown={handleKeyDown}
    >
      <AnimatePresence mode='wait'>
        {isEditing ? renderEditingForm() : renderViewMode()}
      </AnimatePresence>
    </motion.div>
  );
}
