import { JSX } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { Label } from 'components/Label';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { TMSCarrier } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import {
  carrierSearchHandler,
  mapCarriersToAntdOptions,
} from 'utils/loadInfoAndBuilding';

interface CarrierSectionFormProps {
  formMethods: UseFormReturn<any>;
  isLoadingCarriers: boolean;
  carriers: Maybe<TMSCarrier[]>;
  setCarriers: React.Dispatch<React.SetStateAction<Maybe<TMSCarrier[]>>>;
  tmsIntegrations: any[];
}

export function CarrierSectionForm({
  formMethods,
  isLoadingCarriers,
  carriers,
  setCarriers,
  tmsIntegrations,
}: CarrierSectionFormProps): JSX.Element {
  const {
    control,
    formState: { errors },
  } = formMethods;

  return (
    <>
      <Label name='carrier'>Carrier:</Label>
      <RHFDebounceSelect
        name='carrier'
        label='Carrier'
        control={control}
        errors={errors}
        data={carriers}
        mapOptions={mapCarriersToAntdOptions}
        fetchOptions={(field, value) =>
          carrierSearchHandler({
            tmsID: tmsIntegrations[0]?.id || 0,
            carriers,
            setCarriers,
            field,
            value,
          })
        }
        refreshHandler={() => {}}
        isLoading={isLoadingCarriers}
        disabled={true} // Make carrier read-only in edit mode
        rules={{
          required: false, // Remove required validation for read-only field
        }}
      />
    </>
  );
}
