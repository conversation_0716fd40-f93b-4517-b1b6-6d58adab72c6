import { useContext, useEffect, useState } from 'react';
import {
  Controller,
  UseFormReturn,
  useFieldArray,
  useWatch,
} from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore framer-motion is in the parent dir
import { AnimatePresence } from 'framer-motion';

import { AddActionButton } from 'components/AddActionButton';
import { Label } from 'components/Label';
import { Input } from 'components/input/Input';
import { TemperatureUnitSelector } from 'components/input/TemperatureUnitSelector';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { CommodityItemCard } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/components/CommodityItemCard';
import { NormalizedLoad, createNewCommodity } from 'types/Load';
import { Unit } from 'types/Load';
import { TemperatureUnit } from 'types/enums/TemperatureUnit';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';

import {
  LoadBuildingTextInput,
  devDisableRequiredFields,
} from '../McleodLoadBuildingForm';
import {
  equipmentList,
  equipmentSizeList,
  serviceTypes,
  weightUnitsList,
} from './constants';

export function SpecificationsForm({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}) {
  const {
    control,
    formState: { errors },
    setValue,
    watch,
    getValues,
  } = formMethods;

  const modes = [
    'TL',
    'LTL',
    'Drayage',
    'Ocean',
    'Air',
    'Rail',
    'Parcel',
    'Intermodal',
  ];

  const {
    currentState: { clickedSuggestion },
  } = useContext(SidebarStateContext);

  const [tempUnit, setTempUnit] = useState<TemperatureUnit>(
    TemperatureUnit.Fahrenheit
  );
  const [tempDisplayValue, setTempDisplayValue] = useState<string>('');
  const [isTempInputFocused, setIsTempInputFocused] = useState<boolean>(false);

  const formatDisplayTemp = (fahrenheit: number | null | undefined) => {
    if (fahrenheit === undefined || fahrenheit === null) return '';
    if (tempUnit === 'F') return fahrenheit.toString();
    return (((fahrenheit - 32) * 5) / 9).toFixed(1);
  };

  const parseInputToFahrenheit = (value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return null;
    if (tempUnit === 'F') return numValue;
    return (numValue * 9) / 5 + 32;
  };

  // When the unit changes, we need to update the form value to reflect the new unit.
  useEffect(() => {
    const currentFahrenheit = getValues('specifications.minTempFahrenheit');
    setValue('specifications.minTempFahrenheit', currentFahrenheit, {
      shouldDirty: true,
    });
  }, [tempUnit, getValues, setValue]);

  const isCommoditiesSupported = true;
  const watchedTotalWeight = watch('specifications.totalWeight.val');
  const watchedWeightUnit = watch('specifications.totalWeight.unit');
  const watchedTransportType = useWatch({
    control,
    name: 'specifications.transportType',
  });

  useEffect(() => {
    if (watchedTotalWeight > 0 && !watchedWeightUnit) {
      setValue('specifications.totalWeight.unit', Unit.Pound, {
        shouldDirty: true,
      });
    }
  }, [watchedTotalWeight, watchedWeightUnit, setValue]);

  const isRefrigerated =
    watchedTransportType?.toLowerCase().includes('reefer') ||
    watchedTransportType?.toLowerCase().includes('refrigerated');

  // Watch for the min temp to be set
  const watchedMinTemp = watch('specifications.minTempFahrenheit');

  useEffect(() => {
    if (!isTempInputFocused) {
      setTempDisplayValue(formatDisplayTemp(watchedMinTemp));
    }
  }, [watchedMinTemp, isTempInputFocused, tempUnit]);

  useEffect(() => {
    if (
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      return;
    }

    setValue('specifications.minTempFahrenheit', null, { shouldDirty: true });
    setValue('specifications.maxTempFahrenheit', null, { shouldDirty: true });
  }, [setValue, watchedTransportType, clickedSuggestion]);

  // When minTempFahrenheit changes, update maxTempFahrenheit to match
  useEffect(() => {
    if (watchedMinTemp !== undefined) {
      setValue('specifications.maxTempFahrenheit', watchedMinTemp);
    }
  }, [watchedMinTemp, setValue]);

  return (
    <Grid cols='4' gap='sm' className='mx-0 w-full'>
      {/* Only Truckloads supported rn, not LTL */}
      <div className='col-span-4'>
        <Label hideAIHint={true} name={'mode'} required={true}>
          Mode
        </Label>
        {/* <div className='flex flex-row w-full items-center gap-2'> */}
        <Controller
          name='mode'
          control={control}
          rules={{ required: devDisableRequiredFields ? false : 'Required' }}
          render={({ field }) => (
            <div className='text-neutral-500'>
              <AntdSelect
                showSearch
                disabled={false} // Only Truckloads supported rn, not LTL
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={modes?.map((mode) => ({
                  value: mode,
                  label: mode,
                }))}
              />
            </div>
          )}
        />
        {/* </div> */}
        <ErrorMessage
          errors={errors}
          name={'mode'}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>
      <div className='col-span-4'>
        <Label
          name={'serviceType'}
          // We always default this value so don't show AI hint unless there's a suggestion
          hideAIHint={
            !clickedSuggestion ||
            clickedSuggestion.pipeline !== SuggestionPipelines.LoadBuilding
          }
          required={true}
        >
          Service Type
        </Label>
        <Controller
          name='specifications.serviceType'
          control={control}
          rules={{ required: devDisableRequiredFields ? false : 'Required' }}
          render={({ field }) => (
            <div className='text-neutral-500'>
              <AntdSelect
                showSearch
                disabled={false}
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={serviceTypes?.map((serviceType) => ({
                  value: serviceType,
                  label: serviceType,
                }))}
              />
            </div>
          )}
        />
        <ErrorMessage
          errors={errors}
          name={'specifications.serviceType'}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>
      <div className='col-span-2'>
        <Label
          name={'specifications.transportType'}
          // We always default this value so don't show AI hint unless there's a suggestion
          hideAIHint={
            !clickedSuggestion ||
            clickedSuggestion.pipeline !== SuggestionPipelines.LoadBuilding
          }
          required={true}
        >
          Equipment
        </Label>
        <Controller
          name='specifications.transportType'
          control={control}
          rules={{ required: true }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-neutral-500'
              placeholder={'Choose'}
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={equipmentList?.map((type) => ({
                value: type,
                label: type,
              }))}
            />
          )}
        />
        <ErrorMessage
          errors={errors}
          name={'specifications.transportType'}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>
      <div className='col-span-2'>
        <Label name={'specifications.transportSize'} required={false}>
          Size
        </Label>
        <Controller
          name='specifications.transportSize'
          control={control}
          rules={{ required: false }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-neutral-500'
              placeholder={'Choose'}
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={equipmentSizeList?.map((size) => ({
                value: size,
                label: size,
              }))}
            />
          )}
        />
      </div>

      <Grid cols='2' gap='sm' className='mx-0 w-full col-span-4'>
        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='specifications.totalWeight.val'
            label={`Equipment Weight`}
            inputType='number'
            options={{ valueAsNumber: true, min: 0 }}
            required={false}
            labelClassName='whitespace-nowrap'
          />
        </div>

        <Flex direction='col' className='col-span-1'>
          <Label
            name={'specifications.totalWeight.unit'}
            required={watchedTotalWeight > 0}
          >
            Units
          </Label>
          <Controller
            name='specifications.totalWeight.unit'
            control={control}
            rules={{ required: watchedTotalWeight > 0 }}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={weightUnitsList?.map((unit) => ({
                  value: unit,
                  label: unit,
                }))}
              />
            )}
          />
        </Flex>
      </Grid>

      <div className='col-span-4'>
        <LoadBuildingTextInput
          name='specifications.commodities'
          label='Description'
          required={false}
        />
      </div>

      {isRefrigerated && (
        <>
          <div className='col-span-4'>
            <Label name='temperature' required={isRefrigerated}>
              Temperature
            </Label>
            <div className='relative flex items-center'>
              <Controller
                name='specifications.minTempFahrenheit'
                control={control}
                rules={{
                  required: isRefrigerated
                    ? 'Temperature is required for refrigerated equipment'
                    : false,
                }}
                render={({ field }) => (
                  <div className='flex flex-row w-full items-center gap-2'>
                    <Input
                      {...field}
                      disabled={!isRefrigerated}
                      placeholder={isRefrigerated ? 'e.g. 32' : 'N/A'}
                      value={tempDisplayValue}
                      onChange={(e) => setTempDisplayValue(e.target.value)}
                      onFocus={() => setIsTempInputFocused(true)}
                      onBlur={() => {
                        setIsTempInputFocused(false);
                        const fahrenheit =
                          parseInputToFahrenheit(tempDisplayValue);
                        field.onChange(fahrenheit);
                      }}
                    />
                    <TemperatureUnitSelector
                      value={tempUnit}
                      onChange={(newUnit) => setTempUnit(newUnit)}
                    />
                  </div>
                )}
              />
            </div>
            <ErrorMessage
              errors={errors}
              name={'specifications.minTempFahrenheit'}
              render={({ message }: { message: string }) => (
                <p className='text-error-500 text-xs'>{message}</p>
              )}
            />
          </div>
        </>
      )}

      {isCommoditiesSupported && (
        <CommoditiesFieldArray formMethods={formMethods} />
      )}
    </Grid>
  );
}

function CommoditiesFieldArray({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}) {
  const { control } = formMethods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'commodities',
  });

  const addCommodity = () => append(createNewCommodity());

  return (
    <div className='col-span-4'>
      <AddActionButton
        onClick={addCommodity}
        buttonText='Add Item'
        descriptionText='Add item(s) for the shipment'
        buttonNamePosthog='add_commodity_item'
        className='mb-2'
      />

      <AnimatePresence>
        <div className={fields.length > 0 ? 'space-y-2' : ''}>
          {fields.map((field, index) => (
            <CommodityItemCard
              key={field.id}
              index={index}
              formMethods={formMethods}
              onRemove={remove}
            />
          ))}
        </div>
      </AnimatePresence>
    </div>
  );
}
