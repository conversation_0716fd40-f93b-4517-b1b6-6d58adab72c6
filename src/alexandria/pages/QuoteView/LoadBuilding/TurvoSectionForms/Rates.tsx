import { JSX } from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore framer-motion is in the parent dir

import { LineItemSection } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/components/LineItemSection';
import { CostLineItem, NormalizedLoad } from 'types/Load';

export function RatesForm({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}): JSX.Element {
  const { control, watch } = formMethods;

  const {
    fields: customerLineItems,
    append: customerLineItemsAppend,
    remove: customerLineItemsRemove,
  } = useFieldArray({
    control,
    name: 'rateData.customerLineItems',
  });

  const watchedCustomerTotal = watch('rateData.customerTotalCharge.val');
  const watchedCustomerLineItems = watch('rateData.customerLineItems');

  const createNewCharge = (): CostLineItem => ({
    label: '',
    unitBasis: '',
    quantity: 1,
    ratePerUnitUSD: 0,
    totalChargeUSD: 0,
    note: '',
  });

  const addCustomerCharge = () => customerLineItemsAppend(createNewCharge());

  return (
    <>
      <LineItemSection
        title='Customer'
        formMethods={formMethods}
        lineItemsFieldPath='rateData.customerLineItems'
        totalFieldPath='rateData.customerTotalCharge'
        fields={customerLineItems}
        onAddCharge={addCustomerCharge}
        onRemoveCharge={customerLineItemsRemove}
        watchedLineItems={watchedCustomerLineItems}
        watchedTotal={watchedCustomerTotal}
      />

      {/* Carrier line items section is not supported yet, possible only when carrier assigned */}
      {/* TODO: ENG-4129 */}
      {/* <Divider />

      <LineItemSection
        title='Carrier'
        formMethods={formMethods}
        lineItemsFieldPath='rateData.carrierLineItems'
        totalFieldPath='rateData.carrierTotalCost'
        fields={carrierLineItems}
        onAddCharge={addCarrierCharge}
        onRemoveCharge={carrierLineItemsRemove}
        watchedLineItems={watchedCarrierLineItems}
        watchedTotal={watchedCarrierTotal}
      /> */}
    </>
  );
}
