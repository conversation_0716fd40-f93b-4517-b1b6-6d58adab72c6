import { JSX } from 'react';

import { InputValue, RHFTextInput } from 'components/input/RHFTextInput';

export function BillToSectionForm(): JSX.Element {
  return (
    <>
      <RHFTextInput name='billTo.name' label='Name' />
      <RHFTextInput name='billTo.addressLine1' label='Address Line 1' />
      <RHFTextInput name='billTo.addressLine2' label='Address Line 2' />
      <RHFTextInput name='billTo.city' label='City' />
      <RHFTextInput name='billTo.state' label='State' />
      <RHFTextInput
        name='billTo.zipCode'
        label='Zip Code'
        placeholder='12345'
      />
      <RHFTextInput name='billTo.contact' label='Contact' />
      <RHFTextInput
        name='billTo.phone'
        label='Phone'
        placeholder='(*************'
        inputValue={InputValue.PHONE_NUMBER}
      />
      <RHFTextInput
        name='billTo.email'
        label='Email'
        placeholder='<EMAIL>'
      />
    </>
  );
}
