import { Controller, UseFormReturn } from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { Label } from 'components/Label';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { NormalizedLoad } from 'types/Load';

import { currencyList } from '../TurvoSectionForms/constants';

interface RatesFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
}

export function RatesForm({ formMethods }: RatesFormProps) {
  const { control, watch } = formMethods;

  const watchedTotalAmount = watch('rateData.customerTotalCharge.val');
  const watchedCustomerLineHaulCharge = watch(
    'rateData.customerLineHaulCharge.val'
  );
  const watchedCustomerFuelSurcharge = watch('rateData.fscFlatRate');

  return (
    <div className='grid grid-cols-2 gap-4 mx-0 w-full'>
      <div className='col-span-1 text-md text-neutral-800 font-semibold mb-1'>
        Customer
      </div>

      <div className='mx-0 w-full col-span-2 grid grid-cols-2 gap-2'>
        <div className='col-span-1'>
          <RHFTextInput
            name='rateData.customerLineHaulCharge.val'
            label='Line Haul Charge'
            options={{ valueAsNumber: true }}
          />
        </div>

        <div className='col-span-1'>
          <RHFTextInput
            name='rateData.fscFlatRate'
            label='Fuel Surcharge'
            options={{ valueAsNumber: true }}
          />
        </div>
      </div>

      <div className='col-span-2'>
        <RHFTextInput
          name='rateData.customerTotalCharge.val'
          label='Total Amount'
          options={{ valueAsNumber: true }}
        />
      </div>

      <div className='flex flex-col col-span-2'>
        <Label
          name='rateData.customerTotalCharge.unit'
          required={
            watchedTotalAmount > 0 ||
            watchedCustomerLineHaulCharge > 0 ||
            (watchedCustomerFuelSurcharge ?? 0) > 0
          }
        >
          Currency
        </Label>
        <Controller
          name='rateData.customerTotalCharge.unit'
          control={control}
          rules={{
            required:
              watchedTotalAmount > 0 ||
              watchedCustomerLineHaulCharge > 0 ||
              (watchedCustomerFuelSurcharge ?? 0) > 0,
          }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-grayscale-content-input'
              placeholder='Choose'
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={currencyList?.map((currency) => ({
                value: currency,
                label: currency,
              }))}
            />
          )}
        />
      </div>
    </div>
  );
}

export default RatesForm;
