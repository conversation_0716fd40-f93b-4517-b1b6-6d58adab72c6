import { useEffect } from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { ReferenceNumberInput } from 'components/input/ReferenceNumberInput';
import { Grid } from 'components/layout';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { getCustomers } from 'lib/api/getCustomers';
import { NormalizedLoad, TMSCustomer } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
  mapCustomerToAntdOptions,
} from 'utils/loadInfoAndBuilding';

export type CustomerSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  customers: Maybe<TMSCustomer[]>;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  isLoadingCustomers: boolean;
  setIsLoadingCustomers: React.Dispatch<React.SetStateAction<boolean>>;
  originalSuggestionData?: NormalizedLoad;
  isCreateMode?: boolean;
};

export function CustomerSectionForm({
  formMethods,
  customers,
  setCustomers,
  isLoadingCustomers,
  setIsLoadingCustomers,
  originalSuggestionData,
  isCreateMode = false,
}: CustomerSectionFormProps) {
  const {
    control,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = formMethods;
  const { toast } = useToast();
  const { tmsIntegrations } = useServiceFeatures();

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    searchTerm: string
  ) => {
    return customerSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      customers,
      setCustomers,
      field,
      value: searchTerm,
    });
  };

  const handleRefreshCustomers = async () => {
    try {
      setIsLoadingCustomers(true);
      const response = await getCustomers(tmsIntegrations?.[0]?.id);
      if (response.isOk()) {
        setCustomers(response.value.customerList);
        toast({
          description: 'Successfully refreshed customer list.',
          variant: 'success',
        });
      } else {
        toast({
          description: response.error.message || 'Failed to refresh customers',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to refresh customers',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingCustomers(false);
    }
  };

  const watchedCustomer = watch('customer');
  const watchedAddress2 = watch('customer.addressLine2');

  useEffect(() => {
    if (watchedCustomer?.externalTMSID) {
      const selectedCustomer = customers?.find(
        (c) => c.externalTMSID === watchedCustomer.externalTMSID
      );
      if (!selectedCustomer) return;
      Object.entries(selectedCustomer).forEach(([key, value]) => {
        setValue(`customer.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true,
        });
      });
    }
  }, [watchedCustomer?.externalTMSID, customers, setValue]);

  // In edit mode, if externalTMSID is empty but we have customer data, create a virtual customer option
  useEffect(() => {
    if (
      !isCreateMode &&
      watchedCustomer &&
      !watchedCustomer.externalTMSID &&
      watchedCustomer.name
    ) {
      const virtualCustomer: TMSCustomer = {
        externalTMSID: '', // Empty but valid
        name: watchedCustomer.name,
        addressLine1: watchedCustomer.addressLine1 || '',
        addressLine2: watchedCustomer.addressLine2 || '',
        city: watchedCustomer.city || '',
        state: watchedCustomer.state || '',
        zipCode: watchedCustomer.zipCode || '',
        country: watchedCustomer.country || '',
        contact: watchedCustomer.contact || '',
        phone: watchedCustomer.phone || '',
        email: watchedCustomer.email || '',
        refNumber: watchedCustomer.refNumber || '',
      };

      // Add virtual customer to the list if it doesn't exist
      if (
        customers &&
        !customers.find(
          (c) => c.name === virtualCustomer.name && c.externalTMSID === ''
        )
      ) {
        setCustomers([virtualCustomer, ...customers]);
      }
    }
  }, [isCreateMode, watchedCustomer, customers, setCustomers]);

  return (
    <>
      <RHFDebounceSelect
        required={isCreateMode}
        name={`customer.externalTMSID`}
        label='Name'
        control={control}
        errors={errors}
        data={customers}
        isLoading={isLoadingCustomers}
        refreshHandler={handleRefreshCustomers}
        fetchOptions={handleCustomerSearch}
        mapOptions={mapCustomerToAntdOptions}
      />

      <RHFTextInput
        name='customer.addressLine1'
        label='Address Line 1'
        readOnly={true}
      />

      {!!watchedAddress2 && (
        <RHFTextInput
          name='customer.addressLine2'
          label='Address Line 2'
          readOnly={true}
        />
      )}

      <RHFTextInput name='customer.city' label='City' readOnly={true} />

      <Grid cols='2' gap='sm' className='w-full m-0'>
        <RHFTextInput name='customer.state' label='State' readOnly={true} />
        <RHFTextInput
          name='customer.zipCode'
          label='Zip Code'
          placeholder='12345'
          readOnly={true}
        />
      </Grid>

      <ReferenceNumberInput
        name='customer.refNumber'
        control={control}
        label='Ref #/ BOL'
        placeholder='LD12345'
        load={originalSuggestionData || getValues()}
      />

      <RHFTextInput name={'poNums'} label='PO #' placeholder='123456' />
    </>
  );
}
