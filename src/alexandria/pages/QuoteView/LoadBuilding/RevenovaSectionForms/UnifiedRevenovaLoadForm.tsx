import { useContext, useEffect, useMemo, useState } from 'react';
import {
  Form<PERSON>rovider,
  SubmitError<PERSON><PERSON>ler,
  SubmitHandler,
  useForm,
} from 'react-hook-form';

import { Accordion } from '@radix-ui/react-accordion';
import {
  BoxIcon,
  Building2,
  CircleDollarSignIcon,
  ReceiptIcon,
  WarehouseIcon,
  Weight,
} from 'lucide-react';

import { Button } from 'components/Button';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { CreateLoadRequest, createLoad } from 'lib/api/createLoad';
import { getCustomers } from 'lib/api/getCustomers';
import { getLocations } from 'lib/api/getLocations';
import { updateTMS } from 'lib/api/updateTMS';
import { FormStorageService } from 'lib/services/FormStorage/service';
import { LoadSectionAccordionItem } from 'pages/LoadView/LoadInformation/Components';
import { ExternalLinks } from 'types/ExternalLinks';
import {
  Load,
  NormalizedLoad,
  TMSCustomer,
  TMSLocation,
  Unit,
  createInitLoad,
  createInitRateData,
  createInitSpecs,
  normalizeLoad,
} from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { TMSOperation } from 'types/api/UpdateTMS';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import { SuggestedLoad } from 'types/suggestions/LoadBuildingSuggestions';
import {
  injectSelectedObject,
  normalizeToStringArray,
} from 'utils/loadInfoAndBuilding';
import { denormalizeDatesForTMSForm } from 'utils/parseDatesForTMSForm';

import { BillToSectionForm } from './BillTo';
import { CustomerSectionForm } from './Customer';
import { RatesForm } from './Rates';
import { SpecificationsForm } from './Specifications';
import { StopForm } from './Stop';

enum AvailableTabs {
  customer = 'customer',
  specifications = 'specifications',
  rates = 'rates',
  pickup = 'pickup',
  consignee = 'consignee',
  billTo = 'billTo',
}

const initLoadBuildingForm = (tmsName: string): NormalizedLoad =>
  normalizeLoad(tmsName, {
    ...createInitLoad(),
    mode: 'TL', // Required as field is read-only
    specifications: {
      ...createInitSpecs(),
      transportType: 'Van',
      serviceType: 'Any',
    },
    rateData: {
      ...createInitRateData(),
      customerTotalCharge: {
        val: 0,
        unit: Unit.USD,
      },
    },
  });

interface UnifiedRevenovaLoadFormProps {
  isCreateMode: boolean;
  load?: Load;
  onSubmit?: (data: NormalizedLoad) => void;
  onInvalid?: SubmitErrorHandler<NormalizedLoad>;
  externalLinks?: ExternalLinks;
}

export default function UnifiedRevenovaLoadForm({
  isCreateMode,
  load,
  onSubmit,
  onInvalid,
  externalLinks: _externalLinks,
}: UnifiedRevenovaLoadFormProps) {
  const { serviceID, tmsIntegrations } = useServiceFeatures();
  const { toast } = useToast();
  const tmsName = TMS.Revenova;
  const [isLoading, setIsLoading] = useState(false);
  const [activeTabs, setActiveTabs] = useState<string[]>(
    Object.values(AvailableTabs)
  );
  const [isLoadBuildingSuggestionClicked, setIsLoadBuildingSuggestionClicked] =
    useState(false);
  const [showAIHints, setShowAIHints] = useState(false);

  const [isLoadingLocations, setIsLoadingLocations] = useState(true);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(true);
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [locations, setLocations] = useState<Maybe<TMSLocation[]>>(null);
  const [builtLoad, setBuiltLoad] = useState<Maybe<Load>>(null);

  const {
    currentState: { clickedSuggestion, threadId, goToSuggestionInCarousel },
    setCurrentState,
  } = useContext(SidebarStateContext);

  const fetchCustomers = async () => {
    setIsLoadingCustomers(true);
    const res = await getCustomers(tmsIntegrations[0]?.id);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
    } else {
      toast({
        description: res.error.message || 'Error while fetching customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  const fetchLocations = async () => {
    setIsLoadingLocations(true);
    const res = await getLocations(tmsIntegrations[0]?.id);
    if (res.isOk()) {
      setLocations(res.value.locationList);
    } else {
      toast({
        description: res.error.message || 'Error while fetching location list.',
        variant: 'destructive',
      });
    }
    setIsLoadingLocations(false);
  };

  useEffect(() => {
    fetchCustomers();
    fetchLocations();
  }, []);

  useEffect(() => {
    // Only auto-navigate to suggestions in create mode
    if (isCreateMode && !clickedSuggestion) {
      goToSuggestionInCarousel({
        suggestionPipeline: SuggestionPipelines.LoadBuilding,
      });
    }
  }, [isCreateMode, clickedSuggestion, goToSuggestionInCarousel]);

  useEffect(() => {
    // Update suggestion clicked state (only for create mode)
    if (isCreateMode) {
      if (
        clickedSuggestion &&
        clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
      ) {
        setIsLoadBuildingSuggestionClicked(true);
        setShowAIHints(true);
      } else {
        setIsLoadBuildingSuggestionClicked(false);
        setShowAIHints(false);
      }
    }
  }, [isCreateMode, clickedSuggestion]);

  useEffect(() => {
    // Handle form state restoration from storage (only for create mode)
    if (isCreateMode) {
      const savedState = FormStorageService.getFormState<
        NormalizedLoad,
        NormalizedLoad
      >(`${tmsName}_${threadId}`);
      if (
        savedState &&
        savedState.threadID === threadId &&
        !(
          clickedSuggestion &&
          clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
        )
      ) {
        setShowAIHints(true);
        goToSuggestionInCarousel({
          suggestionID: savedState.clickedSuggestion?.id,
        });
      }
    }
  }, [isCreateMode, threadId, clickedSuggestion, goToSuggestionInCarousel]);

  const memoizedDefaultValues: NormalizedLoad = useMemo(() => {
    let base: NormalizedLoad;

    // Handle AI suggestions for create mode
    if (
      isCreateMode &&
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      const castedClickedSuggestion =
        clickedSuggestion.suggested as SuggestedLoad;
      const transportType =
        castedClickedSuggestion.specifications?.transportType?.toLowerCase();

      const suggestedFields = {
        ...castedClickedSuggestion,
        mode: 'TL',
        // Preserve refNumberCandidates fields
        customer: {
          ...castedClickedSuggestion.customer,
          refNumberCandidates: normalizeToStringArray(
            castedClickedSuggestion.customer?.refNumberCandidates
          ),
        },
        pickup: {
          ...castedClickedSuggestion.pickup,
          refNumberCandidates: normalizeToStringArray(
            castedClickedSuggestion.pickup?.refNumberCandidates
          ),
        },
        consignee: {
          ...castedClickedSuggestion.consignee,
          refNumberCandidates: normalizeToStringArray(
            castedClickedSuggestion.consignee?.refNumberCandidates
          ),
        },
        specifications: {
          ...createInitSpecs(),
          ...castedClickedSuggestion.specifications,
          transportType:
            transportType === 'reefer'
              ? 'Reefer'
              : transportType === 'flatbed'
                ? 'Flatbed'
                : 'Van',
        },
        rateData: {
          ...createInitRateData(),
          ...castedClickedSuggestion.rateData,
          customerTotalCharge: {
            ...castedClickedSuggestion.rateData.customerTotalCharge,
            unit: Unit.USD,
          },
        },
      };
      setShowAIHints(true);

      base = {
        ...normalizeLoad(tmsName, createInitLoad()),
        ...suggestedFields,
        mode: 'TL',
        rateData: {
          ...suggestedFields.rateData,
          customerTotalCharge: {
            ...suggestedFields.rateData.customerTotalCharge,
            unit: Unit.USD,
          },
        },
      } as NormalizedLoad;
    } else if (builtLoad) {
      base = normalizeLoad(tmsName, builtLoad);
    } else if (!isCreateMode && load) {
      // Handle edit mode with existing load data
      base = normalizeLoad(tmsName, load);
    } else {
      base = initLoadBuildingForm(tmsName);
    }

    return base;
  }, [
    isCreateMode ? threadId : null,
    isCreateMode ? clickedSuggestion?.id : null,
    builtLoad,
    isCreateMode,
    load,
    tmsName,
  ]);

  const formMethods = useForm<NormalizedLoad>({
    defaultValues: memoizedDefaultValues,
  });

  function handleClearForm() {
    setShowAIHints(false);
    setBuiltLoad(null);
    FormStorageService.clearFormState(`${tmsName}_${threadId}`);
    formMethods.reset(initLoadBuildingForm(tmsName), {
      keepDefaultValues: false,
      keepDirty: false,
      keepDirtyValues: false,
      keepTouched: false,
    });
  }

  // Persist form state on every change (only for create mode)
  useEffect(() => {
    if (isCreateMode) {
      const subscription = formMethods.watch((value) => {
        FormStorageService.saveFormState(`${tmsName}_${threadId}`, {
          threadID: threadId,
          values: value,
          clickedSuggestion: isLoadBuildingSuggestionClicked
            ? clickedSuggestion
            : null,
          dirtyFields: formMethods.formState.dirtyFields,
        });
      });
      return () => subscription.unsubscribe();
    }
    return undefined;
  }, [
    isCreateMode,
    formMethods,
    threadId,
    isLoadBuildingSuggestionClicked,
    clickedSuggestion,
  ]);

  // Reset form when default values change
  useEffect(() => {
    if (!memoizedDefaultValues) {
      return;
    }
    formMethods.reset(memoizedDefaultValues, {
      keepDefaultValues: false,
      keepTouched: false,
      keepDirtyValues: false,
      keepDirty: false,
    });

    // After resetting the form, ensure that options arrays include the necessary data
    const customerID = memoizedDefaultValues.customer?.externalTMSID;
    if (customerID) {
      setCustomers((prevCustomers) =>
        injectSelectedObject(
          memoizedDefaultValues.customer,
          prevCustomers ?? []
        )
      );
    }

    const additionalLocations: TMSLocation[] = [];
    if (memoizedDefaultValues.pickup?.externalTMSID) {
      additionalLocations.push(memoizedDefaultValues.pickup);
    }

    if (memoizedDefaultValues.consignee?.externalTMSID) {
      additionalLocations.push(memoizedDefaultValues.consignee);
    }

    setLocations((prevLocations) => {
      let updatedLocations = prevLocations ?? [];

      additionalLocations.forEach((loc) => {
        updatedLocations = injectSelectedObject(loc, updatedLocations);
      });

      return updatedLocations;
    });
  }, [memoizedDefaultValues]);

  const handleSubmit: SubmitHandler<NormalizedLoad> = async (data) => {
    setIsLoading(true);

    try {
      if (isCreateMode) {
        // Create new load
        data.freightTrackingID = '';
        data.externalTMSID = '';
        const reqData: CreateLoadRequest = {
          load: {
            ...data,
            tmsID: tmsIntegrations[0]?.id,
          } as unknown as Load,
        };

        const res = await createLoad(reqData);
        if (res.isOk()) {
          setBuiltLoad(res.value.load);
          FormStorageService.clearFormState(`${tmsName}_${threadId}`);
          if (
            clickedSuggestion &&
            clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
          ) {
            setCurrentState((prevState) => {
              const filteredList = prevState.curSuggestionList.filter(
                ({ id }) => id !== clickedSuggestion.id
              );
              return {
                ...prevState,
                clickedSuggestion: null,
                curSuggestionList: filteredList,
              };
            });
          }
          toast({
            title: res.value.message,
            description:
              'Load ID: ' +
              (res.value.load.externalTMSID ??
                res.value.load.freightTrackingID),
            variant: 'success',
          });
        } else {
          toast({
            description: res.error.message || 'Error building load.',
            variant: 'destructive',
          });
        }
      } else {
        // Edit mode - update existing load
        const denormalizedData = denormalizeDatesForTMSForm(tmsName, data);

        if (!load?.ID) {
          toast({
            description: 'Load ID is required for updates.',
            variant: 'destructive',
          });
          setIsLoading(false);
          return;
        }

        const res = await updateTMS(load.ID, {
          operation: TMSOperation.UpdateLoad,
          load: denormalizedData as Load,
        });

        if (res.isOk()) {
          toast({
            description: 'Load updated successfully!',
            variant: 'success',
          });
        } else {
          toast({
            description: res.error.message || 'Error updating load.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        description: isCreateMode
          ? 'Error building load.'
          : 'Error updating load.',
        variant: 'destructive',
      });
    }

    setIsLoading(false);

    // Call custom submit handler if provided
    if (onSubmit) {
      onSubmit(data);
    }
  };

  const handleInvalid: SubmitErrorHandler<NormalizedLoad> = async (errors) => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });

    if (onInvalid) {
      onInvalid(errors);
    }
  };

  return (
    <div className='mb-5'>
      <ExtendedFormProvider aiDefaultValues={showAIHints}>
        <FormProvider {...formMethods}>
          <form
            onSubmit={formMethods.handleSubmit(handleSubmit, handleInvalid)}
          >
            <Accordion
              type='multiple'
              value={activeTabs}
              onValueChange={setActiveTabs}
            >
              <LoadSectionAccordionItem
                label='Customer'
                icon={<Building2 className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.customer}
                activeTabs={activeTabs}
              >
                <CustomerSectionForm
                  formMethods={formMethods}
                  customers={customers || []}
                  setCustomers={setCustomers}
                  isLoadingCustomers={isLoadingCustomers}
                  setIsLoadingCustomers={setIsLoadingCustomers}
                  originalSuggestionData={undefined}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Specs'
                icon={<Weight className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.specifications}
                activeTabs={activeTabs}
              >
                <SpecificationsForm
                  formMethods={formMethods}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Rates'
                icon={
                  <CircleDollarSignIcon className='h-6 w-6' strokeWidth={1} />
                }
                name={AvailableTabs.rates}
                activeTabs={activeTabs}
              >
                <RatesForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Pickup'
                icon={<WarehouseIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.pickup}
                activeTabs={activeTabs}
              >
                <StopForm
                  formMethods={formMethods}
                  locations={locations || []}
                  setLocations={setLocations}
                  isLoadingLocations={isLoadingLocations}
                  setIsLoadingLocations={setIsLoadingLocations}
                  stopType='pickup'
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Consignee'
                icon={<BoxIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.consignee}
                activeTabs={activeTabs}
              >
                <StopForm
                  formMethods={formMethods}
                  locations={locations || []}
                  setLocations={setLocations}
                  isLoadingLocations={isLoadingLocations}
                  setIsLoadingLocations={setIsLoadingLocations}
                  stopType='consignee'
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              {!isCreateMode && (
                <LoadSectionAccordionItem
                  label='Bill To'
                  icon={<ReceiptIcon className='h-6 w-6' strokeWidth={1} />}
                  name={AvailableTabs.billTo}
                  activeTabs={activeTabs}
                >
                  <BillToSectionForm />
                </LoadSectionAccordionItem>
              )}
            </Accordion>

            <section className='w-full mt-4'>
              <Button
                buttonNamePosthog={
                  isCreateMode
                    ? ButtonNamePosthog.BuildLoad
                    : ButtonNamePosthog.UpdateTMS
                }
                type='submit'
                className='w-full'
                disabled={isLoading}
                logProperties={{ serviceID }}
              >
                {isLoading ? (
                  <ButtonLoader />
                ) : isCreateMode ? (
                  ButtonText.BuildLoad
                ) : (
                  ButtonText.UpdateTMS
                )}
              </Button>

              {isCreateMode && formMethods.formState.isDirty && (
                <Flex justify='center' align='center'>
                  <Button
                    buttonNamePosthog={ButtonNamePosthog.ClearForm}
                    type='button'
                    className='w-50% mt-4 h-8 text-sm text-neutral-500'
                    disabled={isLoading}
                    variant='outline'
                    onClick={handleClearForm}
                    logProperties={{ serviceID }}
                  >
                    {ButtonText.ClearForm}
                  </Button>
                </Flex>
              )}

              {isCreateMode && builtLoad?.externalTMSID && (
                <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-800 px-4 bg-success-50'>
                  <Typography className='mb-2'>Load Created 🎉</Typography>
                  <Typography variant='body-sm' className='mb-2'>
                    <Typography variant='body-sm' weight='bold'>
                      Revenova Load ID:{' '}
                    </Typography>
                    {builtLoad.externalTMSID}
                  </Typography>
                  <Typography variant='body-sm' className='mb-2'>
                    <Typography variant='body-sm' weight='bold'>
                      Shipment ID:{' '}
                    </Typography>
                    {builtLoad.freightTrackingID}
                  </Typography>
                </div>
              )}
            </section>
          </form>
        </FormProvider>
      </ExtendedFormProvider>
    </div>
  );
}
