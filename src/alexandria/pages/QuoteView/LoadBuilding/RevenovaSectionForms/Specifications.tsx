import { Controller, UseFormReturn } from 'react-hook-form';

import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Grid } from 'components/layout';
import useTMSContext from 'hooks/useTMSContext';
import { NormalizedLoad } from 'types/Load';
import { TMS } from 'types/enums/Integrations';

export function SpecificationsForm({
  formMethods,
  isCreateMode = false,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
  isCreateMode?: boolean;
}) {
  const { control } = formMethods;
  const { tmsName } = useTMSContext();

  const serviceTypeOptions = [
    { value: 'Any', label: 'Any' },
    { value: 'Standard', label: 'Standard' },
    { value: 'Expedited', label: 'Expedited' },
  ];

  // Hide Volume field for Revenova TMS in create mode or always for Revenova
  const isVolumeSupported = tmsName !== TMS.Revenova && !isCreateMode;

  // Hide Distance field during create mode
  const isDistanceSupported = !isCreateMode;

  return (
    <Grid cols='1' gap='md' className='w-full mx-0'>
      {/* Mode field - like LoadInformationTab */}
      <div className='space-y-1'>
        <Label name='mode' required={isCreateMode}>
          Mode
        </Label>
        <Controller
          name='mode'
          control={control}
          rules={isCreateMode ? { required: 'Mode is required' } : {}}
          render={({ field }) => (
            <Select value={field.value || 'TL'} onValueChange={field.onChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select mode' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='TL'>Truckload</SelectItem>
                <SelectItem value='LTL'>LTL</SelectItem>
                <SelectItem value='Refrigerated'>Refrigerated</SelectItem>
              </SelectContent>
            </Select>
          )}
        />
      </div>

      {/* Commodity, Quantity, and Units row */}
      <Grid cols='3' gap='sm' className='w-full'>
        <RHFTextInput name='specifications.commodities' label='Item' />

        <RHFTextInput
          required={isCreateMode}
          name='specifications.totalPieces.val'
          label='Quantity'
          inputType='number'
          options={{
            ...(isCreateMode && { required: 'Quantity is required' }),
            min: {
              value: 0,
              message: 'Quantity cannot be negative',
            },
            valueAsNumber: true,
          }}
        />

        <RHFTextInput name='specifications.totalPiecesType' label='Units' />
      </Grid>

      {/* Weight */}
      <RHFTextInput
        required={isCreateMode}
        name='specifications.totalWeight.val'
        label='Gross Weight'
        inputType='number'
        options={{
          ...(isCreateMode && { required: 'Weight is required' }),
          min: {
            value: 0,
            message: 'Weight cannot be negative',
          },
          valueAsNumber: true,
        }}
      />

      {/* Volume - hidden for Revenova TMS */}
      {isVolumeSupported && (
        <RHFTextInput
          name='specifications.totalVolume.val'
          label='Volume'
          inputType='number'
          options={{
            min: {
              value: 0,
              message: 'Volume must be greater than 0',
            },
            valueAsNumber: true,
          }}
        />
      )}

      {/* Distance and Pallets row */}
      <Grid cols='2' gap='sm' className='w-full'>
        {isDistanceSupported && (
          <RHFTextInput
            name='specifications.totalDistance.val'
            label='Distance'
            inputType='number'
            options={{
              min: {
                value: 0,
                message: 'Distance must be greater than 0',
              },
              valueAsNumber: true,
            }}
          />
        )}

        <RHFTextInput
          required={isCreateMode}
          name='specifications.totalOutPalletCount'
          label='Pallets'
          inputType='number'
          options={{
            ...(isCreateMode && { required: 'Pallets is required' }),
            min: {
              value: 0,
              message: 'Pallets cannot be negative',
            },
            valueAsNumber: true,
          }}
        />
      </Grid>

      {/* Service Type */}
      <div className='space-y-1'>
        <Label name='specifications.serviceType' required={isCreateMode}>
          Service Type
        </Label>
        <Controller
          name='specifications.serviceType'
          control={control}
          rules={isCreateMode ? { required: 'Service type is required' } : {}}
          render={({ field }) => (
            <Select value={field.value || 'Any'} onValueChange={field.onChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select service type' />
              </SelectTrigger>
              <SelectContent>
                {serviceTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
      </div>
    </Grid>
  );
}
