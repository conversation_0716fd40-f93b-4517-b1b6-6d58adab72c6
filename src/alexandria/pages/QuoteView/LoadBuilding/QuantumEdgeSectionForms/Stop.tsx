import { Controller, <PERSON>Path, UseFormReturn } from 'react-hook-form';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import { Checkbox } from 'components/Checkbox';
import { Label } from 'components/Label';
import { Flex } from 'components/layout';
import {
  LoadDateTimeInput,
  LoadSelectInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import { LoadStopsItemTypes, NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from '../QuantumEdgeLoadForm';
import { quantumEdgeApptTypes } from './constants';

type StopFormProps = {
  stop: 'pickup' | 'consignee';
  formMethods: UseFormReturn<NormalizedLoad>;
  isCreateMode: boolean;
};

const datetimeFieldOptions = {};

export default function StopForm({
  stop,
  isCreateMode,
  formMethods,
}: StopFormProps): React.JSX.Element {
  const stopPrefix = stop;
  const isPickup = stop === 'pickup';

  const stops = formMethods.watch('stops') || [];
  const stopIndex = isPickup
    ? stops.findIndex((s) => s.stopType === LoadStopsItemTypes.Pickup)
    : stops.findIndex(
        (s) =>
          s.stopType === LoadStopsItemTypes.Delivery ||
          s.stopType === LoadStopsItemTypes.Dropoff
      );

  return (
    <Flex direction='col' className='gap-4'>
      <LoadBuildingTextInput
        name={`${stopPrefix}.name` as FieldPath<NormalizedLoad>}
        label='Name'
        readOnly={!isCreateMode}
        required={isCreateMode}
      />

      <LoadBuildingTextInput
        name={`${stopPrefix}.addressLine1` as FieldPath<NormalizedLoad>}
        label='Address Line 1'
        readOnly={!isCreateMode}
      />

      <LoadBuildingTextInput
        name={`${stopPrefix}.city` as FieldPath<NormalizedLoad>}
        label='City'
        readOnly={!isCreateMode}
      />

      <Flex direction='row' className='gap-4'>
        <Flex grow='1'>
          <LoadBuildingTextInput
            name={`${stopPrefix}.state` as FieldPath<NormalizedLoad>}
            label='State'
            readOnly={!isCreateMode}
          />
        </Flex>
        <Flex grow='1'>
          <LoadBuildingTextInput
            name={`${stopPrefix}.zipCode` as FieldPath<NormalizedLoad>}
            label='ZIP Code'
            readOnly={!isCreateMode}
            placeholder='12345'
          />
        </Flex>
      </Flex>

      {stopIndex !== -1 && (
        <Accordion type='multiple' className='w-full mt-3'>
          <AccordionItem value='appointment' className='border-b-0'>
            <AccordionTrigger className='text-sm font-medium py-2'>
              Appointment Info
            </AccordionTrigger>
            <AccordionContent className='pt-2'>
              <Flex direction='col' gap='md'>
                <LoadTextInput
                  name={`stops.${stopIndex}.refNumber`}
                  label='Ref #'
                  readOnly={false}
                />

                <div className='w-full [&>div]:w-full'>
                  <LoadSelectInput
                    name={`stops.${stopIndex}.apptType`}
                    label='Appointment Type'
                    options={quantumEdgeApptTypes}
                    placeholder='Select type'
                  />
                </div>

                <Controller
                  name={`stops.${stopIndex}.apptRequired`}
                  control={formMethods.control}
                  render={({ field }) => (
                    <Flex align='center' gap='sm'>
                      <Checkbox
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                        }}
                        checked={field.value || undefined}
                      />
                      <Label
                        htmlFor={`stops.${stopIndex}.apptRequired`}
                        name=''
                      >
                        Appointment Required?
                      </Label>
                    </Flex>
                  )}
                />

                <LoadDateTimeInput
                  name={`stops.${stopIndex}.readyTime`}
                  label='Req From'
                  options={datetimeFieldOptions}
                  load={formMethods.watch()}
                />

                <LoadDateTimeInput
                  name={`stops.${stopIndex}.readyEndTime`}
                  label='Req To'
                  options={datetimeFieldOptions}
                  load={formMethods.watch()}
                />

                <LoadDateTimeInput
                  name={`stops.${stopIndex}.apptStartTime`}
                  label='Appointment Start Time'
                  options={datetimeFieldOptions}
                  load={formMethods.watch()}
                />

                <LoadDateTimeInput
                  name={`stops.${stopIndex}.apptEndTime`}
                  label='Appointment End Time'
                  options={datetimeFieldOptions}
                  load={formMethods.watch()}
                />

                <LoadTextInput
                  name={`stops.${stopIndex}.apptNote`}
                  label='Appointment Note'
                  readOnly={false}
                />
              </Flex>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )}
    </Flex>
  );
}
