import { Controller, UseFormReturn } from 'react-hook-form';

import _ from 'lodash';
import { WarehouseIcon } from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import { Badge } from 'components/Badge';
import { Checkbox } from 'components/Checkbox';
import { Label } from 'components/Label';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import {
  LoadDateTimeInput,
  LoadSelectInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import { LoadStopsItemTypes, NormalizedLoad } from 'types/Load';
import { cn } from 'utils/shadcn';

import { quantumEdgeApptTypes } from '../constants';

const datetimeFieldOptions = {};

export function QuantumEdgeStopsTimeline({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}) {
  const stops = formMethods.watch('stops') || [];
  const sortedStops = [...stops].sort(
    (a, b) => (a.order || 0) - (b.order || 0)
  );

  return (
    <div className='w-full'>
      <Flex direction='col' gap='lg' className='w-full'>
        {sortedStops.map((stop, index) => {
          const stopLabel =
            stop.stopType === LoadStopsItemTypes.Pickup ? `Pickup` : `Dropoff`;

          return (
            <div
              key={stop.id || index}
              className='w-full px-3 py-3 bg-neutral-50 rounded border border-neutral-400 shadow-sm'
            >
              <Flex align='start' gap='sm' className='w-full'>
                <Flex direction='col' align='start' gap='sm' className='w-full'>
                  <Flex
                    align='center'
                    justify='between'
                    gap='md'
                    className='w-full'
                  >
                    <Flex align='start' className='w-full'>
                      {stop.address?.city && (
                        <Typography
                          variant='body-sm'
                          weight='semibold'
                          className='truncate max-w-[108px]'
                          title={_.startCase(_.toLower(stop.address.city))}
                        >
                          {_.startCase(_.toLower(stop.address.city))}
                        </Typography>
                      )}

                      {stop.address?.state && stop.address?.zip && (
                        <Typography
                          variant='body-sm'
                          weight='semibold'
                          className='whitespace-nowrap'
                        >
                          {`, ${stop.address.state} ${stop.address.zip}`}
                        </Typography>
                      )}
                    </Flex>

                    <Badge
                      variant='default'
                      className={cn(
                        'text-[10px]',
                        stop.stopType === LoadStopsItemTypes.Delivery
                          ? 'bg-success-50 border-success text-success'
                          : 'bg-brand-50 border-brand text-brand'
                      )}
                    >
                      {stopLabel}
                    </Badge>
                  </Flex>

                  <Flex align='end' gap='sm'>
                    <WarehouseIcon className='w-4 h-4' />
                    <Typography
                      variant='body-xs'
                      weight='bold'
                      className='text-neutral-600 leading-3.5'
                    >
                      {stop.address?.name}
                    </Typography>
                  </Flex>
                </Flex>
              </Flex>

              <Accordion type='multiple' className='w-full mt-3'>
                <AccordionItem value={`${stop.id || index}-address`}>
                  <AccordionTrigger className='text-sm font-medium py-2'>
                    Address & Contact Info
                  </AccordionTrigger>
                  <AccordionContent className='space-y-3 pt-2'>
                    <LoadTextInput
                      name={`stops.${index}.address.name`}
                      label='Name'
                      readOnly={true}
                      defaultValue={stop.address?.name || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.addressLine1`}
                      label='Address Line 1'
                      readOnly={true}
                      defaultValue={stop.address?.addressLine1 || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.city`}
                      label='City'
                      readOnly={true}
                      defaultValue={stop.address?.city || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.state`}
                      label='State'
                      readOnly={true}
                      defaultValue={stop.address?.state || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.zip`}
                      label='Zip Code'
                      readOnly={true}
                      defaultValue={stop.address?.zip || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.country`}
                      label='Country'
                      readOnly={true}
                      defaultValue={stop.address?.country || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.contact`}
                      label='Contact'
                      readOnly={true}
                      defaultValue={stop.contact || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.phone`}
                      label='Phone'
                      readOnly={true}
                      defaultValue={stop.phone || ''}
                    />
                    <LoadTextInput
                      name={`stops.${index}.email`}
                      label='Email'
                      readOnly={true}
                      defaultValue={stop.email || ''}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value={`${stop.id || index}-appointment`}
                  className='border-b-0'
                >
                  <AccordionTrigger className='text-sm font-medium py-2'>
                    Appointment Info
                  </AccordionTrigger>
                  <AccordionContent className='pt-2'>
                    <Flex direction='col' gap='md'>
                      <LoadTextInput
                        name={`stops.${index}.refNumber`}
                        label='Ref #'
                        readOnly={false}
                      />

                      <div className='w-full [&>div]:w-full'>
                        <LoadSelectInput
                          name={`stops.${index}.apptType`}
                          label='Appointment Type'
                          options={quantumEdgeApptTypes}
                          placeholder='Select type'
                        />
                      </div>

                      <Controller
                        name={`stops.${index}.apptRequired`}
                        control={formMethods.control}
                        render={({ field }) => (
                          <Flex align='center' gap='sm'>
                            <Checkbox
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                              }}
                              checked={field.value || undefined}
                            />
                            <Label
                              htmlFor={`stops.${index}.apptRequired`}
                              name=''
                            >
                              Appointment Required?
                            </Label>
                          </Flex>
                        )}
                      />

                      <LoadDateTimeInput
                        name={`stops.${index}.readyTime`}
                        label='Req From'
                        options={datetimeFieldOptions}
                        load={formMethods.watch()}
                      />

                      <LoadDateTimeInput
                        name={`stops.${index}.readyEndTime`}
                        label='Req To'
                        options={datetimeFieldOptions}
                        load={formMethods.watch()}
                      />

                      <LoadDateTimeInput
                        name={`stops.${index}.apptStartTime`}
                        label='Appointment Start Time'
                        options={datetimeFieldOptions}
                        load={formMethods.watch()}
                      />
                      <LoadDateTimeInput
                        name={`stops.${index}.apptEndTime`}
                        label='Appointment End Time'
                        options={datetimeFieldOptions}
                        load={formMethods.watch()}
                      />

                      <LoadTextInput
                        name={`stops.${index}.apptNote`}
                        label='Appointment Note'
                        readOnly={false}
                      />
                    </Flex>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          );
        })}
      </Flex>
    </div>
  );
}
