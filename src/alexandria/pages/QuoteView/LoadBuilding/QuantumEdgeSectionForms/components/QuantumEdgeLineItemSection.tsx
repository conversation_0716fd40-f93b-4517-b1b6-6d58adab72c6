import { JSX, useEffect } from 'react';
import {
  Controller,
  FieldArrayWithId,
  FieldPath,
  UseFormReturn,
} from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';
import { PlusIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { CostLineItem, NormalizedLoad } from 'types/Load';
import { cn } from 'utils/shadcn';

import { LoadBuildingTextInput } from '../../QuantumEdgeLoadForm';
import { currencyList } from '../constants';
import { QuantumEdgeCostItemCard } from './QuantumEdgeCostItemCard';

interface QuantumEdgeLineItemSectionProps {
  title: string;
  isReadOnly: boolean;
  formMethods: UseFormReturn<NormalizedLoad>;
  lineItemsFieldPath:
    | 'rateData.customerLineItems'
    | 'rateData.carrierLineItems';
  totalFieldPath: 'rateData.customerTotalCharge' | 'rateData.carrierTotalCost';
  fields: FieldArrayWithId<NormalizedLoad, any, 'id'>[];
  onAddCharge: () => void;
  onRemoveCharge: (index: number) => void;
  watchedLineItems: CostLineItem[];
  watchedTotal: number;
}

export function QuantumEdgeLineItemSection({
  title,
  isReadOnly,
  formMethods,
  lineItemsFieldPath,
  totalFieldPath,
  fields,
  onAddCharge,
  onRemoveCharge,
  watchedLineItems,
  watchedTotal,
}: QuantumEdgeLineItemSectionProps): JSX.Element {
  const { control, setValue } = formMethods;

  const totalLineItems =
    watchedLineItems?.reduce(
      (sum, charge) => sum + (charge.totalChargeUSD || 0),
      0
    ) || 0;

  useEffect(() => {
    if (totalLineItems !== watchedTotal && !isReadOnly) {
      setValue(`${totalFieldPath}.val`, totalLineItems, {
        shouldDirty: true,
        shouldValidate: true,
      });
    }
  }, [totalLineItems, watchedTotal, setValue, totalFieldPath, isReadOnly]);

  return (
    <>
      <Typography
        variant='h6'
        className='col-span-1 text-md text-neutral-800 font-semibold mb-1'
      >
        {title}
      </Typography>

      <Flex
        direction='col'
        gap='lg'
        className={cn('w-full', fields.length === 0 && 'hidden')}
      >
        {fields.map((field, index) => (
          <QuantumEdgeCostItemCard
            isReadOnly={isReadOnly}
            key={field.id}
            index={index}
            formMethods={formMethods}
            onRemove={onRemoveCharge}
            fieldPath={lineItemsFieldPath}
          />
        ))}
      </Flex>

      {!isReadOnly && (
        <Flex justify='between' align='center'>
          <Typography variant='body-xs' className='text-neutral-500'>
            Add charges like freight, fuel, and accessorials.
          </Typography>
          <Button
            onClick={onAddCharge}
            variant='outline'
            size='sm'
            className='h-7 px-2 text-xs'
            buttonNamePosthog='add_line_item'
            type='button'
          >
            <PlusIcon className='h-3 w-3 mr-1' />
            Add Charge
          </Button>
        </Flex>
      )}

      <Grid cols='2' gap='sm' className='mx-0 w-full'>
        <LoadBuildingTextInput
          name={`${totalFieldPath}.val` as FieldPath<NormalizedLoad>}
          label={`Total Amount`}
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
          readOnly={true}
          inputClassName='no-spin-buttons'
        />

        <Flex direction='col' className='col-span-1'>
          <Label
            name={`${totalFieldPath}.unit` as FieldPath<NormalizedLoad>}
            required={false}
          >
            Currency
          </Label>
          <Controller
            name={`${totalFieldPath}.unit` as FieldPath<NormalizedLoad>}
            control={control}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={currencyList?.map((currency) => ({
                  value: currency,
                  label: currency,
                }))}
              />
            )}
          />
        </Flex>
      </Grid>
    </>
  );
}
