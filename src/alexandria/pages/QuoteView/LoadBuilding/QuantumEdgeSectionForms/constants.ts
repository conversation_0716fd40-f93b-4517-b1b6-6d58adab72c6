export const currencyList: string[] = ['USD'];

export const quantumEdgeCostTypesList: string[] = [
  'LINE HAUL',
  'Breakthrough Fuel',
  'LUMPER',
  'DETENTION',
  'FUEL SURCHARGE',
  'ACCESSORIAL',
  'STOP OFF',
  'EXTRA PICKUP',
  'EXTRA DELIVERY',
  'LOADING',
  'UNLOADING',
  'STORAGE',
  'LAYOVER',
  'TARP',
  'TOLLS',
  'PERMITS',
  'ESCORT',
  'HAZMAT',
  'OVERSIZE',
  'OVERWEIGHT',
  'CRANE',
  'FORKLIFT',
  'LIFTGATE',
  'INSIDE DELIVERY',
  'RESIDENTIAL',
  'REDELIVERY',
  'COD FEE',
  'OTHER',
];

// Rate qualifiers/unit basis for QuantumEdge
export const quantumEdgeUnitBasisList: string[] = [
  'Flat Rate',
  'Per Mile',
  'Per Hundredweight',
  'Per Ton',
  'Per Pound',
  'Per Hour',
  'Per Stop',
  'Percent',
  'Per Each',
  'Per Piece',
  'Per Pallet',
  'Per Unit',
  'Per Gallon',
  'Per Case',
  'Per Box',
  'Minimum',
  'Maximum',
];

export const quantumEdgeReferenceTypes = [
  { qualifier: 'BOL', label: 'BOL#' },
  { qualifier: 'CHASSIS', label: 'CHASSIS#' },
  { qualifier: 'SEAL', label: 'SEAL#' },
  { qualifier: 'CONTAINER', label: 'CONT#' },
];

export const quantumEdgeApptTypes = [
  { value: 'Live', label: 'Live' },
  { value: 'Drop', label: 'Drop' },
];
