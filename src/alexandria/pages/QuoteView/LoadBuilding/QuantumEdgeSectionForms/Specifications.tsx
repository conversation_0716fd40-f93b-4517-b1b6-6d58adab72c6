import { UseFormReturn } from 'react-hook-form';

import { Grid } from 'components/layout';
import { NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from '../QuantumEdgeLoadForm';

type SpecificationsSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
};

export default function SpecificationsSectionForm({
  formMethods,
}: SpecificationsSectionFormProps): React.JSX.Element {
  const { watch } = formMethods;

  const weightUnit = watch('specifications.totalWeight.unit');

  // Format label to include unit if available
  const weightLabel = weightUnit
    ? `Total Weight (${weightUnit})`
    : 'Total Weight';

  return (
    <Grid cols='2' gap='sm' className='w-full m-0'>
      <div className='col-span-2'>
        <LoadBuildingTextInput
          name='specifications.commodities'
          label='Commodity Description'
          readOnly={true}
        />
      </div>

      <div className='col-span-2'>
        <LoadBuildingTextInput
          name='specifications.transportType'
          label='Transport Type'
          readOnly={true}
        />
      </div>

      <div className='col-span-2'>
        <LoadBuildingTextInput
          name='specifications.totalWeight.val'
          label={weightLabel}
          readOnly={true}
          inputType='number'
        />
      </div>
    </Grid>
  );
}
