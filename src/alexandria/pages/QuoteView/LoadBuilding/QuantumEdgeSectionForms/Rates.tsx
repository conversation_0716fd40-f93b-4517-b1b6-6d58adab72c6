import { JSX } from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';

import { CostLineItem, NormalizedLoad } from 'types/Load';

import { QuantumEdgeLineItemSection } from './components/QuantumEdgeLineItemSection';

interface QuantumEdgeRatesFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
  isCreateMode: boolean;
}

export function QuantumEdgeRatesForm({
  formMethods,
  isCreateMode,
}: QuantumEdgeRatesFormProps): JSX.Element {
  const { control, watch } = formMethods;

  const {
    fields: customerLineItems,
    append: customerLineItemsAppend,
    remove: customerLineItemsRemove,
  } = useFieldArray({
    control,
    name: 'rateData.customerLineItems',
  });

  const {
    fields: carrierLineItems,
    append: carrierLineItemsAppend,
    remove: carrierLineItemsRemove,
  } = useFieldArray({
    control,
    name: 'rateData.carrierLineItems',
  });

  const watchedCustomerTotal = watch('rateData.customerTotalCharge.val');
  const watchedCustomerLineItems = watch('rateData.customerLineItems');
  const watchedCarrierTotal = watch('rateData.carrierTotalCost.val');
  const watchedCarrierLineItems = watch('rateData.carrierLineItems');

  const createNewCharge = (): CostLineItem => ({
    label: '',
    unitBasis: '',
    quantity: 1,
    ratePerUnitUSD: 0,
    totalChargeUSD: 0,
    note: '',
  });

  const addCustomerCharge = () => customerLineItemsAppend(createNewCharge());
  const addCarrierCharge = () => carrierLineItemsAppend(createNewCharge());

  return (
    <>
      <QuantumEdgeLineItemSection
        title='Customer'
        isReadOnly={true}
        formMethods={formMethods}
        lineItemsFieldPath='rateData.customerLineItems'
        totalFieldPath='rateData.customerTotalCharge'
        fields={customerLineItems}
        onAddCharge={addCustomerCharge}
        onRemoveCharge={customerLineItemsRemove}
        watchedLineItems={watchedCustomerLineItems}
        watchedTotal={watchedCustomerTotal}
      />

      {(isCreateMode || watchedCarrierLineItems?.length > 0) && (
        <>
          <hr className='my-4' />
          <QuantumEdgeLineItemSection
            title='Carrier'
            isReadOnly={true}
            formMethods={formMethods}
            lineItemsFieldPath='rateData.carrierLineItems'
            totalFieldPath='rateData.carrierTotalCost'
            fields={carrierLineItems}
            onAddCharge={addCarrierCharge}
            onRemoveCharge={carrierLineItemsRemove}
            watchedLineItems={watchedCarrierLineItems}
            watchedTotal={watchedCarrierTotal}
          />
        </>
      )}
    </>
  );
}
