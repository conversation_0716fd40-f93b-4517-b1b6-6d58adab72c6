import { UseFormReturn } from 'react-hook-form';

import { Flex } from 'components/layout';
import { NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from '../QuantumEdgeLoadForm';

type CustomerSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  isCreateMode: boolean;
};

export default function CustomerSectionForm({
  isCreateMode,
}: CustomerSectionFormProps): React.JSX.Element {
  return (
    <Flex direction='col' className='gap-4'>
      <LoadBuildingTextInput
        name='customer.name'
        label='Name'
        readOnly={!isCreateMode}
        required={isCreateMode}
      />

      <LoadBuildingTextInput
        name='customer.addressLine1'
        label='Address Line 1'
        readOnly={!isCreateMode}
      />

      <LoadBuildingTextInput
        name='customer.city'
        label='City'
        readOnly={!isCreateMode}
      />

      <Flex direction='row' className='gap-4'>
        <Flex grow='1'>
          <LoadBuildingTextInput
            name='customer.state'
            label='State'
            readOnly={!isCreateMode}
          />
        </Flex>
        <Flex grow='1'>
          <LoadBuildingTextInput
            name='customer.zipCode'
            label='ZIP Code'
            readOnly={!isCreateMode}
            placeholder='12345'
          />
        </Flex>
      </Flex>

      <LoadBuildingTextInput
        name='customer.phone'
        label='Phone'
        readOnly={!isCreateMode}
        inputType='tel'
      />

      <LoadBuildingTextInput
        name='customer.refNumber'
        label='Ref #'
        readOnly={!isCreateMode}
      />
    </Flex>
  );
}
