import { useEffect } from 'react';
import { FieldPath, UseFormReturn, useFieldArray } from 'react-hook-form';

import { Grid } from 'components/layout';
import { AdditionalReference, NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from '../QuantumEdgeLoadForm';
import { quantumEdgeReferenceTypes } from './constants';

const AdditionalReferencesForm = ({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}) => {
  const { control, watch } = formMethods;
  const { fields, replace } = useFieldArray({
    control,
    name: 'additionalReferences',
  });

  const watchedAdditionalReferences = watch('additionalReferences');

  useEffect(() => {
    if (
      !watchedAdditionalReferences ||
      watchedAdditionalReferences.length < 4
    ) {
      const defaultRefs: AdditionalReference[] = quantumEdgeReferenceTypes.map(
        (refType) => ({
          qualifier: refType.qualifier,
          number: '',
          weight: 0,
          pieces: 0,
          shouldSendToDriver: false,
        })
      );

      if (
        watchedAdditionalReferences &&
        watchedAdditionalReferences.length > 0
      ) {
        watchedAdditionalReferences.forEach((ref: AdditionalReference) => {
          const refTypeIndex = quantumEdgeReferenceTypes.findIndex(
            (type) => type.qualifier === ref.qualifier
          );
          if (refTypeIndex !== -1) {
            defaultRefs[refTypeIndex] = ref;
          }
        });
      }

      replace(defaultRefs);
    }
  }, [watchedAdditionalReferences, replace]);

  const getRefIndexByQualifier = (qualifier: string): number => {
    return fields.findIndex((field) => field.qualifier === qualifier);
  };

  return (
    <>
      <LoadBuildingTextInput
        name='poNums'
        label='PO Number'
        placeholder='PO Number'
      />

      <Grid cols='2' gap='sm' className='w-full m-0'>
        <LoadBuildingTextInput
          name={
            `additionalReferences.${getRefIndexByQualifier('BOL')}.number` as FieldPath<NormalizedLoad>
          }
          label='BOL#'
          placeholder='BOL#'
        />
        <LoadBuildingTextInput
          name={
            `additionalReferences.${getRefIndexByQualifier('CHASSIS')}.number` as FieldPath<NormalizedLoad>
          }
          label='CHASSIS#'
          placeholder='CHASSIS#'
          readOnly={true}
        />
      </Grid>

      <Grid cols='2' gap='sm' className='w-full m-0'>
        <LoadBuildingTextInput
          name={
            `additionalReferences.${getRefIndexByQualifier('SEAL')}.number` as FieldPath<NormalizedLoad>
          }
          label='SEAL#'
          placeholder='SEAL#'
        />
        <LoadBuildingTextInput
          name={
            `additionalReferences.${getRefIndexByQualifier('CONTAINER')}.number` as FieldPath<NormalizedLoad>
          }
          label='CONT#'
          placeholder='CONT#'
        />
      </Grid>
    </>
  );
};

export default AdditionalReferencesForm;
