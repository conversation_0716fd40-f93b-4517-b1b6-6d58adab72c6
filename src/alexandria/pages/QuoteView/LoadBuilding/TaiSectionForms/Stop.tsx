import { JSX, useEffect } from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { ReferenceNumberInput } from 'components/input/ReferenceNumberInput';
import { Grid } from 'components/layout';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { NormalizedLoad, TMSLocation } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import {
  GenericCompanySearchableFields,
  locationSearchHandler,
  mapLocationsToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { LoadBuildingTextInput } from '../McleodLoadBuildingForm';

export type StopFormProps = {
  stop: 'pickup' | 'consignee';
  formMethods: UseFormReturn<NormalizedLoad>;
  isLoadingLocations: boolean;
  locations: Maybe<TMSLocation[]>;
  handleRefreshLocations: () => void;
  setLocations: React.Dispatch<React.SetStateAction<Maybe<TMSLocation[]>>>;
  originalSuggestionData?: NormalizedLoad;
};

export function StopForm({
  stop,
  formMethods,
  isLoadingLocations,
  locations,
  handleRefreshLocations,
  setLocations,
  originalSuggestionData,
}: StopFormProps): JSX.Element {
  const {
    control,
    formState: { errors },
    watch,
    setValue,
    getValues,
  } = formMethods;
  const { tmsIntegrations } = useServiceFeatures();

  const watchedStop = watch(stop);
  const watchedAddress2 = watch(`${stop}.addressLine2`);

  const handleLocationSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return locationSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      locations,
      setLocations,
      field,
      value,
    });
  };

  useEffect(() => {
    if (watchedStop?.externalTMSID) {
      const selectedLocation = locations?.find(
        (l) => l.externalTMSID === watchedStop?.externalTMSID
      );
      if (!selectedLocation) {
        return;
      }

      // Copy over the base CompanyCoreInfo fields
      const baseFields = {
        externalTMSID: selectedLocation.externalTMSID,
        name: selectedLocation.name,
        addressLine1: selectedLocation.addressLine1,
        addressLine2: selectedLocation.addressLine2 || '',
        city: selectedLocation.city,
        state: selectedLocation.state,
        zipCode: selectedLocation.zipCode,
        country: selectedLocation.country || '',
        contact: selectedLocation.contact || '',
        phone: selectedLocation.phone || '',
        email: selectedLocation.email || '',
      };

      // Set the base fields
      Object.entries(baseFields).forEach(([key, value]) => {
        setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true,
        });
      });

      // Set the additional fields from the location
      const additionalFields = {
        notes: selectedLocation.notes || '',
        instructions: selectedLocation.instructions || '',
        note: selectedLocation.note || '',
        refNumber: selectedLocation.refNumber || '',
        locationType: selectedLocation.locationType || '',
        openTime: selectedLocation.openTime || null,
        closeTime: selectedLocation.closeTime || null,
        tsaCompliant: selectedLocation.isTSACompliant || false,
      };

      Object.entries(additionalFields).forEach(([key, value]) => {
        setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true,
        });
      });
    }
  }, [watchedStop?.externalTMSID]);

  return (
    <>
      <RHFDebounceSelect
        required
        name={`${stop}.externalTMSID` as FieldPath<NormalizedLoad>}
        label='Location'
        control={control}
        errors={errors}
        data={locations}
        isLoading={isLoadingLocations}
        refreshHandler={handleRefreshLocations}
        fetchOptions={handleLocationSearch}
        mapOptions={mapLocationsToAntdOptions}
      />

      {watchedStop && (
        <>
          <div className='text-sm text-neutral-500 mb-2'>
            Location Information
          </div>
          <div className='space-y-2'>
            <div className='text-sm'>
              <span className='font-medium'>Address:</span>{' '}
              {watchedStop.addressLine1}
            </div>
            {watchedAddress2 && (
              <div className='text-sm'>
                <span className='font-medium'>Address Line 2:</span>{' '}
                {watchedAddress2}
              </div>
            )}
            <div className='text-sm'>
              <span className='font-medium'>City:</span> {watchedStop.city}
            </div>
            <div className='text-sm'>
              <span className='font-medium'>State:</span> {watchedStop.state}
            </div>
            <div className='text-sm'>
              <span className='font-medium'>Zip Code:</span>{' '}
              {watchedStop.zipCode}
            </div>
          </div>

          <div className='mt-4'>
            <LoadBuildingTextInput
              name={`${stop}.notes` as FieldPath<NormalizedLoad>}
              label='Notes'
              placeholder='Enter location notes'
            />

            <LoadBuildingTextInput
              name={`${stop}.instructions` as FieldPath<NormalizedLoad>}
              label='Instructions'
              placeholder='Enter special instructions'
            />

            <LoadBuildingTextInput
              name={`${stop}.note` as FieldPath<NormalizedLoad>}
              label='Note'
              placeholder='Enter additional notes'
            />

            <ReferenceNumberInput
              name={`${stop}.refNumber` as FieldPath<NormalizedLoad>}
              control={control}
              label='Reference Number'
              placeholder='Enter reference number'
              load={originalSuggestionData || getValues()}
            />

            <LoadBuildingTextInput
              name={`${stop}.locationType` as FieldPath<NormalizedLoad>}
              label='Location Type'
              placeholder='Enter location type'
            />

            <Grid cols='2' gap='lg' className='mx-0 w-full'>
              <LoadBuildingTextInput
                name={`${stop}.openTime` as FieldPath<NormalizedLoad>}
                label='Open Time'
                inputType='time'
                placeholder='Enter open time'
              />

              <LoadBuildingTextInput
                name={`${stop}.closeTime` as FieldPath<NormalizedLoad>}
                label='Close Time'
                inputType='time'
                placeholder='Enter close time'
              />
            </Grid>

            <div className='mt-2'>
              <label className='flex items-center space-x-2'>
                <input
                  type='checkbox'
                  checked={watchedStop.tsaCompliant || false}
                  onChange={(e) => {
                    setValue(
                      `${stop}.tsaCompliant` as FieldPath<NormalizedLoad>,
                      e.target.checked,
                      {
                        shouldDirty: true,
                      }
                    );
                  }}
                  className='rounded border-neutral-400 text-info-600 focus:ring-info-500'
                />
                <span className='text-sm text-neutral-700'>TSA Compliant</span>
              </label>
            </div>
          </div>
        </>
      )}
    </>
  );
}
