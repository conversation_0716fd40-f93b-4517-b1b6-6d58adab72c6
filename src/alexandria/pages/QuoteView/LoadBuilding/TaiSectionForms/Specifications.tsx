import { JSX } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { Label } from 'components/Label';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from '../McleodLoadBuildingForm';

const serviceTypes = ['Any', 'Expedite', 'Standard'];
const transportTypes = ['Van', 'Flatbed', 'Reefer'];
const equipmentSizeList = ['20ft', '40ft', '45ft', '48ft', '53ft'];
const weightUnitsList = ['t', 'oz', 'ton', 'g', 'lb', 'kg'];
const unitsList = ['PCS', 'PAL', 'BOX', 'DRUM', 'ROLL', 'SKID'];

export function SpecificationsForm({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}): JSX.Element {
  const {
    control,
    formState: { errors },
    watch,
  } = formMethods;

  const modes = ['Truckload'];
  const watchedCommodities = watch('specifications.commodities');
  const watchedGrossWeight = watch('specifications.totalWeight.val');
  const watchedNetWeight = watch('specifications.netWeight.val');

  return (
    <Grid cols='4' gap='sm' className='mx-0 w-full'>
      <div className='col-span-4'>
        <Label name={'mode'} required={true}>
          Mode
        </Label>
        <Controller
          name='mode'
          control={control}
          rules={{ required: 'Required' }}
          render={({ field }) => (
            <div className='text-neutral-500'>
              <AntdSelect
                showSearch
                disabled={true} // Only Truckloads supported
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={modes?.map((mode) => ({
                  value: mode,
                  label: mode,
                }))}
              />
            </div>
          )}
        />
        <ErrorMessage
          errors={errors}
          name={'mode'}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>

      <div className='col-span-4'>
        <Label name={'serviceType'} required={true}>
          Service Type
        </Label>
        <Controller
          name='specifications.serviceType'
          control={control}
          rules={{ required: 'Required' }}
          render={({ field }) => (
            <div className='text-neutral-500'>
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={serviceTypes?.map((serviceType) => ({
                  value: serviceType,
                  label: serviceType,
                }))}
              />
            </div>
          )}
        />
        <ErrorMessage
          errors={errors}
          name={'specifications.serviceType'}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>

      <Grid cols='4' gap='sm' className='mx-0 w-full col-span-4'>
        <div className='col-span-2'>
          <LoadBuildingTextInput
            name='specifications.commodities'
            label='Item'
            required={true}
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='specifications.totalPieces.val'
            label='Quantity'
            inputType='number'
            options={{
              valueAsNumber: true,
              required: watchedCommodities != '' ? 'Required' : undefined,
            }}
            required={watchedCommodities != ''}
          />
        </div>

        <div className='col-span-1'>
          <Label
            name={'specifications.totalPieces.unit'}
            required={watchedCommodities != ''}
          >
            Units
          </Label>
          <Controller
            name='specifications.totalPieces.unit'
            control={control}
            rules={{ required: watchedCommodities != '' }}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={unitsList?.map((unit) => ({
                  value: unit,
                  label: unit,
                }))}
              />
            )}
          />
        </div>
      </Grid>

      <div className='col-span-2'>
        <Label name={'specifications.transportType'} required={true}>
          Equipment
        </Label>
        <Controller
          name='specifications.transportType'
          control={control}
          rules={{ required: true }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-neutral-500'
              placeholder={'Choose'}
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={transportTypes?.map((type) => ({
                value: type,
                label: type,
              }))}
            />
          )}
        />
        <ErrorMessage
          errors={errors}
          name={'specifications.transportType'}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>

      <div className='col-span-2'>
        <Label name={'specifications.transportSize'} required={false}>
          Size
        </Label>
        <Controller
          name='specifications.transportSize'
          control={control}
          rules={{ required: false }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-neutral-500'
              placeholder={'Choose'}
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={equipmentSizeList?.map((size) => ({
                value: size,
                label: size,
              }))}
            />
          )}
        />
      </div>

      <Grid cols='2' gap='sm' className='mx-0 w-full col-span-4'>
        <Grid cols='2' gap='sm' className='mx-0 w-full col-span-2'>
          <div className='col-span-1'>
            <LoadBuildingTextInput
              name='specifications.totalWeight.val'
              label='Gross Weight'
              inputType='number'
              options={{ valueAsNumber: true }}
              required={false}
            />
          </div>

          <Flex direction='col' className='col-span-1'>
            <Label
              name={'specifications.totalWeight.unit'}
              required={watchedGrossWeight > 0}
            >
              Units
            </Label>
            <Controller
              name='specifications.totalWeight.unit'
              control={control}
              rules={{ required: watchedGrossWeight > 0 }}
              render={({ field }) => (
                <AntdSelect
                  showSearch
                  className='h-9 text-neutral-500'
                  placeholder={'Choose'}
                  optionFilterProp='children'
                  filterOption={(
                    input: string,
                    option: BaseOptionType | undefined
                  ) =>
                    (option?.label.toLocaleLowerCase() ?? '').includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(
                    optionA: BaseOptionType,
                    optionB: BaseOptionType
                  ) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  onChange={field.onChange}
                  value={field.value}
                  options={weightUnitsList?.map((unit) => ({
                    value: unit,
                    label: unit,
                  }))}
                />
              )}
            />
          </Flex>
        </Grid>

        <Grid cols='2' gap='sm' className='mx-0 w-full col-span-2'>
          <div className='col-span-1'>
            <LoadBuildingTextInput
              name='specifications.netWeight.val'
              label='Net Weight'
              inputType='number'
              options={{ valueAsNumber: true }}
              required={false}
            />
          </div>

          <Flex direction='col' className='col-span-1'>
            <Label
              name={'specifications.netWeight.unit'}
              required={watchedNetWeight > 0}
            >
              Units
            </Label>
            <Controller
              name='specifications.netWeight.unit'
              control={control}
              rules={{ required: watchedNetWeight > 0 }}
              render={({ field }) => (
                <AntdSelect
                  showSearch
                  className='h-9 text-neutral-500'
                  placeholder={'Choose'}
                  optionFilterProp='children'
                  filterOption={(
                    input: string,
                    option: BaseOptionType | undefined
                  ) =>
                    (option?.label.toLocaleLowerCase() ?? '').includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(
                    optionA: BaseOptionType,
                    optionB: BaseOptionType
                  ) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  onChange={field.onChange}
                  value={field.value}
                  options={weightUnitsList?.map((unit) => ({
                    value: unit,
                    label: unit,
                  }))}
                />
              )}
            />
          </Flex>
        </Grid>
      </Grid>
    </Grid>
  );
}
