import { JSX, useEffect, useRef, useState } from 'react';
import {
  Controller,
  FieldErrors,
  FieldPath,
  UseFormReturn,
  useFieldArray,
} from 'react-hook-form';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { ReferenceNumberCard } from 'components/ReferenceNumberCard';
import { ReferenceNumberModal } from 'components/ReferenceNumberModal';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { InputValue } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { LoadDateTimeInput } from 'pages/LoadView/LoadInformation/Components';
import { getReferenceNumberOptions } from 'pages/QuoteView/LoadBuilding/McleodSectionForms/helpers';
import { AdditionalReference, NormalizedLoad, TMSLocation } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import {
  GenericCompanySearchableFields,
  locationSearchHandler,
  mapLocationsToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { LoadBuildingTextInput } from '../McleodLoadBuildingForm';

export function StopForm({
  stop,
  formMethods,
  isLoadingLocations,
  locations,
  handleRefreshLocations,
  setLocations,
  originalSuggestionData,
}: {
  stop: 'pickup' | 'consignee';
  formMethods: UseFormReturn<NormalizedLoad>;
  isLoadingLocations: boolean;
  locations: Maybe<TMSLocation[]>;
  handleRefreshLocations: () => void;
  setLocations: React.Dispatch<React.SetStateAction<Maybe<TMSLocation[]>>>;
  originalSuggestionData?: NormalizedLoad;
}): JSX.Element {
  const [isEditingLocation] = useState(false);
  const [isRefNumberModalOpen, setIsRefNumberModalOpen] = useState(false);
  const [editingRefIndex, setEditingRefIndex] = useState<number | null>(null);
  const [tempRefNumber, setTempRefNumber] = useState<AdditionalReference>({
    qualifier: '',
    number: '',
    weight: 0,
    pieces: 0,
    shouldSendToDriver: false,
  });
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  // const [isCreatingLocation, setIsCreatingLocation] = useState(false); // placeholder to demo loading state
  // const { toast } = useToast();

  const { tmsIntegrations } = useServiceFeatures();
  const tms = tmsIntegrations?.find((tms) => tms.name === TMS.McleodEnterprise);
  const {
    control,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = formMethods;

  const { fields, append, remove, update } = useFieldArray({
    control,
    name: `${stop}.additionalReferences`,
  });

  const qualifierCodes = getReferenceNumberOptions(tms);

  // Helper function to determine if a field is required based on tenant
  const getFieldValidation = (fieldName: string) => {
    // For FCFM, only city and state are required
    if (
      tms?.featureFlags.isOnlyCityStateRequired === true &&
      !['city', 'state'].includes(fieldName)
    ) {
      return {};
    }

    // For all other tenants (including Trident), use existing validation
    return !watchedLocationID
      ? { required: 'Required if existing ID not selected' }
      : isEditingLocation
        ? { required: 'Required' }
        : {};
  };

  // const isPickup = stop === 'pickup';
  const watchedLocationID = watch(`${stop}.externalTMSID`);
  const watchedLocObj = watch(stop);
  const watchedLoad = getValues();

  const handleRefNumberModalOk = () => {
    // Validate required fields
    if (!tempRefNumber.qualifier?.trim() || !tempRefNumber.number?.trim()) {
      return; // Don't proceed if required fields are missing
    }

    const newRef: AdditionalReference = { ...tempRefNumber };

    if (editingRefIndex !== null) {
      // Update existing reference
      update(editingRefIndex, newRef);
    } else {
      // Add new reference
      append(newRef);
    }

    // Clear temporary values
    setTempRefNumber({
      qualifier: '',
      number: '',
      weight: 0,
      pieces: 0,
      shouldSendToDriver: false,
    });

    setIsRefNumberModalOpen(false);
    setEditingRefIndex(null);
  };

  const handleRefNumberModalCancel = () => {
    // Clear temporary values
    setTempRefNumber({
      qualifier: '',
      number: '',
      weight: 0,
      pieces: 0,
      shouldSendToDriver: false,
    });

    setIsRefNumberModalOpen(false);
    setEditingRefIndex(null);
  };

  const handleEditReference = (index: number) => {
    const reference = fields[index] as AdditionalReference;
    // Pre-populate the modal with existing values
    setTempRefNumber({
      qualifier: reference.qualifier,
      number: reference.number,
      weight: reference.weight,
      pieces: reference.pieces,
      shouldSendToDriver: reference.shouldSendToDriver,
    });
    setEditingRefIndex(index);
    setIsRefNumberModalOpen(true);
  };

  const handleAddNewReference = () => {
    // Clear any existing temporary values
    setTempRefNumber({
      qualifier: '',
      number: '',
      weight: 0,
      pieces: 0,
      shouldSendToDriver: false,
    });
    setEditingRefIndex(null);
    setIsRefNumberModalOpen(true);
  };

  // Update the temp state when modal form changes
  const handleTempRefChange = (
    field: keyof AdditionalReference,
    value: any
  ) => {
    setTempRefNumber((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  {
    /* TODO: Finalize manaul location creation UI/UX */
  }
  // const handleCreateLocation = async () => {
  //   const isValid = await validateNewLocation(watchedLocObj);
  //   if (!isValid) {
  //     return;
  //   }

  //   setIsCreatingLocation(true);
  //   const res = await createLocation({
  //     tmsID: tmsIntegrations[0]?.id,
  //     location: {
  //       ...(getValues(`${stop}`) as TMSLocation),
  //       isShipper: isPickup,
  //       isConsignee: !isPickup,
  //     },
  //   });

  //   if (res.isOk()) {
  //     setIsEditingLocation(false);
  //     toast({
  //       description: `Location ${res.value.location.externalTMSID} created in McLeod`,
  //       variant: 'success',
  //     });
  //     setLocations((prevLocations) =>
  //       injectSelectedObject(res.value.location, prevLocations ?? [])
  //     );
  //     for (const [key, value] of Object.entries(res.value.location)) {
  //       setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
  //         shouldDirty: true,
  //       });
  //     }
  //   } else {
  //     toast({
  //       description: res.error.message,
  //       variant: 'destructive',
  //     });
  //   }

  //   setIsCreatingLocation(false);
  // };

  // const handleCancelEditingLocation = () => {
  //   setValue(`${stop}.externalTMSID`, '');
  //   setValue(`${stop}.name`, '');
  //   setValue(`${stop}.addressLine1`, '');
  //   setValue(`${stop}.addressLine2`, '');
  //   setValue(`${stop}.city`, '');
  //   setValue(`${stop}.state`, '');
  //   setValue(`${stop}.zipCode`, '');
  //   setValue(`${stop}.apptRequired`, false);

  //   clearErrors(`${stop}`);
  //   setIsEditingLocation(false);
  // };

  // const handleEditlocation = () => {
  //   setIsEditingLocation(true);
  //   setValue(`${stop}.externalTMSID`, '');
  // };

  const handleLocationSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return locationSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      locations,
      setLocations,
      field,
      value,
    });
  };
  // const validateNewLocation = async (
  //   data: NormalizedPickup | NormalizedDropoff
  // ): Promise<boolean> => {
  //   const tmsLocationKeys: Array<keyof TMSLocation> = [
  //     'name',
  //     'addressLine1',
  //     'addressLine2',
  //     'city',
  //     'state',
  //     'zipCode',
  //     'apptRequired',
  //   ];

  //   // array.forEach does not handle async/await properly
  //   const results = await Promise.all(
  //     tmsLocationKeys.map(async (key) => {
  //       if (key in data) {
  //         const valid = await trigger(
  //           `${stop}.${key}` as FieldPath<NormalizedLoad>
  //         );
  //         return valid;
  //       }
  //       return true; // If the key is not in data, consider it valid.
  //     })
  //   );

  //   const isSectionValid = results.every((result) => result);
  //   return isSectionValid;
  // };

  useEffect(() => {
    if (watchedLocationID) {
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );
      if (!selectedLoc) {
        return;
      }

      Object.entries(selectedLoc).forEach(([key, value]) => {
        if (key === 'contact' || key === 'phone') {
          return;
        }

        setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true, // this ensures the AI-label is handled correctly
        });
      });
    }
  }, [watchedLocationID]);

  useEffect(() => {
    if (watchedLocationID) {
      // Remove ID if address is write-in
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );
      if (!selectedLoc) {
        return;
      }

      if (
        (selectedLoc?.name &&
          watchedLocObj?.name.toLowerCase() !==
            selectedLoc?.name.toLowerCase()) ||
        (selectedLoc?.addressLine1 &&
          watchedLocObj?.addressLine1.toLowerCase() !==
            selectedLoc?.addressLine1.toLowerCase()) ||
        (selectedLoc?.addressLine2 &&
          watchedLocObj?.addressLine2?.toLowerCase() !==
            selectedLoc?.addressLine2?.toLowerCase()) ||
        (selectedLoc?.city &&
          watchedLocObj?.city?.toLowerCase() !==
            selectedLoc?.city?.toLowerCase()) ||
        (selectedLoc?.state &&
          watchedLocObj?.state?.toLowerCase() !==
            selectedLoc?.state?.toLowerCase()) ||
        (selectedLoc?.zipCode &&
          watchedLocObj?.zipCode?.toLowerCase() !==
            selectedLoc?.zipCode?.toLowerCase())
      ) {
        setValue(`${stop}.externalTMSID` as FieldPath<NormalizedLoad>, '');
      }
    }
  }, [
    watchedLocationID,
    watchedLocObj?.name,
    watchedLocObj?.addressLine1,
    watchedLocObj?.addressLine2,
    watchedLocObj?.city,
    watchedLocObj?.state,
    watchedLocObj?.zipCode,
  ]);

  // Reset scroll position when going from multiple cards to single card
  useEffect(() => {
    if (fields.length === 1 && scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft = 0;
    }
  }, [fields.length]);

  return (
    <>
      <RHFDebounceSelect
        // required={isPickup && !isEditingLocation}
        required={false}
        disabled={isEditingLocation}
        name={`${stop}.externalTMSID`}
        label='Location'
        control={control}
        placeholder='Search by name, address, or city'
        errors={errors}
        data={locations}
        isLoading={isLoadingLocations}
        refreshHandler={handleRefreshLocations}
        fetchOptions={handleLocationSearch}
        mapOptions={mapLocationsToAntdOptions}
        showSearchParamDropdown={false}
        searchFieldDefault='nameAddress'
        valueRenderer={(selectedOption) => {
          // Show the location name instead of external TMS ID in the input
          return {
            label: selectedOption.name || selectedOption.label,
            value: selectedOption.value,
          };
        }}
      />

      {isEditingLocation && <hr className='my-2' />}

      <LoadBuildingTextInput
        name={`${stop}.name`}
        label='Name'
        // The Mcleod location objects *probably* have all the address fields we required, but in case it doesn't conform to our expectations,
        // we don't require it when the user selects an existing location so the form isn't blocked.
        // This happened before where we required zipcodes to be 5 digits, but the existing TMS location had 6 digits and the input was read-only
        // so the user couldn't submit the form.
        options={getFieldValidation('name')}
      />

      <LoadBuildingTextInput
        name={`${stop}.addressLine1`}
        label='Address Line 1'
        options={getFieldValidation('addressLine1')}
      />

      <Grid cols='2' gap='sm' className='w-full m-0'>
        <LoadBuildingTextInput
          name={`${stop}.addressLine2`}
          label='Address Line 2'
        />

        <LoadBuildingTextInput
          name={`${stop}.city`}
          label='City'
          options={getFieldValidation('city')}
        />
      </Grid>
      <Grid cols='2' gap='sm' className='w-full m-0'>
        <LoadBuildingTextInput
          name={`${stop}.state`}
          label='State'
          options={getFieldValidation('state')}
        />

        {/* <div className='grid items-end grid-cols-2 w-full m-0 gap-2'> */}
        <LoadBuildingTextInput
          name={`${stop}.zipCode`}
          label='Zip Code'
          placeholder='12345'
          options={getFieldValidation('zipCode')}
        />
      </Grid>

      <Typography
        variant='h6'
        weight='medium'
        className='text-neutral-800 mt-3 mb-2'
      >
        Contact Information
      </Typography>

      <Grid cols='2' gap='sm' className='w-full m-0'>
        <LoadBuildingTextInput
          name={`${stop}.contact`}
          label='Contact Person'
          required={false}
          placeholder='Contact name'
        />

        <LoadBuildingTextInput
          name={`${stop}.phone`}
          label='Phone'
          required={false}
          placeholder='(*************'
          inputValue={InputValue.PHONE_NUMBER}
        />
      </Grid>

      {/* Reference Number Section */}
      <Flex direction='col' gap='sm' className='mt-1'>
        <Flex align='center' justify='between' className='mr-px'>
          <Typography variant='h6' weight='medium' className='text-neutral-800'>
            Reference Numbers
          </Typography>
          <Button
            type='button'
            variant='ghost'
            size='sm'
            onClick={handleAddNewReference}
            className='h-8 px-3 text-xs text-brand-500 hover:-mx-px'
            buttonNamePosthog={
              ButtonNamePosthog.LoadBuildingAddMcleodReferenceNumber
            }
          >
            + Add Reference
          </Button>
        </Flex>

        {/* Display existing reference numbers */}
        {fields.length > 0 && (
          <div className='relative'>
            <Flex
              align='start'
              gap='md'
              ref={scrollContainerRef}
              className={`overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-neutral-300 scrollbar-track-neutral-100 ${
                fields.length === 1 ? 'justify-center' : ''
              }`}
            >
              {fields.map((field, index) => {
                const ref = field as AdditionalReference;

                return (
                  <ReferenceNumberCard
                    key={field.id}
                    reference={ref}
                    index={index}
                    onRemove={remove}
                    onEdit={handleEditReference}
                  />
                );
              })}
            </Flex>
            {/* Scroll hint for multiple cards */}
            {fields.length > 1 && (
              <Typography
                variant='body-sm'
                align='center'
                className='text-neutral-500 mt-1'
              >
                ← Scroll to view more references →
              </Typography>
            )}
          </div>
        )}

        {/* Custom Reference Modal */}
        <ReferenceNumberModal
          qualifierOptions={qualifierCodes}
          isOpen={isRefNumberModalOpen}
          onCancel={handleRefNumberModalCancel}
          onOk={handleRefNumberModalOk}
          editingRefIndex={editingRefIndex}
          tempRefNumber={tempRefNumber}
          onTempRefChange={handleTempRefChange}
          stop={stop}
        />
      </Flex>

      {/* TODO: Finalize manaul location creation UI/UX */}
      {/* {isEditingLocation ? (
          <Flex direction="col" gap="sm">
            <AntdButton
              type='link'
              className='h-0 text-neutral-400'
              onClick={handleCancelEditingLocation}
            >
              Cancel
            </AntdButton>
            <Button size='sm' type='button' onClick={handleCreateLocation}>
              {isCreatingLocation ? <ButtonLoader /> : 'Submit'}
            </Button>
          </Flex>
        ) : (
          <Button size='sm' type='button' onClick={handleEditlocation}>
            Add Location
          </Button>
        )} */}
      {/* </div> */}
      {/* Not supported rn. Buggy duplication behavior on update */}
      {/* <LoadBuildingTextInput
        name={`${stop}.refNumber`}
        label='Ref #'
        placeholder='Comma-separated list (e.g. 123,456,789)'
      /> */}

      {(watchedLocObj?.apptRequired ||
        isEditingLocation ||
        !watchedLocObj.externalTMSID) && (
        <Controller
          name={`${stop}.apptRequired`}
          control={control}
          render={({ field }) => (
            <Checkbox
              label='Appointment Required?'
              labelClassName='leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-neutral-500'
              checked={field.value ?? false}
              onCheckedChange={(checked) => field.onChange(checked)}
            />
          )}
        />
      )}

      {isEditingLocation && <hr className='my-2' />}

      <LoadDateTimeInput
        name={`${stop}.apptStartTime` as FieldPath<NormalizedLoad>}
        label='Appointment Start Time'
        load={originalSuggestionData || watchedLoad}
        required
      />

      <LoadDateTimeInput
        name={`${stop}.apptEndTime` as FieldPath<NormalizedLoad>}
        label='Appointment End Time'
        load={originalSuggestionData || watchedLoad}
      />
    </>
  );
}

export function hasNoErrorsInSubsection(
  subsectionPath: string,
  formErrors: FieldErrors
): boolean {
  const subsectionErrors = Object.entries(formErrors).filter(([key]) =>
    key.startsWith(subsectionPath)
  );

  return subsectionErrors.length === 0;
}
