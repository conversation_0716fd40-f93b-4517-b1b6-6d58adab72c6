import {
  McleodCommodityOption,
  McleodReferenceNumberOption,
  ValueLabelOption,
} from 'pages/QuoteView/LoadBuilding/McleodSectionForms/types';

/* --------- TENANTS --------- */

export enum Tenant {
  Trident = 'trident',
  Fetch = 'fcfm',
  Syfan = 'syfn',
  Tumalo = 'tmka',
}

/* --------- TRANSPORT TYPE OPTIONS --------- */

export const fetchFreightTransportTypeOptions: ValueLabelOption[] = [
  { value: 'AC', label: 'AC - Auto Carrier (DAT)' },
  { value: 'CN', label: 'CN - Conestoga (DAT)' },
  { value: 'C', label: 'C - Container (DAT)' },
  { value: 'CR', label: 'CR - Container Refrigerated (DAT)' },
  { value: 'CI', label: 'CI - Container, Insulated (DAT)' },
  { value: 'CV', label: 'CV - Conveyor (DAT)' },
  { value: 'DD', label: 'DD - Double Drop (DAT)' },
  { value: 'DDT', label: 'DDT - Drop Deck Trailers' },
  { value: 'LA', label: 'LA - Drop Deck, Landoll (DAT)' },
  { value: 'DT', label: 'DT - Dump Trailer (DAT)' },
  { value: 'FR', label: 'FR - Flat/Van/Reefer (DAT)' },
  { value: 'F', label: 'F - Flatbed (DAT)' },
  { value: 'FA', label: 'FA - Flatbed Airride (DAT)' },
  { value: 'BT', label: 'BT - Flatbed B-Train (DAT)' },
  { value: 'FN', label: 'FN - Flatbed Conestoga (DAT)' },
  { value: 'FZ', label: 'FZ - Flatbed Hazmat (DAT)' },
  { value: 'FH', label: 'FH - Flatbed Hotshot (DAT)' },
  { value: 'MX', label: 'MX - Flatbed Maxi (DAT)' },
  { value: 'FD', label: 'FD - Flatbed or Step Deck (DAT)' },
  { value: 'FO', label: 'FO - Flatbed Over-dimension' },
  { value: 'FM', label: 'FM - Flatbed w/ Team (DAT)' },
  { value: 'FT', label: 'FT - Flatbed w/Tarps (DAT)' },
  { value: 'FC', label: 'FC - Flatbed with Chains (DAT)' },
  { value: 'LOG', label: 'LOG - Forestry Trailers' },
  { value: 'HB', label: 'HB - Hopper Bottom (DAT)' },
  { value: 'IM10', label: "IM10 - IMDL Container 10'" },
  { value: 'IM20', label: "IM20 - IMDL Container 20'" },
  { value: 'IM40', label: "IM40 - IMDL Container 40'" },
  { value: 'IM45', label: "IM45 - IMDL Container 45'" },
  { value: 'IM48', label: "IM48 - IMDL Container 48'" },
  { value: 'IM53', label: "IM53 - IMDL Container 53'" },
  { value: 'IR', label: 'IR - Insulated Van or Reefer (DAT)' },
  { value: 'LB', label: 'LB - Lowboy (DAT)' },
  { value: 'LR', label: 'LR - Lowboy or RGN (DAT)' },
  { value: 'LO', label: 'LO - Lowboy Overdimension (DAT)' },
  { value: 'MV', label: 'MV - Moving Van (DAT)' },
  { value: 'O', label: 'O - Other' },
  { value: 'NU', label: 'NU - Pneumatic (DAT)' },
  { value: 'EPO', label: 'EPO - Power Only (DAT)' },
  { value: 'R', label: 'R - Reefer (DAT)' },
  { value: 'RA', label: 'RA - Reefer Airride (DAT)' },
  { value: 'R2', label: 'R2 - Reefer Doubles (DAT)' },
  { value: 'RZ', label: 'RZ - Reefer Hazmat (DAT)' },
  { value: 'RN', label: 'RN - Reefer Intermodal (DAT)' },
  { value: 'RL', label: 'RL - Reefer Logistics (DAT)' },
  { value: 'RV', label: 'RV - Reefer or Vented Van' },
  { value: 'RP', label: 'RP - Reefer Pallet Exchange (DAT)' },
  { value: 'RM', label: 'RM - Reefer w/ Team (DAT)' },
  { value: 'RG', label: 'RG - Removable Gooseneck (DAT)' },
  { value: 'SD', label: 'SD - Step Deck (DAT)' },
  { value: 'SR', label: 'SR - Step Deck or RGN' },
  { value: 'SN', label: 'SN - Stepdeck Conestoga (DAT)' },
  { value: 'SB', label: 'SB - Straight Box Truck' },
  { value: 'SBT', label: 'SBT - Straight Box Trucks' },
  { value: 'ST', label: 'ST - Stretch Trailer (DAT)' },
  { value: 'TA', label: 'TA - Tanker Aluminum (DAT)' },
  { value: 'TN', label: 'TN - Tanker Intermodal (DAT)' },
  { value: 'TS', label: 'TS - Tanker Steel (DAT)' },
  { value: 'TT', label: 'TT - Truck and Trailer (DAT)' },
  { value: 'V', label: 'V - Van (DAT)' },
  { value: 'VA', label: 'VA - Van Airride (DAT)' },
  { value: 'VW', label: 'VW - Van Blanket Wrap' },
  { value: 'VS', label: 'VS - Van Conestoga (DAT)' },
  { value: 'VC', label: 'VC - Van Curtain (DAT)' },
  { value: 'V2', label: 'V2 - Van Double (DAT)' },
  { value: 'VZ', label: 'VZ - Van Hazmat (DAT)' },
  { value: 'VH', label: 'VH - Van Hotshot (DAT)' },
  { value: 'VI', label: 'VI - Van Insulated (DAT)' },
  { value: 'VN', label: 'VN - Van Intermodal (DAT)' },
  { value: 'VG', label: 'VG - Van Lift-Gate (DAT)' },
  { value: 'VL', label: 'VL - Van Logistics (DAT)' },
  { value: 'VLTL', label: 'VLTL - Van LTL' },
  { value: 'OT', label: 'OT - Van Opentop (DAT)' },
  { value: 'VF', label: 'VF - Van or Flatbed (DAT)' },
  { value: 'VT', label: 'VT - Van or Flats w/Tarps (DAT)' },
  { value: 'VR', label: 'VR - Van or Reefer (DAT)' },
  { value: 'VP', label: 'VP - Van Pallet Exchange (DAT)' },
  { value: 'VB', label: 'VB - Van Roller Bed (DAT)' },
  { value: 'VV', label: 'VV - Van Vented (DAT)' },
  { value: 'VM', label: 'VM - Van w/ Team (DAT)' },
];

// trident transport, not trident logistics
export const tridentTransportTypeOptions: ValueLabelOption[] = [
  { value: '26B', label: "26'" },
  { value: '53F', label: '53ft Flatbed' },
  { value: 'AC', label: 'Auto Carrier' },
  { value: 'B', label: 'Beam Trailer' },
  { value: 'CN', label: 'Conestoga (DAT)' },
  { value: 'C', label: 'Container' },
  { value: 'CR', label: 'Container Refrigerated (DAT)' },
  { value: 'CI', label: 'Container, Insulated (DAT)' },
  { value: 'CV', label: 'Conveyor (DAT)' },
  { value: 'DD', label: 'Double Drop (DAT)' },
  { value: 'DDT', label: 'Drop Deck Trailers (DAT)' },
  { value: 'DL', label: 'Drop Deck, Landoll (DAT)' },
  { value: 'DT', label: 'Dump Trailer (DAT)' },
  { value: 'FR', label: 'Flat/Van/Reefer (DAT)' },
  { value: 'F', label: 'Flatbed (DAT)' },
  { value: 'FA', label: 'Flatbed Airride (DAT)' },
  { value: 'BT', label: 'Flatbed B-Train (DAT)' },
  { value: 'FN', label: 'Flatbed Conestoga (DAT)' },
  { value: 'FZ', label: 'Flatbed Hazmat (DAT)' },
  { value: 'FH', label: 'Flatbed Hotshot (DAT)' },
  { value: 'MX', label: 'Flatbed Maxi (DAT)' },
  { value: 'FMO', label: 'Flatbed Moffett (DAT)' },
  { value: 'FD', label: 'Flatbed or Step Deck (DAT)' },
  { value: 'FO', label: 'Flatbed Over-dimension (DAT)' },
  { value: 'FTE', label: 'Flatbed Tanker Endorsed (DAT)' },
  { value: 'FM', label: 'Flatbed w/ Team (DAT)' },
  { value: 'FT', label: 'Flatbed w/Tarps (DAT)' },
  { value: 'FC', label: 'Flatbed with Chains (DAT)' },
  { value: 'LOG', label: 'Forestry Trailers' },
  { value: 'HB', label: 'Hopper Bottom (DAT)' },
  { value: 'IR', label: 'Insulated Van or Reefer (DAT)' },
  { value: 'LB', label: 'Lowboy (DAT)' },
  { value: 'LR', label: 'Lowboy or RGN (DAT)' },
  { value: 'LO', label: 'Lowboy Overdimension (DAT)' },
  { value: 'MV', label: 'Moving Van (DAT)' },
  { value: 'O', label: 'Other' },
  { value: 'NU', label: 'Pneumatic (DAT)' },
  { value: 'PO', label: 'Power Only (DAT)' },
  { value: 'R', label: 'Reefer (DAT)' },
  { value: 'RA', label: 'Reefer Airride (DAT)' },
  { value: 'R2', label: 'Reefer Doubles (DAT)' },
  { value: 'RZ', label: 'Reefer Hazmat (DAT)' },
  { value: 'RI', label: 'Reefer Intermodal (DAT)' },
  { value: 'RN', label: 'Reefer Logistics (DAT)' },
  { value: 'RV', label: 'Reefer or Vented Van (DAT)' },
  { value: 'RP', label: 'Reefer Pallet Exchange (DAT)' },
  { value: 'RTE', label: 'Reefer Tanker Endorsed (DAT)' },
  { value: 'RTEH', label: 'Reefer Tanker Endorsed Hazmat (DAT)' },
  { value: 'RM', label: 'Reefer w/ Team (DAT)' },
  { value: 'RG', label: 'Removable Gooseneck (DAT)' },
  { value: 'SPR', label: 'Sprinter' },
  { value: 'SD', label: 'Step Deck (DAT)' },
  { value: 'SR', label: 'Step Deck or RGN (DAT)' },
  { value: 'SN', label: 'Stepdeck Conestoga (DAT)' },
  { value: 'SB', label: 'Straight Box Truck' },
  { value: 'SBT', label: 'Straight Box Trucks' },
  { value: 'ST', label: 'Stretch Trailer (DAT)' },
  { value: 'TA', label: 'Tanker Aluminum (DAT)' },
  { value: 'TN', label: 'Tanker Intermodal (DAT)' },
  { value: 'TS', label: 'Tanker Steel (DAT)' },
  { value: 'TT', label: 'Truck and Trailer (DAT)' },
  { value: 'V', label: 'Van (DAT)' },
  { value: 'VA', label: 'Van Airride (DAT)' },
  { value: 'VW', label: 'Van Blanket Wrap' },
  { value: 'VS', label: 'Van Conestoga (DAT)' },
  { value: 'VC', label: 'Van Curtain (DAT)' },
  { value: 'V2', label: 'Van Double (DAT)' },
  { value: 'VZ', label: 'Van Hazmat (DAT)' },
  { value: 'VH', label: 'Van Hotshot (DAT)' },
  { value: 'VI', label: 'Van Insulated (DAT)' },
  { value: 'VN', label: 'Van Intermodal (DAT)' },
  { value: 'VG', label: 'Van Lift-Gate (DAT)' },
  { value: 'VL', label: 'Van Logistics (DAT)' },
  { value: 'OT', label: 'Van Opentop (DAT)' },
  { value: 'VF', label: 'Van or Flatbed (DAT)' },
  { value: 'VT', label: 'Van or Flats w/Tarps (DAT)' },
  { value: 'VR', label: 'Van or Reefer (DAT)' },
  { value: 'VP', label: 'Van Pallet Exchange (DAT)' },
  { value: 'VB', label: 'Van Roller Bed (DAT)' },
  { value: 'VTE', label: 'Van Tanker Endorsed (DAT)' },
  { value: 'VTEH', label: 'Van Tanker Endorsed Hazmat (DAT)' },
  { value: 'VV', label: 'Van Vented (DAT)' },
  { value: 'VM', label: 'Van w/ Team (DAT)' },
];

// NOTE: Because of duplicate labels with different *codes*, we prepend the code to the label.
// This is required for backend to properly map the transport type to the correct code.
export const syfanTransportTypeOptions: ValueLabelOption[] = [
  { label: 'V12 - 12 FT VAN', value: 'V12' },
  { label: '16LG - 16 FOOT BOXTRUCK W/ LIFTGATE', value: '16LG' },
  { label: 'VS20 - 20 FOOT STRAIGHT TRUCK', value: 'VS20' },
  { label: '20C - 20 FT CONTAINER', value: '20C' },
  { label: '20CW - 20 FT CONTAINER OVERWEIGHT', value: '20CW' },
  { label: 'VS22 - 22 FOOT BOX TRUCK-9 YEARS OLD OR NEWER', value: 'VS22' },
  { label: '24LG - 24 FOOT BOXTRUCK - LIFT GATE', value: '24LG' },
  { label: 'V24 - 24 FOOT VAN - 9 YEARS OLD OR NEWER', value: 'V24' },
  { label: '26SG - 26 FOOT STRAIGHT TRUCK WITH LIFT GATE', value: '26SG' },
  { label: 'V28 - 28 FOOT VAN', value: 'V28' },
  { label: 'V40 - 40 FOOT DRY VAN TRAILER', value: 'V40' },
  { label: '40C - 40 FT CONTAINER', value: '40C' },
  { label: '40CW - 40 FT CONTAINER OVERWEIGHT', value: '40CW' },
  { label: 'V45 - 45 FOOT DRY VAN TRAILER', value: 'V45' },
  { label: '48LG - 48 FT TRL WITH LIFT GATE', value: '48LG' },
  { label: '53BP - 53 FOOT BULLETPROOF VAN', value: '53BP' },
  { label: 'SD53 - 53 FOOT STEP DECK', value: 'SD53' },
  { label: 'V53T - 53 FOOT VAN WITH TEAM', value: 'V53T' },
  { label: '53HD - 53 FT HEAVY DUTY VAN', value: '53HD' },
  { label: '53LG - 53 FT VAN WITH LIFT GATE', value: '53LG' },
  { label: 'V53L - 53 FT VAN- LOCAL MOVES', value: 'V53L' },
  { label: "SV53 - 53' SUPER VAN", value: 'SV53' },
  { label: "R53N - 9 YEARS OLD OR NEWER 53' REFR", value: 'R53N' },
  { label: "V53N - 9 YEARS OLD OR NEWER 53' VAN", value: 'V53N' },
  { label: "V53M - 9 YEARS OLD OR NEWER 53' VAN MEXICO ROUT", value: 'V53M' },
  { label: 'ARBT - AIR RIDE BOX TRUCK', value: 'ARBT' },
  { label: 'AC - Auto Carrier (DAT)', value: 'AC' },
  { label: 'BT12 - BOX TRUCK 12 FOOT', value: 'BT12' },
  { label: 'CVAN - CARGO VAN', value: 'CVAN' },
  { label: 'CON - CONESTOGA', value: 'CON' },
  { label: 'CN - Conestoga (DAT)', value: 'CN' },
  { label: 'CO48 - CONESTOGA 48 FOOT', value: 'CO48' },
  { label: 'C053 - CONESTOGA 53 FOOT', value: 'C053' },
  { label: 'C - Container (DAT)', value: 'C' },
  { label: 'CR - Container Refrigerated (DAT)', value: 'CR' },
  { label: 'CI - Container, Insulated (DAT)', value: 'CI' },
  { label: 'CV - Conveyor (DAT)', value: 'CV' },
  { label: 'Code - Description', value: 'Code' },
  { label: 'ST12 - DOCK HIGH STRAIGHT TRUCK 12FT', value: 'ST12' },
  { label: 'DD - Double Drop (DAT)', value: 'DD' },
  { label: 'DDT - Drop Deck Trailers', value: 'DDT' },
  { label: 'LA - Drop Deck, Landoll (DAT)', value: 'LA' },
  { label: 'DT - Dump Trailer (DAT)', value: 'DT' },
  { label: '53A5 - FCA 53 DV Shape', value: '53A5' },
  { label: '53A2 - FCA 53 DV Sterling Heights', value: '53A2' },
  { label: 'FR - Flat/Van/Reefer (DAT)', value: 'FR' },
  { label: 'F - Flatbed (DAT)', value: 'F' },
  { label: 'F25 - FLATBED 25FT', value: 'F25' },
  { label: 'F48 - FLATBED 48 FT', value: 'F48' },
  { label: 'F53 - FLATBED 53 FT', value: 'F53' },
  { label: 'FA - Flatbed Airride (DAT)', value: 'FA' },
  { label: 'BT - Flatbed B-Train (DAT)', value: 'BT' },
  { label: 'FN - Flatbed Conestoga (DAT)', value: 'FN' },
  { label: 'FZ - Flatbed Hazmat (DAT)', value: 'FZ' },
  { label: 'FH - Flatbed Hotshot (DAT)', value: 'FH' },
  { label: 'MX - Flatbed Maxi (DAT)', value: 'MX' },
  { label: 'FD - Flatbed or Step Deck (DAT)', value: 'FD' },
  { label: 'FO - Flatbed Over-dimension', value: 'FO' },
  { label: 'FM - Flatbed w/ Team (DAT)', value: 'FM' },
  { label: 'FT - Flatbed w/Tarps (DAT)', value: 'FT' },
  { label: 'FC - Flatbed with chains (DAT)', value: 'FC' },
  { label: 'FVSD - FLATBED/VAN/STEPDECK', value: 'FVSD' },
  { label: "HCV - HIGH CUBE 53' DRY VAN", value: 'HCV' },
  { label: 'HB - Hopper Bottom (DAT)', value: 'HB' },
  { label: 'HS - Hotshot', value: 'HS' },
  { label: 'IR - Insulated Van or Reefer (DAT)', value: 'IR' },
  { label: 'IM - Intermodal Trailers', value: 'IM' },
  { label: 'LTT - Load to Truck - Nestle Drop', value: 'LTT' },
  { label: 'LB - Lowboy (DAT)', value: 'LB' },
  { label: 'LR - Lowboy or RGN (DAT)', value: 'LR' },
  { label: 'LO - Lowboy Overdimension (DAT)', value: 'LO' },
  { label: 'LB55 - Lowboy Trailer, 55 ton', value: 'LB55' },
  { label: 'MV - Moving Van (DAT)', value: 'MV' },
  { label: 'O - Other', value: 'O' },
  { label: 'PAR - PARTIAL TRUCK LOAD- MIXED FREIGHT', value: 'PAR' },
  { label: 'NU - Pneumatic (DAT)', value: 'NU' },
  { label: 'PO-D - Power Only - DOUBLES', value: 'PO-D' },
  { label: 'PO-H - Power Only - HAZMAT', value: 'PO-H' },
  { label: 'PO-T - Power Only - Team', value: 'PO-T' },
  { label: 'PO - Power Only (DAT)', value: 'PO' },
  { label: 'LM - POWER ONLY LOCAL', value: 'LM' },
  { label: '4LM - POWER ONLY LOCAL MOVES', value: '4LM' },
  { label: 'R - Reefer (DAT)', value: 'R' },
  { label: 'R48 - REEFER 48 FT', value: 'R48' },
  { label: 'R53 - REEFER 53 FT', value: 'R53' },
  { label: 'R53D - REEFER 53 FT', value: 'R53D' },
  { label: 'RA - Reefer Airride (DAT)', value: 'RA' },
  { label: 'R2 - Reefer Doubles (DAT)', value: 'R2' },
  { label: 'RZ - Reefer Hazmat (DAT)', value: 'RZ' },
  { label: 'RN - Reefer Intermodal (DAT)', value: 'RN' },
  { label: 'RL - Reefer Logistics (DAT)', value: 'RL' },
  { label: 'RV - Reefer or Vented Van', value: 'RV' },
  { label: 'RP - Reefer Pallet Exchange (DAT)', value: 'RP' },
  { label: 'RM - Reefer w/ Team (DAT)', value: 'RM' },
  { label: 'RG - Removable GooseNeck (DAT)', value: 'RG' },
  { label: 'SW - Southwest carrier', value: 'SW' },
  { label: 'SWR - Southwest reefer Carrier', value: 'SWR' },
  { label: 'SV - SPRINTER VAN', value: 'SV' },
  { label: 'SD - Step Deck (DAT)', value: 'SD' },
  { label: 'SR - Step Deck or RGN', value: 'SR' },
  { label: 'SN - Stepdeck Conestoga (DAT)', value: 'SN' },
  { label: 'SB - Straight Box Truck', value: 'SB' },
  { label: 'ST - Strectch Trailer (DAT)', value: 'ST' },
  { label: 'TA - Tanker Aluminum (DAT)', value: 'TA' },
  { label: 'TN - Tanker Intermodal (DAT)', value: 'TN' },
  { label: 'TS - Tanker Steel (DAT)', value: 'TS' },
  { label: 'TEAM - TEAMS AVAILABLE', value: 'TEAM' },
  { label: 'LA50 - Traveling Axle Trailer, Landoll (Cali)', value: 'LA50' },
  { label: 'LA53 - Traveling Axle Trailer, Landoll w/ Winch', value: 'LA53' },
  { label: 'LA41 - Traveling Axle Trailer, Landoll w/ Winch', value: 'LA41' },
  { label: 'LA48 - Traveling Axle Trailer, Landoll w/ Winch', value: 'LA48' },
  { label: 'TT - Truck and Trailer (DAT)', value: 'TT' },
  { label: 'UPS - UPS', value: 'UPS' },
  { label: 'PS15 - UPS PEAK 2015', value: 'PS15' },
  { label: 'V - Van (DAT)', value: 'V' },
  { label: 'V48 - VAN 48 FT', value: 'V48' },
  { label: 'V5NT - VAN 53 9 YEARS OR NEWER/TEAM NEEDED', value: 'V5NT' },
  { label: 'V53D - VAN 53 FT', value: 'V53D' },
  { label: 'V53 - VAN 53 FT', value: 'V53' },
  { label: 'VA - Van Airride (DAT)', value: 'VA' },
  { label: 'VW - Van Blanket Wrap', value: 'VW' },
  { label: 'VS - Van Conestoga (DAT)', value: 'VS' },
  { label: 'VC - Van Curtain (DAT)', value: 'VC' },
  { label: 'V2 - Van Double (DAT)', value: 'V2' },
  { label: 'VZ - Van Hazmat (DAT)', value: 'VZ' },
  { label: 'VH - Van Hotshot (DAT)', value: 'VH' },
  { label: 'VI - Van Insulated (DAT)', value: 'VI' },
  { label: 'VN - Van Intermodal (DAT)', value: 'VN' },
  { label: 'VG - Van Lift-Gate (DAT)', value: 'VG' },
  { label: 'VL - Van Logistics (DAT)', value: 'VL' },
  { label: 'OT - Van Opentop (DAT)', value: 'OT' },
  { label: 'VF - Van or Flatbed (DAT)', value: 'VF' },
  { label: 'VT - Van or Flats w/Tarps (DAT)', value: 'VT' },
  { label: 'VR - Van or Reefer (DAT)', value: 'VR' },
  { label: 'VR48 - VAN OR REEFER 48 FT', value: 'VR48' },
  { label: 'VR53 - VAN OR REEFER 53 FT', value: 'VR53' },
  { label: 'VRTM - VAN OR REEFER 53 WITH TEAM', value: 'VRTM' },
  { label: 'VP - Van Pallet Exchange (DAT)', value: 'VP' },
  { label: 'VB - Van Roller Bed (DAT)', value: 'VB' },
  { label: 'VSP - VAN SPRINTER', value: 'VSP' },
  { label: 'V53A - VAN TRAILER AIR RIDE 53FT', value: 'V53A' },
  { label: 'VV - Van Vented (DAT)', value: 'VV' },
  { label: 'VM - Van w/ Team (DAT)', value: 'VM' },
];

export const tumaloTransportTypeOptions: ValueLabelOption[] = [
  { value: 'VR53', label: '53 Foot dry van or reefer' },
  { value: 'V53', label: "53' van" },
  { value: 'ACP', label: 'AIR - CARGO PLANE' },
  { value: 'AC', label: 'Auto Carrier (DAT)' },
  { value: 'CN', label: 'Conestoga (DAT)' },
  { value: 'C', label: 'Container (DAT)' },
  { value: 'CR', label: 'Container Refrigerated (DAT)' },
  { value: 'CI', label: 'Container, Insulated (DAT)' },
  { value: 'CV', label: 'Conveyor (DAT)' },
  { value: 'DD', label: 'Double Drop (DAT)' },
  { value: 'LA', label: 'Drop Deck, Landoll (DAT)' },
  { value: 'DT', label: 'Dump Trailer (DAT)' },
  { value: 'FR', label: 'Flat/Van/Reefer (DAT)' },
  { value: 'F', label: 'Flatbed (DAT)' },
  { value: 'FA', label: 'Flatbed Airride (DAT)' },
  { value: 'BT', label: 'Flatbed B-Train (DAT)' },
  { value: 'FN', label: 'Flatbed Conestoga (DAT)' },
  { value: 'FZ', label: 'Flatbed Hazmat (DAT)' },
  { value: 'FH', label: 'Flatbed Hotshot (DAT)' },
  { value: 'MX', label: 'Flatbed Maxi (DAT)' },
  { value: 'FD', label: 'Flatbed or Step Deck (DAT)' },
  { value: 'FO', label: 'Flatbed Over-dimension' },
  { value: 'FM', label: 'Flatbed w/ Team (DAT)' },
  { value: 'FS', label: 'FLATBED W/SIDES' },
  { value: 'FT', label: 'Flatbed w/Tarps (DAT)' },
  { value: 'FC', label: 'Flatbed with chains (DAT)' },
  { value: 'HB', label: 'Hopper Bottom (DAT)' },
  { value: 'LB', label: 'Lowboy (DAT)' },
  { value: 'LR', label: 'Lowboy or RGN (DAT)' },
  { value: 'LO', label: 'Lowboy Overdimension (DAT)' },
  { value: 'MV', label: 'Moving Van (DAT)' },
  { value: 'NU', label: 'Pneumatic (DAT)' },
  { value: 'PO', label: 'Power Only (DAT)' },
  { value: 'R', label: 'Reefer (DAT)' },
  { value: 'RA', label: 'Reefer Airride (DAT)' },
  { value: 'R2', label: 'Reefer Doubles (DAT)' },
  { value: 'RZ', label: 'Reefer Hazmat (DAT)' },
  { value: 'RN', label: 'Reefer Intermodal (DAT)' },
  { value: 'RL', label: 'Reefer Logistics (DAT)' },
  { value: 'RV', label: 'Reefer or Vented Van' },
  { value: 'RX', label: 'REEFER PALLET EXCHANGE' },
  { value: 'RP', label: 'Reefer Pallet Exchange (DAT)' },
  { value: 'RM', label: 'Reefer w/ Team (DAT)' },
  { value: 'RG', label: 'Removable GooseNeck (DAT)' },
  { value: 'SD', label: 'Step Deck (DAT)' },
  { value: 'SR', label: 'Step Deck or RGN' },
  { value: 'SN', label: 'Stepdeck Conestoga (DAT)' },
  { value: 'SB', label: 'Straight Box Truck' },
  { value: 'ST', label: 'Strectch Trailer (DAT)' },
  { value: 'TA', label: 'Tanker Aluminum (DAT)' },
  { value: 'TN', label: 'Tanker Intermodal (DAT)' },
  { value: 'TS', label: 'Tanker Steel (DAT)' },
  { value: 'TT', label: 'Truck and Trailer (DAT)' },
  { value: 'V', label: 'Van (DAT)' },
  { value: 'VA', label: 'Van Airride (DAT)' },
  { value: 'VW', label: 'Van Blanket Wrap' },
  { value: 'VS', label: 'Van Conestoga (DAT)' },
  { value: 'VC', label: 'Van Curtain (DAT)' },
  { value: 'V2', label: 'Van Double (DAT)' },
  { value: 'VZ', label: 'Van Hazmat (DAT)' },
  { value: 'VH', label: 'Van Hotshot (DAT)' },
  { value: 'VI', label: 'Van Insulated (DAT)' },
  { value: 'VN', label: 'Van Intermodal (DAT)' },
  { value: 'VG', label: 'Van Lift-Gate (DAT)' },
  { value: 'VL', label: 'Van Logistics (DAT)' },
  { value: 'OT', label: 'Van Opentop (DAT)' },
  { value: 'VF', label: 'Van or Flatbed (DAT)' },
  { value: 'VT', label: 'Van or Flats w/Tarps (DAT)' },
  { value: 'VR', label: 'Van or Reefer (DAT)' },
  { value: 'VP', label: 'Van Pallet Exchange (DAT)' },
  { value: 'VB', label: 'Van Roller Bed (DAT)' },
  { value: 'VV', label: 'Van Vented (DAT)' },
  { value: 'VM', label: 'Van w/ Team (DAT)' },
];

/* --------- REVENUE CODE OPTIONS --------- */

// Unfortunately Mcleod API doesn't enumerate revenue codes, so we have to hardcode them
// To do so, log into their Mcleod app -> Dispatch -> Customer Service -> Order entry ->
// Click Add (but do NOT actually add a record unless in dev) -> Click on magnifying glass next to Revenue Code ->
// Screenshot all of the rows -> Click "Abort" -->
// Ask LLM generate TS list. More info here https://www.notion.so/drumkitai/McLeod-1052b16b087a80098bdfe6b44934ca6b?pvs=4

// trident transport, not trident logistics
export const tridentRevenueCodes: ValueLabelOption[] = [
  { value: 'BRADE', label: 'Bradenton' },
  { value: 'CHATT', label: 'Chattanooga' },
  { value: 'DRAY', label: 'Drayage' },
  { value: 'ENT', label: 'Enterprise' },
  { value: 'MINNE', label: 'Minneapolis' },
  { value: 'TAMPA', label: 'Tampa' },
  { value: 'CHSC', label: 'Charleston' },
];

export const fetchRevenueCodes: ValueLabelOption[] = [
  { value: 'FETCH', label: 'Fetch Freight' },
  { value: 'SALES', label: 'Sales Team' },
];

export const syfanRevenueCodes: ValueLabelOption[] = [
  { value: 'ADT1', label: 'ADT1' },
  { value: 'BDT1', label: 'BDT1' },
  { value: 'BNSF', label: 'BNSF' },
  { value: 'DED', label: 'DEDICATED' },
  { value: 'DRAY1', label: 'DRAYAGE 1' },
  { value: 'DRY', label: 'DRY' },
  { value: 'EXP', label: 'EXPEDITED' },
  { value: 'EXP1', label: 'EXPEDITED 1' },
  { value: 'EXP2', label: 'EXPEDITED 2' },
  { value: 'EXP3', label: 'EXPEDITED 3' },
  { value: 'EXP4', label: 'EXPEDITED 4' },
  { value: 'EXP6', label: 'EXPEDITED 6' },
  { value: 'FITCO', label: 'FITCO' },
  { value: 'FLAT', label: 'FLATBED' },
  { value: 'LTL1', label: 'LTL1' },
  { value: 'MWD', label: 'MIDWEST' },
  { value: 'PROJ', label: 'PROJECT' },
  { value: 'SDI', label: 'SDI' },
  { value: 'SED', label: 'SOUTHEAST' },
  { value: 'TEMP', label: 'TEMP' },
  { value: 'TEMP1', label: 'TEMP1' },
  { value: 'TEMP2', label: 'TEMP2' },
  { value: 'TEMP3', label: 'TEMP3' },
  { value: 'TRN', label: 'Training' },
  { value: 'UPSP', label: 'UPS PEAK' },
];

export const tumaloRevenueCodes: ValueLabelOption[] = [
  { label: 'REBID', value: 'REBID' },
  { label: 'ABOVETGT', value: 'ABOVE' },
  { label: 'COLUCCI GROUP', value: 'FLA' },
  { label: 'KING GROUP', value: 'BKG' },
  { label: 'LTL', value: 'LTL' },
  { label: 'MURPHY GROUP', value: 'MMG' },
  { label: 'PARTIAL LOAD', value: 'PRTL' },
  { label: 'PEPSI', value: 'PBC' },
  { label: 'PER DIEM', value: 'PD' },
  { label: 'QUAKER', value: 'QFB' },
  { label: 'QUAKER DETENTIO', value: 'DET' },
  { label: 'SPOT FREIGHT', value: 'SPOT' },
  { label: 'USF-BRET', value: 'USFB' },
  { label: 'USF-SCOTT', value: 'USFS' },
];
/* --------- COMMODITY OPTIONS --------- */

export const fetchCommodityOptions: McleodCommodityOption[] = [
  {
    code: 'N-BUTYL',
    description: '2-Propenoic Acid / N-Butyl Acrylate',
    hazmat: 'N',
    un: 'UN2348',
  },
  { code: 'VTRAILER', description: "53' Van Trailer", hazmat: 'N', un: '' },
  { code: 'DRUMS', description: '55gal Poly Drum', hazmat: 'N', un: '' },
  {
    code: 'CHAIRS',
    description: 'ACMA Chairs in Racking',
    hazmat: 'N',
    un: '',
  },
  { code: 'ACRYLICAC', description: 'Acrylic acid', hazmat: 'N', un: 'UN2218' },
  { code: 'AERATOR', description: 'AERATOR', hazmat: 'N', un: '' },
  { code: 'AEROSOLS', description: 'Aerosol Cans', hazmat: 'N', un: 'UN1950' },
  {
    code: 'AGCHEM',
    description: 'Agricultural Chemicals Hazardous',
    hazmat: 'N',
    un: 'UN3266',
  },
  {
    code: 'ACHEMNONH',
    description: 'Agricultural Chemicals Non-Hazardous',
    hazmat: 'N',
    un: '',
  },
  { code: 'AG', description: 'Agricultural Products', hazmat: 'N', un: '' },
  { code: 'ALCOHOL', description: 'Alcohol Products', hazmat: 'N', un: '' },
  {
    code: 'ALCOHOLS',
    description: 'ALCOHOLS, N.O.S',
    hazmat: 'N',
    un: 'UN1987',
  },
  {
    code: 'ALKYLSULF',
    description: 'Alkyl sulfonic acids',
    hazmat: 'N',
    un: 'UN2586',
  },
  {
    code: 'ALUDOCK',
    description: 'ALuminum Dock Walkway',
    hazmat: 'N',
    un: '',
  },
  { code: 'ALUMPAN', description: 'aluminum panels', hazmat: 'N', un: '' },
  { code: 'ALURIMS', description: 'Aluminum Rims', hazmat: 'N', un: '' },
  {
    code: 'ALS70',
    description: 'Ammonium Lauryl Sulfate',
    hazmat: 'N',
    un: '',
  },
  { code: 'TURF', description: 'Artificial Turf', hazmat: 'N', un: '' },
  { code: 'AUTOPARTS', description: 'Auto Parts', hazmat: 'N', un: '' },
  { code: 'CHARCOAL', description: 'Bags of Charcoal', hazmat: 'N', un: '' },
  { code: 'BALEALUM', description: 'Baled Aluminum', hazmat: 'N', un: '' },
  { code: 'BATHTUB', description: 'BATHTUBS', hazmat: 'N', un: '' },
  { code: 'BATTERIES', description: 'BATTERIES', hazmat: 'N', un: '' },
  {
    code: 'BATTWET',
    description: 'Batteries - wet filled with acid',
    hazmat: 'N',
    un: 'UN2794',
  },
  { code: 'BATTPART', description: 'Battery Parts', hazmat: 'N', un: '' },
  {
    code: 'BATCAR',
    description: 'battery powered vehicle',
    hazmat: 'N',
    un: 'UN3171',
  },
  { code: 'BEDDING', description: 'bedding products', hazmat: 'N', un: '' },
  { code: 'BEVERAGES', description: 'Beverages', hazmat: 'N', un: '' },
  { code: 'BLEACHER', description: 'bleacher parts', hazmat: 'N', un: '' },
  {
    code: 'METALS',
    description: 'Boxed/Baled Non Ferrous Metals',
    hazmat: 'N',
    un: '',
  },
  { code: 'STRUCTURE', description: 'Building Structure', hazmat: 'N', un: '' },
  { code: 'BUILDSUP', description: 'building supplies', hazmat: 'N', un: '' },
  { code: 'CABLE', description: 'Cable Reels', hazmat: 'N', un: '' },
  {
    code: 'CALCAR',
    description: 'Calcium Carbide 4.3 / PG1',
    hazmat: 'N',
    un: 'UN1402',
  },
  { code: 'CAR', description: 'car tires', hazmat: 'N', un: '' },
  { code: 'TIRES', description: 'car tires', hazmat: 'N', un: '' },
  { code: 'CARPET', description: 'Carpet', hazmat: 'N', un: '' },
  { code: 'CARPETTIL', description: 'Carpet Tile', hazmat: 'N', un: '' },
  { code: 'CHEMICALS', description: 'chemicals', hazmat: 'N', un: 'UN2924' },
  {
    code: 'HAZUN1268',
    description: 'Chemicals (liquid)',
    hazmat: 'N',
    un: 'UN1268',
  },
  { code: 'UN1751', description: 'class 8 chemical', hazmat: 'N', un: '' },
  { code: 'APPAREL', description: 'clothing', hazmat: 'N', un: '' },
  { code: 'COAL', description: 'COAL', hazmat: 'N', un: '' },
  { code: 'COALTAR', description: 'Coal Tar Pitch', hazmat: 'N', un: '3077' },
  { code: 'COMPAIR', description: 'Compressed Air', hazmat: 'N', un: 'UN1956' },
  { code: 'CONCRECBAR', description: 'Concrete Barriers', hazmat: 'N', un: '' },
  { code: 'CONTAINER', description: 'CONTAINERS', hazmat: 'N', un: '' },
  { code: 'COPIER', description: 'copiers', hazmat: 'N', un: '' },
  {
    code: 'CORROSIVE',
    description: 'CORROSIVE LIQUID, BASIC, INORGANIC, N.O.S',
    hazmat: 'N',
    un: 'UN3266',
  },
  {
    code: 'HAZCOR',
    description: 'Corrosive liquids, flammable, n.o.s.',
    hazmat: 'N',
    un: 'UN2920',
  },
  { code: 'CRANE', description: 'CRANE', hazmat: 'N', un: '' },
  { code: 'CRANERAIL', description: 'crane rails', hazmat: 'N', un: '' },
  { code: 'DAIRY', description: 'Dairy Food Products', hazmat: 'N', un: '' },
  {
    code: 'DISINFECT',
    description: 'Didecyldimethylammonium chloride',
    hazmat: 'N',
    un: 'UN1903',
  },
  { code: 'DOCKEQUIP', description: 'Dock Equipment', hazmat: 'N', un: '' },
  { code: 'DOGVEST', description: 'Dog Vests', hazmat: 'N', un: '' },
  { code: 'SPREADER', description: 'DROP SPREADER', hazmat: 'N', un: '' },
  { code: 'DRYFOOD', description: 'Dry Food Goods', hazmat: 'N', un: '' },
  { code: 'DRYGOODS', description: 'Dry Nonfood Goods', hazmat: 'N', un: '' },
  { code: 'DRYWALL', description: 'Dry Wall', hazmat: 'N', un: '' },
  { code: 'DUMPSTER', description: 'Dumpster', hazmat: 'N', un: '' },
  { code: 'DYE', description: 'dye material in gaylord', hazmat: 'N', un: '' },
  {
    code: 'UN2801',
    description: 'Dyes, liquid, corrosive, N.O.S.(Glycolic acid)',
    hazmat: 'N',
    un: 'UN2801',
  },
  { code: 'EGGS', description: 'Eggs and Egg Products', hazmat: 'N', un: '' },
  {
    code: 'BIKES',
    description: 'electric bicycles or scooters, no batteries',
    hazmat: 'N',
    un: '',
  },
  { code: 'ELECTRON', description: 'Electronic Items', hazmat: 'N', un: '' },
  { code: 'EMPTYBAG', description: 'Empty bags to fill', hazmat: 'N', un: '' },
  {
    code: 'EMPTYBATT',
    description: 'empty plastic battery containers',
    hazmat: 'N',
    un: '',
  },
  { code: 'ENCLOS', description: 'Enclosures', hazmat: 'N', un: '' },
  {
    code: 'ENVHAZ',
    description: 'Environmentally Haz Substance',
    hazmat: 'N',
    un: 'UN3077',
  },
  { code: 'EQUIPMENT', description: 'equipment', hazmat: 'N', un: '' },
  {
    code: 'EROSION',
    description: 'Erosion Control Products',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'ETHANOL',
    description: 'Ethanol or Ethyl alcohol',
    hazmat: 'N',
    un: 'UN1170',
  },
  { code: 'EXCAVATOR', description: 'EXCAVATOR', hazmat: 'N', un: '' },
  { code: 'FAB', description: 'fabricated materials', hazmat: 'N', un: '' },
  { code: 'FAKFURNIT', description: 'FAK Furniture', hazmat: 'N', un: '' },
  {
    code: 'FAKFLOOR',
    description: 'FAK Furniture - Floor Loaded',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'FIREEXTG',
    description: 'Fire Extinguishers',
    hazmat: 'N',
    un: 'UN1044',
  },
  { code: 'FISHEQP', description: 'Fishing Equipment', hazmat: 'N', un: '' },
  { code: 'FITTINGS', description: 'Fittings', hazmat: 'N', un: '' },
  {
    code: 'FLALIQUID',
    description: 'FLAMMABLE LIQUIDS, N.O.S. (CONTAINS METHANOL)',
    hazmat: 'N',
    un: 'UN1993',
  },
  { code: 'CARROTS', description: 'Floor loaded carrots', hazmat: 'N', un: '' },
  {
    code: 'FLFURN',
    description: 'Floor Loaded Furniture',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'MATTRESS',
    description: 'Floor loaded mattresses',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'SPRINGS',
    description: 'FLOOR LOADED METAL SPRINGS',
    hazmat: 'N',
    un: '',
  },
  { code: 'FLOORING', description: 'Flooring', hazmat: 'N', un: '' },
  { code: 'FOAMTRAYS', description: 'Foam Trays', hazmat: 'N', un: '' },
  { code: 'FORKLIFT', description: 'Forklift', hazmat: 'N', un: '' },
  { code: 'FAK', description: 'Freight All Kinds', hazmat: 'N', un: '' },
  { code: 'BEEF', description: 'Fresh Beef', hazmat: 'N', un: '' },
  { code: 'FRESHFOOD', description: 'Fresh Food Items', hazmat: 'N', un: '' },
  {
    code: 'BAKING',
    description: 'Frozen Baking / Yeast Products',
    hazmat: 'N',
    un: '',
  },
  { code: 'FOFBRE', description: 'Frozen Bread', hazmat: 'N', un: '' },
  { code: 'FRZFOOD', description: 'Frozen Food', hazmat: 'N', un: '' },
  {
    code: 'FROZENFOO',
    description: 'Frozen Food Ships at -10',
    hazmat: 'N',
    un: '',
  },
  { code: 'ICECREAM', description: 'Frozen Ice Cream', hazmat: 'N', un: '' },
  {
    code: 'HAZGLUT',
    description: 'GLUT; Corrosive Liquids, toxic, n.o.s.',
    hazmat: 'N',
    un: 'UN2922',
  },
  { code: 'GOLFCART', description: 'Golfcarts', hazmat: 'N', un: '' },
  { code: 'GREENROLL', description: 'GREENROLLERS', hazmat: 'N', un: '' },
  { code: 'SPRAYER', description: 'greens sprayer', hazmat: 'N', un: '' },
  {
    code: 'HAZCHEM32',
    description: 'Hazardous chems',
    hazmat: 'N',
    un: 'UN3267',
  },
  {
    code: 'HAZGAS',
    description: 'Hazardous Gasses',
    hazmat: 'N',
    un: 'UN1077',
  },
  {
    code: 'HAZFLAME',
    description: 'Hazardous Liquids - Flammable',
    hazmat: 'N',
    un: 'UN1993',
  },
  { code: 'HAZCHEM', description: 'Hazardous Product', hazmat: 'N', un: '' },
  {
    code: 'HAZREC',
    description: 'Hazardous Reclosers',
    hazmat: 'N',
    un: 'UN3091',
  },
  {
    code: 'SUR',
    description: 'Hazmat Chemicals (Surfactant)',
    hazmat: 'N',
    un: 'UN2586',
  },
  {
    code: 'RESIN',
    description: 'Hazmat Resin Solution',
    hazmat: 'N',
    un: 'UN1866',
  },
  {
    code: 'AMINE2735',
    description: 'Hazmat UN2735',
    hazmat: 'N',
    un: 'UN2735',
  },
  { code: 'UNIOUAT', description: 'HAZQUAT', hazmat: 'N', un: 'UN2280' },
  {
    code: 'HVAC',
    description: 'Heating and Air Conditioning Items',
    hazmat: 'N',
    un: '',
  },
  { code: 'HOPS', description: 'HOPS', hazmat: 'N', un: '' },
  {
    code: 'HOTRUSH',
    description: 'Hot Rush Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'HYCA',
    description: 'Hydrochloric Acid Solution',
    hazmat: 'N',
    un: 'UN1789',
  },
  {
    code: 'INDUSTRIA',
    description: 'Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'IBC',
    description: 'Intermediate Bulk Container',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'HAZALC',
    description: 'Isopropanol or Isopropyl alcohol',
    hazmat: 'N',
    un: 'UN1219',
  },
  { code: 'LOADER', description: 'John Deere 844L', hazmat: 'N', un: '' },
  { code: 'LABTRIAL', description: 'labtrial', hazmat: 'N', un: '' },
  { code: 'LADD', description: 'LADDERS', hazmat: 'N', un: '' },
  { code: 'LADDERS', description: 'Ladders', hazmat: 'N', un: '' },
  { code: 'LANDTIE', description: 'Landscape Ties', hazmat: 'N', un: '' },
  { code: 'LAWN', description: 'LAWN EQUIPMENT', hazmat: 'N', un: '' },
  {
    code: 'LIGHTING',
    description: 'LIGHTS AND LIGHTING SUPPLIES',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LABSA',
    description: 'Linear alkyl benzene sulphonic acid',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LABSAID',
    description: 'Linear alkyl benzene sulphonic acid',
    hazmat: 'N',
    un: 'UN2586',
  },
  {
    code: 'UN3082',
    description: 'Liquid, n.o.s. (Diethanolamine) 9,III, Diethanolamine, NS 67',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'HAZBATT',
    description: 'Lithium Batteries',
    hazmat: 'N',
    un: 'UN3480',
  },
  {
    code: 'LITHBAT',
    description: 'Lithium-Ion Batteries',
    hazmat: 'N',
    un: 'UN3480',
  },
  { code: 'LOCKERS', description: 'LOCKERS', hazmat: 'N', un: '' },
  { code: 'LUMBER', description: 'Lumber Products', hazmat: 'N', un: '' },
  { code: 'MACHINERY', description: 'Machinery', hazmat: 'N', un: '' },
  { code: 'HMMAN', description: 'Mandrel', hazmat: 'N', un: '' },
  { code: 'METAL', description: 'METAL', hazmat: 'N', un: '' },
  { code: 'BUILDING', description: 'metal building', hazmat: 'N', un: '' },
  { code: 'TANK', description: 'Metal Tank', hazmat: 'N', un: '' },
  { code: 'TRASHCANS', description: 'METAL TRASH CANS', hazmat: 'N', un: '' },
  { code: 'METALTRAY', description: 'Metal Trays', hazmat: 'N', un: '' },
  { code: 'METALSDR', description: 'Metals in Drums', hazmat: 'N', un: '' },
  { code: 'MINPROD', description: 'Mineral Products', hazmat: 'N', un: '' },
  {
    code: 'MISCEMBED',
    description: 'Misc Embeds Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  { code: 'CLASS9', description: 'Misc Haz', hazmat: 'N', un: 'UN3295' },
  {
    code: 'MISC',
    description: 'Misc Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  { code: 'UN1750', description: 'monoCHLOROACETIC ACID', hazmat: 'N', un: '' },
  { code: 'MOWER', description: 'mower', hazmat: 'N', un: '' },
  { code: 'MULCH', description: 'Mulch', hazmat: 'N', un: '' },
  {
    code: 'NONHAZLC',
    description: 'NON - Hazardous Liquid Chemicals',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'NONSPBATT',
    description: 'Non Spillable Batteries',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'NON-HAZ',
    description: 'NON-Hazardous Chemicals',
    hazmat: 'N',
    un: '',
  },
  { code: 'NHBATT', description: 'Non-Hazmat Batteries', hazmat: 'N', un: '' },
  { code: 'FABRIC', description: 'Nonwoven Fabrics', hazmat: 'N', un: '' },
  { code: 'OFFICEEQU', description: 'Office Equipment', hazmat: 'N', un: '' },
  { code: 'ONIONS', description: 'Onions', hazmat: 'N', un: '' },
  {
    code: 'PACKAGING',
    description: 'Packaging and Dunnage',
    hazmat: 'N',
    un: '',
  },
  { code: 'PACKSUPP', description: 'Packaging Supplies', hazmat: 'N', un: '' },
  {
    code: 'POLYMER',
    description: 'Palletized Polymer Product',
    hazmat: 'N',
    un: '',
  },
  { code: 'PLIFT', description: 'Pallet Lift', hazmat: 'N', un: '' },
  {
    code: 'APPLIANCE',
    description: 'Palletized Appliances',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'PALART',
    description: 'Palletized Art Supplies',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'FEED',
    description: 'Palletized Bagged Livestock Feed',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'GROUT',
    description: 'Palletized Bags of Grout',
    hazmat: 'N',
    un: '',
  },
  { code: 'BEARINGS', description: 'Palletized Bearings', hazmat: 'N', un: '' },
  {
    code: 'CANDLES',
    description: 'Palletized Candle Products',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'COFFEE',
    description: 'Palletized Coffee Products',
    hazmat: 'N',
    un: '',
  },
  { code: 'PALDRUMS', description: 'Palletized Drums', hazmat: 'N', un: '' },
  { code: 'PENV', description: 'Palletized Envelopes', hazmat: 'N', un: '' },
  {
    code: 'FASTNERS',
    description: 'Palletized Fasteners',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'STARTERLO',
    description: 'Palletized Fire Starter Logs',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'PFLOORING',
    description: 'Palletized Flooring',
    hazmat: 'N',
    un: '',
  },
  { code: 'PFOOD', description: 'Palletized Food', hazmat: 'N', un: '' },
  { code: 'PFURN', description: 'Palletized Furniture', hazmat: 'N', un: '' },
  { code: 'ICEMELT', description: 'Palletized Ice Melt', hazmat: 'N', un: '' },
  {
    code: 'REEFER',
    description: 'Palletized Machinery',
    hazmat: 'N',
    un: 'UN2857',
  },
  { code: 'MALT', description: 'Palletized Malt', hazmat: 'N', un: '' },
  {
    code: 'PLASTICPE',
    description: 'Palletized Plastic Pellets',
    hazmat: 'N',
    un: '',
  },
  { code: 'PSU', description: 'palletized server units', hazmat: 'N', un: '' },
  {
    code: 'FLOORSUP',
    description: 'palletized subfloor mix',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'TIRELUBE',
    description: 'Palletized Tire Lubricant',
    hazmat: 'N',
    un: '',
  },
  { code: 'PALLETS', description: 'Pallets', hazmat: 'N', un: '' },
  {
    code: 'CUPSNLIDS',
    description: 'Paper Cups and Lids',
    hazmat: 'N',
    un: '',
  },
  { code: 'PAPER', description: 'Paper Products', hazmat: 'N', un: '' },
  { code: 'PARTITION', description: 'partitions', hazmat: 'N', un: '' },
  {
    code: 'PAVEREP',
    description: 'Pavement Repair Product',
    hazmat: 'N',
    un: '',
  },
  { code: 'PAVER', description: 'PAVER', hazmat: 'N', un: '' },
  {
    code: 'PHOSPEN',
    description: 'Phosphorus pentoxide',
    hazmat: 'N',
    un: 'UN1807',
  },
  {
    code: 'UN1831',
    description: 'Phosphorus Pentoxide',
    hazmat: 'N',
    un: 'UN1831',
  },
  { code: 'BARK', description: 'Pine Bark Nuggets', hazmat: 'N', un: '' },
  { code: 'PIPE', description: 'Pipe', hazmat: 'N', un: '' },
  { code: 'BOTTLE', description: 'plastic bottles', hazmat: 'N', un: '' },
  { code: 'PLABOT', description: 'Plastic Bottles/Caps', hazmat: 'N', un: '' },
  { code: 'AGCOIL', description: 'Plastic Coil', hazmat: 'N', un: '' },
  { code: 'CRATES', description: 'Plastic Crates', hazmat: 'N', un: '' },
  { code: 'PLASTICS', description: 'Plastics', hazmat: 'N', un: '' },
  { code: 'PLUMBING', description: 'plumbing supplies', hazmat: 'N', un: '' },
  { code: 'POULTRY', description: 'Poultry Products', hazmat: 'N', un: '' },
  { code: 'PWRSUPP', description: 'Power Supplies', hazmat: 'N', un: '' },
  { code: 'TOOLS', description: 'power tools', hazmat: 'N', un: '' },
  { code: 'PRODUCE', description: 'Produce', hazmat: 'N', un: '' },
  { code: 'PROACID', description: 'Propionic Acid', hazmat: 'N', un: 'UN3463' },
  { code: 'PUMP', description: 'Pump and Related Parts', hazmat: 'N', un: '' },
  { code: 'RAILCAR', description: 'Rail Car', hazmat: 'N', un: '' },
  { code: 'WHEELSET', description: 'Railcar Wheel Sets', hazmat: 'N', un: '' },
  { code: 'RAILTIES', description: 'Railroad Ties', hazmat: 'N', un: '' },
  { code: 'REELGRIND', description: 'REELGRINDER', hazmat: 'N', un: '' },
  {
    code: 'RPG',
    description: 'Refrigerated Packaged Goods',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'RESONNHAZ',
    description: 'Resin class 55 non haz',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'UN1866',
    description: 'Resin solution, flammable',
    hazmat: 'N',
    un: '',
  },
  { code: 'ROLSTCK', description: 'Roll Stock', hazmat: 'N', un: '' },
  { code: 'RFRAMES', description: 'Roof Frames', hazmat: 'N', un: '' },
  { code: 'SCAFFOLD', description: 'scaffolding', hazmat: 'N', un: '' },
  { code: 'SHEETMETA', description: 'sheet metal', hazmat: 'N', un: '' },
  { code: 'SHELVING', description: 'SHELVING', hazmat: 'N', un: '' },
  { code: 'PLASTIC', description: 'Shredded Plastic', hazmat: 'N', un: '' },
  { code: 'SCOILS', description: 'Skidded Coils', hazmat: 'N', un: '' },
  { code: 'SLINKCOI', description: 'Slinky Coils', hazmat: 'N', un: '' },
  {
    code: 'HAZMCHEM2',
    description: 'Sodium chloroacetate',
    hazmat: 'N',
    un: 'UN2659',
  },
  {
    code: 'UN1384',
    description: 'Sodium dithionite',
    hazmat: 'N',
    un: 'UN1384',
  },
  {
    code: 'UN2949',
    description: 'Sodium hydrosulfide',
    hazmat: 'N',
    un: 'UN2949',
  },
  {
    code: 'SODHYD',
    description: 'SODIUM HYDROXIDE',
    hazmat: 'N',
    un: 'UN1824',
  },
  { code: 'SOAD', description: 'Soil Additive', hazmat: 'N', un: '' },
  { code: 'SPORTS', description: 'Sporting Equipment', hazmat: 'N', un: '' },
  { code: 'STARCH', description: 'Starch', hazmat: 'N', un: '' },
  { code: 'SCREENS', description: 'STATIC SCREENS', hazmat: 'N', un: '' },
  { code: 'STATBAT', description: 'Stationary Batteries', hazmat: 'N', un: '' },
  {
    code: 'BRIDGE',
    description: 'Stay-In-Place Bridge plates',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'STEELBATT',
    description: 'Steel Cased Batteries',
    hazmat: 'N',
    un: '',
  },
  { code: 'COILS', description: 'Steel Coils', hazmat: 'N', un: '' },
  {
    code: 'STCONT',
    description: 'Steel Containers 24×96"x5\'',
    hazmat: 'N',
    un: '',
  },
  { code: 'DIES', description: 'steel dies', hazmat: 'N', un: '' },
  { code: 'STEELMES', description: 'steel mesh', hazmat: 'N', un: '' },
  { code: 'PLATES', description: 'Steel Plates', hazmat: 'N', un: '' },
  { code: 'STEELPOLE', description: 'Steel Poles', hazmat: 'N', un: '' },
  { code: 'STEEL', description: 'Steel Products', hazmat: 'N', un: '' },
  { code: 'REBAR', description: 'Steel Rebar', hazmat: 'N', un: '' },
  { code: 'STANKS', description: 'Steel Tanks', hazmat: 'N', un: '' },
  { code: 'STEELT', description: 'Steel Ties', hazmat: 'N', un: '' },
  { code: 'STEELTUBE', description: 'steel tubing', hazmat: 'N', un: '' },
  {
    code: 'STOREFIX',
    description: 'Store Shelving and Fixtures',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'STRUCTURA',
    description: 'Structural Steel Sequence',
    hazmat: 'N',
    un: '',
  },
  { code: 'SULFACID', description: 'Sulfamic Acid', hazmat: 'N', un: 'UN2967' },
  { code: 'SUPERSACK', description: 'Super Sacks', hazmat: 'N', un: '' },
  { code: 'SWITCH', description: 'Switch Phases', hazmat: 'N', un: '' },
  {
    code: 'SYNRFUND',
    description: 'Synthetic Roof Underlayment',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'TEXTILES',
    description: 'Textiles and Apparel',
    hazmat: 'N',
    un: '',
  },
  { code: 'TILT', description: 'Tilt Angels', hazmat: 'N', un: '' },
  {
    code: 'TOTES',
    description: 'Totes of Liquid Animal Feed Supplement',
    hazmat: 'N',
    un: '',
  },
  { code: 'TOWER', description: 'Tower Stand', hazmat: 'N', un: '' },
  { code: 'TRACTOR', description: 'TRACTOR', hazmat: 'N', un: '' },
  { code: 'TRAILERS', description: 'TRAILERS', hazmat: 'N', un: '' },
  { code: 'TRNSFRMR', description: 'Transformers', hazmat: 'N', un: '' },
  {
    code: 'TREXCODC',
    description: 'Trex - composite decking',
    hazmat: 'N',
    un: '',
  },
  { code: 'TRUCK', description: 'TRUCK', hazmat: 'N', un: '' },
  { code: 'TRUCKB', description: 'Truck Bodies', hazmat: 'N', un: '' },
  { code: 'PROCORE64', description: 'Turf Cutter', hazmat: 'N', un: '' },
  {
    code: 'HAZLQ',
    description: 'UN1760 Hazmat Liq',
    hazmat: 'N',
    un: 'UN1760',
  },
  { code: 'HAZCHEM1', description: 'UN2659', hazmat: 'N', un: '' },
  { code: 'HAZCHEM2', description: 'UN2967(sulfamic)', hazmat: 'N', un: '' },
  { code: 'RESIDUE', description: 'unwashed IBC totes', hazmat: 'N', un: '' },
  { code: 'VACUUM', description: 'VACUUM', hazmat: 'N', un: '' },
  { code: 'FOAM', description: 'variety of foams', hazmat: 'N', un: '' },
  { code: 'VEHICLE', description: 'Vehicle', hazmat: 'N', un: '' },
  {
    code: 'ACCESSORI',
    description: 'washroom accessories',
    hazmat: 'N',
    un: '',
  },
  { code: 'WATER', description: 'Water Bottles', hazmat: 'N', un: '' },
  {
    code: 'CHILLER',
    description: 'Water Chiller on Skids',
    hazmat: 'N',
    un: 'UN2857',
  },
  { code: 'WATTANK', description: 'Water Tank', hazmat: 'N', un: '' },
  { code: 'WETCELL', description: 'Wet Cell Batteries', hazmat: 'N', un: '' },
  { code: 'WINDOW', description: 'Window Materials', hazmat: 'N', un: '' },
  { code: 'MESH', description: 'Wire Mesh', hazmat: 'N', un: '' },
  { code: 'WIRE', description: 'Wire Products', hazmat: 'N', un: '' },
  {
    code: 'SUBFLOOR',
    description: 'wrapped/palletized bags',
    hazmat: 'N',
    un: '',
  },
  { code: 'YARN', description: 'yarn', hazmat: 'N', un: '' },
  { code: 'ZIMATE', description: 'Zimate Powder', hazmat: 'N', un: '' },
];

// trident transport, not trident logistics
export const tridentCommodityOptions: McleodCommodityOption[] = [
  { code: 'UTILITY', description: 'Utility Trailer', hazmat: 'N', un: '' },
  { code: 'UTVPARTSN', description: 'UTV Parts New', hazmat: 'N', un: '' },
  { code: 'UVPIPELIN', description: 'UV PIPE LINERS', hazmat: 'N', un: '' },
  { code: 'VEHICLES', description: 'vehicles', hazmat: 'N', un: '' },
  {
    code: 'WSTEMGMT',
    description: 'Waste Management Sysytems',
    hazmat: 'N',
    un: '',
  },
  { code: 'WAT', description: 'WATER', hazmat: 'N', un: '' },
  { code: 'H2OTRUCK', description: 'Water Truck', hazmat: 'N', un: '' },
  { code: 'WATRMELN', description: 'Watermelons', hazmat: 'N', un: '' },
  { code: 'WHEELS', description: 'Wheels', hazmat: 'N', un: '' },
  { code: 'WINDOW', description: 'Windows', hazmat: 'N', un: '' },
  { code: 'WIREMESH', description: 'WIRE MESH', hazmat: 'N', un: '' },
  { code: 'REELS', description: 'Wire reels', hazmat: 'N', un: '' },
  { code: 'PELLETS', description: 'WOOD PELLETS', hazmat: 'N', un: '' },
  { code: 'WOODWALL', description: 'wooden wall panels', hazmat: 'N', un: '' },
  { code: 'YARDGOAT', description: 'Yard Goat', hazmat: 'N', un: '' },
  { code: 'YARN', description: 'Yarn', hazmat: 'N', un: '' },
  { code: 'SUGAR', description: 'SUGAR', hazmat: 'N', un: '' },
  {
    code: 'GEOFILL',
    description: 'Supersacks of Geofill',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'SUPSACKS',
    description: 'Supersacks of Raw Material',
    hazmat: 'N',
    un: '',
  },
  { code: 'SUPPLEMEN', description: 'supplements', hazmat: 'N', un: '' },
  { code: 'TANK', description: 'Tank', hazmat: 'N', un: '' },
  { code: 'TEA', description: 'tea/sugar', hazmat: 'N', un: '' },
  { code: 'TH', description: 'Telehandler', hazmat: 'N', un: '' },
  {
    code: 'CHILLED',
    description: 'Temp Controlled Foods',
    hazmat: 'N',
    un: '',
  },
  { code: 'PPE', description: 'TENNIS', hazmat: 'N', un: '' },
  { code: 'TENT', description: 'Tent equipment', hazmat: 'N', un: '' },
  { code: 'THANKSGIV', description: 'Thanksgiving', hazmat: 'N', un: '' },
  {
    code: 'RESIN',
    description: 'Thermoplastic Resin (Non-Hazmat)',
    hazmat: 'N',
    un: '',
  },
  { code: 'TILE', description: 'TILE', hazmat: 'N', un: '' },
  { code: 'TIRES', description: 'Tires', hazmat: 'N', un: '' },
  { code: 'TISSUE', description: 'TISSUE PAPER', hazmat: 'N', un: '' },
  { code: 'TOPSOIL', description: 'Topsoil', hazmat: 'N', un: '' },
  {
    code: 'TOTHAZ',
    description: 'Totes of HAZARDOUS liquid',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'BINDER',
    description: 'Totes of non hazardous binder',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'GLUE',
    description: 'Totes of Non-Hazmat Glue',
    hazmat: 'N',
    un: '',
  },
  { code: 'SOAP', description: 'Totes of Soap', hazmat: 'N', un: '' },
  { code: 'TOWAWAYT', description: 'tow away tanker', hazmat: 'N', un: '' },
  { code: 'TOWAWAYVA', description: 'Tow Away Van', hazmat: 'N', un: '' },
  { code: 'TOYS', description: 'TOYS & GAMES', hazmat: 'N', un: '' },
  { code: 'TRACT', description: 'Tractors', hazmat: 'N', un: '' },
  { code: 'TRLFRAM', description: 'Trailer Frames', hazmat: 'N', un: '' },
  { code: 'TRASHBAGS', description: 'TRASH BAGS', hazmat: 'N', un: '' },
  { code: 'TRAY', description: 'Trayliners', hazmat: 'N', un: '' },
  { code: 'TROPHIE', description: 'Trophies', hazmat: 'N', un: '' },
  { code: 'TTM', description: 'TTM', hazmat: 'N', un: '' },
  { code: 'TURFEQUIP', description: 'Turf Equipment', hazmat: 'N', un: '' },
  { code: 'UBOX', description: 'U-HAUL U-BOX', hazmat: 'N', un: '' },
  { code: 'MAT', description: 'Used Mattresses', hazmat: 'N', un: '' },
  {
    code: 'FOILPAN',
    description: 'Utility Foil Pan w/Lid',
    hazmat: 'N',
    un: '',
  },
  { code: 'ROOFING', description: 'Roof & Insulation', hazmat: 'N', un: '' },
  { code: 'RUBBER', description: 'Rubber', hazmat: 'N', un: '' },
  { code: 'SAND', description: 'Sand', hazmat: 'N', un: '' },
  { code: 'SCAFFOLD', description: 'Scaffolding', hazmat: 'N', un: '' },
  { code: 'SEED', description: 'SEED', hazmat: 'N', un: '' },
  { code: 'TRUCK', description: 'Service Truck', hazmat: 'N', un: '' },
  {
    code: 'CONTAINER',
    description: 'Sheet Steel Containers',
    hazmat: 'N',
    un: '',
  },
  { code: 'SHEETMTL', description: 'SHEETMETAL PARTS', hazmat: 'N', un: '' },
  { code: 'SHEL', description: 'SHELVING', hazmat: 'N', un: '' },
  { code: 'SHINGLES', description: 'SHINGLES', hazmat: 'N', un: '' },
  { code: 'SHOCK', description: 'Shockpad', hazmat: 'N', un: '' },
  { code: 'S/C', description: 'Shopping Carts', hazmat: 'N', un: '' },
  { code: 'SHORT', description: 'Shortening', hazmat: 'N', un: '' },
  {
    code: 'DOORASSEM',
    description: 'SLIDING DOOR ASSEMBLIES',
    hazmat: 'N',
    un: '',
  },
  { code: 'SP', description: 'SMOKELESS POWDER', hazmat: 'N', un: '' },
  { code: 'SNACKFOOD', description: 'SNACK FOODS', hazmat: 'N', un: '' },
  { code: 'SNACKS', description: 'Snack Foods', hazmat: 'N', un: '' },
  { code: 'SOLAR', description: 'Solar', hazmat: 'N', un: '' },
  { code: 'SOLVENT', description: 'SOLVENT', hazmat: 'N', un: 'UN1993' },
  {
    code: 'SO',
    description: 'SOUTHERN HOSPITALITY TRAILER',
    hazmat: 'N',
    un: '',
  },
  { code: 'SPONGES', description: 'SPONGES', hazmat: 'N', un: '' },
  { code: 'SPRTEQPMT', description: 'Sports Equipment', hazmat: 'N', un: '' },
  { code: 'SPRAY', description: 'Sprayer Tanks', hazmat: 'N', un: '' },
  {
    code: 'FLATSTACK',
    description: 'Stack of Flatbeds - Straps/Chains/Binders Required',
    hazmat: 'N',
    un: '',
  },
  { code: 'STAGEE', description: 'Staging Equipment', hazmat: 'N', un: '' },
  { code: 'STEEL', description: 'Steel', hazmat: 'N', un: '' },
  { code: 'STEELCAGE', description: 'Steel Cages', hazmat: 'N', un: '' },
  { code: 'STEELCOIL', description: 'Steel Coil', hazmat: 'N', un: '' },
  { code: 'STEELDUMP', description: 'Steel Dumpsters', hazmat: 'N', un: '' },
  { code: 'SF', description: 'STEEL FORGINGS', hazmat: 'N', un: '' },
  { code: 'STONE', description: 'Stone', hazmat: 'N', un: '' },
  { code: 'STORCONT', description: 'Storage Containers', hazmat: 'N', un: '' },
  { code: 'STUDS', description: 'STUDS', hazmat: 'N', un: '' },
  { code: 'P', description: 'Pump', hazmat: 'N', un: '' },
  {
    code: '##',
    description: 'Quartz Slabs on A Frames / palletized tiles',
    hazmat: 'N',
    un: '',
  },
  { code: 'RACK', description: 'RACK', hazmat: 'N', un: '' },
  { code: 'RAILCAR', description: 'Rail Car', hazmat: 'N', un: '' },
  { code: 'RAILPARTS', description: 'RAILROAD PARTS', hazmat: 'N', un: '' },
  { code: 'REBAR', description: 'Rebar', hazmat: 'N', un: '' },
  { code: 'RECYLED', description: 'Recycled Goods', hazmat: 'N', un: '' },
  { code: 'REEFER10', description: 'Reefer -10', hazmat: 'N', un: '' },
  {
    code: 'REEF2432',
    description: 'REEFER 24 - 32 DEGREES',
    hazmat: 'N',
    un: '',
  },
  { code: 'REEFER26', description: 'Reefer 26 Degrees', hazmat: 'N', un: '' },
  { code: 'REEFER28', description: 'Reefer 28', hazmat: 'N', un: '' },
  { code: 'REEF3868', description: 'Reefer 38 -68', hazmat: 'N', un: '' },
  { code: 'REEFER38', description: 'Reefer 38 degrees', hazmat: 'N', un: '' },
  { code: 'REEFER42', description: 'Reefer 42', hazmat: 'N', un: '' },
  { code: 'REEFER60', description: 'Reefer 60', hazmat: 'N', un: '' },
  { code: 'REEFER65', description: 'REEFER 65 DEGREES', hazmat: 'N', un: '' },
  { code: 'REEFER54', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER55', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEF5060', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEF5565', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER32', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER33', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER34', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER35', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER36', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  {
    code: 'REFGROCER',
    description: 'Refrigerated Grocery',
    hazmat: 'N',
    un: '',
  },
  { code: 'RESEQP', description: 'Resturant Equipment', hazmat: 'N', un: '' },
  { code: 'RICE', description: 'RICE', hazmat: 'N', un: '' },
  { code: 'ROBOT', description: 'ROBOTIC EQUIPMENT', hazmat: 'N', un: '' },
  { code: 'BELT', description: 'role', hazmat: 'N', un: '' },
  { code: 'PAD', description: 'Rolled Foam Padding', hazmat: 'N', un: '' },
  { code: 'ROLLPAPER', description: 'Rolls of Paper', hazmat: 'N', un: '' },
  { code: 'ROLLTURF', description: 'Rolls of Turf', hazmat: 'N', un: '' },
  { code: 'PALLETS', description: 'Pallets', hazmat: 'N', un: '' },
  { code: 'PARPRPROD', description: 'Paper Products', hazmat: 'N', un: '' },
  { code: 'PT', description: 'Paper Towel', hazmat: 'N', un: '' },
  { code: 'PASTA', description: 'PASTA', hazmat: 'N', un: '' },
  { code: 'PERMIT', description: 'PERMITS', hazmat: 'N', un: '' },
  { code: 'PETFILM', description: 'Pet Film', hazmat: 'N', un: '' },
  { code: 'PETFOOD', description: 'PET FOOD', hazmat: 'N', un: '' },
  { code: 'PETTREATS', description: 'PET TREATS', hazmat: 'N', un: '' },
  {
    code: 'PETROLEUM',
    description: 'PETROLEUM lubricants',
    hazmat: 'N',
    un: '',
  },
  { code: 'PHARM', description: 'Pharmaceuticals', hazmat: 'N', un: '' },
  { code: 'PIGGUP', description: 'Pig Guppy Trailer', hazmat: 'N', un: '' },
  { code: 'PIPE', description: 'Pipe', hazmat: 'N', un: '' },
  { code: 'PISTACHIO', description: 'Pistachios', hazmat: 'N', un: '' },
  { code: 'CUTLERY', description: 'Plastic Cutlery', hazmat: 'N', un: '' },
  { code: 'PLASTICPA', description: 'PLASTIC PANELS', hazmat: 'N', un: '' },
  { code: 'TUBING', description: 'Plastic Tubing', hazmat: 'N', un: '' },
  { code: 'PLASTICS', description: 'Plastics', hazmat: 'N', un: '' },
  { code: 'PLATES', description: 'PLATES', hazmat: 'N', un: '' },
  { code: 'PLAY', description: 'Play structure', hazmat: 'N', un: '' },
  { code: 'CARDS', description: 'PLAYING CARDS', hazmat: 'N', un: '' },
  { code: 'POL', description: 'Poles', hazmat: 'N', un: '' },
  { code: 'POOLPRO', description: 'POOL PRODUCTS', hazmat: 'N', un: '' },
  { code: 'PO', description: 'pools', hazmat: 'N', un: '' },
  { code: 'POPCORN', description: 'Popcorn', hazmat: 'N', un: '' },
  { code: '1.3C', description: 'Powder, Smokeless', hazmat: 'Y', un: '' },
  { code: 'PRECAST', description: 'PreCast Concrete', hazmat: 'N', un: '' },
  {
    code: 'PS',
    description: 'Prefabricated Steel Products',
    hazmat: 'N',
    un: '',
  },
  { code: 'PRELOAD', description: 'PRELOADED TRAILERS', hazmat: 'N', un: '' },
  { code: 'PRINTER', description: 'printer', hazmat: 'N', un: '' },
  { code: 'PROD', description: 'Produce', hazmat: 'N', un: '' },
  { code: 'PRODUCE', description: 'Produce', hazmat: 'N', un: '' },
  {
    code: 'PRODEQUIP',
    description: 'Production Equipment',
    hazmat: 'N',
    un: '',
  },
  { code: 'PROMO', description: 'Promotional Products', hazmat: 'N', un: '' },
  { code: 'MACHINERY', description: 'Machinery', hazmat: 'N', un: '' },
  { code: 'MAGGIE', description: 'maggie', hazmat: 'N', un: '' },
  { code: 'MARSH', description: 'MARSHMELLOWS', hazmat: 'N', un: '' },
  { code: 'MED', description: 'Medical Equip', hazmat: 'N', un: '' },
  { code: 'METLPROD', description: 'Metal Products', hazmat: 'N', un: '' },
  { code: 'MILITARY', description: 'Military MISC', hazmat: 'N', un: '' },
  { code: 'MISC', description: 'Miscellaneous', hazmat: 'N', un: '' },
  { code: 'NUTS', description: 'Mixed Nuts', hazmat: 'N', un: '' },
  { code: 'MHOME', description: 'Mobile Homes', hazmat: 'N', un: '' },
  {
    code: 'MOVEMANU',
    description: 'Motor Vehicle Parts Manufacturing',
    hazmat: 'N',
    un: '',
  },
  { code: 'MOTORS', description: 'motors', hazmat: 'N', un: '' },
  { code: 'MULCH', description: 'MULCH', hazmat: 'N', un: '' },
  {
    code: 'NONALBEV',
    description: 'NON-ALCOHOLIC BEVERAGES',
    hazmat: 'N',
    un: '',
  },
  { code: 'BATTNOHAZ', description: 'NON-HAZ BATTERIES', hazmat: 'N', un: '' },
  { code: 'PLANTS', description: 'Nursery Plants', hazmat: 'N', un: '' },
  { code: 'OFFICESUP', description: 'OFFICE SUPPLIES', hazmat: 'N', un: '' },
  { code: 'OLIVE/OEIL', description: 'OLIVE OIL', hazmat: 'N', un: '' },
  { code: 'OJ', description: 'ORANGE JUICE', hazmat: 'N', un: '' },
  { code: 'ORANGES', description: 'Oranges', hazmat: 'N', un: '' },
  { code: 'PACKAGING', description: 'Packaging Material', hazmat: 'N', un: '' },
  {
    code: 'PAINT',
    description: 'paint related material,',
    hazmat: 'N',
    un: 'UN1263',
  },
  { code: 'POOLHS', description: 'PALLETISED HOSE', hazmat: 'N', un: '' },
  { code: 'PA', description: 'Palletized Axles', hazmat: 'N', un: '' },
  { code: 'PB', description: 'PALLETIZED BRICK', hazmat: 'N', un: '' },
  {
    code: 'COCOCOIR',
    description: 'Palletized Coco Coir',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'PALFLUM',
    description: 'Palletized Finished Lumber',
    hazmat: 'N',
    un: '',
  },
  { code: 'FLOORING', description: 'Palletized Flooring', hazmat: 'N', un: '' },
  { code: 'PF', description: 'Palletized Footwear', hazmat: 'N', un: '' },
  { code: 'PALCA', description: 'palletized metal cages', hazmat: 'N', un: '' },
  { code: 'DOGFOOD', description: 'palletized pet food', hazmat: 'N', un: '' },
  { code: 'RUGS', description: 'Palletized Rugs', hazmat: 'N', un: '' },
  {
    code: 'PALLSOLAR',
    description: 'Palletized Solar Equipment',
    hazmat: 'N',
    un: '',
  },
  { code: 'PALSTO', description: 'PALLETIZED STONE', hazmat: 'N', un: '' },
  { code: 'GD', description: 'GARAGE DOORS', hazmat: 'N', un: '' },
  { code: 'SOIL', description: 'GARDEN SOIL', hazmat: 'N', un: '' },
  { code: 'GENERAL', description: 'General Freight', hazmat: 'N', un: '' },
  { code: 'LIFT', description: 'Genie S -65 XC', hazmat: 'N', un: '' },
  { code: 'GLA', description: 'Glass', hazmat: 'N', un: '' },
  { code: 'GLASS', description: 'Glass Beads', hazmat: 'N', un: '' },
  { code: 'GC', description: 'Golf Carts', hazmat: 'N', un: '' },
  { code: 'GRAVEL', description: 'Gravel', hazmat: 'N', un: '' },
  { code: 'PARTS', description: 'Grinding Rings', hazmat: 'N', un: '' },
  {
    code: 'CREAMERS',
    description: 'half and half / heavy whipping cream',
    hazmat: 'N',
    un: '',
  },
  { code: 'HARDWARE', description: 'HARDWARE', hazmat: 'N', un: '' },
  { code: 'CAR', description: 'Hauling Car(s)', hazmat: 'N', un: '' },
  { code: 'EQUI', description: 'Heavy Equipment', hazmat: 'N', un: '' },
  { code: 'HERBICIDE', description: 'HERBICIDE', hazmat: 'N', un: 'UN2922' },
  { code: 'HIPSRG', description: 'HIPS Regrind', hazmat: 'N', un: '' },
  { code: 'TOWAWAY', description: 'Hook and Drop', hazmat: 'N', un: '' },
  { code: 'HOTTUBS', description: 'Hot Tubs', hazmat: 'N', un: '' },
  { code: 'HV', description: 'HVAC UNIT', hazmat: 'N', un: '' },
  { code: 'HYDRO', description: 'Hydrochill', hazmat: 'N', un: '' },
  { code: 'HYGIENE', description: 'Hygiene Products', hazmat: 'N', un: '' },
  { code: 'ICECREAM', description: 'ICE CREAM -20', hazmat: 'N', un: '' },
  { code: 'INFL', description: 'INFILL', hazmat: 'N', un: '' },
  {
    code: 'INSULATIO',
    description: 'Insulation Material',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LAWNGARD',
    description: 'LAWN & GARDEN PRODUCTS',
    hazmat: 'N',
    un: '',
  },
  { code: 'LIGHTERF', description: 'LIGHTER FLUID', hazmat: 'N', un: 'UN1993' },
  { code: 'LIGHTERS', description: 'LIGHTERS', hazmat: 'N', un: 'UN1057' },
  { code: 'LIGHTING', description: 'Lighting', hazmat: 'N', un: '' },
  { code: 'LINERS', description: 'liners', hazmat: 'N', un: '' },
  {
    code: 'LIQUIDWHO',
    description: 'Liquidation Wholesale',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LOADOUT',
    description: 'LOADOUT TRAILER - CAN USE',
    hazmat: 'N',
    un: '',
  },
  { code: 'LTL', description: 'LTL Default', hazmat: 'N', un: '' },
  { code: 'LUMBER', description: 'Lumber', hazmat: 'N', un: '' },
  {
    code: 'ATTACHMEN',
    description: 'Machine Attachments',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'TOWTRALRS',
    description: 'EMPTY TRAILER HOOK AND DROP',
    hazmat: 'N',
    un: '',
  },
  { code: 'EWT', description: 'EMPTY WATER TRUCK', hazmat: 'N', un: '' },
  { code: 'ENG', description: 'ENGINES', hazmat: 'N', un: '' },
  {
    code: 'ENTER',
    description: 'ENTERTAINMENT EQUIPMENT',
    hazmat: 'N',
    un: '',
  },
  { code: 'EXHIBITS', description: 'Exhibits', hazmat: 'N', un: '' },
  { code: 'FABRIC', description: 'Fabric', hazmat: 'N', un: '' },
  { code: 'FERT', description: 'Fertilizer', hazmat: 'N', un: '' },
  { code: 'FILTRAT', description: 'Filtration System', hazmat: 'N', un: '' },
  {
    code: 'FIRESAF',
    description: 'FIRST SAFETY PRODUCTS',
    hazmat: 'N',
    un: '',
  },
  { code: 'FLEX', description: 'FLEXI FLOATS', hazmat: 'N', un: '' },
  { code: 'FLOODBAR', description: 'Flood Barrier', hazmat: 'N', un: '' },
  { code: 'FLOORMATS', description: 'FLOOR MATS', hazmat: 'N', un: '' },
  { code: 'FLOOR', description: 'Flooring', hazmat: 'N', un: '' },
  {
    code: 'FLOWBIN',
    description: 'Flow Bins - Straps Only',
    hazmat: 'N',
    un: '',
  },
  { code: 'FOAM', description: 'Foam', hazmat: 'N', un: '' },
  { code: 'FP', description: 'Foil Pack', hazmat: 'N', un: '' },
  {
    code: 'FOOD',
    description: 'Food Disturbution or Wholesale',
    hazmat: 'N',
    un: '',
  },
  { code: 'F1', description: 'Ford Truck', hazmat: 'N', un: '' },
  { code: 'FORKLFT', description: 'Forklift', hazmat: 'N', un: '' },
  { code: 'FT', description: 'FRAC TANK', hazmat: 'N', un: '' },
  { code: 'FAK', description: 'Freight of All Kinds', hazmat: 'N', un: '' },
  { code: 'FRSHCHKN', description: 'Fresh Chicken', hazmat: 'N', un: '' },
  { code: 'FRZCHICKE', description: 'FROZEN CHICKEN', hazmat: 'N', un: '' },
  { code: 'FRZNCHKN', description: 'Frozen Chicken', hazmat: 'N', un: '' },
  { code: 'FROZEN28', description: 'FROZEN FOODS', hazmat: 'N', un: '' },
  { code: 'FROZEN', description: 'FROZEN FOODS', hazmat: 'N', un: '' },
  { code: 'FROZEN5', description: 'FROZEN FOODS (-5)', hazmat: 'N', un: '' },
  { code: 'FROZEN10', description: 'FROZEN FOODS -10', hazmat: 'N', un: '' },
  { code: 'FROZEN18', description: 'FROZEN FOODS -18', hazmat: 'N', un: '' },
  { code: 'FROZEN0', description: 'FROZEN FOODS 0', hazmat: 'N', un: '' },
  { code: 'FRZGROCER', description: 'Frozen Grocery', hazmat: 'N', un: '' },
  {
    code: 'FRZPROD',
    description: 'Frozen Produce/ Grocery',
    hazmat: 'N',
    un: '',
  },
  { code: 'FURNITURE', description: 'Furniture', hazmat: 'N', un: '' },
  { code: 'CCM', description: 'CONSTRUCTION MATERIALS', hazmat: 'N', un: '' },
  { code: 'CM', description: 'CONSTRUCTION MATERIALS', hazmat: 'N', un: '' },
  { code: 'CPG', description: 'CONSUMER PACKAGED GOODS', hazmat: 'N', un: '' },
  { code: 'CONVEYOR', description: 'CONVEYOR', hazmat: 'N', un: '' },
  { code: 'COOKOIL', description: 'COOKING OIL', hazmat: 'N', un: '' },
  { code: 'COOLING', description: 'Cooling Parts', hazmat: 'N', un: '' },
  { code: 'COPPER', description: 'Copper', hazmat: 'N', un: '' },
  { code: 'CORK', description: 'CORK', hazmat: 'N', un: '' },
  { code: 'COUPLERS', description: 'Couplers & Knuckles', hazmat: 'N', un: '' },
  { code: 'CRACKERS', description: 'CRACKERS', hazmat: 'N', un: '' },
  { code: 'CRANEMATS', description: 'Crane Mats', hazmat: 'N', un: '' },
  { code: 'MARBLE', description: 'CRATED MARBLE', hazmat: 'N', un: '' },
  { code: 'CRA', description: 'Crates', hazmat: 'N', un: '' },
  { code: 'DAIRY', description: 'DAIRY PRODUCTS', hazmat: 'N', un: '' },
  { code: 'DETERGENT', description: 'Detergent', hazmat: 'N', un: '' },
  { code: 'DIRT', description: 'Dirt/Organics', hazmat: 'N', un: '' },
  { code: 'DISRELIEF', description: 'Disaster Relief', hazmat: 'N', un: '' },
  { code: 'DISPLAY', description: 'display equipment', hazmat: 'N', un: '' },
  { code: 'D', description: 'DOORS', hazmat: 'N', un: '' },
  { code: 'MUD', description: 'Drilling Mud', hazmat: 'N', un: '' },
  { code: 'GROCERY', description: 'Dry Food Goods', hazmat: 'N', un: '' },
  { code: 'DRYGROCER', description: 'Dry Grocery', hazmat: 'N', un: '' },
  { code: 'DRYVAN', description: 'DRY VAN TRAILER', hazmat: 'N', un: '' },
  { code: 'DUNNAGE', description: 'Dunnage', hazmat: 'N', un: '' },
  { code: 'DUSTCO', description: 'DUST COVERS', hazmat: 'N', un: '' },
  { code: 'TEST', description: 'EDI Test Commodity', hazmat: 'N', un: '' },
  { code: 'EGGS', description: 'EGGS', hazmat: 'N', un: '' },
  {
    code: 'ELECCOMP',
    description: 'Electrical Components',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'ELECWHOL',
    description: 'Electronics Wholesale',
    hazmat: 'N',
    un: '',
  },
  { code: 'MTDRUMS', description: 'EMPTY DRUMS', hazmat: 'N', un: 'UN1090' },
  {
    code: 'EMPPROP',
    description: 'Empty Propane Cylinders',
    hazmat: 'N',
    un: '',
  },
  { code: 'EMPTYTANK', description: 'Empty Tanker', hazmat: 'N', un: '' },
  { code: 'EMPTOTES', description: 'Empty Totes', hazmat: 'N', un: '' },
  { code: 'BO', description: 'Boats', hazmat: 'N', un: '' },
  { code: 'TUBES', description: 'BOILER TUBING', hazmat: 'N', un: '' },
  { code: 'WATER', description: 'Bottled Water', hazmat: 'N', un: '' },
  { code: 'BOX', description: 'box', hazmat: 'N', un: '' },
  { code: 'BREAD', description: 'BREAD', hazmat: 'N', un: '' },
  { code: 'B', description: 'Brick/Stone', hazmat: 'N', un: '' },
  { code: 'BRICK', description: 'Bricks', hazmat: 'N', un: '' },
  { code: 'BM', description: 'Building Materials', hazmat: 'N', un: '' },
  { code: 'CABELTRAY', description: 'CABEL TRAY', hazmat: 'N', un: '' },
  { code: 'CABINETS', description: 'Cabinets', hazmat: 'N', un: '' },
  { code: 'CABLE', description: 'Cable Reels', hazmat: 'N', un: '' },
  { code: 'CANDLE', description: 'CANDLES', hazmat: 'N', un: '' },
  { code: 'CANDY', description: 'CANDY', hazmat: 'N', un: '' },
  { code: 'CANFOOD', description: 'CANNED FOODS', hazmat: 'N', un: '' },
  { code: 'CARPART', description: 'Car Hood', hazmat: 'N', un: '' },
  { code: 'CARWASH', description: 'CAR WASH MATERIALS', hazmat: 'N', un: '' },
  { code: 'CARPET', description: 'Carpet', hazmat: 'N', un: '' },
  { code: 'CHEESE', description: 'Cheese', hazmat: 'N', un: '' },
  { code: 'CHEMICALS', description: 'Chemicals', hazmat: 'N', un: '' },
  { code: 'CHIPS', description: 'Chips', hazmat: 'N', un: '' },
  {
    code: 'CRAN/CHER',
    description: 'Choc Cov Cran or Cherries',
    hazmat: 'N',
    un: '',
  },
  { code: 'CHOCOLATE', description: 'Chocolate', hazmat: 'N', un: '' },
  { code: 'XMASTREES', description: 'Christmas trees', hazmat: 'N', un: '' },
  { code: 'CLEANPR', description: 'CLEANING PRODUCTS', hazmat: 'N', un: '' },
  { code: 'CLOTHING', description: 'Clothing', hazmat: 'N', un: '' },
  { code: 'COCOIL', description: 'Coconut Oil', hazmat: 'N', un: '' },
  { code: 'COFFEE', description: 'Coffee', hazmat: 'N', un: '' },
  { code: 'COMPP', description: 'COMPACTOR & PARTS', hazmat: 'N', un: '' },
  { code: 'COMPEQIP', description: 'Computer Equipment', hazmat: 'N', un: '' },
  { code: 'CONCRETE', description: 'Concrete', hazmat: 'N', un: '' },
  { code: 'CC', description: 'Conex Container', hazmat: 'N', un: '' },
  {
    code: 'CONEQUIP',
    description: 'Construction Equipment',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'CONSTRUCT',
    description: 'CONSTRUCTION MATERIAL',
    hazmat: 'N',
    un: '',
  },
  { code: '-', description: '-', hazmat: 'N', un: '' },
  { code: '20CONTAIN', description: "20' CONTAINER", hazmat: 'N', un: '' },
  { code: 'LO', description: '53 Load out', hazmat: 'N', un: '' },
  { code: 'DRUM', description: '55 Gallon Drum', hazmat: 'N', un: '' },
  { code: 'AFRAME', description: 'A Frames', hazmat: 'N', un: '' },
  { code: 'AC', description: 'AC Units', hazmat: 'N', un: '' },
  { code: 'ADDITIVE', description: 'Additive', hazmat: 'N', un: '' },
  { code: 'ADHESIVE', description: 'Adhesive', hazmat: 'N', un: '' },
  { code: 'AEROARPL', description: 'Aerospace/ Aircraft', hazmat: 'N', un: '' },
  { code: 'AGRI', description: 'AGRICULTURE PARTS', hazmat: 'N', un: '' },
  {
    code: 'PREHEATER',
    description: 'Air preheater baskets',
    hazmat: 'N',
    un: '',
  },
  { code: 'SKIDS', description: 'AIR PRESSURE VESSELS', hazmat: 'N', un: '' },
  { code: 'ALUMIN', description: 'aluminum', hazmat: 'N', un: '' },
  { code: 'APPLES', description: 'apples', hazmat: 'N', un: '' },
  { code: 'APPL', description: 'Appliances', hazmat: 'N', un: '' },
  {
    code: 'HAZCLEAN',
    description: 'ARMAKLEEN - WATER BASED CLEANERS',
    hazmat: 'N',
    un: 'UN1760',
  },
  { code: 'ARTTURF', description: 'Artificial Turf', hazmat: 'N', un: '' },
  { code: 'SILO', description: 'Asphalt Silo', hazmat: 'N', un: '' },
  { code: 'AE', description: 'Audio Equipment', hazmat: 'N', un: '' },
  { code: 'AUGER', description: 'AUGER', hazmat: 'N', un: '' },
  { code: 'AUTOMOTI', description: 'Automotive Parts', hazmat: 'N', un: '' },
  { code: 'BAGS', description: 'BAGS', hazmat: 'N', un: '' },
  { code: 'ICEMELT', description: 'Bags of Ice Melt', hazmat: 'N', un: '' },
  { code: 'BALED', description: 'Baled Plastic', hazmat: 'N', un: '' },
  { code: 'CARDBOARD', description: 'BALES OF CARDBOARD', hazmat: 'N', un: '' },
  { code: 'BB', description: 'Ballast Blocks', hazmat: 'N', un: '' },
  { code: 'BANDSAW', description: 'BAND SAW ON PALLET', hazmat: 'N', un: '' },
  {
    code: 'BATT2794',
    description: 'BATTERIES, WET, FILLED WITH ACID',
    hazmat: 'N',
    un: 'UN2794',
  },
  { code: 'BEAMS', description: 'BEAMS', hazmat: 'N', un: '' },
  { code: 'BEDPROD', description: 'Bedding Products', hazmat: 'N', un: '' },
  { code: 'BEER', description: 'BEER', hazmat: 'N', un: '' },
  { code: 'BVGC0NT', description: 'Beverage Containers', hazmat: 'N', un: '' },
  { code: 'BEVERAGE', description: 'Beverages', hazmat: 'N', un: '' },
];

export const syfanCommodityOptions: McleodCommodityOption[] = [
  { code: 'AIRCRAFT', description: 'AIRCRAFT PARTS', hazmat: 'N', un: '' },
  { code: 'APPLIANCE', description: 'APPLIANCES', hazmat: 'N', un: '' },
  { code: 'AUTO', description: 'AUTOMOTIVE NON GLASS', hazmat: 'N', un: '' },
  { code: 'SEALANT', description: 'AUTOMOTIVE SEALANT', hazmat: 'N', un: '' },
  {
    code: 'BATTERIES',
    description: 'BATTERIES, WET, NON-SPILLABLE, ELECTRIC STORAGE',
    hazmat: 'N',
    un: '2800',
  },
  { code: 'BED', description: 'BEDDING', hazmat: 'N', un: '' },
  { code: 'BEEF', description: 'BEEF', hazmat: 'N', un: '' },
  { code: 'BEV', description: 'BEVERAGES', hazmat: 'N', un: '' },
  { code: 'FASTENERS', description: 'BOLTS & NUTS', hazmat: 'N', un: '' },
  { code: 'BONES', description: 'BONES & CARCASSES', hazmat: 'N', un: '' },
  { code: 'WATER', description: 'BOTTLED WATER', hazmat: 'N', un: '' },
  { code: 'BROTH', description: 'BROTH', hazmat: 'N', un: '' },
  { code: 'BUILD', description: 'BUILDING SUPPLIES', hazmat: 'N', un: '' },
  { code: 'MAIL', description: 'BULK/PKGS/PARCELS', hazmat: 'N', un: '' },
  { code: 'BUTTER', description: 'BUTTER', hazmat: 'N', un: '' },
  { code: 'CAR PARTS', description: 'CAR PARTS', hazmat: 'N', un: '' },
  { code: 'PARTS', description: 'CAR PARTS', hazmat: 'N', un: '' },
  { code: 'CHEESE', description: 'CHEESE', hazmat: 'N', un: '' },
  { code: 'CHEMICAL', description: 'CHEMICALS NON HAZ', hazmat: 'N', un: '' },
  { code: 'CHOC', description: 'CHOCOLATE', hazmat: 'N', un: '' },
  { code: 'hazmat9', description: 'CLASS 9- NON PLACARD', hazmat: 'N', un: '' },
  { code: 'COMP EQUI', description: 'COMPUTER EQUIPMENT', hazmat: 'N', un: '' },
  { code: 'OIL', description: 'COOKING OIL', hazmat: 'N', un: '' },
  { code: 'DAIRY', description: 'DAIRY', hazmat: 'N', un: '' },
  { code: 'DRY', description: 'DRY', hazmat: 'N', un: '' },
  { code: 'EGGS', description: 'EGGS', hazmat: 'N', un: '' },
  {
    code: 'ELECTRONI',
    description: 'ELECTRONIC PRODUCTS',
    hazmat: 'N',
    un: '',
  },
  { code: 'CHASSIS', description: 'EMPTY CHASSIS MOVES', hazmat: 'N', un: '' },
  { code: 'ENGINES', description: 'ENGINES', hazmat: 'N', un: '' },
  { code: 'FEED', description: 'FEED', hazmat: 'N', un: '' },
  { code: 'FIL', description: 'FILTERS', hazmat: 'N', un: '' },
  { code: 'FIXTURES', description: 'FIXTURES', hazmat: 'N', un: '' },
  { code: 'FLOWERS', description: 'FLOWERS/FLORAL', hazmat: 'N', un: '' },
  { code: 'FOOD', description: 'FOOD MISCELLANEOUS', hazmat: 'N', un: '' },
  { code: 'FAK', description: 'FREIGHT OF ALL KIND', hazmat: 'N', un: '' },
  { code: 'FRESHFOOD', description: 'FRESH FOOD', hazmat: 'N', un: '' },
  { code: 'FRESH', description: 'FRESH POULTRY', hazmat: 'N', un: '' },
  { code: 'FROZEN', description: 'FROZEN', hazmat: 'N', un: '' },
  { code: 'FURN', description: 'FURNITURE', hazmat: 'N', un: '' },
  { code: 'MERCH', description: 'GENERAL MERCHANDISE', hazmat: 'N', un: '' },
  { code: 'RACKS', description: 'GM RACK RETURN', hazmat: 'N', un: '' },
  { code: 'GROCERY', description: 'GROCERY SHIPMENT', hazmat: 'N', un: '' },
  { code: 'hazmat', description: 'HAZARDOUS MATERIALS', hazmat: 'Y', un: '' },
  { code: 'HIGHVAL', description: 'HIGH VALUE +250K', hazmat: 'N', un: '' },
  { code: 'ICE', description: 'ICE', hazmat: 'N', un: '' },
  { code: 'ICECREAM', description: 'ICE CREAM', hazmat: 'N', un: '' },
  { code: 'JUICE', description: 'JUICE', hazmat: 'N', un: '' },
  { code: 'KAYAKS', description: 'KAYAKS', hazmat: 'N', un: '' },
  { code: 'LETTUCE', description: 'LETTUCE', hazmat: 'N', un: '' },
  { code: 'LIGHT', description: 'LIGHTING', hazmat: 'N', un: '' },
  { code: 'BIRDS', description: 'LIVE BIRDS', hazmat: 'N', un: '' },
  { code: 'LTLMISC', description: 'LTL MISC.', hazmat: 'N', un: '' },
  { code: 'LUMBER', description: 'LUMBER', hazmat: 'N', un: '' },
  { code: 'MACH', description: 'MACHINERY', hazmat: 'N', un: '' },
  { code: 'MATTRESS', description: 'MATTRESSES', hazmat: 'N', un: '' },
  { code: 'MEDICAL', description: 'MEDICAL EQUIPMENT', hazmat: 'N', un: '' },
  { code: 'MILK', description: 'MILK', hazmat: 'N', un: '' },
  { code: 'MSC', description: 'MSC', hazmat: 'N', un: '' },
  { code: 'PAINT', description: 'NO HAZARDOUS PAINT', hazmat: 'N', un: '' },
  {
    code: 'AIRBAGS',
    description: 'NON PLACARD hazmat',
    hazmat: 'N',
    un: 'HAZ-9',
  },
  { code: 'PACK', description: 'PACKAGES', hazmat: 'N', un: '' },
  { code: 'AMZN', description: 'Packages', hazmat: 'N', un: '' },
  { code: 'PLTS', description: 'PALLETS', hazmat: 'N', un: '' },
  { code: 'PAPER', description: 'PAPER', hazmat: 'N', un: '' },
  { code: 'PEACHES', description: 'PEACHES', hazmat: 'N', un: '' },
  { code: 'PETFOOD', description: 'PET FOOD', hazmat: 'N', un: '' },
  { code: 'PHARM', description: 'PHARMACEUTICALS', hazmat: 'N', un: '' },
  { code: 'PLANTS', description: 'PLANTS', hazmat: 'N', un: '' },
  {
    code: 'PLASTIC',
    description: 'PLASTIC/ PLASTIC MOLDS',
    hazmat: 'N',
    un: '',
  },
  { code: 'PORK', description: 'PORK', hazmat: 'N', un: '' },
  { code: 'POTATOES', description: 'POTATOES', hazmat: 'N', un: '' },
  { code: 'POULTRY', description: 'POULTRY', hazmat: 'N', un: '' },
  {
    code: 'PRINT-CAR',
    description: 'PRINTERS AND PRINTER CATRIDGES',
    hazmat: 'N',
    un: '',
  },
  { code: 'PRODUCE', description: 'PRODUCE', hazmat: 'N', un: '' },
  { code: 'PUPTRLR', description: 'PUP TRAILER P/U', hazmat: 'N', un: '' },
  { code: 'RACKC', description: 'RACK - CAR PARTS', hazmat: 'N', un: '' },
  { code: 'RAIL', description: 'RAIL LOAD', hazmat: 'N', un: '' },
  {
    code: 'REFFOOD',
    description: 'REFRIGERATED FOOD PRODUCT',
    hazmat: 'N',
    un: '',
  },
  { code: 'REEFER', description: 'REFRIGERATED PRODUCT', hazmat: 'N', un: '' },
  { code: 'REWORK', description: 'rework product', hazmat: 'N', un: '' },
  { code: 'SAFETYWEA', description: 'SAFETY GARMENTS', hazmat: 'N', un: '' },
  { code: 'SEAFOOD', description: 'SEAFOOD', hazmat: 'N', un: '' },
  { code: 'STEEL', description: 'STEEL BEAMS', hazmat: 'N', un: '' },
  { code: 'STONE', description: 'STONE CASTING', hazmat: 'N', un: '' },
  { code: 'PODS', description: 'STORAGE CONTAINERS', hazmat: 'N', un: '' },
  { code: 'SUGAR', description: 'SUGAR', hazmat: 'N', un: '' },
  { code: 'TEMPGM', description: 'TEMPCONTROL GM PARTS', hazmat: 'N', un: '' },
  { code: 'TIRES', description: 'TIRES', hazmat: 'N', un: '' },
  { code: 'TRACTOR', description: 'TRACTOR PARTS', hazmat: 'N', un: '' },
  { code: 'TRACTORS', description: 'TRACTORS', hazmat: 'N', un: '' },
  { code: 'TRAILER', description: 'TRAILER MOVES', hazmat: 'N', un: '' },
  { code: 'TURKEY', description: 'TURKEY', hazmat: 'N', un: '' },
  {
    code: 'US MAIL',
    description: 'UNITED STATES POSTAL SERVICE MAIL',
    hazmat: 'N',
    un: '',
  },
  { code: 'UPS', description: 'UPS PACKAGES', hazmat: 'N', un: '' },
  { code: 'VIDEO', description: 'VIDEO EQUIPMENT', hazmat: 'N', un: '' },
  { code: 'GLASS', description: 'WINDSHIELDS', hazmat: 'N', un: '' },
  { code: 'WIRE', description: 'WIRE', hazmat: 'N', un: '' },
  { code: 'YARD DOG', description: 'YARD DOG', hazmat: 'N', un: '' },
  { code: 'YOGURT', description: 'YOGURT', hazmat: 'N', un: '' },
];

export const tumaloCommodityOptions: McleodCommodityOption[] = [
  { code: 'INGOT', description: 'ALUMINUM INGOT', hazmat: '', un: '' },
  { code: 'ALUM', description: 'ALUMINUM T-BARS/INGOT', hazmat: '', un: '' },
  { code: 'APPAREL', description: 'APPAREL', hazmat: '', un: '' },
  { code: 'APPLIANCE', description: 'APPLIANCES', hazmat: '', un: '' },
  { code: 'AUTO', description: 'AUTO FLUIDS', hazmat: '', un: '' },
  { code: 'AZTECA', description: 'AZTECA TORTILLAS', hazmat: '', un: '' },
  { code: 'BAKERY', description: 'BAKERY MIX', hazmat: '', un: '' },
  { code: 'BALED', description: 'BALED METAL', hazmat: '', un: '' },
  { code: 'BAL', description: 'BALER', hazmat: '', un: '' },
  { code: 'BAT', description: 'BATTERIES- NON HAZMAT', hazmat: '', un: '' },
  { code: 'BEVERAGE', description: 'BOTTLED BEVERAGE', hazmat: '', un: '' },
  { code: 'SODA', description: 'BOTTLED SODA', hazmat: '', un: '' },
  { code: 'WATER', description: 'BOTTLED WATER', hazmat: '', un: '' },
  { code: 'METAL', description: 'BOXED METAL', hazmat: '', un: '' },
  {
    code: 'CANS',
    description: 'CANS, PLATE TRAILER, FOOD GRADE',
    hazmat: '',
    un: '',
  },
  { code: 'CAR', description: 'CARTONS', hazmat: '', un: '' },
  { code: 'CLE', description: 'CLEANING/HYGIENE PRODUCTS', hazmat: '', un: '' },
  { code: 'DEER', description: 'DEER BLINDS', hazmat: '', un: '' },
  { code: 'DET', description: 'DETERGENT', hazmat: '', un: '' },
  { code: 'MILK', description: 'DRY MILK', hazmat: '', un: '' },
  {
    code: 'TRADE',
    description: 'DRY MILK - TRADE MITIGATION',
    hazmat: '',
    un: '',
  },
  { code: 'MILK32', description: 'DRY MILK32', hazmat: '', un: '' },
  { code: 'DRY', description: 'DRY PRODUCT', hazmat: '', un: '' },
  { code: 'ELE', description: 'ELECTRONICS/RECYCLE', hazmat: '', un: '' },
  { code: 'EQUIPMENT', description: 'EQUIPMENT', hazmat: '', un: '' },
  { code: 'EQUIP', description: 'EQUIPMENT/DICER', hazmat: '', un: '' },
  { code: 'FASTENERS', description: 'FASTENERS', hazmat: '', un: '' },
  { code: 'TIL', description: 'FLOOR/WALL TILE', hazmat: '', un: '' },
  { code: 'FRH', description: 'FRESH/CHILLED PRODUCTS', hazmat: '', un: '' },
  { code: 'FZN', description: 'FROZEN PRODUCT', hazmat: '', un: '' },
  { code: 'FUR', description: 'FURNITURE', hazmat: '', un: '' },
  {
    code: 'GLASSWARE',
    description: 'GLASSWARE ON SKIDS - FOOD GRADE',
    hazmat: '',
    un: '',
  },
  { code: 'HAZ', description: 'HAZARDOUS GOODS', hazmat: 'N', un: '' },
  { code: 'HOU', description: 'HOUSEHOLD GOODS', hazmat: '', un: '' },
  {
    code: 'SALT',
    description: 'ICE MELT / CALCIUM CLORIDE',
    hazmat: '',
    un: '',
  },
  { code: 'LEAD', description: 'LEAD-SOW/SCRAP', hazmat: '', un: '' },
  { code: 'LFT', description: 'LIFT TRUCK/FORK LIFT', hazmat: '', un: '' },
  { code: 'LIME', description: 'LIMESTONE', hazmat: '', un: '' },
  { code: 'MANG', description: 'MANGENESE ROCKS', hazmat: '', un: '' },
  { code: 'BALER', description: 'MECHANICAL BALER', hazmat: '', un: '' },
  { code: 'INGT', description: 'METAL INGOT/SOW', hazmat: '', un: '' },
  { code: 'MISSION', description: 'MISSION TORTILLAS', hazmat: '', un: '' },
  { code: 'NOPRO', description: 'NO PRO', hazmat: 'N', un: '' },
  { code: 'NURS', description: 'NURSERY STOCK', hazmat: '', un: '' },
  { code: 'ONION', description: 'ONIONS', hazmat: '', un: '' },
  { code: 'PKG', description: 'PACKAGING MATERIAL', hazmat: '', un: '' },
  { code: 'PAPER', description: 'PAPER PRODUCTS', hazmat: '', un: '' },
  { code: 'PERISH', description: 'PERISHABLE DRY PRODUCT', hazmat: '', un: '' },
  { code: 'PLASTIC', description: 'PLASTIC ARTICLES', hazmat: '', un: '' },
  { code: 'BOARDS', description: 'PLASTIC BOARDS', hazmat: '', un: '' },
  {
    code: 'BOTTLES',
    description: 'PLASTIC BOTTLES ON SKIDS - FOOD GRADE TRAILER',
    hazmat: '',
    un: '',
  },
  {
    code: 'EQUPMENT',
    description: 'PLAY GROUNG EQUIPMENT',
    hazmat: '',
    un: '',
  },
  { code: 'POL', description: 'POLY PLASTIC', hazmat: '', un: '' },
  { code: 'FOAM', description: 'POLY-FOAM - NON HAZ', hazmat: '', un: '' },
  { code: 'POT', description: 'POTATOES', hazmat: '', un: '' },
  { code: 'PRI', description: 'PRINTED MATERIAL', hazmat: '', un: '' },
  { code: 'PRODUCE', description: 'PRODUCE', hazmat: '', un: '' },
  { code: 'PRO', description: 'PROTECT FROM HEAT/COLD', hazmat: '', un: '' },
  { code: 'RAD', description: 'RADIATORS - CRATED', hazmat: '', un: '' },
  {
    code: 'RECYCLE',
    description: 'RECYCLED ALUMINUM BAR/ROD/INGOT',
    hazmat: '',
    un: '',
  },
  { code: 'CHL', description: 'REFRIGERATED PRODUCT', hazmat: '', un: '' },
  { code: 'SCRP', description: 'SCRAP METAL', hazmat: '', un: '' },
  { code: 'PLAST', description: 'SCRAP PLASTIC', hazmat: '', un: '' },
  { code: 'SOA', description: 'SOAP PRODUCTS', hazmat: '', un: '' },
  { code: 'STEEL', description: 'STEEL DISCS', hazmat: '', un: '' },
  { code: 'STRFX', description: 'STORE FIXTURES', hazmat: '', un: '' },
  { code: 'SUN', description: 'SUNDRIES/WIPES/SOAP', hazmat: '', un: '' },
  { code: 'PALLETS', description: 'TCNU7844354', hazmat: '', un: '' },
  { code: 'TIME', description: 'TIME SENSITIVE MATERIAL', hazmat: '', un: '' },
  { code: 'WEL', description: 'WELDING EQUIPMENT', hazmat: 'N', un: '' },
  { code: 'WOOD', description: 'WOOD PRODUCTS/PANELS', hazmat: '', un: '' },
];

/* --------- REFERENCE NUMBER OPTIONS --------- */

export const tridentReferenceNumberOptions: McleodReferenceNumberOption[] = [
  { description: 'Accounts Receivable Number', code: 'AP' },
  { description: 'Bill of Lading Number', code: 'BM' },
  { description: "Buyer's Credit Memo", code: 'CM' },
  { description: "Consignee's Order Number", code: 'CG' },
  { description: 'Customer Order Number', code: 'CO' },
  { description: 'Delivery Reference', code: 'KK' },
  { description: 'Diversion Authority Number', code: 'DV' },
  { description: 'Line Quanitity', code: 'QL' },
  { description: 'Master Bill of Lading', code: 'MB' },
  { description: 'Mutally Defined', code: 'ZZ' },
  { description: 'Order Number', code: 'OR' },
  { description: 'Original Invoice Number', code: 'OI' },
  { description: 'Original Return Request Reference Number', code: 'OD' },
  { description: 'Pickset Number', code: 'PS' },
  { description: 'Pickup Number', code: 'PU' },
  { description: 'Pickup Reference Number', code: 'P8' },
  { description: 'Price List Change or Issue Number', code: 'PI' },
  { description: 'Purchase Order Number', code: 'PO' },
  { description: 'Seal Number', code: 'SN' },
  { description: "Shipper's Identifying Number (SID)", code: 'SI' },
  { description: "Shipper's Order (Invoice Number)", code: 'SO' },
];

export const fetchReferenceNumberOptions: McleodReferenceNumberOption[] = [
  { description: 'Pickup Number', code: 'PU' },
  { description: 'Price List Change or Issue Number', code: 'PI' },
  { description: 'Price List Number', code: 'PL' },
  { description: 'Price Quote Number', code: 'PR' },
  { description: 'Product Period for which Labor Costs are Firm', code: 'LM' },
  { description: 'Purchase Order Number', code: 'PO' },
  { description: 'Release invoice number for prior bill and hold', code: 'RI' },
  { description: 'Release Number', code: 'RE' },
  { description: 'Repetitive Booking Number', code: 'RO' },
  {
    description:
      'Repetitive Waybill Code (Origin Carrier, Standard Point Location Code,',
    code: 'RW',
  },
  { description: 'Run Number', code: 'RN' },
  { description: 'Sample Number', code: 'XO' },
  { description: 'Scan Line', code: 'SP' },
  { description: "Seller's Debit Memo", code: 'DL' },
  { description: "Seller's Sale Number", code: 'SW' },
  { description: 'Serial Number', code: 'SE' },
  { description: "Shipper's Identifying Number (SID)", code: 'SI' },
  { description: "Shipper's Order (Invoice Number)", code: 'SO' },
  { description: 'Special Processing Code', code: 'SU' },
  { description: 'Standard Carrier Alpha Code (SCAC)', code: 'SCA' },
  { description: 'Store Number', code: 'ST' },
  { description: 'Tariff Supplement Number', code: 'OU' },
  { description: 'Terminal Operator Number', code: 'TO' },
  { description: 'Title XIX Identifier Number', code: 'XN' },
  { description: 'Unit Number', code: 'QQ' },
  { description: 'Vendor Order Number', code: 'VN' },
  { description: 'Ward', code: 'VM' },
  { description: 'Work Order Number', code: 'WO' },
  { description: 'Accounts Receivable Number', code: 'AP' },
  { description: "Agent's Shipment Number", code: 'AG' },
  { description: 'Appointment Number', code: 'AO' },
  { description: 'Appropriation Number', code: 'AT' },
  { description: 'Associated Invoices', code: 'AI' },
  { description: 'Authorization to Meet Competition Number', code: 'AU' },
  { description: 'Beginning Serial Number', code: 'BG' },
  { description: 'Bill of Lading Number', code: 'BM' },
  { description: 'Bin Location Number', code: 'BO' },
  {
    description:
      'Bonded Carrier Internal Revenue Service Identification Number',
    code: 'BI',
  },
  { description: 'Booking Number', code: 'BN' },
  { description: 'Broker or Sales Office Number', code: 'BR' },
  { description: "Buyer's Credit Memo", code: 'CM' },
  { description: 'Carrier Assigned Code', code: 'AAO' },
  { description: "Carrier's Reference Number (PRO/Invoice)", code: 'CN' },
  { description: 'Class of Contract Code', code: 'CE' },
  { description: 'Clear Text Clause', code: 'CU' },
  { description: 'Collocation Indicator', code: 'COL' },
  { description: 'Condition of Purchase Document Number', code: 'CP' },
  { description: 'Condition of Sale Document Number', code: 'CS' },
  { description: "Consignee's Order Number", code: 'CG' },
  { description: 'Container or Equipment Receipt Number', code: 'ER' },
  { description: 'Customer catalog number', code: 'CH' },
  { description: 'Customer Order Number', code: 'CO' },
  { description: 'Customer Reference Number', code: 'CR' },
  { description: 'Dealer Order Number', code: 'ON' },
  { description: 'Dealer purchase order number', code: 'DC' },
  { description: 'Delivery Order Number', code: 'DO' },
  {
    description:
      'Department of Defense Transportation Service Code Number (Household Goods)',
    code: 'DY',
  },
  { description: 'Dependents Information', code: 'DU' },
  { description: 'Depositor Number', code: 'DE' },
  { description: 'Discounter Registration Number', code: 'NM' },
  { description: 'Domicile Branch Number', code: 'DA' },
  { description: 'Drug Formulary Number', code: 'FO' },
  { description: 'Export Reference Number', code: 'RF' },
  { description: 'Government Bill of Lading', code: 'BL' },
  { description: 'Government Transportation Request', code: 'TR' },
  { description: 'Grain Order Reference Number', code: 'GR' },
  { description: 'Heat Code', code: 'HC' },
  { description: 'Institution Loan Number', code: 'VO' },
  { description: 'Insurance Certificate Number', code: 'ID' },
  { description: 'Job (Project) Number', code: 'JB' },
  { description: 'Lease Schedule Number - Blanket', code: 'BH' },
  { description: "Line Item Identifier (Seller's)", code: 'LI' },
  { description: 'Line Quanitity', code: 'QL' },
  { description: 'Loan Acquisition Number', code: 'GM' },
  { description: 'Major System Affected Code', code: 'QV' },
  { description: 'Mammography Certification Number', code: 'EW' },
  { description: 'Manufacturing Operation Number', code: 'MO' },
  { description: 'Master Bill of Lading', code: 'MB' },
  { description: 'Merchandise Type Code', code: 'MR' },
  { description: 'Message Address or ID', code: 'ME' },
  { description: 'Microfilm Number', code: 'MC' },
  { description: 'Mill Order Number', code: 'MI' },
  { description: 'Multiple Listing Number', code: 'JO' },
  { description: 'Multiple Listing Service Map Y Coordinate', code: 'JN' },
  { description: 'Mutally Defined', code: 'ZZ' },
  { description: 'Non pickup Limited Tariff Number', code: 'LN' },
  { description: 'Ocean Manifest', code: 'OM' },
  { description: 'Open and Prepaid Station List Number', code: 'OE' },
  { description: 'Order Number', code: 'OR' },
  { description: 'Original Invoice Number', code: 'OI' },
  { description: 'Part Number', code: 'PM' },
  {
    description:
      "Payee's Financial Institution Account Number for Check, Draft or Wire Payments",
    code: 'PY',
  },
  { description: 'Permit Number', code: 'PN' },
  { description: 'Pickset Number', code: 'PS' },
];

export const syfanReferenceNumberOptions: McleodReferenceNumberOption[] = [
  { description: 'Acceptable Source DUNS Number', code: 'AD' },
  { description: 'Accounting (Equipment) Location Number', code: 'AL' },
  { description: 'Accounts Receivable Number', code: 'AP' },
  { description: 'Adjustment Control Number', code: 'BP' },
  { description: 'Adjustment Memo (Charge Back)', code: 'AM' },
  { description: 'Advertiser Number', code: 'A0' },
  { description: "Agent's Shipment Number", code: 'AG' },
  { description: 'Airline Ticket Number', code: 'XG' },
  { description: 'Airlines Flight Identification Number', code: 'AF' },
  { description: 'Appointment Number', code: 'AO' },
  { description: 'Approval Code', code: 'K0' },
  { description: 'Arrival Code', code: 'AR' },
  { description: 'Associated Invoices', code: 'AI' },
  { description: 'Associated Product Number', code: 'DM' },
  {
    description:
      'Association of American Railroads (AAR) Railway Accounting Rules',
    code: '2L',
  },
  { description: 'Authorization for Expense (AFE) Number', code: 'AE' },
  { description: 'Authorization Number', code: 'BB' },
  { description: 'Batch Number', code: 'BT' },
  { description: 'Begin Mile Marker', code: 'BMM' },
  { description: 'Beginning Serial Number', code: 'BG' },
  { description: 'Bill of Lading Number', code: 'BM' },
  { description: 'Bin Location Number', code: 'BO' },
  { description: 'Blended With Batch Number', code: 'BW' },
  {
    description:
      'Bonded Carrier Internal Revenue Service Identification Number',
    code: 'BI',
  },
  { description: 'Booking Number', code: 'BN' },
  { description: 'Broker or Sales Office Number', code: 'BR' },
  { description: "Broker's Order Number", code: 'BK' },
  { description: 'Business Activity', code: 'BE' },
  { description: "Buyer's Approval Mark", code: 'BU' },
  { description: "Buyer's Credit Memo", code: 'CM' },
  { description: "Buyer's Shipment Mark Number", code: 'BX' },
  { description: 'Carrier Assigned Code', code: 'AAO' },
  { description: "Carrier's Customs Bond Number", code: 'BJ' },
  { description: "Carrier's Reference Number (PRO/Invoice)", code: 'CN' },
  { description: 'CHASSIS NUMBER', code: 'CH' },
  { description: 'Civil Action Number', code: 'HG' },
  { description: 'Clear Text Clause', code: 'CU' },
  { description: 'Combined Shipment', code: 'CB' },
  { description: 'Condition of Sale Document Number', code: 'CS' },
  { description: "Consignee's Order Number", code: 'CG' },
  { description: 'Container or Equipment Receipt Number', code: 'ER' },
  { description: 'Container/Packaging Specification Number', code: '98' },
  { description: 'Contract Co-op Number', code: 'CC' },
  { description: 'Contract Number', code: 'CT' },
  { description: 'Cornbore Certification Number', code: 'GJ' },
  { description: 'County Legislative District', code: 'VH' },
  { description: 'Current or Latest Course Number', code: 'UJ' },
  { description: 'Customer Order Number', code: 'CO' },
  { description: 'Customer Reference Number', code: 'CR' },
  { description: 'Customs Drawback Entry Number', code: 'KO' },
  { description: 'Delivery Order Number', code: 'DO' },
  { description: 'Density Order Number', code: 'DON' },
  { description: 'Department Number', code: 'DP' },
  {
    description: 'Department of Interior Acquisition Regulation (DIAR)',
    code: 'DOI',
  },
  { description: 'Department/Agency Number', code: 'DX' },
  { description: 'Deposit Sequence Number', code: 'DW' },
  { description: 'Depositor Number', code: 'DE' },
  { description: 'Depository Trust Company Identification', code: 'WW' },
  { description: 'Discounter Registration Number', code: 'NM' },
  { description: 'Dock Number', code: 'DK' },
  { description: 'Dock Receipt Number', code: 'DR' },
  { description: 'Document Identification Code', code: 'DD' },
  { description: 'Downstream Shipper Contract Number', code: 'DT' },
  { description: 'Drug Formulary Number', code: 'FO' },
  { description: 'DUNS#', code: 'DN' },
  { description: 'Election District', code: 'EE' },
  { description: 'Electronic device pin number', code: 'EL' },
  { description: 'Electronic Payment Reference Number', code: 'EM' },
  { description: 'Elevation', code: 'JQ' },
  { description: 'Equipment Number', code: 'EQ' },
  { description: 'Export Reference Number', code: 'RF' },
  {
    description:
      'Federal Home Loan Mortgage Corporation Default/Foreclosure Specialist Number',
    code: 'XJ',
  },
  {
    description: 'Federal Maritime Commission (FMC) Forwarders Number',
    code: 'FM',
  },
  {
    description: 'Federal Maritime Commission (FMC) Tariff Number',
    code: 'TE',
  },
  { description: 'FEDWIRE (Federal Wire Transfer)', code: '54' },
  { description: 'File Identifier', code: 'FI' },
  { description: 'Final Sequence Number', code: 'FS' },
  { description: 'Fleet Reference Number', code: 'CF' },
  { description: 'Folder Number', code: 'GN' },
  { description: 'For Pickup Limited Freight Tariff Number', code: 'LP' },
  { description: 'Foreign Military Sales Notice Number', code: 'K1' },
  { description: 'Formation', code: 'YO' },
  { description: 'Gauge Ticket Number', code: 'GG' },
  { description: 'Geographic Destination Zone Number', code: 'GL' },
  { description: 'Geographic Key', code: 'YY' },
  { description: 'Goods and Service Tax Registration Number', code: 'GT' },
  { description: 'Government Advance Progress', code: 'GA' },
  { description: 'Government Bill of Lading', code: 'BL' },
  { description: 'Government Contract Number', code: 'GC' },
  { description: 'Grain Order Reference Number', code: 'GR' },
  { description: 'Health Maintenance Organization Code Number', code: 'BQ' },
  { description: 'Inbound-to or Outbound-from Party', code: 'IO' },
  { description: 'Inquiry Request Number', code: 'K9' },
  { description: 'Inspection Report Number', code: 'IP' },
  { description: 'Insurance Certificate Number', code: 'ID' },
  { description: 'Interchange Agreement Number', code: 'IE' },
  { description: 'Internal Vendor Number', code: 'IA' },
  {
    description: 'International Registration Plan Sticker Number',
    code: 'ISN',
  },
  { description: 'Intra Plant Routing', code: 'IR' },
  { description: 'Invoice Number', code: 'IK' },
  { description: 'Invoice Number Suffix', code: 'IS' },
  { description: 'Issuer Number', code: 'XM' },
  { description: 'Letter of Credit Number', code: 'NB' },
  { description: 'Line Quanitity', code: 'QL' },
  { description: 'Load Planning Number', code: 'LO' },
  { description: 'Loan Acquisition Number', code: 'GM' },
  { description: 'Local Student Identification Number', code: 'LR' },
  { description: 'Locally Assigned Control Number', code: 'WF' },
  {
    description: 'Longitude expressed in Degrees, Minutes and Seconds',
    code: 'LK',
  },
  { description: 'Magazine Code', code: 'MD' },
  { description: 'Manufacturing Operation Number', code: 'MO' },
  { description: 'Master Bill of Lading', code: 'MB' },
  { description: 'Master Lease Agreement Number', code: 'MM' },
  { description: 'Meter Number', code: 'MG' },
  { description: 'MICR Number', code: 'MN' },
  {
    description:
      'Migrant Number, This number is assigned by the national Migrant Records',
    code: 'MV',
  },
  { description: 'Multiple Listing Number', code: 'JO' },
  { description: 'Multiple Listing Service Book Type', code: 'JP' },
  { description: 'Mutally Defined', code: 'ZZ' },
  { description: 'National Property Registry System Level 3', code: 'ABN' },
  { description: 'National Stock Number', code: 'NS' },
  { description: 'New Part Number', code: 'QW' },
  { description: 'No OT5 Authority-zero Mileage Rate', code: 'NO' },
  { description: 'Non pickup Limited Tariff Number', code: 'LN' },
  { description: 'Nonprovisional Patent Application Number', code: 'PAN' },
  { description: 'North American Hazardous Classification Number', code: 'NA' },
  { description: 'Ocean Manifest', code: 'OM' },
  { description: 'Offer Group', code: 'OK' },
  { description: 'Open and Prepaid Station List Number', code: 'OE' },
  { description: 'Operator Lease Number', code: 'YR' },
  { description: 'Order Number', code: 'OQ' },
  { description: 'Order/Paragraph Number', code: 'OR' },
  { description: 'Original Filing', code: 'O8' },
  { description: 'Original Invoice Number', code: 'OI' },
  { description: 'Original Purchase Order', code: 'OP' },
  { description: "Original Shipper's Bill of Lading Number", code: 'OL' },
  {
    description: 'Originating Depository Financial Institution Identifier',
    code: '8P',
  },
  { description: 'Other Unlisted Type of Reference Number', code: 'XY' },
  { description: 'Outbound-from Party', code: 'OS' },
  { description: 'Outlet Number', code: 'OA' },
  { description: 'Packer Number', code: 'PJ' },
  { description: 'Page Number', code: 'P9' },
  { description: 'Part Number', code: 'PM' },
  {
    description:
      "Payee's Financial Institution Account Number for Check, Draft or Wire Payments",
    code: 'PY',
  },
  {
    description:
      "Payee's Financial Institution Transit Routing Number for Check, Draft or Wire",
    code: 'RT',
  },
  {
    description:
      "Payer's Financial Institution Account Number for Check, Draft, or Wire",
    code: 'PB',
  },
  {
    description:
      "Payer's Financial Institution Transit Routing Number for Check, Draft or Wire",
    code: 'RR',
  },
  { description: 'Permit Number', code: 'PN' },
  { description: 'Pickset Number', code: 'PS' },
  { description: 'Pickup Reference Number', code: 'P8' },
  { description: 'Picture Number', code: '51' },
  { description: 'Platform Identification Number', code: 'PIN' },
  { description: 'Policy Form Identifying Number', code: '0K' },
  { description: 'Policy Number', code: 'POL' },
  { description: 'Pre-Award Survey', code: 'WJ' },
  { description: 'Previous Bill of Lading Number', code: 'PU' },
  { description: 'Previous Invoice Number', code: 'PX' },
  { description: 'Previous Policy Number', code: '66' },
  { description: 'Previous Ticket Number', code: 'ACG' },
  { description: 'Price List Change or Issue Number', code: 'PI' },
  { description: 'Price List Number', code: 'PL' },
  { description: 'Price Quote Number', code: 'PR' },
  { description: 'Priority Rating', code: 'PH' },
  { description: 'Product Line Number', code: 'P7' },
  { description: 'Production Code', code: 'PC' },
  { description: 'Provider Control Number', code: '6R' },
  { description: 'Purchase Description', code: 'K6' },
  { description: 'Purchase Option Agreement', code: 'PT' },
  { description: 'Purchase Order Number', code: 'PO' },
  { description: 'Purchase Order Revision Number', code: 'PP' },
  { description: 'Purchase Requisition Number', code: 'RQ' },
  { description: 'Quality Inspection Area Identifier', code: 'QI' },
  { description: 'Quality Report Number', code: 'QR' },
  { description: 'Quarter Quarter Section Number', code: 'UM' },
  { description: 'Quarter Quarter Spot Number', code: 'UO' },
  { description: 'Quote Number', code: 'Q1' },
  { description: 'Rail Routing Code', code: 'RC' },
  { description: 'Rating Period', code: 'XX' },
  { description: 'Raw material supplier Dun & Bradstreet number', code: 'RM' },
  { description: 'Rebate Number', code: 'RY' },
  { description: 'Receiving Number', code: 'RV' },
  { description: 'Reference Version Number', code: 'ZI' },
  { description: 'Region', code: 'ACB' },
  { description: 'Release invoice number for prior bill and hold', code: 'RI' },
  { description: 'Release Number', code: 'RE' },
  { description: 'Repetitive Booking Number', code: 'RO' },
  { description: 'Repetitive Cargo Shipment Number', code: 'RA' },
  { description: 'Replacement Assembly Serial Number', code: 'QH' },
  { description: 'Reporter Identification', code: 'YT' },
  { description: 'Reserve Assembly Line Feed Location', code: 'RL' },
  { description: 'Response to a Request for Quotation Reference', code: 'KR' },
  { description: 'Resubmit number', code: 'RX' },
  { description: 'Return Material Authorization Number', code: 'QJ' },
  { description: 'Returnable Container Serial Number', code: 'RS' },
  { description: 'Route Number', code: 'RU' },
  { description: 'Run Number', code: 'RN' },
  { description: 'Safety of Ship Certificate', code: 'Z1' },
  { description: 'Sales Allowance Number', code: 'OT' },
  { description: 'Sales Office Number', code: 'SM' },
  { description: 'Sales Program Number', code: 'QK' },
  { description: 'Sample Number', code: 'XO' },
  { description: 'Savings', code: 'SG' },
  { description: 'Scan Line', code: 'SP' },
  { description: 'Seal Number', code: 'SN' },
  { description: 'Seal Off Number', code: 'S8' },
  { description: 'Secondary Coverage Company Number', code: 'NC' },
  { description: 'Sender Defined Clause', code: 'SH' },
  { description: 'Serial Number', code: 'SE' },
  { description: 'Service Interrupt Tracking Number', code: 'SX' },
  { description: 'Set Number', code: 'SJ' },
  { description: 'Ship Notice/Manifest Number', code: 'MA' },
  { description: 'Shipper Car Order Number', code: 'SC' },
  { description: "Shipper's Hazardous Number", code: 'HO' },
  {
    description: "Shipper's Identifying Number for Shipment (SID)",
    code: 'SI',
  },
  { description: "Shipper's Order (Invoice Number)", code: 'SO' },
  { description: 'Signal Code', code: 'T4' },
  { description: 'Sort', code: 'SR' },
  { description: 'Special Processing Code', code: 'SU' },
  { description: 'Split Shipment Number', code: 'SS' },
  {
    description:
      'Standard Transportation Commodity Code (STCC) Replacement Code',
    code: 'STR',
  },
  { description: 'Stop Sequence Number', code: 'QN' },
  { description: 'Store Number', code: 'ST' },
  { description: 'Sub-Servicer Loan Number', code: 'ABM' },
  { description: 'Subday Number', code: 'SD' },
  {
    description:
      'Systematized Nomenclature of Human and Veterinary Medicine (SNOMED)',
    code: 'SNH',
  },
  { description: 'Tariff Number', code: 'TS' },
  { description: 'Tariff Supplement Number', code: 'OU' },
  { description: 'Tax License Exemption', code: 'TL' },
  { description: 'Title XIX Identifier Number', code: 'XN' },
  { description: 'Tracer Action Request Number', code: 'TQ' },
  { description: 'Tracking Number', code: '2I' },
  { description: 'TRAILER NUMBER', code: 'TR' },
  { description: 'TRAILER TYPE', code: 'TT' },
  { description: 'Transaction Reference Number', code: 'TN' },
  { description: 'Transportation Control Number (TCN)', code: 'TG' },
  { description: 'Transportation Priority Number', code: 'XE' },
  { description: 'Travel Manifest (ACI or OTR)', code: 'TM' },
  { description: 'Type of Law Suit', code: 'KH' },
  { description: 'Unacceptable Source Purchaser ID', code: 'UB' },
  { description: 'Unique Consignment Identifier', code: 'CI' },
  { description: 'Unit Number', code: 'QQ' },
  { description: 'United Nations Hazardous Classification Number', code: 'UN' },
  { description: 'United States Government Visa Number', code: '30' },
  { description: 'Upstream Shipper Contract Number', code: 'UP' },
  { description: 'Vendor Change Procedure Code', code: 'VG' },
  { description: 'Vendor ID Number', code: 'VR' },
  { description: 'Vendor Order Number', code: 'VN' },
  { description: 'Volume Number', code: 'VD' },
  { description: 'Volume Purchase Agreement Number', code: 'V1' },
  { description: 'Ward', code: 'VM' },
  { description: 'Warehouse Receipt Number', code: 'WR' },
  { description: 'Warehouse storage location number', code: 'WS' },
  { description: 'Work Order Number', code: 'WO' },
];

/* --------- ORDER TYPE OPTIONS --------- */

export const fetchOrderTypes: ValueLabelOption[] = [
  { value: 'BACKUP', label: 'Backup Commitment' },
  { value: 'PRIMARY', label: 'Primary Commitment' },
  { value: 'SPOT', label: 'Spot Load' },
  { value: 'SUBJECT', label: 'Dummy Load' },
];

export const tridentOrderTypes: ValueLabelOption[] = [
  { value: 'T', label: 'Truck Load' },
  { value: 'ENT', label: 'ENT' },
];

// NOTE: Because of duplicate labels with different *codes*, we prepend the code to the label.
// This is required for backend to properly map the order type to the correct code.
export const syfanOrderTypes: ValueLabelOption[] = [
  { value: '3MLOAD', label: '3MLOAD - 3M LOADS' },
  { value: 'ABBOTT', label: 'ABBOTT - abbott nutrition' },
  { value: 'ABICAFSC', label: 'ABICAFSC - ABI Cali Fuel' },
  { value: 'ABIREEF', label: 'ABIREEF - ANHEUSER BUSCH REEFER LOAD' },
  { value: 'ACE', label: 'ACE - ACE HARDWARE' },
  { value: 'ACTAERO', label: 'ACTAERO - ACTIVE AERO SHIPMENTS' },
  { value: 'ACTCAN', label: 'ACTCAN - ACTAERO CANADA' },
  { value: 'ACTIMEX', label: 'ACTIMEX - ACTIVE AERO MEXICO SHIPMENTS' },
  { value: 'AEROTEMP', label: 'AEROTEMP - ACTIVE AERO TEMP CONTROLLED' },
  { value: 'AGILDROP', label: 'AGILDROP - AGILE COLD EXPORT DROP' },
  { value: 'AGILECOL', label: 'AGILECOL - AGILE COLD STORAGE EXPORTS' },
  { value: 'AGILLIVE', label: 'AGILLIVE - AGILE COLD  EXPORTS LIVE' },
  { value: 'AGILMACO', label: 'AGILMACO - AGILE MACON' },
  { value: 'AGILPROJ', label: 'AGILPROJ - AGILE SPECIAL PROJECT' },
  { value: 'AGROSUP', label: 'AGROSUP - AGROSUPER' },
  { value: 'AINSWRTH', label: 'AINSWRTH - Transplace - Ainsworth' },
  { value: 'AIREXP', label: 'AIREXP - UPS AIR TEAM LOAD' },
  { value: 'AIREXS', label: 'AIREXS - UPS AIR SOLO LOAD' },
  { value: 'AKPIZZA', label: 'AKPIZZA - Port City Bakery DBA AK Pizz' },
  { value: 'ALBAF', label: 'ALBAF - ALBA FORM INC' },
  { value: 'ALLIRIWI', label: 'ALLIRIWI - alliance laundry' },
  { value: 'ALLSBUGA', label: 'ALLSBUGA - ALLSOUTH SPRIKLER' },
  { value: 'ALLSOURC', label: 'ALLSOURC - ALLSOURCE SUPPLY INC' },
  { value: 'AMAZON', label: 'AMAZON - AMAZON (DIRECT SHIPMENTS)' },
  { value: 'AMAZONGA', label: 'AMAZONGA - Amazon Project' },
  { value: 'AMBIENT', label: 'AMBIENT - Ambient Loads' },
  { value: 'AMERCOLR', label: 'AMERCOLR - American Color' },
  { value: 'AMERDEGA', label: 'AMERDEGA - Ameripipe' },
  { value: 'AMERICOL', label: 'AMERICOL - AMERICOLD' },
  { value: 'AMICK', label: 'AMICK - AMICK FARMS' },
  { value: 'AMKSOLO', label: 'AMKSOLO - Amick Farms Solo' },
  { value: 'AMKTEAM', label: 'AMKTEAM - Amick Farms Team' },
  { value: 'AMRELAY', label: 'AMRELAY - Amazon Relay' },
  { value: 'ANHBSCH', label: 'ANHBSCH - ANHEUSER BUSCH' },
  { value: 'ANHEBUSC', label: 'ANHEBUSC - ANHEUSER BUSCH' },
  { value: 'ANNCMT', label: 'ANNCMT - ANNOUNCMENT CONVERTORS' },
  { value: 'AOD-GM', label: 'AOD-GM - Active Aero- GM' },
  { value: 'APEX', label: 'APEX - APEX LOGISTICS' },
  { value: 'APLHOT', label: 'APLHOT - APL HOT SHIPMENTS' },
  { value: 'APLLOG', label: 'APLLOG - APL LOGISTICS' },
  { value: 'APN', label: 'APN - APNONWEILER' },
  { value: 'APRILE', label: 'APRILE - APRILE' },
  { value: 'APS', label: 'APS - EQUIPMENT' },
  { value: 'ARCBEST', label: 'ARCBEST - ARCBEST LOADS' },
  { value: 'ARMADA', label: 'ARMADA - ARMADA SHIPMENT' },
  { value: 'ARMADHOT', label: 'ARMADHOT - ARMADA - HOT SHIPMENT' },
  { value: 'ARMATEAM', label: 'ARMATEAM - ARMADA - TEAM SHIPMENT' },
  { value: 'ARROW', label: 'ARROW - ARROWSTREAM' },
  { value: 'ATHENS', label: 'ATHENS - ATHENS STONE CASTING' },
  { value: 'AUTOCAR', label: 'AUTOCAR - YARDDOG' },
  { value: 'AVAILABL', label: 'AVAILABL - AVAILABLE LOAD - BOOKED SUB' },
  { value: 'AVERITT', label: 'AVERITT - GLASS' },
  { value: 'AX-EMPTY', label: 'AX-EMPTY - AXIS EMPTY TRAILER MOVES' },
  { value: 'AXIS', label: 'AXIS - AXIS WAREHOUSE MOVES' },
  { value: 'AXIS-NOR', label: 'AXIS-NOR - AXIS NORFOL' },
  { value: 'AZMACHIN', label: 'AZMACHIN - AZ MACHINE' },
  { value: 'BACKUP', label: 'BACKUP - BACK UP TRUCK' },
  { value: 'BEASLEY', label: 'BEASLEY - BEASLEY FREIGHT' },
  { value: 'BERK', label: 'BERK - BERK ENTERPRISES INC' },
  { value: 'BIG4', label: 'BIG4 - AUTO LUBRUCATION' },
  { value: 'BINDER', label: 'BINDER - BINDERHOLZ' },
  { value: 'BITZER', label: 'BITZER - BITZER US, INC' },
  { value: 'BLIND', label: 'BLIND - BLIND SHIPMENT' },
  { value: 'BLINDHOT', label: 'BLINDHOT - BLIND SHIPMENT HOT LOAD' },
  { value: 'BLINDPLT', label: 'BLINDPLT - BLIND SHIPMT/PALLET EXCHANGE' },
  { value: 'BLOCKS', label: 'BLOCKS - DRINK BLOCKS LLC' },
  { value: 'BLUEBIRD', label: 'BLUEBIRD - BLUE BIRD BODY COMPANY' },
  { value: 'BLUEBUFF', label: 'BLUEBUFF - BLUE BUFFALO' },
  { value: 'BLUEWAT', label: 'BLUEWAT - BLUE WATER' },
  { value: 'BNDRAY', label: 'BNDRAY - BNSF DRAY MOVES' },
  { value: 'BNSF', label: 'BNSF - BNSF RAIL RECOVERY' },
  { value: 'BNSF-HOT', label: 'BNSF-HOT - BNSF HOT LOADS' },
  { value: 'BNSF-MW', label: 'BNSF-MW - BNSF CICERO' },
  { value: 'BNSFDBLS', label: 'BNSFDBLS - BNSF DOUBLES' },
  { value: 'BNSFHAZ', label: 'BNSFHAZ - BNSF HAZMAT' },
  { value: 'BNSFLOG', label: 'BNSFLOG - BNSF LOGISTICS' },
  { value: 'BNSFOTR', label: 'BNSFOTR - BNSF RAIL RECOVERY' },
  { value: 'BNSFPUP', label: 'BNSFPUP - PUP TRAILER' },
  { value: 'BODYARMR', label: 'BODYARMR - BODYARMOUR' },
  { value: 'BOONE', label: 'BOONE - BOONE TRAILER MOVES' },
  { value: 'BOONELOG', label: 'BOONELOG - BOONE LOGISTICS' },
  { value: 'BOONEPLN', label: 'BOONEPLN - BOONE PLANNED LOADS' },
  { value: 'BORG', label: 'BORG - BORG WARNER' },
  { value: 'BOURBROS', label: 'BOURBROS - BOURBON BROTHERS' },
  { value: 'BRAND', label: 'BRAND - BRAND ENERGY' },
  { value: 'BRIGHT', label: 'BRIGHT - BRIGHT FARMS' },
  { value: 'BUITONI', label: 'BUITONI - Buitoni Foods Loads' },
  { value: 'BULK', label: 'BULK - BULK HEAD REQUIRED' },
  { value: 'BURTMD', label: 'BURTMD - BURTONSVILLE, MD' },
  { value: 'BYCOWI', label: 'BYCOWI - BYCO' },
  { value: 'CABWORLD', label: 'CABWORLD - CAB WORLDWIDE' },
  { value: 'CAMCO', label: 'CAMCO - CAMCO' },
  { value: 'CAPITAL', label: 'CAPITAL - CAPITAL LIGHTING' },
  { value: 'CARELINE', label: 'CARELINE - CARELINE INC' },
  { value: 'CARG-HOT', label: 'CARG-HOT - CARGILL - HOT SHIPMENT' },
  { value: 'CARGILL', label: 'CARGILL - CARGILL SHIPMENTS' },
  { value: 'CARGTEAM', label: 'CARGTEAM - CARGILL - TEAM SHIPMENT' },
  { value: 'CARLEX', label: 'CARLEX - CARLEX -  AUTO GLASS' },
  { value: 'CASON', label: 'CASON - Cason' },
  { value: 'CATRPLR', label: 'CATRPLR - CATERPILLAR LOADS' },
  { value: 'CBEARS', label: 'CBEARS - CHARLIE BEARS' },
  { value: 'CBTRANS', label: 'CBTRANS - CB TRANSPORTATION' },
  { value: 'CDC', label: 'CDC - CDC' },
  { value: 'CDCTEMP', label: 'CDCTEMP - CDC Temp Control' },
  { value: 'CEJOES', label: 'CEJOES - TRAVELMATION' },
  { value: 'CELEB', label: 'CELEB - CELEB LUXURY' },
  { value: 'CENATL', label: 'CENATL - Central Atlantic' },
  { value: 'CEVA', label: 'CEVA - CEVA LOGISTICS' },
  { value: 'CHAINLNK', label: 'CHAINLNK - CHAINLINK' },
  { value: 'CHASSIS', label: 'CHASSIS - CHASSIS LEG' },
  { value: 'CHELMMA', label: 'CHELMMA - CHELMSFORD, MA' },
  { value: 'CHEMTREA', label: 'CHEMTREA - ChemTreat' },
  { value: 'CHFLOWER', label: 'CHFLOWER - CH Robinson Flowers' },
  { value: 'CHICAGO', label: 'CHICAGO - CHICAGO,IL' },
  { value: 'CHILLED', label: 'CHILLED - Chilled Loads' },
  { value: 'CHIPOTLE', label: 'CHIPOTLE - CHIPOTLE LOADS' },
  { value: 'CHRYSLER', label: 'CHRYSLER - CHRYSLER AUTOMOTIVE' },
  { value: 'COILP+', label: 'COILP+ - COILPLUS' },
  { value: 'COKE', label: 'COKE - Coca-Cola' },
  { value: 'COLONIAL', label: 'COLONIAL - COLONIAL MATERIALS' },
  { value: 'CONAGRA', label: 'CONAGRA - CONAGRA' },
  { value: 'CONWADD', label: 'CONWADD - CONWAY - ADDHOCK' },
  { value: 'CONWAY', label: 'CONWAY - CONWAY/XPO' },
  { value: 'COPPER', label: 'COPPER - POULTRY BROKER' },
  { value: 'CORRIM', label: 'CORRIM - CORRIM DOORS' },
  { value: 'COYOPEAK', label: 'COYOPEAK - COYOTE TRL MOVES' },
  { value: 'COYOTE', label: 'COYOTE - COYOTE LOADS' },
  { value: 'CRANE', label: 'CRANE - CRANE SOLUTIONS' },
  { value: 'CRESCENT', label: 'CRESCENT - CRESCENT KAYAKS' },
  { value: 'CRITICAL', label: 'CRITICAL - FAILURE NOT AN OPTION' },
  { value: 'CRYSFEED', label: 'CRYSFEED - CRYSTAL FEED LOADS' },
  { value: 'CSM-HOT', label: 'CSM-HOT - CSM - HOT SHIPMENT' },
  { value: 'CSM-TEAM', label: 'CSM-TEAM - CSM - TEAM SHIPMENT' },
  { value: 'CSMBAKE', label: 'CSMBAKE - CSM BAKERY SHIPMENT' },
  { value: 'CSX', label: 'CSX - CSX SERVICE RECOVERY' },
  { value: 'CSX-HOT', label: 'CSX-HOT - CSX - HOT LOADS' },
  { value: 'CSX-SHUT', label: 'CSX-SHUT - CSX SHUTTLE MOVES' },
  { value: 'CSXCHASS', label: 'CSXCHASS - CSX W/ CHASSIS' },
  { value: 'CSXD2D', label: 'CSXD2D - CSX Door to Door' },
  { value: 'CSXLOCAL', label: 'CSXLOCAL - CSX LOCAL' },
  { value: 'CSXPUP', label: 'CSXPUP - CSX PUP TRAILER' },
  { value: 'CTVTRANS', label: 'CTVTRANS - CTV TRANSPORTATION' },
  { value: 'DANONE', label: 'DANONE - DANONE' },
  { value: 'DART', label: 'DART - DART CONTAINERS' },
  { value: 'DBLBLIND', label: 'DBLBLIND - DOUBLE BLIND' },
  { value: 'DBLSAIR', label: 'DBLSAIR - UPS DOUBLES AIR LOAD' },
  { value: 'DEEPCHIL', label: 'DEEPCHIL - DEEP CHILL 28 DEGREES' },
  { value: 'DEEPFRZ', label: 'DEEPFRZ - Deep Freeze -20' },
  { value: 'DEEPSOUT', label: 'DEEPSOUT - terminal tractors' },
  { value: 'DELMONTE', label: 'DELMONTE - DEL MONTE LOADS' },
  { value: 'DELTA', label: 'DELTA - DELTA CARGO LOAD' },
  { value: 'DETENT', label: 'DETENT - DETENTION' },
  { value: 'DHL', label: 'DHL - DHL Global Forwarding' },
  { value: 'DILLARD', label: 'DILLARD - DILLARDS GULF COAST' },
  { value: 'DOCKLEV', label: 'DOCKLEV - DOCK LEVELERS' },
  { value: 'DOD', label: 'DOD - DEPT OF DEFENSE' },
  { value: 'DOLE', label: 'DOLE - DOLE' },
  { value: 'DONATION', label: 'DONATION - donation load' },
  { value: 'DOUBLE', label: 'DOUBLE - DOUBLES QUALIFIED' },
  { value: 'DRILCHM', label: 'DRILCHM - DRILLCHEM' },
  { value: 'DRINK', label: 'DRINK - DRINK BLOCKS LLC' },
  { value: 'DROP', label: 'DROP - GEORGIA DROP TRAILER PROJECT' },
  { value: 'DROP-AMB', label: 'DROP-AMB - Nestle SYFK - Ambient' },
  { value: 'DROP-CHL', label: 'DROP-CHL - Nestle SYFK - Chilled' },
  { value: 'DROP-FRZ', label: 'DROP-FRZ - Nestle SYFK - Frozen' },
  { value: 'DROP-TMP', label: 'DROP-TMP - Nestle SYFK- Temp Controlled' },
  { value: 'DROPTRL', label: 'DROPTRL - DROP TRAILER' },
  { value: 'DRPEPPER', label: 'DRPEPPER - DR.PEPPER/7UP LOADS' },
  { value: 'DRY', label: 'DRY - DRY FREIGHT' },
  { value: 'DSI', label: 'DSI - DISTRIBUTION SERVICES INTERN' },
  { value: 'DUKE', label: 'DUKE - ENERGY COMPANY' },
  { value: 'EAPOLFO', label: 'EAPOLFO - Eastern Poultry Foods' },
  { value: 'EASTCST', label: 'EASTCST - PADD1:EAST COAST - FUEL' },
  { value: 'ECHO', label: 'ECHO - ECHO' },
  { value: 'EDI', label: 'EDI - EDI - LOAD UPDATED VIA EDI' },
  { value: 'EDI BLIN', label: 'EDI BLIN - EDI LOAD BLIND SHIPMENT' },
  { value: 'EDI HOT', label: 'EDI HOT - EDI UPDATED HOT LOAD VIA EDI' },
  { value: 'EDI PLTS', label: 'EDI PLTS - EDI LOAD PALLET XCHANG' },
  { value: 'ELKAY', label: 'ELKAY - ELKAY INTERIOR SYSTEMS' },
  { value: 'ELLIOTT', label: 'ELLIOTT - RICHARD A ELLIOTT TRANS' },
  { value: 'EMMAUS', label: 'EMMAUS - EMMAUS FOODS' },
  { value: 'ENNIS-PA', label: 'ENNIS-PA - ENNIS PAINT' },
  { value: 'ENRU', label: 'ENRU - ENRU' },
  { value: 'EPTUSA', label: 'EPTUSA - EPTUSA' },
  { value: 'ESKIMO', label: 'ESKIMO - ESKIMO COLD STORAGE' },
  { value: 'EXP', label: 'EXP - UPS EXPEDITED TEAM' },
  { value: 'EXP-PUP', label: 'EXP-PUP - UPS EXPEDITED TEAM PUP' },
  { value: 'EXPADHOC', label: 'EXPADHOC - EXP ADHOC LOADS' },
  { value: 'EXPAIR', label: 'EXPAIR - UPS EXPEDITED AIR TEAM' },
  { value: 'EXPDBL', label: 'EXPDBL - EXP DOUBLES' },
  { value: 'EXPMEDS', label: 'EXPMEDS - UPS EXPRESS CRIT. PHARM.' },
  { value: 'EXPMILT', label: 'EXPMILT - UPS EXPRESS CRIT. MILITARY' },
  { value: 'EXPRAIL', label: 'EXPRAIL - UPS TEAM RAIL' },
  { value: 'EXS', label: 'EXS - UPS EXPEDITED SOLO' },
  { value: 'EXS-PUP', label: 'EXS-PUP - UPS EXPEDITED SOLO PUP' },
  { value: 'EXSADHOC', label: 'EXSADHOC - EXS ADHOC LOADS' },
  { value: 'EXSAIR', label: 'EXSAIR - UPS EXPEDITED AIR SOLO' },
  { value: 'EXSDBL', label: 'EXSDBL - EXS DOUBLES' },
  { value: 'EXSRAIL', label: 'EXSRAIL - SOLO RAIL' },
  { value: 'FARMER', label: 'FARMER - FARMER FOCUS' },
  { value: 'FCA', label: 'FCA - FIAT CHRYSLER AUTOMOTIVE' },
  { value: 'FCADROP', label: 'FCADROP - FCA DROP AND HOOK' },
  { value: 'FCARAIL', label: 'FCARAIL - FCA RAIL MOVES' },
  { value: 'FDXADHOC', label: 'FDXADHOC - FEDEX FRT ADHOC' },
  { value: 'FDXDBL', label: 'FDXDBL - FEDEX DOUBLES' },
  { value: 'FDXSHIFT', label: 'FDXSHIFT - FEDEX EXPRESS YARD SHIFTERS' },
  { value: 'FECRAIL', label: 'FECRAIL - FLORIDA E.COAST RAILWAY' },
  { value: 'FEDADHOC', label: 'FEDADHOC - FEDEX ADHOC LOADS' },
  { value: 'FEDEX', label: 'FEDEX - FEDEX GROUND LOADS' },
  { value: 'FEDEXATL', label: 'FEDEXATL - ATL FED EXP LOCAL' },
  { value: 'FEDEXCC', label: 'FEDEXCC - FEDEX CUSTOM CRITICAL' },
  { value: 'FEDEXDED', label: 'FEDEXDED - FEDEX FREIGHT DEDICATED' },
  { value: 'FEDEXHUB', label: 'FEDEXHUB - FEDEX HUB MOVES' },
  { value: 'FEDEXPRS', label: 'FEDEXPRS - FEDEX EXPRESS' },
  { value: 'FEDEXTB', label: 'FEDEXTB - FEDEX TRUCKLOAD BROKERAGE' },
  { value: 'FEDFRT', label: 'FEDFRT - FEDEX FREIGHT' },
  { value: 'FEDROP', label: 'FEDROP - FEDEX DROP' },
  { value: 'FEDSTAND', label: 'FEDSTAND - FEDEX STANDBY TEAM' },
  { value: 'FEDXTEMP', label: 'FEDXTEMP - FEDEX TEMP CONTROL' },
  { value: 'FEMA', label: 'FEMA - FEMA' },
  { value: 'FENIX', label: 'FENIX - FENIX PARTS-GAINESVILLE' },
  { value: 'FERRCAND', label: 'FERRCAND - ferrara candy' },
  { value: 'FIELDALE', label: 'FIELDALE - FIELDALE' },
  { value: 'FINLAYS', label: 'FINLAYS - FINLAYS LOADS' },
  { value: 'FITCO', label: 'FITCO - FITCO LOADS' },
  { value: 'FLATBED', label: 'FLATBED - FLATBED LOAD' },
  { value: 'FLDTURF', label: 'FLDTURF - FIELD TURF' },
  { value: 'FLEXFILM', label: 'FLEXFILM - FLEX FILM' },
  { value: 'FLEXNGAT', label: 'FLEXNGAT - FLEX N GATE' },
  { value: 'FLEXPORT', label: 'FLEXPORT - Flexport DBA Deliverr' },
  { value: 'FLOWERWD', label: 'FLOWERWD - FLOWERWOOD' },
  { value: 'FLYFRESH', label: 'FLYFRESH - FLYING FRESH USA' },
  { value: 'FOODLION', label: 'FOODLION - FOOD LION LOADS' },
  { value: 'FOODTRAN', label: 'FOODTRAN - FOODINTRANSIT' },
  { value: 'FORD250', label: 'FORD250 - Ford FSC 1-250 Miles' },
  { value: 'FORD500', label: 'FORD500 - Ford FSC 250-500 Miles' },
  { value: 'FORD501', label: 'FORD501 - Ford FSC 500+ Miles' },
  { value: 'FORDDROP', label: 'FORDDROP - FORD DROP AND HOOK' },
  { value: 'FORMEX', label: 'FORMEX - FORMEX' },
  { value: 'FROZEN', label: 'FROZEN - Frozen Loads' },
  { value: 'FRTHAWK', label: 'FRTHAWK - FreightHawk Loads' },
  { value: 'FSCAUTO', label: 'FSCAUTO - fcsautoparts' },
  { value: 'FUSIONF', label: 'FUSIONF - FUSION FURNITURE, INC' },
  { value: 'GARIMARK', label: 'GARIMARK - GARIMARK' },
  { value: 'GCHDA100', label: 'GCHDA100 - Honda Gulf 0-100 Mi' },
  { value: 'GCHDA300', label: 'GCHDA300 - Honda Gulf 101-300 Mi' },
  { value: 'GCHDA800', label: 'GCHDA800 - Honda Gulf 301-800 Mi' },
  { value: 'GCHDA801', label: 'GCHDA801 - Honda Gulf 800+ Mi' },
  { value: 'GE', label: 'GE - GE APPLIANCES' },
  { value: 'GENMILLS', label: 'GENMILLS - General Mills' },
  { value: 'GERACKS', label: 'GERACKS - Rack Return' },
  { value: 'GERDAU', label: 'GERDAU - GERDAU LOADS' },
  { value: 'GEVEKO', label: 'GEVEKO - Thermoplastic Sheets' },
  { value: 'GLDCREEK', label: 'GLDCREEK - GOLD CREEK FOODS' },
  { value: 'GLOBEX', label: 'GLOBEX - GLOBEX INTERNATIONAL INC' },
  { value: 'GLOBXOTR', label: 'GLOBXOTR - GLOBEX INTERNATIONAL INC OTR' },
  { value: 'GM PARTS', label: 'GM PARTS - GM PARTS' },
  { value: 'GM-CAN', label: 'GM-CAN - GM CANADA' },
  { value: 'GM-CCA', label: 'GM-CCA - GM RXO LOADS' },
  { value: 'GM-DN', label: 'GM-DN - GM DROP NEW' },
  { value: 'GM-DROP', label: 'GM-DROP - GM DROP & HOOK' },
  { value: 'GM-DUN', label: 'GM-DUN - GM EMPTY RACKS' },
  { value: 'GM-HOT', label: 'GM-HOT - GM HOT LOADS' },
  { value: 'GM-MEX', label: 'GM-MEX - MEXICO SHIPMENTS' },
  { value: 'GM-MISC', label: 'GM-MISC - GM MISC - NOT TRACKED IN LMS' },
  { value: 'GM-MXDRP', label: 'GM-MXDRP - GM MEXICO DROP & HOOK LOADS' },
  { value: 'GM-PN', label: 'GM-PN - GM PARTS NEW' },
  { value: 'GM-RACKS', label: 'GM-RACKS - GM RACK LOAD' },
  { value: 'GM-SHARE', label: 'GM-SHARE - GM-SHARE THE SPARE' },
  { value: 'GM-TANK', label: 'GM-TANK - TANKER LOADS' },
  { value: 'GM-WKND', label: 'GM-WKND - GM WEEKEND PRODUCTION SHIPME' },
  { value: 'GMCCA', label: 'GMCCA - XPO/GMCCA' },
  { value: 'GMCCA-MX', label: 'GMCCA-MX - GMCCA/MENLO - MEXICO' },
  { value: 'GMHAZMAT', label: 'GMHAZMAT - CAR PARTS- HAZARDOUS' },
  { value: 'GMPTEMP', label: 'GMPTEMP - GM PARTS TEMP' },
  { value: 'GODAWGS', label: 'GODAWGS - UGA SHIPMENTS' },
  { value: 'GORFOODS', label: 'GORFOODS - GORDON FOOD LOADS' },
  { value: 'GRAP-HOT', label: 'GRAP-HOT - GRAPHIC PKG - HOT SHIPMENT' },
  { value: 'GRAPHIC', label: 'GRAPHIC - GRAPHIC PACKAGING' },
  { value: 'GRAPTEAM', label: 'GRAPTEAM - GRAPHIC TEAM LOAD' },
  { value: 'GREENNC', label: 'GREENNC - GREENSBORO,NC' },
  { value: 'GROVE', label: 'GROVE - GROVE SERVICES' },
  { value: 'GROVEOTR', label: 'GROVEOTR - GROVE SERVICES OTR' },
  { value: 'GTR', label: 'GTR - GATEWAY TRUCK AND REFRIGERAT' },
  { value: 'GULFCST', label: 'GULFCST - PADD3: GULF COAST FUEL REG' },
  { value: 'GULFCSTF', label: 'GULFCSTF - GULF COAST' },
  { value: 'GULFSOUT', label: 'GULFSOUT - GULF SOUTH FOREST PRODUCTS' },
  { value: 'HAMILTON', label: 'HAMILTON - HAMILTON GROUP' },
  { value: 'HAPPTAFL', label: "HAPPTAFL - HAPPY'S HOME CENTERS INC" },
  { value: 'HAZBATT', label: 'HAZBATT - ATL MOTIVE POWER SHIPMENTS' },
  { value: 'HAZMAT', label: 'HAZMAT - HAZARDOUS MATERIALS' },
  { value: 'HECNY', label: 'HECNY - HECNY TRANSPORTATION' },
  { value: 'HERMOR', label: 'HERMOR - HERMISTON, OR' },
  { value: 'HFC-PART', label: 'HFC-PART - HOSPITALITY FREIGHT- PARTIAL' },
  { value: 'HHL', label: 'HHL - HORMEL JOT HHL LOADS' },
  { value: 'HMLTMGRP', label: 'HMLTMGRP - Hamilton Group LLC.' },
  { value: 'HNA-RACK', label: 'HNA-RACK - HONDA RACK LOADS' },
  { value: 'HOGAN', label: 'HOGAN - HOGAN' },
  { value: 'HOMEDEPT', label: 'HOMEDEPT - HOME DEPOT LOADS' },
  { value: 'HONDA', label: 'HONDA - HONDA EXPEDITES' },
  { value: 'HONDA-AD', label: 'HONDA-AD - HONDA ADHOC' },
  { value: 'HONDA100', label: 'HONDA100 - HONDA 0-100 MILES' },
  { value: 'HONDA300', label: 'HONDA300 - HONDA 101-300 MILES' },
  { value: 'HONDA800', label: 'HONDA800 - HONDA 301-800 MILES' },
  { value: 'HONDA801', label: 'HONDA801 - HONDA OVER 801 MILES' },
  { value: 'HONEBEAR', label: 'HONEBEAR - HONE BEAR' },
  { value: 'HORDAIRY', label: 'HORDAIRY - HORIZON DAIRY / UBER FREIGHT' },
  { value: 'HORIZON', label: 'HORIZON - HORIZON AIR FREIGHT' },
  { value: 'HORMEL', label: 'HORMEL - HORMEL FOODS' },
  { value: 'HORMELR', label: 'HORMELR - Hormel Reefer' },
  { value: 'HORMELV', label: 'HORMELV - Hormel Dry' },
  { value: 'HORSHAM', label: 'HORSHAM - HORSHAM,PA' },
  { value: 'HOT', label: 'HOT - HOT LOAD' },
  { value: 'HOT PLTS', label: 'HOT PLTS - HOT LOAD PALLET EXCHANGE' },
  { value: 'HUBER', label: 'HUBER - HUBER' },
  { value: 'HUBFRT', label: 'HUBFRT - HUBGROUP ADHOC FREIGHT' },
  { value: 'HUBGROUP', label: 'HUBGROUP - HUB GROUP' },
  { value: 'HUBHARB', label: 'HUBHARB - HARBOR FREIGHT' },
  { value: 'HVAC', label: 'HVAC - 1 CLEAR CHOICE HVAC' },
  { value: 'ICECREAM', label: 'ICECREAM - Ice Cream -20F' },
  { value: 'IGS', label: 'IGS - INTEGRATEDGLOBAL' },
  { value: 'INDIGO', label: 'INDIGO - indigo energy' },
  { value: 'INGLES', label: 'INGLES - INGLES' },
  { value: 'INTEBEOH', label: 'INTEBEOH - packaging' },
  { value: 'INTERRA', label: 'INTERRA - INTERRA INTERNATIONAL' },
  { value: 'INTERVIS', label: 'INTERVIS - INTERVISION FOODS' },
  { value: 'IRONHRS', label: 'IRONHRS - IRON HORSE AUSTIONS' },
  { value: 'JAXFL', label: 'JAXFL - JACKSONVILLE,FL' },
  { value: 'JBFOOD', label: 'JBFOOD - JB GROUP' },
  { value: 'JBMULTI', label: 'JBMULTI - J&B Multi-Stop' },
  { value: 'JBSTATE', label: 'JBSTATE - J&B State-State' },
  { value: 'JENNI-O', label: 'JENNI-O - JENNI-O / HORMEL' },
  { value: 'JKYLBRWG', label: 'JKYLBRWG - JEKYLL BREWING' },
  { value: 'JOHNDEER', label: 'JOHNDEER - JOHN DEER' },
  { value: 'JOHNLARM', label: 'JOHNLARM - John L Armitage & Co' },
  { value: 'JOYCE', label: 'JOYCE - JOYCE-FARMS' },
  { value: 'KENWORTH', label: 'KENWORTH - Auto Car Kenworth' },
  { value: 'KHAZHOT', label: 'KHAZHOT - KUBOTA HAZMAT HOT' },
  { value: 'KINEXO', label: 'KINEXO - KINEXO' },
  { value: 'KINEXOR', label: 'KINEXOR - KINEXO REEFER' },
  { value: 'KINEXOV', label: 'KINEXOV - KINEXO VAN' },
  { value: 'KINGINT', label: "KINGINT - King's International" },
  { value: 'KIRCH', label: 'KIRCH - KIRCHOFF' },
  { value: 'KIRSCH', label: 'KIRSCH - Kirsch Transportation' },
  { value: 'KOCH', label: 'KOCH - KOCH FOODS' },
  { value: 'KOCHDRAY', label: 'KOCHDRAY - KOCH FOODS EXPORT DRAY' },
  { value: 'KORMAN', label: 'KORMAN - KORMAN' },
  { value: 'KRAFT', label: 'KRAFT - KRAFT FOODS' },
  { value: 'KRAFTDRY', label: 'KRAFTDRY - KRAFT DRY LOADS' },
  { value: 'KT-STRAP', label: 'KT-STRAP - KUBOTA STRAPS REQUIRED' },
  { value: 'KUBDIRE', label: 'KUBDIRE - KUBOTA DIRECT HOT DEL' },
  { value: 'KUBHAZ', label: 'KUBHAZ - KUBOTA HAZMAT' },
  { value: 'KUBHAZHO', label: 'KUBHAZHO - KUBOTA HAZMAT HOT' },
  { value: 'KUBHOT', label: 'KUBHOT - KUBOTA HOT' },
  { value: 'KUBOTA', label: 'KUBOTA - KUBOTA EAST' },
  { value: 'KUBWEST', label: 'KUBWEST - KUBOTA WEST CALI IMPORTS' },
  { value: 'KUBWHOT', label: 'KUBWHOT - KUBOTA WEST HOT' },
  { value: 'KUEHNE', label: 'KUEHNE - KUEHNE + NAGEL' },
  { value: 'LAHDA100', label: 'LAHDA100 - Honda LowAtl 0-100 Mi' },
  { value: 'LAHDA300', label: 'LAHDA300 - Honda LowAtl 101-300 Mi' },
  { value: 'LAHDA800', label: 'LAHDA800 - Honda LowAtl 301-800 Mi' },
  { value: 'LAHDA801', label: 'LAHDA801 - Honda LowAtl 800+ Mi' },
  { value: 'LAKANTO', label: 'LAKANTO - LAKANTO' },
  { value: 'LAKE', label: 'LAKE - LAKE FOODS' },
  { value: 'LAMEX', label: 'LAMEX - LAMEX FOODS' },
  { value: 'LAND', label: "LAND - LAND O' LAKES" },
  { value: 'LANGSTON', label: 'LANGSTON - LANGSTON BAGS' },
  { value: 'LAYOVER', label: 'LAYOVER - LAYOVER' },
  { value: 'LCD', label: 'LCD - LOCAL LCD' },
  { value: 'LCDSHIFT', label: 'LCDSHIFT - LOCAL SHIFTER WORK' },
  { value: 'LCL', label: 'LCL - LOCAL LCL' },
  { value: 'LCLDBL', label: 'LCLDBL - LOCAL DOUBLES REQUIRED' },
  { value: 'LCLPUP', label: 'LCLPUP - LOCAL PUP' },
  { value: 'LIFEWAY', label: 'LIFEWAY - paper products' },
  { value: 'LINEAGE', label: 'LINEAGE - LINEAGE FREIGHT MANAGEMENT L' },
  { value: 'LINECARG', label: 'LINECARG - LINE CARGO IMPORTS (KUBOTA)' },
  { value: 'LOADLCKS', label: 'LOADLCKS - LOAD LOCKS REQ' },
  { value: 'LOCALLCL', label: 'LOCALLCL - LOCAL CL' },
  { value: 'LOUPFORD', label: 'LOUPFORD - LOUP FORD' },
  { value: 'LOUPGM', label: 'LOUPGM - LOUP GM LOADS' },
  { value: 'LOUPHAZ', label: 'LOUPHAZ - LOUP HAZ SHIPMENTS' },
  { value: 'LOUPHOT', label: 'LOUPHOT - LOUP HOT SHIPMENTS' },
  { value: 'LOUPPUP', label: 'LOUPPUP - LOUP PUP TRAILER' },
  { value: 'LOUPRAIL', label: 'LOUPRAIL - LOUP RAIL (UP RAIL)' },
  { value: 'LOUPSVCR', label: 'LOUPSVCR - LOUP SERVICE RECOVERY' },
  { value: 'LOUPTSLA', label: 'LOUPTSLA - LOUP TESLA' },
  { value: 'LOUPWHOL', label: 'LOUPWHOL - LOUP WHOLESALE LOADS' },
  { value: 'LOWATL', label: 'LOWATL - LOWER ATLANTIC' },
  { value: 'LSCS', label: 'LSCS - LANDSTAR SUPPLY CHAIN' },
  { value: 'LTL', label: 'LTL - LTL LOADS' },
  { value: 'MACHINE', label: 'MACHINE - MACHINERY' },
  { value: 'MAERSK', label: 'MAERSK - MAERSK, INC' },
  { value: 'MANWAH', label: 'MANWAH - MANWAH FURNITURE' },
  { value: 'MARINE', label: 'MARINE - MARINE-SPECIALTIES' },
  { value: 'MARUBENI', label: 'MARUBENI - MARUBENI TRANS' },
  { value: 'MAUSERPK', label: 'MAUSERPK - Mauser Packaging Solutions' },
  { value: 'MCCAIN', label: 'MCCAIN - MCCAIN' },
  { value: 'MCGRIFF', label: 'MCGRIFF - MCGRIFF TREADING' },
  { value: 'MCMASTER', label: 'MCMASTER - McMaster-Carr' },
  { value: 'MEC-AIR', label: 'MEC-AIR - MORRISON AIR LOAD' },
  { value: 'MEC-APPL', label: 'MEC-APPL - MORRISON EXPRESS APPLE' },
  { value: 'MEC-BAS', label: 'MEC-BAS - MORRISON - BRIGGS & STRATTON' },
  { value: 'MEC-MAS', label: 'MEC-MAS - MORRISON - MASONITE' },
  { value: 'MEM-HAZ', label: 'MEM-HAZ - MEMPHIS BNSF PROJECT HAZMAT' },
  { value: 'MEM-PROJ', label: 'MEM-PROJ - BNSF MEMPHIS PROJECT' },
  { value: 'MEMPHIS', label: 'MEMPHIS - MEMPHIS PROJECT' },
  { value: 'MEN-TEAM', label: 'MEN-TEAM - MENLO TEAM' },
  { value: 'MENASHA', label: 'MENASHA - MENASHA' },
  { value: 'MENLO', label: 'MENLO - Menlo Worldwide' },
  { value: 'MERITOR', label: 'MERITOR - MERITOR PARTS SHIPMENTS' },
  { value: 'MIDWEST', label: 'MIDWEST - BNSF IL MN' },
  { value: 'MIDWESTF', label: 'MIDWESTF - PADD2: MIDWEST FUEL REGION' },
  { value: 'MILECORP', label: 'MILECORP - MILE CORP TRAILER PROJECT' },
  { value: 'MINCEY', label: 'MINCEY - MINCEY MARBLE LOADS' },
  { value: 'MINN', label: 'MINN - MINNEAPOLIS, MN' },
  { value: 'MISC', label: 'MISC - miscellaneous' },
  { value: 'MISSOULA', label: 'MISSOULA - MISSOULA, MT' },
  { value: 'MNX', label: 'MNX - MNX UPS' },
  { value: 'MONDI', label: 'MONDI - Mondi Group' },
  { value: 'MONTAL', label: 'MONTAL - MONTGOMERY,AL' },
  { value: 'MONTILE', label: 'MONTILE - MONTERREY TILE' },
  { value: 'MORRISON', label: 'MORRISON - MORRISON EXPRESS' },
  { value: 'MORSOLO', label: 'MORSOLO - Morrison Express Corp Solo' },
  { value: 'MORTEAM', label: 'MORTEAM - Morrison Express Corp Team' },
  { value: 'MPS', label: 'MPS - PAPER PRODUCT' },
  { value: 'MRI', label: 'MRI - Manufacturing Resources Intl' },
  { value: 'MURRAY', label: 'MURRAY - MURRAY PLASTICS' },
  { value: 'N.BERGEN', label: 'N.BERGEN - NORTH BERGEN,NJ' },
  { value: 'NACARATO', label: 'NACARATO - truck center' },
  { value: 'NAHDA100', label: "NAHDA100 - Honda Nat'Avg 0-100 Mi" },
  { value: 'NAHDA300', label: "NAHDA300 - Honda Nat'Avg 101-300 Mi" },
  { value: 'NAHDA800', label: "NAHDA800 - Honda Nat'Avg 301-800 Mi" },
  { value: 'NAHDA801', label: "NAHDA801 - Honda Nat'Avg 800+ Mi" },
  { value: 'NAK', label: 'NAK - NAL KILN SERVICES LLC' },
  { value: 'NEW CUST', label: 'NEW CUST - MENASHA' },
  { value: 'NEWCOLD', label: 'NEWCOLD - NEW COLD SHUTTLE PROGRAM' },
  { value: 'NEWCUS', label: 'NEWCUS - NEW CUSTOMER' },
  { value: 'NEWEFOOD', label: 'NEWEFOOD - NEW ENGLAND FOODS' },
  { value: 'NEXTLOG', label: 'NEXTLOG - NEXTLOGISTIX' },
  { value: 'NFI', label: 'NFI - NFI INDUSTRIES' },
  { value: 'NHSTEMP', label: 'NHSTEMP - Nestle NHS Temp Controlled' },
  { value: 'NRI', label: 'NRI - NRI' },
  { value: 'NSHAZMAT', label: 'NSHAZMAT - NS RAIL HAZMAT' },
  { value: 'NSHEAVY', label: 'NSHEAVY - NS LOADS 43000+ LBS' },
  { value: 'NSHOT', label: 'NSHOT - NS RAIL HOT LOAD' },
  { value: 'NSPUP', label: 'NSPUP - NS RAIL PUP LOAD' },
  { value: 'NSRAIL', label: 'NSRAIL - NORFOLK SOUTHERN RECOVERY' },
  { value: 'NUCOR', label: 'NUCOR - NUCOR STEEL KINGMAN' },
  { value: 'OASIS', label: 'OASIS - OASIS CONSULTING SERVICES' },
  { value: 'OLDCAST', label: 'OLDCAST - old castle' },
  { value: 'ORGAMETI', label: 'ORGAMETI - ORGAMETICS LLC' },
  { value: 'OVAINN', label: 'OVAINN - OvaIn novations' },
  { value: 'OVERDIM', label: 'OVERDIM - OVERDIMENSIONAL' },
  { value: 'P-FLOWER', label: 'P-FLOWER - PUBLIX FLOWERS' },
  { value: 'PACBID', label: 'PACBID - Pacella Bid Board' },
  { value: 'PACELLA', label: 'PACELLA - PACELLA-FACING' },
  { value: 'PACKAIR', label: 'PACKAIR - Product Handling Solutions' },
  { value: 'PACRAIL', label: 'PACRAIL - PACEL-RAIL' },
  { value: 'PALLETS', label: 'PALLETS - PALLET EXCHANGE' },
  { value: 'PARMAN', label: 'PARMAN - Parman Energy Group' },
  { value: 'PARSIP', label: 'PARSIP - PARSIPPANY, NJ' },
  { value: 'PEAKRAIL', label: 'PEAKRAIL - PEAK RAIL LOAD' },
  { value: 'PEARSON', label: 'PEARSON - PEARSON CONSTRUCTION' },
  { value: 'PEN-DROP', label: 'PEN-DROP - PENSKE DROP TRAILER' },
  { value: 'PENN', label: 'PENN - penn power group' },
  { value: 'PENSKDBL', label: 'PENSKDBL - PENSKE DBLS' },
  { value: 'PENSKE', label: 'PENSKE - PENSKE EXPEDITE' },
  { value: 'PEPSI', label: 'PEPSI - PEPSI LOGISTICS' },
  { value: 'PEPSIGPI', label: 'PEPSIGPI - PEPSI/GPI' },
  { value: 'PEPSPIZZ', label: "PEPSPIZZ - PEP'S PIZZA CO" },
  { value: 'PEPTEAM', label: 'PEPTEAM - PEPSI/GPI TEAM SERVICE' },
  { value: 'PETFOOD', label: 'PETFOOD - AINSWORTH PET FOOD' },
  { value: 'PFG', label: 'PFG - PERFORMANCE FOOD GROUP' },
  { value: 'PFS2STOP', label: 'PFS2STOP - PFS 2 STOP LOAD RATES' },
  { value: 'PILG-HOT', label: 'PILG-HOT - PILGRIMS - HOT SHIPMENTS' },
  { value: 'PILGCOPK', label: 'PILGCOPK - PILGRIMS CO-PACK' },
  { value: 'PILGRIMS', label: 'PILGRIMS - PILGRIMS LOADS' },
  { value: 'PILGTEAM', label: 'PILGTEAM - PILGRIMS - TEAM SHIPMENTS' },
  { value: 'PLANPAC', label: 'PLANPAC - PLANNED PACELLA RAIL' },
  { value: 'PLP', label: 'PLP - PREMIER LOGISTICS PARTNERS' },
  { value: 'PODS', label: 'PODS - PODS' },
  { value: 'POLAR3PL', label: 'POLAR3PL - POLAR 3PL LOADS' },
  { value: 'POOLPAL', label: 'POOLPAL - My Pool Pal' },
  { value: 'PORTLAND', label: 'PORTLAND - SPOPOR' },
  { value: 'POULTRY', label: 'POULTRY - GOLD CREEK PROCESSING LLC' },
  { value: 'PRIMAL', label: 'PRIMAL - Primal Racing Group' },
  { value: 'PRINTPRO', label: 'PRINTPRO - PRINTPRO' },
  { value: 'PROTRAN', label: 'PROTRAN - PROTRAN AUTO PARTS' },
  { value: 'PUBCLOUT', label: 'PUBCLOUT - PUBLIX CLEAN OUTS' },
  { value: 'PUBLIX', label: 'PUBLIX - PUBLIX EDI LOADS' },
  { value: 'PUBLIXPO', label: 'PUBLIXPO - PUBLIX POWER ONLY' },
  { value: 'PUBLSTOR', label: 'PUBLSTOR - PUBLIX STORE RUNS' },
  { value: 'PUBLSTR2', label: 'PUBLSTR2 - PUBLIX STORE RUNS 2' },
  { value: 'PUBREEF', label: 'PUBREEF - PUBLIX REEFER' },
  { value: 'PUBSHELL', label: 'PUBSHELL - PUBLIX SHELL LOADS' },
  { value: 'PWRONLY', label: 'PWRONLY - POWER ONLY' },
  { value: 'PX-ADHOC', label: 'PX-ADHOC - PX ADHOC LOADS' },
  { value: 'PX-AIR', label: 'PX-AIR - PX-UPS AIR ROUTE' },
  { value: 'PX-DBLS', label: 'PX-DBLS - PX DOUBLES LOAD' },
  { value: 'PX-PUP', label: 'PX-PUP - UPS PX PUP TRAILER LOADS' },
  { value: 'PX-RAIL', label: 'PX-RAIL - PX RAIL' },
  { value: 'PX-UPS', label: 'PX-UPS - UPS PX PROJECT' },
  { value: 'QUAD', label: 'QUAD - QUAD' },
  { value: 'QVC', label: 'QVC - QVC COYOTE LOADS' },
  { value: 'RAHABS', label: 'RAHABS - RAHABS ROPE' },
  { value: 'RAI', label: 'RAI - fabrications' },
  { value: 'RAVEN', label: 'RAVEN - FEC-RAVEN' },
  { value: 'RCSLLOCA', label: 'RCSLLOCA - Freight Forwarder' },
  { value: 'REDMOND', label: 'REDMOND - REDMOND, WA' },
  { value: 'REDVSUGA', label: 'REDVSUGA - RED V FOODS' },
  { value: 'REEFER', label: 'REEFER - REEFER FREIGHT' },
  { value: 'RELAY', label: 'RELAY - PACELLA AMAZON ADHOC' },
  { value: 'REPOWER', label: 'REPOWER - REPOWER LOAD' },
  { value: 'REWORK', label: 'REWORK - REWORK' },
  { value: 'RICCHICK', label: 'RICCHICK - RICH CHICKS' },
  { value: 'RICHELIE', label: 'RICHELIE - RICHELIEU AMERICAN LTD' },
  { value: 'ROADIE', label: 'ROADIE - RoadieXD' },
  { value: 'ROADRUNN', label: 'ROADRUNN - ROADRUNNER' },
  { value: 'ROCKHILL', label: 'ROCKHILL - ROCKHILL FOODS' },
  { value: 'ROCKYMTN', label: 'ROCKYMTN - PADD4: ROCKY MTN - FUEL REG' },
  { value: 'ROYSTON', label: 'ROYSTON - FREIGHT OF ALL KIND' },
  { value: 'RPCFRT', label: 'RPCFRT - REHRIG PACIFIC COMAPNY FRT' },
  { value: 'RUAN', label: 'RUAN - RUAN ADHOC LOADS' },
  { value: 'RUANAUTO', label: 'RUANAUTO - RAUN AUTOMOTIVE' },
  { value: 'RUANCLN', label: 'RUANCLN - RUAN CLEANOUT' },
  { value: 'RUANDED', label: 'RUANDED - Ruan Dedicated' },
  { value: 'RUANPUBL', label: 'RUANPUBL - RUAN PUBLIX LOADS' },
  { value: 'RUANTARG', label: 'RUANTARG - RUAN TARGET LOADS' },
  { value: 'RUANTEMP', label: 'RUANTEMP - RUAN REEFER' },
  { value: 'RUDOLPH', label: 'RUDOLPH - HOT CHRISTMAS EVE SHIPMENTS' },
  { value: 'RUIZ', label: 'RUIZ - RUIZ FOODS' },
  { value: 'RXO', label: 'RXO - RXO EXPEDITE' },
  { value: 'RXO-BT', label: 'RXO-BT - RXO BOXTRUCK' },
  { value: 'RXO-FB', label: 'RXO-FB - RXO FLATBEAD LOADS' },
  { value: 'RXO-HPRO', label: 'RXO-HPRO - RXO HAZMAT PROJECT' },
  { value: 'RXO-MX', label: 'RXO-MX - RXO MEXICO ADHOC' },
  { value: 'RXO-PO', label: 'RXO-PO - RXO POWER ONLY' },
  { value: 'RXO-PROJ', label: 'RXO-PROJ - RXO PROJECT LOADS' },
  { value: 'RXO-RT', label: 'RXO-RT - RXO ROUND TRIP' },
  { value: 'RXO-SV', label: 'RXO-SV - RXO SPRINTER VAN' },
  { value: 'RYDER', label: 'RYDER - Ryder Temp' },
  { value: 'S3PL', label: 'S3PL - Superior 3rd Party Logistics' },
  { value: 'SAILS', label: 'SAILS - SAIL RESTAURANT' },
  { value: 'SAKS', label: 'SAKS - SAKS OFF 5TH' },
  { value: 'SAMSUNG', label: 'SAMSUNG - Samsung-Neovia Logistics' },
  { value: 'SANCO', label: 'SANCO - SANCO EQUIPMENT' },
  { value: 'SANTA', label: 'SANTA - HOT CHRISTMAS SHIPMENTS' },
  { value: 'SCHWANS', label: 'SCHWANS - SCHWANS' },
  { value: 'SCIGAMES', label: 'SCIGAMES - Scientific Games' },
  { value: 'SDI', label: 'SDI - SYFAN DEDICATED LOAD' },
  { value: 'SEATTLE', label: 'SEATTLE - SPOSEA' },
  { value: 'SECAUCUS', label: 'SECAUCUS - SECAUCUS,NJ' },
  { value: 'SELIT', label: 'SELIT - SELIT North America, Inc' },
  { value: 'SENSIENT', label: 'SENSIENT - SENSIENT TECHNOLOGIES CO' },
  { value: 'SFC', label: 'SFC - SCHWANS COMPANY' },
  { value: 'SFL', label: 'SFL - FOUNDATION' },
  { value: 'SHA-CHP', label: 'SHA-CHP - SHASTA - CHEP' },
  { value: 'SHASTA', label: 'SHASTA - SHASTA BEVERAGE' },
  { value: 'SHAW', label: 'SHAW - SHAW FLOORING PRODUCTS' },
  { value: 'SHEALY', label: 'SHEALY - Dealership' },
  { value: 'SHIFTERS', label: 'SHIFTERS - MOVING TRAILERS' },
  { value: 'SIGNODE', label: 'SIGNODE - SIGNODE' },
  { value: 'SLEEPNUM', label: 'SLEEPNUM - SLEEP NUMBER' },
  { value: 'SMITHFLD', label: 'SMITHFLD - SMITHFIELD LOADS' },
  { value: 'SMITLA', label: "SMITLA - SMITTY'S SUPPLY, INC" },
  { value: 'SMUCKERS', label: 'SMUCKERS - SMUCKERS' },
  { value: 'SOUTH', label: 'SOUTH - SOUTHWIRE' },
  { value: 'SPARKY', label: 'SPARKY - SPARKY TRANS' },
  { value: 'SPIRE', label: 'SPIRE - SPIRE COLLECTIVE' },
  { value: 'SPOKANE', label: 'SPOKANE - SPOKANE DELIVERY' },
  { value: 'SPOT', label: 'SPOT - SPOT QUOTE' },
  { value: 'SPTCNATL', label: 'SPTCNATL - Spot Central Atlantic' },
  { value: 'SPTCTATL', label: 'SPTCTATL - Spot Central Atlantic' },
  { value: 'SPTG', label: 'SPTG - SOUTH PORT TRANSPOR GROUP' },
  { value: 'SPTGUCST', label: 'SPTGUCST - SPOT GULF COAST' },
  { value: 'SPTLOATL', label: 'SPTLOATL - Spot - Lower Atlantic' },
  { value: 'SPTMIDW', label: 'SPTMIDW - Spot - Midwest' },
  { value: 'SRSDIST', label: 'SRSDIST - BULDING PRODUCTS' },
  { value: 'STANDBY', label: 'STANDBY - UPS STANDBY' },
  { value: 'STARBCKS', label: 'STARBCKS - Nestle Starbucks' },
  { value: 'STARBKS', label: 'STARBKS - Nestle Starbucks Loads' },
  { value: 'STAWATER', label: 'STAWATER - STAGEWATER' },
  { value: 'STLCELL', label: 'STLCELL - JAIL CELLS' },
  { value: 'STLCELOD', label: 'STLCELOD - JAIL CELLS OVER DIM' },
  { value: 'STODRY', label: 'STODRY - NESTLE STO DRY LOADS' },
  { value: 'STOTEMP', label: 'STOTEMP - NESTLE STO TEMP LOADS' },
  { value: 'STOTRL', label: 'STOTRL - STOUGHTON TRAILER LEASING' },
  { value: 'STPAUL', label: 'STPAUL - SAINT PAUL' },
  { value: 'STRATESU', label: 'STRATESU - Strategic Telecom Supply' },
  { value: 'SUNBELT', label: 'SUNBELT - Equipment and Tools' },
  { value: 'SUPERIOR', label: 'SUPERIOR - SUPERIOR DAIRY' },
  { value: 'SUPERVAL', label: 'SUPERVAL - SuperValue' },
  { value: 'SWIFT', label: 'SWIFT - SWIFT TRAILER  MOVES' },
  { value: 'SYFAN', label: 'SYFAN - SYFAN' },
  { value: 'T-CHASSI', label: 'T-CHASSI - TRAC CHASSIS' },
  { value: 'TALENTI', label: 'TALENTI - TALENTI' },
  { value: 'TARPS', label: 'TARPS - TARPS' },
  { value: 'TBC/FDSI', label: 'TBC/FDSI - TIRE BATTERY COMPANY' },
  { value: 'TEAM', label: 'TEAM - TEAM LOAD' },
  { value: 'TEAMPLTS', label: 'TEAMPLTS - TEAM & PALLETS EXCHANGE' },
  { value: 'TELOS', label: 'TELOS - TELOS LOGISTICS' },
  { value: 'TEMP', label: 'TEMP - Temp Controlled Loads' },
  { value: 'TEMP REC', label: 'TEMP REC - TEMP RECORDER' },
  { value: 'TENDER', label: 'TENDER - TENDERGRASS' },
  { value: 'TERBERG', label: 'TERBERG - TAYLOR BIG RED - TERMINAL TR' },
  { value: 'TEST', label: 'TEST - TEST LOAD' },
  { value: 'TFORCE', label: 'TFORCE - TFORCE' },
  { value: 'TFORCE!!', label: 'TFORCE!! - HOT LOADS - LOADED EACH WAY' },
  { value: 'TFORDROP', label: 'TFORDROP - TFORCE DROP' },
  { value: 'TFORRAIL', label: 'TFORRAIL - TFORCE RAIL' },
  { value: 'THERMO', label: 'THERMO - THERMOFISHER/ PLASTIC PARTS' },
  { value: 'TICO', label: 'TICO - SPOTTER TRUCKS' },
  { value: 'TIER1MEX', label: 'TIER1MEX - TIER 1 MEXICO (RYDER)' },
  { value: 'TIERONE', label: 'TIERONE - RYDER TIER ONE' },
  { value: 'TKW', label: 'TKW - THERMO KING' },
  { value: 'TOFCDBLS', label: 'TOFCDBLS - UPS TOFC DBLS LOADS' },
  { value: 'TOTALQL', label: 'TOTALQL - TOTAL QUALITY LOGISTICS' },
  { value: 'TOWER', label: 'TOWER - TOWER AUTOMOTIVE' },
  { value: 'TOYOTA', label: 'TOYOTA - TOYOTA AUTOMOTOVE GROUP' },
  { value: 'TRAC', label: 'TRAC - TRAC INTERMODAL' },
  { value: 'TRADEPRO', label: 'TRADEPRO - TRADEPRO' },
  { value: 'TRAILER', label: 'TRAILER - TRAILER MOVE OR REPAIR' },
  { value: 'TRANSEXP', label: 'TRANSEXP - TRANS EXPEDITE' },
  { value: 'TRI-STAT', label: 'TRI-STAT - TRI STATE' },
  { value: 'TRLMOVES', label: 'TRLMOVES - TRAILER MOVES' },
  { value: 'TSLINBND', label: 'TSLINBND - TSL-INBOUND FROM CUSTOMER' },
  { value: 'TSLLEASE', label: 'TSLLEASE - TSL-LEASE OUTGOING UNITS' },
  { value: 'TSLRENT', label: 'TSLRENT - TSL-RENTAL OUTGOING UNITS' },
  { value: 'TSLSALES', label: 'TSLSALES - TSL-SALES OUTGOING UNITS' },
  { value: 'TSLSWAP', label: 'TSLSWAP - TSL-SWAP UNITS OUT' },
  { value: 'TURBO', label: 'TURBO - TURBO TRUCK CENTER' },
  { value: 'TWCWINAT', label: 'TWCWINAT - TCW LOGISTICS' },
  { value: 'TYSON', label: 'TYSON - TYSON LOADS' },
  { value: 'TYSONHOT', label: 'TYSONHOT - TYSON - HOT SHIPMENT' },
  { value: 'TYSOTEAM', label: 'TYSOTEAM - TYSON - TEAM SHIPMENT' },
  { value: 'UNA BLIN', label: 'UNA BLIN - UNA BLIND SHIPMENT' },
  { value: 'UNA BLPL', label: 'UNA BLPL - UNA BLIND/PALLETS' },
  { value: 'UNA HOT', label: 'UNA HOT - UNA HOT LOAD' },
  { value: 'UNA PLTS', label: 'UNA PLTS - UNA PALLET EXCHANGE LOAD' },
  { value: 'UNA TARP', label: 'UNA TARP - UNA TARP LOAD' },
  { value: 'UNA TEAM', label: 'UNA TEAM - UNA TEAM LOAD' },
  { value: 'UNA UPS', label: 'UNA UPS - UNA UPS LOAD' },
  { value: 'UNA USPS', label: 'UNA USPS - UNA USPS LOAD' },
  { value: 'UNAUPSAR', label: 'UNAUPSAR - UNA UPS AIR LOAD' },
  { value: 'UNFIGA', label: 'UNFIGA - Groceries' },
  { value: 'UNIGRP', label: 'UNIGRP - UNIGROUP' },
  { value: 'UNIS', label: 'UNIS - UNIS TRANSPORTATION' },
  { value: 'UPFRTHAZ', label: 'UPFRTHAZ - UPS FRT HAZMAT LOADS' },
  { value: 'UPS', label: 'UPS - UPS LOAD' },
  { value: 'UPS AIR', label: 'UPS AIR - UPS AIR LOAD' },
  { value: 'UPS-FTD', label: 'UPS-FTD - UPS FTD LOADS' },
  { value: 'UPS-HOT', label: 'UPS-HOT - HOT UPS SHIPMENT' },
  { value: 'UPSALCON', label: 'UPSALCON - ALCON-UPS LOADS' },
  { value: 'UPSAMZN', label: 'UPSAMZN - UPS AMAZON' },
  { value: 'UPSASTRA', label: 'UPSASTRA - ASTRA ZENECA SHIPMENTS' },
  { value: 'UPSCARGO', label: 'UPSCARGO - UPS AIR CARGO LOADS' },
  { value: 'UPSCARRD', label: 'UPSCARRD - UPS CARRIER DIRECT' },
  { value: 'UPSCEPW', label: 'UPSCEPW - UPS EXP PRATT&WHITNEY' },
  { value: 'UPSCSTBY', label: 'UPSCSTBY - UPS CARRIER DIRECT STANDBY' },
  { value: 'UPSDBLS', label: 'UPSDBLS - UPS DOUBLES' },
  { value: 'UPSDRAY', label: 'UPSDRAY - UPS DRAY MOVES' },
  { value: 'UPSDSHIP', label: 'UPSDSHIP - UPS DIRECT SHIP' },
  { value: 'UPSENT', label: 'UPSENT - UPS ENTERTAINMENT' },
  { value: 'UPSERREC', label: 'UPSERREC - UP SERVICE RECOVERY' },
  { value: 'UPSEXP', label: 'UPSEXP - UPS EXPRESS CRITICAL' },
  { value: 'UPSFFWD', label: 'UPSFFWD - UPS Freight Forwarding' },
  { value: 'UPSFLWR', label: 'UPSFLWR - UPSFLOWER' },
  { value: 'UPSFNEST', label: 'UPSFNEST - UPS Freight Nestle' },
  { value: 'UPSFR-CH', label: 'UPSFR-CH - UPSFREIGHT CHARLOTTE' },
  { value: 'UPSFRT', label: 'UPSFRT - UPS FREIGHT' },
  { value: 'UPSFTNST', label: 'UPSFTNST - UPS Freight Nestle' },
  { value: 'UPSHLTH', label: 'UPSHLTH - UPS HEALTHCARE' },
  { value: 'UPSHR', label: 'UPSHR - UPS - HELLO FRESH' },
  { value: 'UPSINTL', label: 'UPSINTL - UPS INTERNATIONAL LOADS' },
  { value: 'UPSLOCAL', label: 'UPSLOCAL - LOCAL WORK' },
  { value: 'UPSLUPIN', label: 'UPSLUPIN - UPS LUPIN' },
  { value: 'UPSNAAF', label: 'UPSNAAF - N.AMER AIR FRT' },
  { value: 'UPSPDROP', label: 'UPSPDROP - UPS PROFLOWER DROP' },
  { value: 'UPSPTEMP', label: 'UPSPTEMP - UPS PROFLOWER LIVE' },
  { value: 'UPSPUP', label: 'UPSPUP - UPS PUP' },
  { value: 'UPSSCS', label: 'UPSSCS - UPS SUPPLY CHAIN SOLUTIONS' },
  { value: 'UPSSMART', label: 'UPSSMART - UPS SMART LOADS' },
  { value: 'UPSSMDBL', label: 'UPSSMDBL - UPS SMART DOUBLES' },
  { value: 'UPSTOFC', label: 'UPSTOFC - UPS TOFC- CACH LOADS' },
  { value: 'UPSTRAN', label: 'UPSTRAN - UPS TRANSPORTATION LOADS' },
  { value: 'UPSWOCK', label: 'UPSWOCK - UPS WOCKHARDT' },
  { value: 'USCAB', label: 'USCAB - US CABINET DEPOT' },
  { value: 'USP-MASK', label: 'USP-MASK - USPOSTAL MASK' },
  { value: 'USPOSTAL', label: 'USPOSTAL - U.S.POST OFFICE LOAD' },
  { value: 'USPS', label: 'USPS - USPS LOAD' },
  { value: 'USPSFRT', label: 'USPSFRT - USPS FREIGHT AUCTION' },
  { value: 'USPSLBX', label: 'USPSLBX - USPS LCL BOX NO PPW' },
  { value: 'USPSLDT', label: 'USPSLDT - USPS LDT' },
  { value: 'USPSPEAK', label: 'USPSPEAK - USPS PEAK 2023' },
  { value: 'USPSUPDT', label: 'USPSUPDT - USPS UPDATE' },
  { value: 'VANTIX', label: 'VANTIX - VANTIX FOOD' },
  { value: 'VANTIXD', label: 'VANTIXD - VANTIX DRY VAN' },
  { value: 'VANTIXR', label: 'VANTIXR - VANTIX REEFER' },
  { value: 'VASCOR', label: 'VASCOR - VASCOR LIMITED SHIPMENTS' },
  { value: 'VDL', label: 'VDL - EVENT PRODUCTS' },
  { value: 'VDRAY', label: 'VDRAY - VOLVO OCEAN DRAYAGE' },
  { value: 'VENSUN', label: 'VENSUN - VENSUN PHARM. LOADS' },
  { value: 'VENTALFS', label: 'VENTALFS - Ventura AL OB FUEL' },
  { value: 'VENTGAFS', label: 'VENTGAFS - Ventura Fuel OB GA' },
  { value: 'VENTOHFS', label: 'VENTOHFS - Ventura Fuel OB OH' },
  { value: 'VENTPAFS', label: 'VENTPAFS - Ventura OB PA Fuel' },
  { value: 'VENWIFSC', label: 'VENWIFSC - Ventura WI FSC' },
  { value: 'VOLBILL', label: 'VOLBILL - VOLVO OCEAN LOAD FOR BILLING' },
  { value: 'VOLDRAY', label: 'VOLDRAY - VOLVO OCEAN DRAYAGE' },
  { value: 'VOLHAZ', label: 'VOLHAZ - VOLVO HAZMAT' },
  { value: 'VOLKSWAG', label: 'VOLKSWAG - VOLKSWAG' },
  { value: 'VOLVO', label: 'VOLVO - VOLVO' },
  { value: 'VOLVO-BT', label: 'VOLVO-BT - VOLVO BOX TRUCK' },
  { value: 'VOLVO-FB', label: 'VOLVO-FB - VOLVO FLATBED LOAD' },
  { value: 'VOLVO-MX', label: 'VOLVO-MX - VOLVO MEXICO' },
  { value: 'VOLVO-SV', label: 'VOLVO-SV - VOLVO SPRINTER VAN LOADS' },
  { value: 'VOLVOPRO', label: 'VOLVOPRO - VOLVO PROJECT' },
  { value: 'WALDRY', label: 'WALDRY - Walmart Dry' },
  { value: 'WALMEDI', label: 'WALMEDI - wal mart dry van' },
  { value: 'WALTEMP', label: 'WALTEMP - Walmart Temp' },
  { value: 'WAYN-HOT', label: 'WAYN-HOT - WAYNE FARMS - HOT SHIPMENT' },
  { value: 'WAYNE', label: 'WAYNE - Wayne Farms - CH Robinson' },
  { value: 'WAYNTEAM', label: 'WAYNTEAM - WAYNE FARMS - TEAM SHIPMENT' },
  { value: 'WEB HOT', label: 'WEB HOT - WEBSITE MAINTAINENCE HOT' },
  { value: 'WEB PLTS', label: 'WEB PLTS - WEBSITE MAINTAINENCE PLT X' },
  { value: 'WEB TEAM', label: 'WEB TEAM - WEBSITE MAINTAINENCE TEAM' },
  { value: 'WEB-CONT', label: 'WEB-CONT - Website - Contract loads' },
  { value: 'WEB-SPOT', label: 'WEB-SPOT - Website - Spot market loads' },
  { value: 'WEBSITE', label: 'WEBSITE - WEBSITE MAINTAINENCE' },
  { value: 'WELLS', label: 'WELLS - BLUE BUNNY ICE CREAM' },
  { value: 'WESTCST', label: 'WESTCST - PADD5: WEST COAST FUEL REG' },
  { value: 'WHCASTLE', label: 'WHCASTLE - WHITE CASTLE LOADS' },
  { value: 'WILCORP', label: 'WILCORP - WILCORP' },
  { value: 'WILLAM', label: 'WILLAM - WILLIAM R HILL' },
  { value: 'WISLLAB', label: 'WISLLAB - WISCONSIN LIGHTING LAB' },
  { value: 'WITHERS', label: 'WITHERS - WITHERS WORLDWIDE' },
  { value: 'WMRHILL', label: 'WMRHILL - WM R HILL' },
  { value: 'WOODADT1', label: 'WOODADT1 - Woodgrain Dry Van' },
  { value: 'WOODARRO', label: 'WOODARRO - Woodstock/Arrons' },
  { value: 'WOODGRAN', label: 'WOODGRAN - WOODGRAIN' },
  { value: 'WOODGRN', label: 'WOODGRN - WOODGRAIN' },
  { value: 'WORMA', label: 'WORMA - WORCESTER,MA' },
  { value: 'XPOBRK', label: 'XPOBRK - XPO- BROKERAGE' },
  { value: 'XPODOD', label: 'XPODOD - DOD LOADS' },
  { value: 'XPONLM', label: 'XPONLM - XPO/NLM EXPEDITE' },
  { value: 'YRC', label: 'YRC - YRC FREIGHT LOAD' },
  { value: 'YRDSHIFT', label: 'YRDSHIFT - UPS YARD SHIFTER' },
  { value: 'ZION', label: 'ZION - Zion Solutions Group' },
  { value: 'ZTEAM', label: 'ZTEAM - Z TEAM UPS AMAZON LOADS' },
];

export const tumaloReferenceNumberOptions: McleodReferenceNumberOption[] = [
  { description: "Carrier's Reference Number (PRO/Invoice)", code: 'CN' },
  { description: "Consignee's Order Number", code: 'CG' },
  { description: 'Previous Bill of Lading Number', code: 'PU' },
  { description: 'Purchase Order Number', code: 'PO' },
  {
    description: "Shipper's Identifying Number for Shipment (SID)",
    code: 'SI',
  },
  { description: 'Stop Sequence Number', code: 'QN' },
  { description: 'Tariff Supplement Number', code: 'OU' },
  { description: 'Upstream Shipper Contract Number', code: 'UP' },
  { description: 'Vendor Contract Number', code: 'VC' },
];

export const tumaloOrderTypes: ValueLabelOption[] = [
  { value: 'AIR', label: 'AIR FREIGHT SHIPMENT' },
  { value: 'AVAIAHOT', label: 'AVAILABLE TO COVER-HOT LOAD' },
  { value: 'AVAILABL', label: 'AVAILABLE TO COVER' },
  { value: 'AVAILHAZ', label: 'AVAILABLE TO COVER-HAZMAT' },
  { value: 'AVAILOFF', label: 'AVAILABLE TO COVER-OFFERED L' },
  { value: 'AVAILPTL', label: 'AVAILABLE TO COVER - PARTIAL' },
  { value: 'CAUTION', label: "DON'T FORGET ABOUT THIS LOAD" },
  { value: 'CLAIM', label: 'CLAIM PENDING' },
  { value: 'COMMIT', label: 'THESE LOADS ARE ACCEPTED' },
  { value: 'CONTAINE', label: 'CONTAINER FREIGHT' },
  { value: 'COVERED', label: 'CARRIER ASSIGNED' },
  { value: 'DETAPPR', label: 'WAITING ON DETENTION APPROVE' },
  { value: 'FINALCHK', label: 'FINAL APPT CHECK' },
  { value: 'FLAA', label: 'FLORIDA AIR' },
  { value: 'FLAL', label: 'FLORIDA LTL' },
  { value: 'FLAO', label: 'FLORIDA OTHER' },
  { value: 'FLAP', label: 'FLORIDA PARCEL' },
  { value: 'FLAT', label: 'FLORIDA TRUCK' },
  { value: 'FREEZPRO', label: 'WAITING FOR FREEZE PROTECTIO' },
  { value: 'IMDL', label: 'INTERMODAL SHIPMENT' },
  { value: 'INFO', label: 'AWAITING ORDER INFORMATION' },
  { value: 'ISSUE', label: 'LOAD WITH ISSUES' },
  { value: 'LATE', label: 'LOAD IS LATE' },
  { value: 'LTL', label: 'LESS THAN TRUCKLOAD' },
  { value: 'NEEDAPPT', label: 'AN APPOINTMENT IS NEEDED' },
  { value: 'ONHOLD', label: 'ORDER IS ON HOLD' },
  { value: 'POD', label: 'WAITING ON POD' },
  { value: 'RATE', label: 'LOOKING FOR RATE INFORMATION' },
  { value: 'REWORKLD', label: 'TIPPED / SHIFTED LOAD' },
  { value: 'ROLL', label: 'ROLLING BOOKING' },
  { value: 'SPOT', label: 'SPOT MARKET LOADS.' },
  { value: 'SPOTADJ', label: 'WAITING-SPOT PRICING CONF.' },
  { value: 'STORAGE', label: 'DROP TRAILER - STORAGE' },
  { value: 'TONU', label: 'TRUCK ORDERED NOT USED' },
  { value: 'TWIC', label: 'REQUIRES A GOVERNMENT TWIC' },
  { value: 'WAITINFO', label: 'WAITING ON INFORMATION' },
  { value: 'WAREHOUS', label: 'WAREHOUSING FEES' },
  { value: 'WTNASSES', label: 'WAITING ON ASSESSORIAL CHARG' },
];
