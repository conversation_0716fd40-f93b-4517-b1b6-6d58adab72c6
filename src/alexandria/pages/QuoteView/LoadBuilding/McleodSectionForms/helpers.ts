import {
  Tenant,
  fetchCommodityOptions,
  fetchFreightTransportTypeOptions,
  fetchOrderTypes,
  fetchReferenceNumberOptions,
  fetchRevenueCodes,
  syfanCommodityOptions,
  syfanOrderTypes,
  syfanReferenceNumberOptions,
  syfanRevenueCodes,
  syfanTransportTypeOptions,
  tridentCommodityOptions,
  tridentOrderTypes,
  tridentReferenceNumberOptions,
  tridentRevenueCodes,
  tridentTransportTypeOptions,
  tumaloCommodityOptions,
  tumaloOrderTypes,
  tumaloReferenceNumberOptions,
  tumaloRevenueCodes,
  tumaloTransportTypeOptions,
} from 'pages/QuoteView/LoadBuilding/McleodSectionForms/constants';
import {
  McleodCommodityOption,
  McleodReferenceNumberOption,
  ValueLabelOption,
} from 'pages/QuoteView/LoadBuilding/McleodSectionForms/types';
import { IntegrationCore } from 'types/Integration';
import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

/**
 * Determines the tenant from a URL or string by checking if it contains any tenant identifier
 * @param input - The URL or string to check (e.g., "syfn.loadtracking.com:5690")
 * @returns The matching Tenant enum value, or undefined if no match found
 */
export const getTenantFromUrl = (input: string): Undef<Tenant> => {
  const normalizedInput = input.toLowerCase();

  // Check each tenant value to see if it's contained in the input
  for (const tenant of Object.values(Tenant)) {
    if (normalizedInput.includes(tenant)) {
      return tenant as Tenant;
    }
  }

  captureException(
    `Unable to parse tenant enum from URL ${input}, unable to load tenant-specific enums`,
    {
      tenant: input,
    }
  );

  return undefined;
};

export const tenantToTransportTypeOptions: Record<Tenant, ValueLabelOption[]> =
  {
    [Tenant.Trident]: tridentTransportTypeOptions,
    [Tenant.Fetch]: fetchFreightTransportTypeOptions,
    [Tenant.Syfan]: syfanTransportTypeOptions,
    [Tenant.Tumalo]: tumaloTransportTypeOptions,
  };

export const tenantToCommodityOptions: Record<Tenant, McleodCommodityOption[]> =
  {
    [Tenant.Trident]: tridentCommodityOptions,
    [Tenant.Fetch]: fetchCommodityOptions,
    [Tenant.Syfan]: syfanCommodityOptions,
    [Tenant.Tumalo]: tumaloCommodityOptions,
  };

export const tenantToRevenueCodes: Record<Tenant, ValueLabelOption[]> = {
  [Tenant.Trident]: tridentRevenueCodes,
  [Tenant.Fetch]: fetchRevenueCodes,
  [Tenant.Syfan]: syfanRevenueCodes,
  [Tenant.Tumalo]: tumaloRevenueCodes,
};

export const tenantToReferenceNumberOptions: Record<
  Tenant,
  McleodReferenceNumberOption[]
> = {
  [Tenant.Trident]: tridentReferenceNumberOptions,
  [Tenant.Fetch]: fetchReferenceNumberOptions,
  [Tenant.Syfan]: syfanReferenceNumberOptions,
  [Tenant.Tumalo]: tumaloReferenceNumberOptions,
};

export const tenantToOrderTypes: Record<Tenant, ValueLabelOption[]> = {
  [Tenant.Trident]: tridentOrderTypes,
  [Tenant.Fetch]: fetchOrderTypes,
  [Tenant.Syfan]: syfanOrderTypes,
  [Tenant.Tumalo]: tumaloOrderTypes,
};

/**
 * Gets transport type options based on a tenant's URL
 * @param tms - The TMS integration
 * @returns Array of transport type options for the tenant
 */
export const getTransportTypeOptions = (
  tms: Undef<IntegrationCore>
): ValueLabelOption[] => {
  const tenant = getTenantFromUrl(tms?.tenant || '');

  if (!tenant) {
    return [];
  }

  const opts = tenantToTransportTypeOptions[tenant];
  if (!opts || opts.length === 0) {
    captureException(
      `No transport type options configured for Mcleod tenant ${tms?.tenant}, please add them`,
      {
        tenant: tms?.tenant,
        tmsID: tms?.id,
      }
    );
  }

  return opts;
};

/**
 * Gets commodity options based on a tenant's URL
 * @param tms - The TMS integration
 * @returns Array of commodity options for the tenant
 */
export const getCommodityOptions = (
  tms: Undef<IntegrationCore>
): McleodCommodityOption[] => {
  const tenant = getTenantFromUrl(tms?.tenant || '');

  if (!tenant) {
    return [];
  }

  const opts = tenantToCommodityOptions[tenant];
  if (!opts || opts.length === 0) {
    captureException(
      `No commodity options configured for Mcleod tenant ${tms?.tenant}`,
      {
        tenant: tms?.tenant,
        tmsID: tms?.id,
      }
    );
  }

  return opts;
};

/**
 * Gets revenue codes based on a URL or tenant identifier
 * @param tms - The TMS integration
 * @returns Array of revenue codes for the tenant
 */
export const getRevenueCodes = (
  tms: Undef<IntegrationCore>
): ValueLabelOption[] => {
  const tenant = getTenantFromUrl(tms?.tenant || '');

  if (!tenant) {
    return [];
  }

  const opts = tenantToRevenueCodes[tenant];
  if (!opts || opts.length === 0) {
    captureException(
      `No revenue codes configured for Mcleod tenant ${tms?.tenant}, please add them`,
      {
        tenant: tms?.tenant,
        tmsID: tms?.id,
      }
    );
  }

  return opts;
};

/**
 * Gets reference number options based on a tenant's URL
 * @param tms - The TMS integration
 * @returns Array of reference number options for the tenant
 */
export const getReferenceNumberOptions = (
  tms: Undef<IntegrationCore>
): McleodReferenceNumberOption[] => {
  const tenant = getTenantFromUrl(tms?.tenant || '');

  if (!tenant) {
    return [];
  }

  const opts = tenantToReferenceNumberOptions[tenant];
  if (!opts || opts.length === 0) {
    captureException(
      `No reference number options configured for Mcleod tenant ${tms?.tenant}`,
      {
        tenant: tms?.tenant,
        tmsID: tms?.id,
      }
    );
  }

  return opts;
};

export const getOrderTypeOptions = (
  tms: Undef<IntegrationCore>
): ValueLabelOption[] => {
  const tenant = getTenantFromUrl(tms?.tenant || '');

  if (!tenant) {
    return [];
  }

  const opts = tenantToOrderTypes[tenant];
  if (!opts || opts.length === 0) {
    captureException(
      `No order type options configured for Mcleod tenant ${tms?.tenant}`,
      {
        tenant: tms?.tenant,
        tmsID: tms?.id,
      }
    );
  }

  return opts;
};
