import * as React from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { InputValue } from 'components/input/RHFTextInput';
import { ReferenceNumberInput } from 'components/input/ReferenceNumberInput';
import { Grid } from 'components/layout/Grid';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { getCustomers } from 'lib/api/getCustomers';
import { Load, NormalizedLoad, TMSCustomer } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { emailFieldOptions } from 'utils/formValidators';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
  mapCustomerToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { LoadBuildingTextInput } from './UnifiedAljexLoadForm';

type CustomerSectionFormProps = {
  load?: Load;
  formMethods: UseFormReturn<NormalizedLoad>;
  customers: Maybe<TMSCustomer[]>;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  isLoadingCustomers: boolean;
  setIsLoadingCustomers: React.Dispatch<React.SetStateAction<boolean>>;
  originalSuggestionData: NormalizedLoad;
  isCreateMode: boolean;
};

export default function CustomerSectionForm({
  load,
  formMethods,
  customers,
  setCustomers,
  isLoadingCustomers,
  setIsLoadingCustomers,
  isCreateMode,
  originalSuggestionData,
}: CustomerSectionFormProps): React.JSX.Element {
  const {
    control,
    formState: { errors },
    watch,
    setValue,
    getValues,
  } = formMethods;

  const { tmsIntegrations } = useServiceFeatures();
  const { toast } = useToast();
  const tmsID = isCreateMode ? tmsIntegrations?.[0]?.id : load?.tmsID;

  const watchedCustomer = watch('customer');
  const watchedAddress2 = watch('customer.addressLine2');

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    if (!tmsID) {
      return [];
    }

    return customerSearchHandler({
      tmsID: tmsID,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  const handleRefreshCustomers = async () => {
    if (!tmsID) {
      return;
    }

    setIsLoadingCustomers(true);

    const res = await getCustomers(tmsID);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description:
          res.error.message || 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  React.useEffect(() => {
    if (watchedCustomer?.externalTMSID) {
      const selectedCustomer = customers?.find(
        (c) => c.externalTMSID === watchedCustomer?.externalTMSID
      );
      if (!selectedCustomer) {
        return;
      }

      Object.entries(selectedCustomer).forEach(([key, value]) => {
        setValue(`customer.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true,
        });
      });
    }
  }, [watchedCustomer?.externalTMSID]);

  return (
    <>
      {isCreateMode ? (
        <RHFDebounceSelect
          required
          name={`customer.externalTMSID`}
          label='Name'
          control={control}
          errors={errors}
          data={customers}
          isLoading={isLoadingCustomers}
          refreshHandler={handleRefreshCustomers}
          fetchOptions={handleCustomerSearch}
          mapOptions={mapCustomerToAntdOptions}
        />
      ) : (
        // Load.Customer.ExternalTMSID is not parsed in GetLoad, so we can't `injectSelectedObject` into RHFDebounceSelect
        // For now we simply show the name as read-only in edit mode as it's rarely changed after the load is created
        <LoadBuildingTextInput name='customer.name' label='Name' readOnly />
      )}

      <LoadBuildingTextInput
        name='customer.addressLine1'
        label='Address Line 1'
        readOnly
      />

      {!!watchedAddress2 && (
        <LoadBuildingTextInput
          name='customer.addressLine2'
          label='Address Line 2'
          readOnly
        />
      )}

      <Grid cols='4' gap='md' className='w-full m-0'>
        <div className='col-span-3'>
          <LoadBuildingTextInput name='customer.city' label='City' readOnly />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput name='customer.state' label='State' readOnly />
        </div>

        <div className='col-span-3'>
          <LoadBuildingTextInput
            name='customer.zipCode'
            label='ZIP'
            placeholder='12345'
            readOnly
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='customer.country'
            label='Country'
            readOnly
          />
        </div>
      </Grid>

      <LoadBuildingTextInput
        name='customer.contact'
        label='Contact'
        placeholder='Contact person'
        required={false}
      />

      <LoadBuildingTextInput
        name='customer.phone'
        label='Phone'
        placeholder='(*************'
        required={false}
        inputValue={InputValue.PHONE_NUMBER}
      />

      {/* TODO: In Aljex integration, double-check we parse first non-empty email */}
      <LoadBuildingTextInput
        name='customer.email'
        label='Email'
        placeholder='<EMAIL>'
        required={false}
        readOnly
        options={emailFieldOptions}
      />

      <ReferenceNumberInput
        name='customer.refNumber'
        control={control}
        label='Customer Ref #'
        placeholder='Enter customer reference number'
        load={originalSuggestionData || getValues()}
      />
    </>
  );
}
