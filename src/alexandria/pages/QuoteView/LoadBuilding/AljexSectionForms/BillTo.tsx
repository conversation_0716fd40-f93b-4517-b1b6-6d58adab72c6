import { JSX, useEffect, useState } from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { InputValue } from 'components/input/RHFTextInput';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { getCustomers } from 'lib/api/getCustomers';
import { CompanyCoreInfo, NormalizedLoad, TMSCustomer } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
  mapCustomerToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { LoadBuildingTextInput } from './UnifiedAljexLoadForm';

type BillToSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  customers: Maybe<TMSCustomer[]>;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  isLoadingCustomers: boolean;
  setIsLoadingCustomers: React.Dispatch<React.SetStateAction<boolean>>;
  isCreateMode: boolean;
};

export default function BillToSectionForm({
  formMethods,
  customers,
  setCustomers,
  isLoadingCustomers,
  setIsLoadingCustomers,
  isCreateMode,
}: BillToSectionFormProps): JSX.Element {
  const {
    control,
    formState: { errors },
    watch,
    setValue,
  } = formMethods;

  const { tmsIntegrations } = useServiceFeatures();
  const { toast } = useToast();

  const watchedBillTo = watch('billTo');
  const watchedCustomer = watch('customer');
  const watchedAddress2 = watch('billTo.addressLine2');
  const [isManuallyOverridden, setIsManuallyOverridden] = useState(false);

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return customerSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  const handleRefreshCustomers = async () => {
    setIsLoadingCustomers(true);

    const res = await getCustomers(tmsIntegrations[0]?.id);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description:
          res.error.message || 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  // Autopopulate Bill To when customer changes
  useEffect(() => {
    if (watchedCustomer?.externalTMSID) {
      const selectedCustomer = customers?.find(
        (c) => c.externalTMSID === watchedCustomer.externalTMSID
      );

      if (selectedCustomer) {
        Object.entries(selectedCustomer).forEach(([key, value]) => {
          if (key !== 'refNumber') {
            setValue(`billTo.${key}` as FieldPath<NormalizedLoad>, value, {
              shouldDirty: false,
            });
          }
        });
      }
    }
  }, [watchedCustomer?.externalTMSID, customers, setValue]);

  // Handle manual Bill To selection from dropdown
  useEffect(() => {
    if (watchedBillTo?.externalTMSID) {
      const selectedBillTo = customers?.find(
        (c) => c.externalTMSID === watchedBillTo?.externalTMSID
      );
      if (!selectedBillTo) {
        return;
      }

      Object.entries(selectedBillTo).forEach(([key, value]) => {
        if (key !== 'refNumber') {
          setValue(`billTo.${key}` as FieldPath<NormalizedLoad>, value, {
            shouldDirty: true,
          });
        }
      });
    }
  }, [watchedBillTo?.externalTMSID, customers, setValue]);

  // Check if Bill To fields have been manually overridden
  useEffect(() => {
    if (!watchedCustomer?.externalTMSID || !watchedBillTo) {
      setIsManuallyOverridden(false);
      return;
    }

    // If Bill To has its own externalTMSID different from customer's, it's overridden
    if (
      watchedBillTo.externalTMSID &&
      watchedBillTo.externalTMSID !== watchedCustomer.externalTMSID
    ) {
      setIsManuallyOverridden(true);
      return;
    }

    // Check if any Bill To field differs from the corresponding customer field
    const fieldsToCheck: FieldPath<CompanyCoreInfo>[] = ['phone', 'contact'];

    const hasManualChanges = fieldsToCheck.some((field) => {
      const billToValue = watchedBillTo[field as keyof typeof watchedBillTo];
      const customerValue =
        watchedCustomer[field as keyof typeof watchedCustomer];
      return billToValue !== customerValue;
    });

    setIsManuallyOverridden(hasManualChanges);
  }, [
    watchedBillTo?.externalTMSID,
    watchedBillTo?.phone,
    watchedBillTo?.contact,
    watchedCustomer?.externalTMSID,
  ]);

  return (
    <>
      {isCreateMode ? (
        <RHFDebounceSelect
          required
          name={`billTo.externalTMSID`}
          label='Name'
          control={control}
          errors={errors}
          data={customers}
          isLoading={isLoadingCustomers}
          refreshHandler={handleRefreshCustomers}
          fetchOptions={handleCustomerSearch}
          mapOptions={mapCustomerToAntdOptions}
        />
      ) : (
        // Load.BillTo.ExternalTMSID is not parsed in GetLoad, so we can't `injectSelectedObject` into RHFDebounceSelect
        // For now we simply show the name as read-only in edit mode as it's rarely changed after the load is created
        <LoadBuildingTextInput name='billTo.name' label='Name' readOnly />
      )}

      <LoadBuildingTextInput
        name='billTo.addressLine1'
        label='Address Line 1'
        required={false}
        readOnly
      />

      {!!watchedAddress2 && (
        <LoadBuildingTextInput
          name='billTo.addressLine2'
          label='Address Line 2'
          required={false}
          readOnly
        />
      )}

      <div className='grid grid-cols-4 w-full m-0 gap-2'>
        <div className='col-span-3'>
          <LoadBuildingTextInput name='billTo.city' label='City' readOnly />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput name='billTo.state' label='State' readOnly />
        </div>

        <div className='col-span-3'>
          <LoadBuildingTextInput
            name='billTo.zipCode'
            label='ZIP'
            placeholder='12345'
            readOnly
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='billTo.country'
            label='Country'
            readOnly
          />
        </div>
      </div>

      <LoadBuildingTextInput
        name='billTo.contact'
        label='Contact'
        placeholder='Contact person'
        required={false}
      />

      <LoadBuildingTextInput
        name='billTo.phone'
        label='Phone'
        placeholder='(*************'
        required={false}
        inputValue={InputValue.PHONE_NUMBER}
      />

      {isManuallyOverridden && (
        <div className='col-span-full mt-2 p-2 bg-info-50 border border-info-200 rounded-md'>
          <div className='flex items-center gap-2'>
            <span className='text-xs text-info-800'>
              You have manually overridden the Bill To information.
            </span>
            <span className='inline-flex items-center rounded-md bg-info-100 px-2 py-1 text-xs font-medium text-info-800 ring-1 ring-inset ring-info-800/10'>
              Beta
            </span>
          </div>
          <span className='text-xs text-info-600'>
            Manually overwriting Bill To is in beta and Aljex may fallback to
            the selected customer{' '}
            {watchedCustomer?.name ? `(${watchedCustomer.name})` : ''}
          </span>
        </div>
      )}
    </>
  );
}
