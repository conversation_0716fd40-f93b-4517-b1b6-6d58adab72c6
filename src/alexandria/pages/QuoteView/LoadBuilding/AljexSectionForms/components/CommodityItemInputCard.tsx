import { JS<PERSON>, useEffect, useState } from 'react';
import { Controller, FieldPath, UseFormReturn } from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore framer-motion is in the parent dir
import { AnimatePresence, motion } from 'framer-motion';
import { CheckIcon, EditIcon, TrashIcon, XIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { Commodity, NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { cn } from 'utils/shadcn';

const freightClassOptions = [
  { value: '50', label: '50' },
  { value: '55', label: '55' },
  { value: '60', label: '60' },
  { value: '65', label: '65' },
  { value: '70', label: '70' },
  { value: '77.5', label: '77.5' },
  { value: '85', label: '85' },
  { value: '92.5', label: '92.5' },
  { value: '100', label: '100' },
  { value: '110', label: '110' },
  { value: '125', label: '125' },
  { value: '150', label: '150' },
  { value: '175', label: '175' },
  { value: '200', label: '200' },
  { value: '250', label: '250' },
  { value: '300', label: '300' },
  { value: '400', label: '400' },
  { value: '500', label: '500' },
];

type CommodityTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<NormalizedLoad>;
};

const CommodityTextInput = (props: CommodityTextInputProps) => (
  <RHFTextInput labelClassName='text-xs' {...props} />
);

interface CommodityItemInputCardProps {
  formMethods: UseFormReturn<NormalizedLoad>;
  item: Commodity;
  index: number;
  onRemove: (id: number) => void;
  onSave?: () => void;
  isDeletable?: boolean;
}

const ANIMATIONS = {
  card: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  toggle: {
    initial: { opacity: 0, height: 0 },
    animate: { opacity: 1, height: 'auto' },
    exit: { opacity: 0, height: 0 },
    transition: { duration: 0.15 },
  },
  actions: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
    transition: { duration: 0.1 },
  },
};

export function CommodityItemInputCard({
  formMethods,
  item,
  index,
  onRemove,
  onSave,
  isDeletable = true,
}: CommodityItemInputCardProps): JSX.Element {
  const { trigger, watch, setValue, control } = formMethods;
  const [isEditing, setIsEditing] = useState(false);
  const [originalValues, setOriginalValues] = useState<Maybe<Commodity>>(null);
  const watchedItem = watch(`commodities.${index}`);

  const isEmpty =
    !watchedItem.description?.trim() &&
    !watchedItem.quantity &&
    !watchedItem.freightClass;

  useEffect(() => {
    if (isEmpty && !isEditing) {
      setIsEditing(true);
    }
  }, [isEmpty, isEditing]);

  const handlers = {
    edit: () => {
      setOriginalValues({ ...watchedItem });
      setIsEditing(true);
    },
    save: async () => {
      // If commodity is empty, remove it instead of validating
      if (isEmpty) {
        handlers.remove();
        return;
      }

      const isValid = await trigger(`commodities.${index}`);
      if (!isValid) {
        return;
      }
      if (onSave) {
        onSave();
      }
      setIsEditing(false);
      setOriginalValues(null);
    },
    discard: () => {
      if (originalValues) {
        setValue(`commodities.${index}`, originalValues);
        if (onSave) {
          onSave();
        }
      }
      setIsEditing(false);
      setOriginalValues(null);
    },
    remove: () => onRemove(index),
  };

  const ActionButton = ({
    onClick,
    className,
    icon: Icon,
    'aria-label': ariaLabel,
  }: {
    onClick: () => void;
    className: string;
    icon: any;
    'aria-label': string;
  }) => (
    <Button
      onClick={onClick}
      className={cn(
        'p-1 rounded-full transition-colors duration-150',
        className
      )}
      variant='ghost'
      size='xs'
      buttonNamePosthog={null}
      aria-label={ariaLabel}
      title={ariaLabel}
      type='button'
    >
      <Icon className='h-4 w-4' />
    </Button>
  );

  const renderActions = () => (
    <AnimatePresence mode='wait'>
      {isEditing ? (
        <motion.div
          key='editing-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1.5'
        >
          <ActionButton
            onClick={handlers.save}
            className='hover:bg-info-200 text-info-600'
            icon={CheckIcon}
            aria-label='Save changes'
          />
          {!isEmpty && (
            <ActionButton
              onClick={handlers.discard}
              className='hover:bg-error-200 text-error-600'
              icon={XIcon}
              aria-label='Discard changes'
            />
          )}
          {isEmpty && isDeletable && (
            <ActionButton
              onClick={handlers.remove}
              className='hover:bg-error-400 text-error-600'
              icon={TrashIcon}
              aria-label='Remove commodity'
            />
          )}
        </motion.div>
      ) : (
        <motion.div
          key='view-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1.5'
        >
          <ActionButton
            onClick={handlers.edit}
            className='hover:bg-info-300 text-info-600'
            icon={EditIcon}
            aria-label='Edit commodity'
          />
          {isDeletable && (
            <ActionButton
              onClick={handlers.remove}
              className='hover:bg-error-400 text-error-600'
              icon={TrashIcon}
              aria-label='Remove commodity'
            />
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      const target = e.target as HTMLElement;

      if (
        target.closest('.ant-select') ||
        target.closest('.ant-select-dropdown') ||
        target.classList.contains('ant-select-search__field') ||
        target.closest('.ant-select-selection-search')
      ) {
        return;
      }

      if (isEditing) {
        e.preventDefault();
        e.stopPropagation();
        handlers.save();
      }
    }
  };

  const renderEditingForm = () => (
    <motion.div
      key='editing-form'
      {...ANIMATIONS.toggle}
      className='overflow-hidden'
    >
      <Flex justify='between' align='center' className='mb-2'>
        <Flex align='center' gap='md'>
          <span className='text-xs font-semibold text-neutral-600'>
            Commodity {index + 1}
          </span>
        </Flex>

        <div onClick={(e) => e.stopPropagation()}>{renderActions()}</div>
      </Flex>

      <Grid cols='2' gap='sm' className='mx-0 w-full'>
        <div className='col-span-2'>
          <CommodityTextInput
            name={`commodities.${index}.description`}
            label='Description'
          />
        </div>

        <div className='col-span-1'>
          <CommodityTextInput
            name={`commodities.${index}.referenceNumber`}
            label='Reference Number'
          />
        </div>

        <div className='col-span-1'>
          <Label name={`commodities.${index}.freightClass`} className='text-xs'>
            Freight Class
          </Label>
          <Controller
            name={`commodities.${index}.freightClass`}
            control={control}
            render={({ field }) => (
              <AntdSelect
                className='h-9 text-neutral-500'
                placeholder='Choose'
                value={field.value || undefined}
                allowClear
                options={freightClassOptions}
                onChange={field.onChange}
              />
            )}
          />
        </div>

        <div className='col-span-1'>
          <CommodityTextInput
            name={`commodities.${index}.quantity`}
            label='Quantity'
            options={{ valueAsNumber: true, min: 0 }}
            placeholder='0'
            inputType='number'
          />
        </div>

        <div className='col-span-1'>
          <CommodityTextInput
            name={`commodities.${index}.weightTotal`}
            label='Weight (lbs)'
            options={{ valueAsNumber: true, min: 0 }}
            inputType='number'
            placeholder='0'
          />
        </div>

        <div className='col-span-2'>
          <Flex direction='row' gap='sm'>
            <CommodityTextInput
              name={`commodities.${index}.length`}
              label='Length'
              inputType='number'
              options={{ valueAsNumber: true, min: 0 }}
            />
            <CommodityTextInput
              name={`commodities.${index}.width`}
              label='Width'
              inputType='number'
              options={{ valueAsNumber: true }}
            />
            <CommodityTextInput
              name={`commodities.${index}.height`}
              label='Height'
              inputType='number'
              options={{ valueAsNumber: true }}
            />
          </Flex>
        </div>
      </Grid>
    </motion.div>
  );

  const renderViewMode = () => {
    if (!item.description) return null;

    return (
      <motion.div
        key='view-mode'
        {...ANIMATIONS.toggle}
        className='overflow-hidden cursor-pointer'
        onClick={handlers.edit}
        onKeyDown={(e: React.KeyboardEvent) =>
          e.key === 'Enter' && handlers.edit()
        }
        role='button'
        tabIndex={0}
        aria-label='Click to edit commodity'
      >
        <Flex justify='between' align='center' className='min-w-0'>
          <Flex align='center' gap='md' className='flex-1 min-w-0 mr-2'>
            <Typography
              variant='caption'
              className='text-neutral-600 font-medium truncate'
              title={item.description}
              aria-label={item.description}
            >
              {item.description}
            </Typography>
          </Flex>

          {/* Action buttons */}
          <Flex onClick={(e) => e.stopPropagation()} className='flex-shrink-0'>
            {renderActions()}
          </Flex>
        </Flex>

        <Flex align='center' gap='sm' className='text-sm text-neutral-900'>
          <Typography variant='body-xs' className='text-neutral-500'>
            Qty:
          </Typography>
          <Typography variant='body-xs' className='text-neutral-600'>
            {item.quantity || 0}
          </Typography>
          <Typography variant='body-xs' className='text-neutral-500'>
            •
          </Typography>
          <Typography variant='body-xs' className='text-neutral-600'>
            FC: {item.freightClass || '-'}
          </Typography>
          <Typography variant='body-xs' className='text-neutral-500'>
            •
          </Typography>
          <Typography
            variant='body-xs'
            className='font-semibold text-brand-main'
          >
            {item.weightTotal || 0} lbs
          </Typography>
        </Flex>
        {item.referenceNumber && (
          <Typography variant='body-xs' className='text-neutral-500 mt-1'>
            Ref: {item.referenceNumber}
          </Typography>
        )}
      </motion.div>
    );
  };

  return (
    <motion.div
      layout
      transition={{ type: 'spring', duration: 0.2, bounce: 0 }}
      {...ANIMATIONS.card}
      className={cn(
        'overflow-hidden rounded-[4px] border bg-linear-to-br shadow-sm relative transition-all duration-200 p-2.5',
        isEmpty
          ? 'border-neutral-400 bg-linear-to-br from-neutral-50 to-neutral-100/50'
          : 'border-neutral-500 bg-linear-to-br from-neutral-50 to-info-50/50'
      )}
      onKeyDown={handleKeyDown}
    >
      <AnimatePresence mode='wait'>
        {isEditing ? renderEditingForm() : renderViewMode()}
      </AnimatePresence>
    </motion.div>
  );
}
