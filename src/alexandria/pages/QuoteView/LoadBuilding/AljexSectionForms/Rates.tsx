import { JSX } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { Label } from 'components/Label';
import { Divider } from 'components/layout/Divider';
import { Typography } from 'components/typography';
import { NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from './UnifiedAljexLoadForm';

const customerRateTypes = [
  '',
  'Flat Rate',
  // Commented out for potential future use:
  // 'All In',
  // 'Auto Rate',
  // 'CWT',
  // 'Ton',
  // 'Pieces',
  // 'Mileage',
  // 'Hourly',
  // 'Gainshare',
];

const carrierRateTypes = [
  '',
  'Flat Rate',
  // Commented out for potential future use:
  // 'All In',
  // 'Auto Rate',
  // 'CWT',
  // 'Ton',
  // 'Pieces',
  // 'Mileage',
  // 'Hourly',
  // 'Percent',
];

export default function RatesForm({
  formMethods,
  isCreateMode = true,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
  isCreateMode: boolean;
}): JSX.Element {
  const { control, watch } = formMethods;

  // const watchedCustomerTotalCharge = watch('rateData.customerTotalCharge.val');
  const watchedCustomerLineHaulRate = watch('rateData.customerLineHaulRate');
  const watchedCarrierLineHaulRate = watch('rateData.carrierLineHaulRate');
  // const watchedFscPercent = watch('rateData.fscPercent');
  // const watchedFscPerMile = watch('rateData.fscPerMile');

  const hasCustomerRates = (watchedCustomerLineHaulRate ?? 0) > 0;
  const hasCarrierRates = (watchedCarrierLineHaulRate ?? 0) > 0;

  return (
    <div className='grid grid-cols-2 gap-4 mx-0 w-full'>
      {/* Customer Section */}
      <>
        <div className='col-span-2 text-md text-neutral-700 font-semibold mb-2'>
          Customer Rates
        </div>

        <div className='col-span-1'>
          <Label name={'rateData.customerRateType'} required={hasCustomerRates}>
            Rate Type
          </Label>
          <Controller
            name='rateData.customerRateType'
            control={control}
            rules={{
              required: hasCustomerRates
                ? 'Required when customer rates are entered'
                : false,
            }}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-600'
                placeholder='Choose rate type'
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                disabled={true}
                options={customerRateTypes?.map((rateType) => ({
                  value: rateType,
                  label: rateType === '' ? 'Select rate type...' : rateType,
                }))}
              />
            )}
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='rateData.customerRateNumUnits'
            label='# of Hours'
            inputType='number'
            options={{ valueAsNumber: true }}
            required={false}
            placeholder='e.g., hours for hourly rate'
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='rateData.customerLineHaulRate'
            label='Line Haul Rate'
            inputType='number'
            options={{ valueAsNumber: true }}
            required={false}
          />
        </div>

        {!isCreateMode && (
          <>
            <div className='col-span-1'>
              <LoadBuildingTextInput
                name='rateData.customerLineHaulCharge.val'
                label='Total LH'
                inputType='number'
                readOnly={true}
                required={false}
              />
            </div>

            <div className='col-span-1 col-start-2'>
              <LoadBuildingTextInput
                name='rateData.customerTotalCharge.val'
                label='Total Cost'
                inputType='number'
                readOnly={true}
                required={false}
              />
            </div>
          </>
        )}
      </>

      <>
        {/* Carrier Section */}
        <div className='col-span-2 text-md text-neutral-700 font-semibold mb-2 mt-4'>
          Carrier Rates
        </div>

        <div className='col-span-1'>
          <Label name={'rateData.carrierRateType'} required={hasCarrierRates}>
            Rate Type
          </Label>
          <Controller
            name='rateData.carrierRateType'
            control={control}
            rules={{
              required: hasCarrierRates
                ? 'Required when carrier rates are entered'
                : false,
            }}
            render={({ field }) => (
              <AntdSelect
                showSearch
                className='h-9 text-neutral-600'
                placeholder='Choose rate type'
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                disabled={true}
                options={carrierRateTypes?.map((rateType) => ({
                  value: rateType,
                  label: rateType === '' ? 'Select rate type...' : rateType,
                }))}
              />
            )}
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='rateData.carrierRateNumUnits'
            label='# of Hours'
            inputType='number'
            options={{ valueAsNumber: true }}
            required={false}
            placeholder='e.g., hours for hourly rate'
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='rateData.carrierLineHaulRate'
            label='Line Haul Rate'
            inputType='number'
            options={{ valueAsNumber: true }}
            required={false}
          />
        </div>

        <div className='col-span-1'>
          <LoadBuildingTextInput
            name='rateData.carrierMaxRate'
            label='Max Rate'
            inputType='number'
            options={{ valueAsNumber: true }}
            required={false}
          />
        </div>

        {!isCreateMode && (
          <>
            <div className='col-span-1'>
              <LoadBuildingTextInput
                name='rateData.carrierLineHaulCharge.val'
                label='Total LH'
                inputType='number'
                readOnly={true}
                required={false}
              />
            </div>

            <div className='col-span-1'>
              <LoadBuildingTextInput
                name='rateData.carrierTotalCost.val'
                label='Total Cost'
                inputType='number'
                readOnly={true}
                required={false}
              />
            </div>
          </>
        )}
      </>

      {/* FSC Section */}
      <Typography variant='h6' weight='semibold' className='col-span-2'>
        Fuel Surcharge
      </Typography>

      <div className='col-span-1'>
        <LoadBuildingTextInput
          name='rateData.fscPercent'
          label='FSC Percentage'
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
          placeholder='0-100'
        />
      </div>

      <div className='col-span-1'>
        <LoadBuildingTextInput
          name='rateData.fscPerMile'
          label='FSC Per Mile'
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
        />
      </div>

      <Divider size='md' className='col-span-2 m-0' />

      {/* Profit Section */}
      <Typography variant='h6' weight='semibold' className='col-span-2'>
        Profit (Aljex-calculated)
      </Typography>
      <div className='col-span-1'>
        <LoadBuildingTextInput
          name='rateData.netProfitUSD'
          label='Net Profit'
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
          readOnly={true}
        />
      </div>
      <div className='col-span-1'>
        <LoadBuildingTextInput
          name='rateData.profitPercent'
          label='Profit Percent'
          inputType='number'
          options={{ valueAsNumber: true }}
          required={false}
          readOnly={true}
        />
      </div>
    </div>
  );
}
