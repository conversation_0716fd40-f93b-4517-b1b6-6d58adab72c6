import { FieldPath } from 'react-hook-form';

import { NormalizedLoad } from 'types/Load';

export const ALJEX_CHARACTER_LIMITS: Partial<
  Record<FieldPath<NormalizedLoad>, number>
> = {
  'customer.name': 30,
  'customer.addressLine1': 30,
  'customer.city': 20,
  'customer.state': 2,
  'customer.zipCode': 6,
  'customer.contact': 18,
  'customer.phone': 14,
  'customer.email': 60,
  'customer.refNumber': 18,

  'billTo.name': 30,
  'billTo.addressLine1': 30,
  'billTo.city': 20,
  'billTo.state': 2,
  'billTo.zipCode': 6,
  'billTo.contact': 18,
  'billTo.phone': 14,

  'pickup.name': 30,
  'pickup.addressLine1': 30,
  'pickup.city': 15,
  'pickup.state': 2,
  'pickup.zipCode': 6,
  'pickup.contact': 18,
  'pickup.phone': 14,
  'pickup.businessHours': 9,
  'pickup.email': 60,
  'pickup.referenceNumber': 18,

  'consignee.name': 30,
  'consignee.addressLine1': 30,
  'consignee.city': 20,
  'consignee.state': 2,
  'consignee.zipCode': 6,
  'consignee.contact': 18,
  'consignee.phone': 14,
  'consignee.businessHours': 9,
  'consignee.email': 60,
  'consignee.referenceNumber': 18,

  'specifications.commodities': 18,
  'specifications.transportSize': 2,
  'specifications.totalPieces': 5,
  'specifications.totalWeight': 6,
  'specifications.billableWeight': 6,
  // 'specifications.customerMiles': 6,
  // 'specifications.truckMiles': 6,
  // 'specifications.declaredValue': 9,
  'specifications.planningComment': 350,

  'additionalReferences.0.number': 18,
  'additionalReferences.1.number': 18,
  'additionalReferences.2.number': 18,
  'additionalReferences.3.number': 18,
  'additionalReferences.4.number': 18,
  'additionalReferences.5.number': 18,
  'additionalReferences.6.number': 18,

  'commodities.0.description': 40,
  'commodities.1.description': 40,
  'commodities.2.description': 40,
  'commodities.3.description': 40,
  'commodities.4.description': 40,
  'commodities.0.referenceNumber': 15,
  'commodities.1.referenceNumber': 15,
  'commodities.2.referenceNumber': 15,
  'commodities.3.referenceNumber': 15,
  'commodities.4.referenceNumber': 15,
} as const;

export type AljexFieldPath = keyof typeof ALJEX_CHARACTER_LIMITS;

export function getAljexCharacterLimit(fieldPath: string): number | undefined {
  return ALJEX_CHARACTER_LIMITS[fieldPath as AljexFieldPath];
}
