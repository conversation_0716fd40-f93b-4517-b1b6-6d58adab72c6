import { UseFormReturn } from 'react-hook-form';

import { InputValue, RHFTextInput } from 'components/input/RHFTextInput';
import { LoadDateTimeInput } from 'pages/LoadView/LoadInformation/Components';
import { NormalizedLoad } from 'types/Load';
import { datetimeFieldOptions, emailFieldOptions } from 'utils/formValidators';

interface CarrierSectionFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
}

export default function CarrierSectionForm({
  formMethods,
}: CarrierSectionFormProps) {
  const currentLoad = formMethods.watch();

  return (
    <>
      <RHFTextInput name='carrier.name' label='Name' />
      <RHFTextInput name='mode' label='Mode' />
      <RHFTextInput name='carrier.equipmentName' label='Equipment Name' />

      <RHFTextInput name='carrier.dotNumber' label='DOT #' />
      <RHFTextInput name='carrier.mcNumber' label='MC #' />
      <RHFTextInput name='carrier.sealNumber' label='Seal #' />
      <RHFTextInput name='carrier.scac' label='SCAC' />
      <RHFTextInput
        name='carrier.phone'
        label='Phone'
        placeholder='(*************'
        inputValue={InputValue.PHONE_NUMBER}
      />
      <RHFTextInput
        name='carrier.email'
        label='Email'
        placeholder='<EMAIL>'
        options={emailFieldOptions}
      />
      <RHFTextInput name='carrier.dispatcher' label='Dispatcher' />

      <RHFTextInput
        name='carrier.rateConfirmationSent'
        label='Rate Confirmation Sent'
      />
      <RHFTextInput name='carrier.firstDriverName' label='First Driver Name' />
      <RHFTextInput
        name='carrier.firstDriverPhone'
        label='First Driver Phone'
        inputValue={InputValue.PHONE_NUMBER}
      />
      <RHFTextInput
        name='carrier.secondDriverName'
        label='Second Driver Name'
      />
      <RHFTextInput
        name='carrier.secondDriverPhone'
        label='Second Driver Phone'
        inputValue={InputValue.PHONE_NUMBER}
      />

      <RHFTextInput name='carrier.dispatchCity' label='Dispatch City' />
      <RHFTextInput name='carrier.dispatchState' label='Dispatch State' />
      <RHFTextInput name='carrier.truckNumber' label='Truck #' />
      <RHFTextInput name='carrier.trailerNumber' label='Trailer #' />

      <RHFTextInput name='carrier.notes' label='Notes' />

      <LoadDateTimeInput
        name='carrier.confirmationSentTime'
        label='Confirmation Sent Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.confirmationReceivedTime'
        label='Confirmation Received Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.dispatchedTime'
        label='Dispatched Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.expectedPickupTime'
        label='Expected Pickup Time'
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.pickupStart'
        label='Pickup Start Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.pickupEnd'
        label='Pickup End Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.expectedDeliveryTime'
        label='Expected Delivery Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.deliveryStart'
        label='Delivery Start Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <LoadDateTimeInput
        name='carrier.deliveryEnd'
        label='Delivery End Time'
        options={datetimeFieldOptions}
        load={currentLoad}
      />
      <RHFTextInput name='carrier.signedBy' label='Signed By' />
    </>
  );
}
