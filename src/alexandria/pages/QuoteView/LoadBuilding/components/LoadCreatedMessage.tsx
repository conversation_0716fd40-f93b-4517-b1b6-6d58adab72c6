import { useEffect, useRef, useState } from 'react';

import { CheckIcon } from 'lucide-react';

import { Button } from 'components/Button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import CopyIcon from 'icons/Copy';
import { Undef } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { copyToClipboard } from 'utils/copyToClipboard';
import { cn } from 'utils/shadcn';

export interface LoadCreatedMessageProps {
  builtLoadId: Undef<string>;
}

export const LoadCreatedMessage = ({
  builtLoadId,
}: LoadCreatedMessageProps) => {
  const [isCopied, setIsCopied] = useState(false);
  const loadCreatedRef = useRef<HTMLDivElement>(null);

  const handleCopyLoadId = async (content: string) => {
    if (content) {
      try {
        await copyToClipboard(content, {
          showToast: false,
        });
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy load ID:', err);
      }
    }
  };

  // Auto-scroll to load created message when load is built
  useEffect(() => {
    if (builtLoadId && loadCreatedRef.current) {
      setTimeout(() => {
        loadCreatedRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }, 100); // Small delay to ensure DOM is updated
    }
  }, [builtLoadId]);

  return (
    <TooltipProvider>
      <Flex direction='col' justify='center' className='w-full'>
        {builtLoadId && (
          <>
            <div
              ref={loadCreatedRef}
              className='whitespace-pre-wrap my-3 rounded py-3  px-4 bg-success-200 w-full '
            >
              <Typography className='mb-1 ' weight='bold'>
                Load Created 🎉
              </Typography>
              <Flex direction='row' align='center' gap='xs' className='mb-2'>
                <Typography variant='body' weight='bold' className=''>
                  Load ID #:{' '}
                </Typography>
                <Typography variant='body' className=''>
                  {builtLoadId}
                </Typography>
                <Button
                  onClick={() => handleCopyLoadId(builtLoadId)}
                  variant='ghost'
                  type='button'
                  buttonNamePosthog={ButtonNamePosthog.LoadBuildingCopyLoadID}
                  className={cn(
                    'p-1 transition-colors border-none',
                    isCopied ? 'cursor-default' : 'cursor-pointer'
                  )}
                  title='Copy Load ID'
                >
                  {isCopied ? (
                    <Tooltip open={true}>
                      <TooltipTrigger asChild>
                        <CheckIcon className='h-4 w-4 ' />
                      </TooltipTrigger>
                      <TooltipContent>
                        {' '}
                        <Typography variant='body-xs'>Copied!</Typography>
                      </TooltipContent>
                    </Tooltip>
                  ) : (
                    <CopyIcon className='h-4 w-4 text-neutral-500' />
                  )}
                </Button>
              </Flex>
            </div>
          </>
        )}
      </Flex>
    </TooltipProvider>
  );
};
