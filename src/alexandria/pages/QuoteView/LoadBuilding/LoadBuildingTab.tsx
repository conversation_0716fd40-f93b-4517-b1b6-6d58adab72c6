import { useContext, useEffect, useState } from 'react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import { SidebarStateContext } from 'contexts/sidebarStateContext';
import useLogPostHogPageView from 'hooks/useLogPostHogPageView';
import { useServiceFeatures } from 'hooks/useServiceContext';
import McleodEnterpriseLoadBuildingForm from 'pages/QuoteView/LoadBuilding/McleodLoadBuildingForm';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import Pageview from 'types/enums/Pageview';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import captureException from 'utils/captureException';

import UnifiedAljexLoadForm from './AljexSectionForms/UnifiedAljexLoadForm';
import UnifiedRevenovaLoadForm from './RevenovaSectionForms/UnifiedRevenovaLoadForm';
import UnifiedStarkLoadForm from './StarkSectionForms/UnifiedStarkLoadForm';
import TaiLoadBuildingForm from './TaiLoadBuildingForm';
import TurvoLoadForm from './TurvoLoadForm';

export default function LoadBuildingTab() {
  const serviceFeatures = useServiceFeatures();
  const serviceID = serviceFeatures.serviceID;
  const tmsIntegrations = serviceFeatures.tmsIntegrations;
  const [isMcleodEnterpriseTMS, setIsMcleodEnterpriseTMS] =
    useState<Maybe<boolean>>(null);
  const [isTurvoTMS, setIsTurvoTMS] = useState<Maybe<boolean>>(null);
  const [isTaiTMS, setIsTaiTMS] = useState<Maybe<boolean>>(null);
  const [isRevenovaTMS, setIsRevenovaTMS] = useState<Maybe<boolean>>(null);
  const [isAljexTMS, setIsAljexTMS] = useState<Maybe<boolean>>(null);
  const [isStarkTMS, setIsStarkTMS] = useState<Maybe<boolean>>(null);

  useLogPostHogPageView(Pageview.LoadBuilding, {
    form_type: (() => {
      switch (true) {
        case isMcleodEnterpriseTMS:
          return TMS.Mcleod;
        case isTurvoTMS:
          return TMS.Turvo;
        case isTaiTMS:
          return TMS.Tai;
        case isRevenovaTMS:
          return TMS.Revenova;
        case isAljexTMS:
          return TMS.Aljex;
        case isStarkTMS:
          return TMS.Stark;
        default:
          return null;
      }
    })(),
    tms_integrations_count: tmsIntegrations?.length,
    service_id: serviceID,
  });

  const {
    currentState: {
      clickedSuggestion,
      curSuggestionList,
      goToSuggestionInCarousel,
    },
    setCurrentState,
  } = useContext(SidebarStateContext);

  useEffect(() => {
    const firstLoadBuildingSuggestion = curSuggestionList.find(
      (s) => s.pipeline === SuggestionPipelines.LoadBuilding
    );

    // Update clickedSuggestion whenever curSuggestionList changes and there's a LoadBuilding suggestion
    if (
      firstLoadBuildingSuggestion &&
      clickedSuggestion !== firstLoadBuildingSuggestion
    ) {
      setCurrentState((prevState) => ({
        ...prevState,
        clickedSuggestion: firstLoadBuildingSuggestion,
      }));
      goToSuggestionInCarousel({
        suggestionID: firstLoadBuildingSuggestion?.id,
      });
    }
  }, [curSuggestionList, goToSuggestionInCarousel]);

  useEffect(() => {
    if (tmsIntegrations?.length > 0) {
      // TODO: add support for multiple TMS integrations
      if (tmsIntegrations?.length > 1 && isProd()) {
        captureException(
          new Error(
            'Service with multiple TMS integrations trying to build load'
          ),
          { serviceID: serviceID, tmsIntegrations: tmsIntegrations }
        );
      }

      switch (tmsIntegrations[0].name) {
        case TMS.McleodEnterprise || TMS.Mcleod:
          setIsMcleodEnterpriseTMS(true);
          break;
        case TMS.Turvo:
          setIsTurvoTMS(true);
          break;
        case TMS.Tai:
          setIsTaiTMS(true);
          break;
        case TMS.Revenova:
          setIsRevenovaTMS(true);
          break;
        case TMS.Aljex:
          setIsAljexTMS(true);
          break;
        case TMS.Stark:
          setIsStarkTMS(true);
          break;
        default:
          break;
      }
    }
  }, [tmsIntegrations]);

  switch (true) {
    case isMcleodEnterpriseTMS:
      return <McleodEnterpriseLoadBuildingForm />;
    case isTurvoTMS:
      return <TurvoLoadForm isCreateMode={true} />;
    case isTaiTMS:
      return <TaiLoadBuildingForm />;
    case isRevenovaTMS:
      return <UnifiedRevenovaLoadForm isCreateMode={true} />;
    case isAljexTMS:
      return <UnifiedAljexLoadForm isCreateMode={true} />;
    case isStarkTMS:
      return <UnifiedStarkLoadForm isCreateMode={true} />;
    default:
      return null;
  }
}
