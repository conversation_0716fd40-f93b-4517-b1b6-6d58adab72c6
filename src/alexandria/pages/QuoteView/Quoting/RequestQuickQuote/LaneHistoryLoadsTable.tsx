import { useMemo, useState } from 'react';

import { ChevronDown, ChevronUp, ChevronsUpDown } from 'lucide-react';

import { Button } from 'components/Button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from 'components/Table';
import { LaneHistoryLoad } from 'lib/api/getLaneHistory';
import { Maybe, Undef } from 'types/UtilityTypes';
import { formatCurrency } from 'utils/formatCurrency';
import { getLoadURLInTMS } from 'utils/getLoadURLInTMS';

const ITEMS_PER_PAGE = 5;
const DEFAULT_SORT_KEY: SortKey = 'totalCost';
const DEFAULT_SORT_DIRECTION: SortDirection = 'desc';

type SortDirection = 'asc' | 'desc';
type SortKey = 'freightTrackingID' | 'totalCost' | 'distance' | 'costPerMile';

interface SortState {
  key: SortKey;
  direction: Maybe<SortDirection>;
}

interface LaneHistoryLoadsTableProps {
  loads: LaneHistoryLoad[];
  source: string;
  tenant: Undef<string>;
}

interface LoadWithCalculatedFields extends LaneHistoryLoad {
  costPerMile: number;
}

// Utility functions
const formatPerMile = (value: number): string => {
  return value ? formatCurrency(value, 'USD', 2) : '—';
};
const formatMileDistance = (value: number): string => {
  return value ? `${value} mi` : '—';
};

const calculateCostPerMile = (load: LaneHistoryLoad): number => {
  return load.distance > 0 ? load.totalCost / load.distance : 0;
};

const sortLoads = (
  loads: LoadWithCalculatedFields[],
  sortState: SortState
): LoadWithCalculatedFields[] => {
  if (!sortState.direction) return loads;

  return [...loads].sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;

    switch (sortState.key) {
      case 'freightTrackingID':
        aValue = a.freightTrackingID;
        bValue = b.freightTrackingID;
        break;
      case 'totalCost':
        aValue = a.totalCost;
        bValue = b.totalCost;
        break;
      case 'distance':
        aValue = a.distance;
        bValue = b.distance;
        break;
      case 'costPerMile':
        aValue = a.costPerMile;
        bValue = b.costPerMile;
        break;
      default:
        return 0;
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortState.direction === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortState.direction === 'asc' ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });
};

const getSortIcon = (key: SortKey, sortState: SortState) => {
  if (sortState.key !== key) {
    return null;
  }

  if (sortState.direction === 'asc') {
    return <ChevronUp className='h-3 w-3' />;
  }

  if (sortState.direction === 'desc') {
    return <ChevronDown className='h-3 w-3' />;
  }

  return null;
};

const getSortIconOpacity = (key: SortKey, sortState: SortState): string => {
  return sortState.key === key && sortState.direction
    ? 'opacity-100'
    : 'opacity-0 group-hover:opacity-100';
};

const getNextSortDirection = (
  currentKey: SortKey,
  currentDirection: Maybe<SortDirection>,
  newKey: SortKey
): Maybe<SortDirection> => {
  if (currentKey !== newKey) {
    return 'asc';
  }

  // Cycle through: asc -> desc -> null -> asc
  if (currentDirection === 'asc') return 'desc';
  if (currentDirection === 'desc') return null;
  return 'asc';
};

const generatePageNumbers = (
  currentPage: number,
  totalPages: number
): number[] => {
  const maxVisiblePages = 5;

  if (totalPages <= maxVisiblePages) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  if (currentPage <= 3) {
    return Array.from({ length: maxVisiblePages }, (_, i) => i + 1);
  }

  if (currentPage >= totalPages - 2) {
    return Array.from(
      { length: maxVisiblePages },
      (_, i) => totalPages - 4 + i
    );
  }

  return Array.from({ length: maxVisiblePages }, (_, i) => currentPage - 2 + i);
};

const SortableTableHeader = ({
  label,
  sortKey,
  sortState,
  onSort,
}: {
  label: string;
  sortKey: SortKey;
  sortState: SortState;
  onSort: (key: SortKey) => void;
}) => (
  <TableHead
    className='cursor-pointer bg-brand-500/30 group whitespace-nowrap px-1 text-xs first:pl-3'
    onClick={() => onSort(sortKey)}
  >
    <div className='flex justify-between items-center'>
      {label}
      <span className={getSortIconOpacity(sortKey, sortState)}>
        {sortState.key === sortKey && sortState.direction ? (
          getSortIcon(sortKey, sortState)
        ) : (
          <ChevronsUpDown className='h-3 w-3' />
        )}
      </span>
    </div>
  </TableHead>
);

const Pagination = ({
  currentPage,
  totalPages,
  onPageChange,
  startIndex,
  endIndex,
  totalItems,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  startIndex: number;
  endIndex: number;
  totalItems: number;
}) => {
  if (totalPages <= 1) return null;

  const pageNumbers = generatePageNumbers(currentPage, totalPages);

  return (
    <div className='flex flex-col items-center space-y-2'>
      <div className='text-xs text-muted-foreground text-center'>
        {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems}
      </div>
      <div className='flex items-center justify-center gap-2 w-full'>
        <Button
          variant='outline'
          size='sm'
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          buttonNamePosthog='lane_history_pagination_previous'
          className='h-6 px-2 text-xs'
          type='button'
        >
          Prev
        </Button>

        <div className='flex items-center gap-1.5'>
          {pageNumbers.map((pageNum) => (
            <Button
              key={pageNum}
              variant={currentPage === pageNum ? 'default' : 'outline'}
              size='sm'
              onClick={() => onPageChange(pageNum)}
              className='w-6 h-6 p-0 text-xs'
              buttonNamePosthog='lane_history_pagination_page'
              type='button'
            >
              {pageNum}
            </Button>
          ))}
        </div>

        <Button
          variant='outline'
          size='sm'
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          buttonNamePosthog='lane_history_pagination_next'
          className='h-6 px-2 text-xs'
          type='button'
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export const LaneHistoryLoadsTable = ({
  loads,
  source,
  tenant,
}: LaneHistoryLoadsTableProps) => {
  const [sortState, setSortState] = useState<SortState>({
    key: DEFAULT_SORT_KEY,
    direction: DEFAULT_SORT_DIRECTION,
  });
  const [currentPage, setCurrentPage] = useState(1);

  if (!loads || loads.length === 0) {
    return null;
  }

  const loadsWithCalculatedFields = useMemo(
    () =>
      loads.map((load) => ({
        ...load,
        costPerMile: calculateCostPerMile(load),
      })),
    [loads]
  );

  const sortedLoads = useMemo(
    () => sortLoads(loadsWithCalculatedFields, sortState),
    [loadsWithCalculatedFields, sortState]
  );

  const totalPages = Math.ceil(sortedLoads.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedLoads = sortedLoads.slice(startIndex, endIndex);

  const handleSort = (key: SortKey) => {
    setSortState((prev) => ({
      key,
      direction: getNextSortDirection(prev.key, prev.direction, key),
    }));
    setCurrentPage(1); // Reset to first page when sorting
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return (
    <div className='mt-4 mb-4 space-y-4'>
      <Table>
        <TableHeader className='text-xs'>
          <TableRow>
            <SortableTableHeader
              label='Load ID'
              sortKey='freightTrackingID'
              sortState={sortState}
              onSort={handleSort}
            />
            <SortableTableHeader
              label='Cost'
              sortKey='totalCost'
              sortState={sortState}
              onSort={handleSort}
            />
            <SortableTableHeader
              label='Miles'
              sortKey='distance'
              sortState={sortState}
              onSort={handleSort}
            />
            <SortableTableHeader
              label='Rate/mi'
              sortKey='costPerMile'
              sortState={sortState}
              onSort={handleSort}
            />
          </TableRow>
        </TableHeader>

        <TableBody className='text-xs'>
          {paginatedLoads.map((load, index) => {
            const loadUrl = getLoadURLInTMS(load.externalTMSID, source, tenant);
            const isEvenRow = index % 2 === 0;

            return (
              <TableRow
                key={load.freightTrackingID}
                className={`${isEvenRow ? 'bg-neutral-100' : 'bg-neutral-50'} font-medium`}
              >
                <TableCell className='px-1 whitespace-nowrap text-xs pl-3'>
                  {loadUrl ? (
                    <a
                      href={loadUrl}
                      target='_blank'
                      rel='noopener noreferrer'
                      className='underline'
                    >
                      {load.freightTrackingID}
                    </a>
                  ) : (
                    load.freightTrackingID
                  )}
                </TableCell>
                <TableCell className='px-1 whitespace-nowrap text-xs text-success-700'>
                  {formatCurrency(load.totalCost, 'USD')}
                </TableCell>
                <TableCell className='px-1 whitespace-nowrap text-xs'>
                  {formatMileDistance(load.distance)}
                </TableCell>
                <TableCell className='px-1 whitespace-nowrap text-xs text-success-700'>
                  {formatPerMile(load.costPerMile)}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        startIndex={startIndex}
        endIndex={endIndex}
        totalItems={sortedLoads.length}
      />
    </div>
  );
};
