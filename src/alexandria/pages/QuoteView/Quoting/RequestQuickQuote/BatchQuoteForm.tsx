import {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FormProvider,
  useFieldArray,
  useForm,
  useWatch,
} from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore lodash is in the parent dir
import _ from 'lodash';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CopyIcon,
  PlusIcon,
  SparklesIcon,
  Truck,
} from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore mustache is in the parent dir
import Mustache from 'mustache';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { Textarea } from 'components/Textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import {
  DrumkitPlatform,
  SidebarStateContext,
  isEmailPlatform,
} from 'contexts/sidebarStateContext';
import { isCustomerSupportedTMS, useCustomers } from 'hooks/useCustomers';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { getBatchQuote } from 'lib/api/getBatchQuote';
import { LaneHistoryResponse, getLaneHistory } from 'lib/api/getLaneHistory';
import { LaneHistoryFromServiceResponse } from 'lib/api/getLaneHistoryFromService';
import { QuickQuoteResponse } from 'lib/api/getQuickQuote';
import { updateQuoteRequestSuggestion } from 'lib/api/updateQuoteRequestSuggestion';
import { createMailClientInstance } from 'lib/mailclient/interface';
import {
  EMPTY_STOP,
  QuoteCountries,
  SelectedQuoteType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { Email } from 'types/Email';
import { TransportType } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import { BatchQuoteStatus } from 'types/api/BatchQuote';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { GenericSuggestion } from 'types/suggestions/CoreSuggestions';
import { SuggestionStatus } from 'types/suggestions/LoadSuggestions';
import { QuoteChanges } from 'types/suggestions/QuoteSuggestions';
import captureException from 'utils/captureException';
import { copyToClipboard } from 'utils/copyToClipboard';
import { formatCurrency } from 'utils/formatCurrency';
import { titleCase } from 'utils/formatStrings';
import { mapCustomerToAntdOptions } from 'utils/loadInfoAndBuilding';
import { calculatePricing } from 'utils/priceCalculations';
import { cn } from 'utils/shadcn';

import BatchQuoteEdit from './BatchQuoteEdit';
import { BatchQuoteInputCard } from './BatchQuoteInputCard';
import { useHelperFunctions } from './helperFunctions';
import {
  BATCH_QUOTE_TEMPLATE,
  BatchEditValues,
  BatchQuoteFormInput,
  BatchQuoteFormValues,
  BatchQuoteResult,
  CarrierCostType,
  ProfitType,
  QuoteFormStop,
  getTransportTypeOptions,
} from './types';

interface ValidQuote {
  index: number;
  quoteData: BatchQuoteFormInput;
  updatedFormValues: BatchQuoteFormInput;
  pickup: QuoteFormStop;
  delivery: QuoteFormStop;
}

interface BatchQuoteFormProps {
  suggestions: GenericSuggestion[];
  email: Maybe<Email>;
}

const BatchQuoteForm = ({ suggestions, email }: BatchQuoteFormProps) => {
  const { toast } = useToast();
  const {
    quickQuoteConfig,
    tmsIntegrations,
    serviceID,
    serviceFeaturesEnabled: {
      isQuoteLaneHistoryEnabled,
      isTMSLaneHistoryEnabled,
      isGetLaneRateFromServiceEnabled,
    },
  } = useServiceFeatures();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [results, setResults] = useState<(BatchQuoteResult | null)[]>([]);
  const [laneHistoryResults, setLaneHistoryResults] = useState<
    (LaneHistoryResponse | null)[]
  >([]);
  const [laneHistoryFromServiceResults, setLaneHistoryFromServiceResults] =
    useState<(LaneHistoryFromServiceResponse | null)[]>([]);
  const [selectedLanes, setSelectedLanes] = useState<Set<number>>(new Set());
  const [showBatchEdit, setShowBatchEdit] = useState(false);
  const [expandedLanes, setExpandedLanes] = useState<Set<number>>(new Set());
  const [draftResponse, setDraftResponse] = useState('');
  const [userEditedDraft, setUserEditedDraft] = useState(false);
  const [hasCopiedDraftResponse, setHasCopiedDraftResponse] = useState(false);
  const [loadingDraftReply, setLoadingDraftReply] = useState(false);

  const [retryingQuotes, setRetryingQuotes] = useState<Set<number>>(new Set());
  const formContainerRef = useRef<HTMLFormElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    currentState: { drumkitPlatform, threadItemId, isOutlookReply },
    setCurrentState,
  } = useContext(SidebarStateContext);

  const [lastBatchEditMargin, setLastBatchEditMargin] = useState(() => {
    const defaultProfitType =
      (quickQuoteConfig?.defaultMarginType as ProfitType) ||
      ProfitType.Percentage;
    const defaultProfit =
      defaultProfitType === ProfitType.Amount
        ? quickQuoteConfig?.defaultFlatMargin || 100
        : quickQuoteConfig?.defaultPercentMargin || 10;

    return {
      margin: defaultProfit.toString(),
      marginType: defaultProfitType,
    };
  });

  const transportTypeOptions = useMemo(
    () => getTransportTypeOptions(quickQuoteConfig),
    [quickQuoteConfig]
  );

  const createEmptyQuote = (): BatchQuoteFormInput => {
    const defaultProfitType =
      (quickQuoteConfig?.defaultMarginType as ProfitType) ||
      ProfitType.Percentage;
    const defaultProfit =
      defaultProfitType === ProfitType.Amount
        ? quickQuoteConfig?.defaultFlatMargin || 100
        : quickQuoteConfig?.defaultPercentMargin || 10;
    return {
      transportType: TransportType.VAN,
      stops: [EMPTY_STOP, EMPTY_STOP],
      pickupDate: new Date(),
      deliveryDate: new Date(),
      isSubmitToTMS: false,
      customerName: '',
      quoteNumber: '',
      quoteExpirationDate: new Date(),
      quoteEta: new Date(),
      profit: defaultProfit,
      profitType: defaultProfitType,
    };
  };

  // TODO: Multi-stop for Batch Quote ENG-4012
  const memoizedDefaultValues = useMemo<BatchQuoteFormValues>(() => {
    const defaultCustomerName = suggestions.length
      ? ((suggestions[0].suggested as QuoteChanges)?.customerExternalTMSID ??
        '')
      : '';

    if (suggestions.length === 0) {
      return { customerName: defaultCustomerName, quotes: [] };
    }

    const defaultProfitType =
      (quickQuoteConfig?.defaultMarginType as ProfitType) ||
      ProfitType.Percentage;
    const defaultProfit =
      defaultProfitType === ProfitType.Amount
        ? quickQuoteConfig?.defaultFlatMargin || 100
        : quickQuoteConfig?.defaultPercentMargin || 10;

    const defaultQuotes = suggestions.map((suggestion) => {
      const suggestedFields = suggestion.suggested as QuoteChanges;

      // Process suggested pickup location
      const pickupLocation = useHelperFunctions.parseLocation(
        suggestedFields?.pickupZip
          ? suggestedFields.pickupZip
          : `${suggestedFields?.pickupCity}, ${suggestedFields?.pickupState}`
      );

      // Process suggested delivery location
      const deliveryLocation = useHelperFunctions.parseLocation(
        suggestedFields?.deliveryZip
          ? suggestedFields.deliveryZip
          : `${suggestedFields?.deliveryCity}, ${suggestedFields?.deliveryState}`
      );

      return {
        transportType: suggestedFields?.transportType ?? '',
        stops: [
          {
            location: pickupLocation
              ? pickupLocation.zip
                ? pickupLocation.zip
                : `${pickupLocation.city}, ${pickupLocation.state}`
              : '',
            city: pickupLocation?.city ?? '',
            state: pickupLocation?.state ?? '',
            zip: pickupLocation?.zip ?? '',
            country: pickupLocation?.country ?? '',
          },
          {
            location: deliveryLocation
              ? deliveryLocation.zip
                ? deliveryLocation.zip
                : `${deliveryLocation.city}, ${deliveryLocation.state}`
              : '',
            city: deliveryLocation?.city ?? '',
            state: deliveryLocation?.state ?? '',
            zip: deliveryLocation?.zip ?? '',
            country: deliveryLocation?.country ?? '',
          },
        ],
        pickupDate: new Date(),
        deliveryDate: new Date(),
        isSubmitToTMS: false,
        customerName: defaultCustomerName,
        quoteNumber: '',
        quoteExpirationDate: new Date(),
        quoteEta: new Date(),
        profit: defaultProfit,
        profitType: defaultProfitType,
      } as BatchQuoteFormInput;
    });

    return { customerName: defaultCustomerName, quotes: defaultQuotes };
  }, [suggestions, quickQuoteConfig]);

  const formMethods = useForm<BatchQuoteFormValues>({
    defaultValues: memoizedDefaultValues,
    reValidateMode: 'onChange',
  });

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    getValues,
    formState: { errors },
  } = formMethods;

  // Watch form values to trigger draft updates when margins change
  const watchedQuotes = useWatch({
    control,
    name: 'quotes',
  });

  const { fields, remove, append } = useFieldArray({
    control,
    name: 'quotes',
  });

  // Watch for customerName changes and propagate will be handled later after fields are defined
  const watchedCustomerName = useWatch({ control, name: 'customerName' });

  // Initialize form with suggestions
  useEffect(() => {
    if (suggestions.length > 0) {
      reset(memoizedDefaultValues);
      setSelectedLanes(new Set());
      setExpandedLanes(new Set());
    }
  }, [suggestions, memoizedDefaultValues, reset]);

  // Reset form and results when email changes
  useEffect(() => {
    setResults([]);
    setLaneHistoryResults([]);
    reset(memoizedDefaultValues);
    setSelectedLanes(new Set());
    setExpandedLanes(new Set());
  }, [email, reset, memoizedDefaultValues]);

  // Propagate customer to all lanes whenever it changes
  useEffect(() => {
    if (watchedCustomerName !== undefined) {
      fields.forEach((_, idx) => {
        setValue(`quotes.${idx}.customerName`, watchedCustomerName);
      });
    }
  }, [watchedCustomerName, fields.length]);

  // For Posthog Button Events
  const logProperties = useMemo(() => {
    return {
      serviceID,
      threadID: email?.threadID,
    };
  }, [serviceID, email?.threadID]);

  const handleAddLane = () => {
    append(createEmptyQuote());

    // Propagate selected customer to the new lane
    const currentCustomer = getValues('customerName');
    const newIndex = fields.length; // index after append
    if (currentCustomer) {
      setValue(`quotes.${newIndex}.customerName`, currentCustomer);
    }

    // Clear any existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Smoothly scroll the nearest scrollable container instead of relying on
    // element.scrollIntoView which can pick the wrong ancestor and cause the
    // viewport to appear "locked" in some hosts (e.g., Gmail wrappers).
    const findScrollableParent = (
      element: HTMLElement | null
    ): HTMLElement | null => {
      if (!element) return null;
      let current: HTMLElement | null = element.parentElement;
      while (current) {
        const style = window.getComputedStyle(current);
        const isScrollable =
          current.scrollHeight > current.clientHeight &&
          (style.overflowY === 'auto' ||
            style.overflowY === 'scroll' ||
            style.overflowY === 'overlay');
        if (isScrollable) return current;
        current = current.parentElement;
      }
      return null;
    };

    scrollTimeoutRef.current = setTimeout(() => {
      const container = formContainerRef.current;
      if (!container) {
        scrollTimeoutRef.current = null;
        return;
      }

      const scrollable = findScrollableParent(container);
      if (scrollable) {
        scrollable.scrollTo({
          top: scrollable.scrollHeight,
          behavior: 'smooth',
        });
      }
      scrollTimeoutRef.current = null;
    }, 100);
  };

  const handleSelectLane = (index: number, isSelected: boolean) => {
    setSelectedLanes((prev) => {
      const newSelected = new Set(prev);
      if (isSelected) {
        newSelected.add(index);
      } else {
        newSelected.delete(index);
        if (newSelected.size === 0) {
          setShowBatchEdit(false);
        }
      }
      return newSelected;
    });
  };

  const handleToggleExpandLane = (index: number) => {
    setExpandedLanes((prev) => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(index)) {
        newExpanded.delete(index);
      } else {
        newExpanded.add(index);
      }
      return newExpanded;
    });
  };

  const handleSelectAll = () => {
    if (selectedLanes.size === fields.length) {
      setSelectedLanes(new Set());
      setShowBatchEdit(false);
    } else {
      setSelectedLanes(new Set(fields.map((_, index) => index)));
    }
  };

  const handleInitiateBatchEdit = () => {
    setShowBatchEdit(true);
  };

  const handleCancelBatchEdit = () => {
    setShowBatchEdit(false);
  };

  const handleApplyBatchEdit = (batchValues: BatchEditValues) => {
    setLastBatchEditMargin({
      margin: batchValues.margin,
      marginType: batchValues.marginType,
    });

    selectedLanes.forEach((index) => {
      if (batchValues.transportType) {
        setValue(
          `quotes.${index}.transportType`,
          batchValues.transportType as TransportType
        );
      }

      if (batchValues.pickupLocation) {
        const parsedPickup = useHelperFunctions.parseLocation(
          batchValues.pickupLocation
        );
        if (parsedPickup) {
          setValue(
            `quotes.${index}.stops.0.location`,
            batchValues.pickupLocation
          );
          setValue(`quotes.${index}.stops.0.city`, parsedPickup.city);
          setValue(`quotes.${index}.stops.0.state`, parsedPickup.state);
          setValue(`quotes.${index}.stops.0.zip`, parsedPickup.zip);
          setValue(`quotes.${index}.stops.0.country`, parsedPickup.country);
        }
      }

      if (batchValues.dropoffLocation) {
        const parsedDropoff = useHelperFunctions.parseLocation(
          batchValues.dropoffLocation
        );
        if (parsedDropoff) {
          setValue(
            `quotes.${index}.stops.1.location`,
            batchValues.dropoffLocation
          );
          setValue(`quotes.${index}.stops.1.city`, parsedDropoff.city);
          setValue(`quotes.${index}.stops.1.state`, parsedDropoff.state);
          setValue(`quotes.${index}.stops.1.zip`, parsedDropoff.zip);
          setValue(`quotes.${index}.stops.1.country`, parsedDropoff.country);
        }
      }

      if (batchValues.margin) {
        setValue(`quotes.${index}.profit`, parseFloat(batchValues.margin));
        setValue(`quotes.${index}.profitType`, batchValues.marginType);
      }
    });

    // Apply buy rate source to selected lanes via registered setters
    if (batchValues.buyRateSource) {
      selectedLanes.forEach((index) => {
        const setter = carrierSetterMapRef.current.get(index);
        if (setter) {
          setter(batchValues.buyRateSource as SelectedQuoteType);
        }
      });
    }

    // Apply price format to selected lanes via registered setters
    if (batchValues.priceFormat) {
      selectedLanes.forEach((index) => {
        const setter = priceFormatSetterMapRef.current.get(index);
        if (setter) {
          setter(batchValues.priceFormat as CarrierCostType | 'Both');
          // Also update our tracking ref
          laneCurrentPriceFormatsRef.current.set(
            index,
            batchValues.priceFormat as CarrierCostType | 'Both'
          );
        }
      });
    }

    setShowBatchEdit(false);
  };

  const handleRemoveQuote = useCallback(
    (index: number) => {
      remove(index);

      setResults((prev) => {
        const newResults = [...prev];
        newResults.splice(index, 1);
        return newResults;
      });

      setLaneHistoryResults((prev) => {
        const newResults = [...prev];
        newResults.splice(index, 1);
        return newResults;
      });

      // Clear any retrying state for this quote
      setRetryingQuotes((prev) => {
        const newSet = new Set(prev);
        newSet.delete(index);
        return newSet;
      });

      // Update expanded lanes, reindexing as needed
      setExpandedLanes((prev) => {
        const newExpanded = new Set<number>();
        prev.forEach((expandedIdx) => {
          if (expandedIdx < index) {
            newExpanded.add(expandedIdx);
          } else if (expandedIdx > index) {
            newExpanded.add(expandedIdx - 1);
          }
        });
        return newExpanded;
      });

      // Clean up Map references
      carrierSetterMapRef.current.delete(index);
      laneCalculatedPricesRef.current.delete(index);
      priceFormatSetterMapRef.current.delete(index);
      laneCurrentPriceFormatsRef.current.delete(index);

      // Reindex remaining Map entries
      const carrierSetters = new Map<
        number,
        (carrier: SelectedQuoteType) => void
      >();
      const laneCalculatedPrices = new Map<
        number,
        {
          finalPrice: number;
          maxDistance: number;
          fuelEstimate: number;
        }
      >();
      const priceFormatSetters = new Map<
        number,
        (priceFormat: CarrierCostType | 'Both') => void
      >();
      const laneCurrentPriceFormats = new Map<
        number,
        CarrierCostType | 'Both'
      >();

      carrierSetterMapRef.current.forEach((setter, mapIndex) => {
        if (mapIndex > index) {
          carrierSetters.set(mapIndex - 1, setter);
        } else if (mapIndex < index) {
          carrierSetters.set(mapIndex, setter);
        }
      });

      laneCalculatedPricesRef.current.forEach((price, mapIndex) => {
        if (mapIndex > index) {
          laneCalculatedPrices.set(mapIndex - 1, price);
        } else if (mapIndex < index) {
          laneCalculatedPrices.set(mapIndex, price);
        }
      });

      priceFormatSetterMapRef.current.forEach((setter, mapIndex) => {
        if (mapIndex > index) {
          priceFormatSetters.set(mapIndex - 1, setter);
        } else if (mapIndex < index) {
          priceFormatSetters.set(mapIndex, setter);
        }
      });

      laneCurrentPriceFormatsRef.current.forEach((format, mapIndex) => {
        if (mapIndex > index) {
          laneCurrentPriceFormats.set(mapIndex - 1, format);
        } else if (mapIndex < index) {
          laneCurrentPriceFormats.set(mapIndex, format);
        }
      });

      carrierSetterMapRef.current = carrierSetters;
      laneCalculatedPricesRef.current = laneCalculatedPrices;
      priceFormatSetterMapRef.current = priceFormatSetters;
      laneCurrentPriceFormatsRef.current = laneCurrentPriceFormats;
    },
    [remove]
  );

  const handleRetryQuote = useCallback(
    async (index: number) => {
      // Set loading state for this quote
      setRetryingQuotes((prev) => new Set(prev).add(index));

      try {
        const startTime = Date.now();
        const quoteData = getValues(`quotes.${index}`);

        // Clear existing error for this quote
        formMethods.clearErrors(`quotes.${index}.root` as any);

        // Parse locations
        const pickup = useHelperFunctions.parseLocation(
          quoteData.stops[0].location || ''
        );
        const delivery = useHelperFunctions.parseLocation(
          quoteData.stops[1].location || ''
        );

        if (!pickup || !delivery) {
          if (!pickup) {
            formMethods.setError(`quotes.${index}.stops.0.location` as any, {
              message: 'Invalid pickup location',
            });
          }

          if (!delivery) {
            formMethods.setError(`quotes.${index}.stops.1.location` as any, {
              message: 'Invalid delivery location',
            });
          }
          // Clear retrying state before returning
          setRetryingQuotes((prev) => {
            const newSet = new Set(prev);
            newSet.delete(index);
            return newSet;
          });

          return;
        }

        const updatedFormValues: BatchQuoteFormInput = {
          ...quoteData,
          stops: [
            {
              order: 0,
              zip: pickup.zip,
              city: pickup.city,
              state: pickup.state,
              country: pickup.country,
              location: quoteData.stops[0].location,
            },
            {
              order: 1,
              zip: delivery.zip,
              city: delivery.city,
              state: delivery.state,
              country: delivery.country,
              location: quoteData.stops[1].location,
            },
          ],
        };

        const batchResponse = await getBatchQuote({
          email: email,
          clickedSuggestion: suggestions.slice(index, index + 1),
          formValues: {
            customerName: getValues('customerName'),
            quotes: [updatedFormValues],
          },
          formMethods: formMethods,
          setQuoteNotConfidentHandler: () => {},
          setProfit: () => {},
          profitType: quoteData.profitType || ProfitType.Percentage,
        });

        if (!batchResponse || !batchResponse.results[0]) {
          return;
        }

        const result = batchResponse.results[0];

        if (result.status === BatchQuoteStatus.Success && result.quotes) {
          // Convert BatchQuoteResult to the expected format
          const response: QuickQuoteResponse = {
            quoteRequestId: result.quoteRequestId,
            stops: result.stops || [],
            selectedRateName:
              result.selectedRateName || SelectedQuoteType.GS_NETWORK,
            inputtedTransportType:
              result.inputtedTransportType || updatedFormValues.transportType,
            submittedTransportType:
              result.submittedTransportType || updatedFormValues.transportType,
            configuration: result.configuration,
            quoteReplyDraftTemplate: result.quoteReplyDraftTemplate || {
              subject: '',
              body: '',
            },
            quotes: result.quotes,
            isZipCodeLookup: result.isZipCodeLookup || false,
          };

          const cards = useHelperFunctions.parseQuoteCardsFromResponse({
            newQuote: response,
            formValues: updatedFormValues,
            setDATFuelSurcharge: () => {},
          });

          // Update the results for this specific index
          setResults((prev) => {
            const newResults = [...prev];
            newResults[index] = { response, cards };
            return newResults;
          });

          // Try to fetch lane history
          if (
            (isQuoteLaneHistoryEnabled || isTMSLaneHistoryEnabled) &&
            result.quoteRequestId
          ) {
            try {
              const laneHistoryResponse = await getLaneHistory({
                quoteRequestId: result.quoteRequestId,
                originCity: pickup.city,
                originState: pickup.state,
                originZip: pickup.zip,
                originCountry: pickup.country,
                destinationCity: delivery.city,
                destinationState: delivery.state,
                destinationZip: delivery.zip,
                destinationCountry: delivery.country,
                transportType: updatedFormValues.transportType,
              });

              if (laneHistoryResponse.isOk()) {
                setLaneHistoryResults((prev) => {
                  const newResults = [...prev];
                  newResults[index] = laneHistoryResponse.value;
                  return newResults;
                });
              }
            } catch (error) {
              captureException(error, {
                functionName: 'retryQuote_getLaneHistory',
              });
            }
          }

          toast({
            description: 'Quote retry successful!',
            variant: 'success',
          });
        } else if (result.status === BatchQuoteStatus.Error) {
          // Handle error case
          if (result.error) {
            formMethods.setError(`quotes.${index}.root` as any, {
              message: result.error,
            });
          }
        }

        const elapsed = Date.now() - startTime;
        if (elapsed < 500) {
          await new Promise((resolve) => setTimeout(resolve, 500 - elapsed));
        }
      } catch (err) {
        captureException(err, {
          functionName: 'handleRetryQuote',
        });
        toast({
          description: 'Failed to retry quote. Please try again.',
          variant: 'destructive',
        });
      } finally {
        // Clear loading state for this quote
        setRetryingQuotes((prev) => {
          const newSet = new Set(prev);
          newSet.delete(index);
          return newSet;
        });
      }
    },
    [
      getValues,
      formMethods,
      email,
      suggestions,
      isQuoteLaneHistoryEnabled,
      isTMSLaneHistoryEnabled,
      toast,
    ]
  );

  const onSubmit = handleSubmit(async (data: BatchQuoteFormValues) => {
    setIsSubmitting(true);
    setResults([]);
    setLaneHistoryResults([]);
    setLaneHistoryFromServiceResults([]);
    setRetryingQuotes(new Set());

    // Clear any existing form errors for individual quotes
    data.quotes.forEach((_, index) => {
      formMethods.clearErrors(`quotes.${index}.root` as any);
    });

    // Validate all quotes upfront and prepare batch request
    const validQuotes: ValidQuote[] = [];

    data.quotes.forEach((quoteData, index) => {
      const pickupLocation = useHelperFunctions.parseLocation(
        quoteData.stops[0].location || ''
      );
      const deliveryLocation = useHelperFunctions.parseLocation(
        quoteData.stops[1].location || ''
      );

      if (!pickupLocation || !deliveryLocation) {
        if (!pickupLocation) {
          formMethods.setError(`quotes.${index}.stops.0.location` as any, {
            message: 'Invalid location',
          });
        }
        if (!deliveryLocation) {
          formMethods.setError(`quotes.${index}.stops.1.location` as any, {
            message: 'Invalid location',
          });
        }
        return;
      }

      // Set form values to update parsed location info in Batch Quote Input Cards
      setValue(`quotes.${index}.stops.0.city`, pickupLocation.city);
      setValue(`quotes.${index}.stops.0.state`, pickupLocation.state);
      setValue(`quotes.${index}.stops.0.zip`, pickupLocation.zip);
      setValue(`quotes.${index}.stops.0.country`, pickupLocation.country);
      setValue(`quotes.${index}.stops.1.city`, deliveryLocation.city);
      setValue(`quotes.${index}.stops.1.state`, deliveryLocation.state);
      setValue(`quotes.${index}.stops.1.zip`, deliveryLocation.zip);
      setValue(`quotes.${index}.stops.1.country`, deliveryLocation.country);

      const pickup: QuoteFormStop = {
        order: 0,
        zip: pickupLocation.zip,
        city: pickupLocation.city,
        state: pickupLocation.state,
        country: pickupLocation.country,
        location: quoteData.stops[0].location,
      };

      const delivery: QuoteFormStop = {
        order: 1,
        zip: deliveryLocation.zip,
        city: deliveryLocation.city,
        state: deliveryLocation.state,
        country: deliveryLocation.country,
        location: quoteData.stops[1].location,
      };

      const updatedFormValues: BatchQuoteFormInput = {
        ...quoteData,
        stops: [pickup, delivery],
      };

      validQuotes.push({
        index,
        quoteData,
        updatedFormValues,
        pickup,
        delivery,
      });
    });

    if (validQuotes.length === 0) {
      setIsSubmitting(false);
      return;
    }

    const batchResponse = await getBatchQuote({
      email: email,
      clickedSuggestion: suggestions,
      formValues: {
        customerName: data.customerName,
        quotes: validQuotes.map((vq) => vq.updatedFormValues),
      },
      formMethods: formMethods,
      setQuoteNotConfidentHandler: () => {},
      setProfit: () => {},
      profitType: validQuotes[0]?.quoteData.profitType || ProfitType.Percentage,
    });

    if (!batchResponse) {
      toast({
        description: 'Failed to get batch quotes. Please try again.',
        variant: 'destructive',
      });
      setIsSubmitting(false);
      return;
    }

    // Show error toast if all results are errors (full batch failure)
    const allFailed = batchResponse.results.every(
      (result: (typeof batchResponse.results)[number]) =>
        result.status === BatchQuoteStatus.Error
    );
    if (allFailed) {
      toast({
        description:
          'All batch quotes failed. Please check your input and try again.',
        variant: 'destructive',
      });
      setIsSubmitting(false);
      return;
    }

    // Process batch response and fetch lane history for successful quotes
    const processedResults: (BatchQuoteResult | null)[] = Array(
      data.quotes.length
    ).fill(null);
    const processedLaneHistoryResults: (LaneHistoryResponse | null)[] = Array(
      data.quotes.length
    ).fill(null);
    const processedLaneHistoryFromServiceResults: (LaneHistoryFromServiceResponse | null)[] =
      Array(data.quotes.length).fill(null);

    const laneHistoryPromises = batchResponse.results.map(
      async (result, resultIndex) => {
        const validQuote = validQuotes[resultIndex];
        if (!validQuote) return;

        const { index, updatedFormValues, pickup, delivery } = validQuote;

        if (result.status === BatchQuoteStatus.Success && result.quotes) {
          // Convert BatchQuoteResult to the expected format
          const response = {
            quoteRequestId: result.quoteRequestId,
            stops: result.stops || [],
            selectedRateName:
              result.selectedRateName || SelectedQuoteType.GS_NETWORK,
            inputtedTransportType:
              result.inputtedTransportType || updatedFormValues.transportType,
            submittedTransportType:
              result.submittedTransportType || updatedFormValues.transportType,
            configuration: result.configuration,
            quoteReplyDraftTemplate: result.quoteReplyDraftTemplate || {
              subject: '',
              body: '',
            },
            quotes: result.quotes,
            isZipCodeLookup: result.isZipCodeLookup || false,
          };

          const cards = useHelperFunctions.parseQuoteCardsFromResponse({
            newQuote: response,
            formValues: updatedFormValues,
            setDATFuelSurcharge: () => {},
          });

          processedResults[index] = { response, cards };

          // Fetch lane history if enabled and we have a valid quote request ID
          if (
            (isQuoteLaneHistoryEnabled || isTMSLaneHistoryEnabled) &&
            result.quoteRequestId
          ) {
            try {
              const laneHistoryResponse = await getLaneHistory({
                quoteRequestId: result.quoteRequestId,
                originCity: pickup.city,
                originState: pickup.state,
                originZip: pickup.zip,
                originCountry: pickup.country || QuoteCountries.USA,
                destinationCity: delivery.city,
                destinationState: delivery.state,
                destinationZip: delivery.zip,
                destinationCountry: delivery.country || QuoteCountries.USA,
                transportType: updatedFormValues.transportType,
              });

              if (laneHistoryResponse.isOk()) {
                processedLaneHistoryResults[index] = laneHistoryResponse.value;
              }
            } catch (error) {
              captureException(error, {
                functionName: 'batchQuote_getLaneHistory',
              });
            }
          }

          // Fetch lane history from service if enabled
          if (isGetLaneRateFromServiceEnabled && result.quoteRequestId) {
            try {
              const laneHistoryResponse =
                await useHelperFunctions.fetchLaneHistoryFromService({
                  quoteRequestId: result.quoteRequestId,
                  originCity: pickup.city,
                  originState: pickup.state,
                  originZip: pickup.zip,
                  originCountry: pickup.country || QuoteCountries.USA,
                  destinationCity: delivery.city,
                  destinationState: delivery.state,
                  destinationZip: delivery.zip,
                  destinationCountry: delivery.country || QuoteCountries.USA,
                  transportType: updatedFormValues.transportType,
                });

              if (laneHistoryResponse.isOk()) {
                processedLaneHistoryFromServiceResults[index] =
                  laneHistoryResponse.value;
              }
            } catch (error) {
              captureException(error, {
                functionName: 'batchQuote_getLaneHistoryFromService',
              });
            }
          }
        } else if (result.status === BatchQuoteStatus.Error) {
          // Handle individual quote errors
          if (result.error) {
            formMethods.setError(`quotes.${index}.root` as any, {
              message: result.error,
            });
          }
        }
      }
    );

    await Promise.all(laneHistoryPromises);

    setResults(processedResults as (BatchQuoteResult | null)[]);
    setLaneHistoryResults(processedLaneHistoryResults);
    setLaneHistoryFromServiceResults(processedLaneHistoryFromServiceResults);
    setIsSubmitting(false);
  });

  const isAllSelected =
    fields.length > 0 && selectedLanes.size === fields.length;
  const hasSelection = selectedLanes.size > 0;
  const isSubmitButtonDisabled = isSubmitting || fields.length === 0;

  // Number of lanes that have results and are expandable
  const expandableLanesCount = useMemo(
    () => results.filter((r) => r).length,
    [results]
  );
  const allLanesExpanded =
    expandableLanesCount > 0 && expandedLanes.size === expandableLanesCount;

  const handleToggleExpandAll = () => {
    if (allLanesExpanded) {
      setExpandedLanes(new Set());
    } else {
      const allResultIndices = results
        .map((result, index) => (result ? index : -1))
        .filter((index) => index !== -1);
      setExpandedLanes(new Set(allResultIndices));
    }
  };

  // Helper function to decode HTML entities
  const decodeHtmlEntities = (text: string): string => {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
  };

  // Format price based on individual lane's selected format
  const formatPriceForDraft = (
    finalPrice: number,
    maxDistance: number,
    fuelEstimate: number,
    priceFormat: CarrierCostType | 'Both'
  ): string => {
    if (priceFormat === CarrierCostType.Linehaul && fuelEstimate) {
      return formatCurrency(finalPrice - fuelEstimate, 'USD');
    }

    if (!maxDistance) return formatCurrency(finalPrice, 'USD');

    if (priceFormat === 'Both') {
      return `${formatCurrency(finalPrice, 'USD')} (${formatCurrency(
        finalPrice / maxDistance,
        'USD',
        2
      )}/mi)`;
    } else if (priceFormat === CarrierCostType.PerMile) {
      return `${formatCurrency(finalPrice / maxDistance, 'USD', 2)}/mi`;
    } else {
      return formatCurrency(finalPrice, 'USD');
    }
  };

  // Reset user edited flag when results change (new quotes generated)
  useEffect(() => {
    setUserEditedDraft(false);
  }, [results]);

  /* handleTerminatingAction should be called whenever user takes an action that "terminates" the quote requests,
   * i.e. updates the QR status from "inFlight" to 1 of 2 terminating statuses: "accepted" or "rejected".
   * As of 6/4/2025, this occurs when user clicks "Create Draft Reply" or one of the copy buttons.
   * This function
   * 1) Updates BE quote request with final quote and margin values (critical for metrics)
   * 2) Sends user quote to service (if enabled)
   * 3) Updates UI to show success or failure toast
   */
  const handleTerminatingAction = async () => {
    // Process each quote result that has completed
    const quotesWithResults = results
      .map((result, index) => {
        if (!result?.response) return null;

        const formData = getValues(`quotes.${index}`);
        const suggestion = suggestions[index];

        // Get the carrier cost from the cheapest card (same logic as individual cards)
        const carrierCost = result.cards?.length
          ? result.cards.reduce((prev, curr) =>
              prev.cost < curr.cost ? prev : curr
            ).cost
          : 0;

        const maxDistance =
          result.response.quotes?.reduce(
            (max, current) => (current.distance > max.distance ? current : max),
            { distance: 0 }
          ).distance || 0;

        const profit = formData.profit || 0;
        const profitType = formData.profitType || ProfitType.Percentage;

        // Use the same price calculation logic as the individual cards
        const { flatCarrierCost, finalProfit } = calculatePricing(
          carrierCost,
          CarrierCostType.Flat, // For terminating action calculation, use flat carrier cost
          profit,
          profitType,
          maxDistance
        );

        const finalPrice = flatCarrierCost + finalProfit;

        return {
          suggestion,
          result,
          formData,
          carrierCost: flatCarrierCost,
          profit: finalProfit,
          finalPrice,
          profitType,
          parentQuoteRequestId: result.response.quoteRequestId || 0,
        };
      })
      .filter(Boolean);

    // Update each quote request suggestion
    const updatePromises = quotesWithResults.map(async (quote) => {
      if (!quote) return;

      const result = await updateQuoteRequestSuggestion(
        quote.parentQuoteRequestId,
        SuggestionStatus.Accepted,
        {
          finalQuotePrice: _.round(quote.finalPrice),
          finalMargin: _.round(quote.profit),
          marginType: quote.profitType,
          finalCarrierCost: _.round(quote.carrierCost),
          carrierCostType: CarrierCostType.Flat,
          customerExternalTMSId: getValues('customerName'),
        }
      );

      if (result.isErr()) {
        captureException(result.error, {
          functionName: 'handleTerminatingAction',
        });
      }
    });

    await Promise.all(updatePromises);

    // Remove processed suggestions from the list
    setCurrentState((prevState) => ({
      ...prevState,
      curSuggestionList: prevState.curSuggestionList.filter(
        (s) =>
          !suggestions.some((batchSuggestion) => batchSuggestion.id === s.id)
      ),
    }));
  };

  const handleCopyToClipboard = async () => {
    try {
      // Decode any HTML entities before copying to ensure clean text
      const cleanedResponse = decodeHtmlEntities(draftResponse);
      const success = await copyToClipboard(cleanedResponse);
      if (success) {
        setHasCopiedDraftResponse(true);
        handleTerminatingAction();
        setTimeout(() => setHasCopiedDraftResponse(false), 2000);
      }
    } catch (error) {
      captureException(error, { functionName: 'handleCopyToClipboard' });

      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  // Outlook draft functionality
  let handleDraftResponse: () => void;
  if (drumkitPlatform === DrumkitPlatform.Outlook) {
    const mailClient = createMailClientInstance(drumkitPlatform);
    handleDraftResponse = async () => {
      if (!draftResponse.trim()) {
        return;
      }

      setLoadingDraftReply(true);

      try {
        // Convert newlines to HTML <br> tags since the mail client works with HTML.
        const formattedDraftBody = draftResponse.trim().replace(/\n/g, '<br>');

        await mailClient.draftReply({
          threadItemId,
          draftBody: formattedDraftBody,
        });

        handleTerminatingAction();

        // When in Read View, there's a lag between when Outlook created the draft in the backend
        // and showing it in the client so wait for a moment before showing toaster.
        setTimeout(
          () => {
            toast({
              description: 'Successfully created draft reply.',
              variant: 'success',
            });

            setLoadingDraftReply(false);
          },
          isOutlookReply ? 1 : 3500
        );
      } catch (error: unknown) {
        captureException(error, { functionName: 'handleDraftResponse' });

        toast({
          description: 'Something went wrong creating draft reply',
          variant: 'destructive',
        });

        setLoadingDraftReply(false);
      }
    };
  }

  const {
    customers,
    isLoading: customersLoading,
    refreshCustomers: handleRefreshCustomers,
    resetCustomerSearch: handleResetCustomerSearch,
    customerSearch: handleCustomerSearch,
  } = useCustomers(tmsIntegrations, {
    toast,
    suggestedCustomerId: (suggestions[0]?.suggested as QuoteChanges)
      ?.customerExternalTMSID,
  });

  // Generate batch draft response with proper price calculation
  const generateBatchDraftResponse = useCallback(() => {
    const quotesWithResults = results
      .map((result, index) => {
        if (!result?.response) return null;

        const formData = getValues(`quotes.${index}`);

        // Get the actual calculated price from the lane card if available
        const laneCalculatedData = laneCalculatedPricesRef.current.get(index);

        let finalPrice: number;
        let maxDistance: number;
        let fuelEstimate: number;

        if (laneCalculatedData) {
          // Use the actual calculated price from the lane card
          finalPrice = laneCalculatedData.finalPrice;
          maxDistance = laneCalculatedData.maxDistance;
          fuelEstimate = laneCalculatedData.fuelEstimate;
        } else {
          // Fallback to basic calculation if lane data not available
          const carrierCost = result.cards?.length
            ? result.cards.reduce((prev, curr) =>
                prev.cost < curr.cost ? prev : curr
              ).cost
            : 0;

          maxDistance =
            result.response.quotes?.reduce(
              (max, current) =>
                current.distance > max.distance ? current : max,
              { distance: 0 }
            ).distance || 0;

          const profit = formData.profit || 0;
          const profitType = formData.profitType || ProfitType.Percentage;

          const { flatCarrierCost, finalProfit } = calculatePricing(
            carrierCost,
            CarrierCostType.Flat,
            profit,
            profitType,
            maxDistance
          );

          finalPrice = flatCarrierCost + finalProfit;
          fuelEstimate = 0;
        }

        // Get the price format for this lane, defaulting to Flat if not found
        const lanesPriceFormat =
          laneCurrentPriceFormatsRef.current.get(index) || CarrierCostType.Flat;

        return {
          quoteNumber: index + 1,
          pickupLocation: `${useHelperFunctions.toTitleCase(result.response.stops[0].city)}, ${result.response.stops[0].state}`,
          deliveryLocation: `${useHelperFunctions.toTitleCase(result.response.stops[1].city)}, ${result.response.stops[1].state}`,
          transportType: titleCase(formData.transportType || ''),
          price: formatPriceForDraft(
            finalPrice,
            maxDistance,
            fuelEstimate,
            lanesPriceFormat
          ),
          rawPrice: finalPrice,
        };
      })
      .filter(Boolean);

    if (quotesWithResults.length === 0) return '';

    // Get the actual customer display name instead of externalTMSID
    const customerId = getValues('customerName');
    const watchedCustomer = watchedCustomerName;

    let customerDisplayName = '';
    const customerIdToLookup = customerId || watchedCustomer;

    if (customerIdToLookup && customers?.length) {
      const foundCustomer = customers.find(
        (customer) => customer.externalTMSID === customerIdToLookup
      );
      if (foundCustomer?.name) {
        customerDisplayName = foundCustomer.name;
      }
    }

    const templateData = {
      customerName: customerDisplayName || '',
      hasCustomer: !!customerDisplayName,
      quotes: quotesWithResults,
      hasTotal: quotesWithResults.length > 1,
    };

    const renderedTemplate = Mustache.render(
      BATCH_QUOTE_TEMPLATE,
      templateData
    );
    return decodeHtmlEntities(renderedTemplate);
  }, [results, customers, watchedCustomerName]);

  // Reset user edited flag when form values change (but not results)
  // This allows the draft to update when user changes margins, transport types, etc.
  useEffect(() => {
    if (results.some((result) => result !== null)) {
      setUserEditedDraft(false);
    }
  }, [watchedQuotes, watchedCustomerName, customers, results]);

  // Force draft regeneration when customers data becomes available
  useEffect(() => {
    if (
      results.some((result) => result !== null) &&
      customers?.length &&
      !userEditedDraft
    ) {
      const newDraft = generateBatchDraftResponse();
      setDraftResponse(newDraft);
    }
  }, [customers, results, generateBatchDraftResponse, userEditedDraft]);

  // Update draft response when results, price format, or form values change
  useEffect(() => {
    if (results.some((result) => result !== null)) {
      const newDraft = generateBatchDraftResponse();
      // Only update the draft if the user hasn't manually edited the textarea
      if (!userEditedDraft) {
        setDraftResponse(newDraft);
      }
    }
  }, [results, generateBatchDraftResponse, userEditedDraft]);

  // Ref to store setter functions for carrier selection per lane
  const carrierSetterMapRef = useRef(
    new Map<number, (carrier: SelectedQuoteType) => void>()
  );

  // Ref to store the actual calculated prices from each lane
  const laneCalculatedPricesRef = useRef(
    new Map<
      number,
      { finalPrice: number; maxDistance: number; fuelEstimate: number }
    >()
  );

  // Ref to store price format setter functions per lane
  const priceFormatSetterMapRef = useRef(
    new Map<number, (priceFormat: CarrierCostType | 'Both') => void>()
  );

  // Ref to store current price format for each lane
  const laneCurrentPriceFormatsRef = useRef(
    new Map<number, CarrierCostType | 'Both'>()
  );

  const registerCarrierSetter = (
    laneIndex: number,
    setter: (carrier: SelectedQuoteType) => void
  ) => {
    if (setter) {
      carrierSetterMapRef.current.set(laneIndex, setter);
    } else {
      carrierSetterMapRef.current.delete(laneIndex);
    }
  };

  const registerPriceCallback = (
    laneIndex: number,
    priceData: {
      finalPrice: number;
      maxDistance: number;
      fuelEstimate: number;
    } | null
  ) => {
    if (priceData) {
      laneCalculatedPricesRef.current.set(laneIndex, priceData);
    } else {
      laneCalculatedPricesRef.current.delete(laneIndex);
    }

    // Trigger draft regeneration when prices change
    if (results.some((result) => result !== null) && !userEditedDraft) {
      const newDraft = generateBatchDraftResponse();
      setDraftResponse(newDraft);
    }
  };

  const registerPriceFormatSetter = (
    laneIndex: number,
    setter: (priceFormat: CarrierCostType | 'Both') => void
  ) => {
    if (setter) {
      priceFormatSetterMapRef.current.set(laneIndex, setter);
      // Initialize with default format if not already set
      if (!laneCurrentPriceFormatsRef.current.has(laneIndex)) {
        laneCurrentPriceFormatsRef.current.set(laneIndex, CarrierCostType.Flat);
      }
    } else {
      priceFormatSetterMapRef.current.delete(laneIndex);
      laneCurrentPriceFormatsRef.current.delete(laneIndex);
    }
  };

  const handlePriceFormatChange = (
    laneIndex: number,
    priceFormat: CarrierCostType | 'Both'
  ) => {
    // Update the current price format tracking
    laneCurrentPriceFormatsRef.current.set(laneIndex, priceFormat);

    // Trigger draft regeneration when price format changes
    if (results.some((result) => result !== null) && !userEditedDraft) {
      const newDraft = generateBatchDraftResponse();
      setDraftResponse(newDraft);
    }
  };

  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={onSubmit} className='space-y-3' ref={formContainerRef}>
        <TooltipProvider>
          <div className='mb-3'>
            <Typography variant='body-xs' className='text-neutral-600'>
              {suggestions.length > 1
                ? 'Review and get quotes for all lanes below.'
                : 'Quote multiple lanes at once.'}
            </Typography>
          </div>

          {/* Customer input */}
          {tmsIntegrations &&
            tmsIntegrations.length > 0 &&
            isCustomerSupportedTMS(tmsIntegrations) && (
              <div className='mb-3'>
                <RHFDebounceSelect
                  required={false}
                  name='customerName'
                  label='Customer'
                  control={control}
                  errors={errors}
                  data={customers}
                  isLoading={customersLoading}
                  showSearchParamDropdown={false}
                  refreshHandler={handleRefreshCustomers}
                  resetOptionsHandler={handleResetCustomerSearch}
                  fetchOptions={handleCustomerSearch}
                  mapOptions={mapCustomerToAntdOptions}
                  disabled={results.length > 0}
                />
              </div>
            )}

          {fields.length > 0 && (
            <Flex
              direction='col'
              gap='xs'
              className='mb-4 border-t border-neutral-400 pt-3'
            >
              <Flex justify='between' align='center' className='w-full'>
                <Flex align='center' gap='md'>
                  <label className='flex items-center gap-2 text-xs text-neutral-600 cursor-pointer'>
                    <Checkbox
                      checked={isAllSelected}
                      onCheckedChange={handleSelectAll}
                    />
                    <span className='font-medium'>
                      {isAllSelected ? 'Deselect All' : 'Select All'}
                    </span>
                  </label>
                </Flex>
                {hasSelection ? (
                  <>
                    {showBatchEdit ? (
                      <span className='text-xs text-neutral-500'>
                        Editing {selectedLanes.size}{' '}
                        {results.length > 0 ? 'quote' : 'lane'}
                        {selectedLanes.size > 1 ? 's' : ''}
                      </span>
                    ) : (
                      <span className='text-xs text-neutral-500'>
                        {selectedLanes.size} of {fields.length} selected
                      </span>
                    )}
                  </>
                ) : (
                  <span className='text-xs text-neutral-500'>
                    Select {results.length > 0 ? 'quotes' : 'lanes'} to edit
                  </span>
                )}
              </Flex>

              {expandableLanesCount > 0 && (
                <Flex align='center' justify='end' className='mt-1.5'>
                  <button
                    type='button'
                    onClick={handleToggleExpandAll}
                    className='flex items-center gap-0.5 text-xs text-info-600 font-medium hover:underline focus:outline-none'
                  >
                    {allLanesExpanded ? (
                      <>
                        <ChevronUpIcon className='w-4 h-4' />
                        <Typography variant='body-sm'>
                          Collapse All Quotes
                        </Typography>
                      </>
                    ) : (
                      <>
                        <ChevronDownIcon className='w-4 h-4' />
                        <Typography variant='body-sm'>
                          Expand All Quotes
                        </Typography>
                      </>
                    )}
                  </button>
                </Flex>
              )}

              <BatchQuoteEdit
                selectedLanes={selectedLanes}
                showBatchEdit={showBatchEdit}
                hasResults={results.length > 0}
                transportTypeOptions={transportTypeOptions}
                lastBatchEditMargin={lastBatchEditMargin}
                onInitiateBatchEdit={handleInitiateBatchEdit}
                onCancelBatchEdit={handleCancelBatchEdit}
                onApplyBatchEdit={handleApplyBatchEdit}
                isTMSLaneHistoryEnabled={isTMSLaneHistoryEnabled}
              />
            </Flex>
          )}

          <Flex
            direction='col'
            gap='md'
            align='center'
            justify='center'
            className='w-full'
          >
            {fields.map((field, index) => (
              <div key={field.id} data-lane-card className='w-full'>
                <BatchQuoteInputCard
                  index={index}
                  control={control}
                  transportTypeOptions={transportTypeOptions}
                  onRemove={() => handleRemoveQuote(index)}
                  result={results[index] ?? undefined}
                  laneHistoryResult={laneHistoryResults[index] ?? undefined}
                  laneHistoryFromServiceResult={
                    laneHistoryFromServiceResults[index] ?? undefined
                  }
                  setValue={setValue}
                  isSelected={selectedLanes.has(index)}
                  onSelect={(isSelected) => handleSelectLane(index, isSelected)}
                  quickQuoteConfig={quickQuoteConfig}
                  isExpanded={expandedLanes.has(index)}
                  onToggleExpand={() => handleToggleExpandLane(index)}
                  registerCarrierSetter={registerCarrierSetter}
                  registerPriceCallback={registerPriceCallback}
                  registerPriceFormatSetter={registerPriceFormatSetter}
                  onPriceFormatChange={handlePriceFormatChange}
                  isTMSLaneHistoryEnabled={isTMSLaneHistoryEnabled}
                  isQuoteLaneHistoryEnabled={isQuoteLaneHistoryEnabled}
                  isGetLaneRateFromServiceEnabled={
                    isGetLaneRateFromServiceEnabled
                  }
                  onRetryQuote={handleRetryQuote}
                  isRetrying={retryingQuotes.has(index)}
                />
              </div>
            ))}

            {fields.length === 0 && (
              <Flex
                direction='col'
                align='center'
                justify='center'
                className='py-8 px-4 border-2 border-neutral-400 rounded-lg bg-neutral-50'
                gap='sm'
              >
                <Flex
                  align='center'
                  justify='center'
                  className='mx-auto h-12 w-12 rounded-full bg-info-100'
                >
                  <Truck className='h-5 w-5 text-info-600' />
                </Flex>
                <div>
                  <Typography
                    variant='h6'
                    weight='medium'
                    className='text-neutral-900'
                  >
                    No lanes to quote
                  </Typography>
                  <Typography
                    variant='body-xs'
                    className='text-neutral-500 mt-1'
                  >
                    Add lanes to get quotes for multiple routes at once
                  </Typography>
                </div>
                <Flex gap='sm' justify='center'>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleAddLane}
                    className='text-xs px-3 py-1.5 h-8 text-neutral-900 hover:border-neutral-600 hover:bg-neutral-200'
                    buttonNamePosthog={ButtonNamePosthog.BatchQuoteAddLane}
                    logProperties={{
                      ...logProperties,
                      state: 'with_no_lanes',
                    }}
                  >
                    <Flex align='center' gap='xs'>
                      <PlusIcon className='w-3 h-3' />
                      Add Lane
                    </Flex>
                  </Button>
                </Flex>
                {suggestions.length > 1 && (
                  <>
                    <Button
                      className='text-sm border-info-300 hover:border-info-500 bg-linear-to-br from-neutral-50 to-info-100/50 text-info-600'
                      onClick={() => {
                        // Repopulate form with quote request suggestions
                        reset(memoizedDefaultValues);
                      }}
                      type='button'
                      buttonNamePosthog={
                        ButtonNamePosthog.BatchQuoteAddLanesUsingAI
                      }
                      variant='outline'
                      size='sm'
                    >
                      <SparklesIcon className='w-3 h-3 mr-1' />
                      <Typography variant='body-xs'>
                        Add lanes using AI suggestions
                      </Typography>
                    </Button>
                  </>
                )}
              </Flex>
            )}

            {fields.length > 0 && (
              <>
                <div className='pb-3'>
                  <Flex gap='sm' justify='center'>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={handleAddLane}
                      className='text-xs px-3 py-1.5 tracking-wide text-neutral-600 border border-transparent hover:border-neutral-600 hover:bg-neutral-200'
                      buttonNamePosthog={ButtonNamePosthog.BatchQuoteAddLane}
                      logProperties={{
                        ...logProperties,
                        state: 'with_lanes',
                      }}
                    >
                      <Flex align='center' gap='xs'>
                        <PlusIcon className='w-3 h-3' />
                        Add Lane
                      </Flex>
                    </Button>
                  </Flex>
                </div>

                <Button
                  type='submit'
                  className='w-full'
                  disabled={isSubmitButtonDisabled}
                  buttonNamePosthog={ButtonNamePosthog.GetBatchQuote}
                  logProperties={logProperties}
                >
                  {isSubmitting ? <ButtonLoader /> : ButtonText.GetBatchQuote}
                </Button>
              </>
            )}

            {/* Show draft response if Drumkit running on email platform (Gmail, Outlook, Front) */}
            {results.length > 0 && (
              <>
                {isEmailPlatform(drumkitPlatform) && (
                  <div className='mt-4'>
                    <Flex justify='between' align='center' className='mb-3'>
                      <Typography
                        variant='h5'
                        weight='semibold'
                        className='text-neutral-800'
                      >
                        Draft Response
                      </Typography>
                      <div className='relative'>
                        <Button
                          buttonNamePosthog={
                            ButtonNamePosthog.BatchQuoteCopyQuoteToClipboard
                          }
                          logProperties={logProperties}
                          className={cn(
                            'h-6 w-6 p-0 border-none',
                            hasCopiedDraftResponse
                              ? 'cursor-default'
                              : 'cursor-pointer'
                          )}
                          variant='ghost'
                          type='button'
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            !hasCopiedDraftResponse && handleCopyToClipboard();
                          }}
                        >
                          {hasCopiedDraftResponse ? (
                            <Tooltip open={true}>
                              <TooltipTrigger asChild>
                                <CheckIcon className='h-4 w-4' />
                              </TooltipTrigger>
                              <TooltipContent>Copied!</TooltipContent>
                            </Tooltip>
                          ) : (
                            <CopyIcon className='h-4 w-4' />
                          )}
                        </Button>
                      </div>
                    </Flex>

                    <Textarea
                      name='batchEmailBody'
                      className='p-3 h-48 rounded-[4px] text-sm'
                      value={draftResponse}
                      placeholder='Quote results will appear here...'
                      onChange={(e) => {
                        setDraftResponse(e.target.value);
                        !userEditedDraft && setUserEditedDraft(true);
                      }}
                    />

                    {/** Reply drafts are only supported on Outlook for now */}
                    {drumkitPlatform === DrumkitPlatform.Outlook && (
                      <Button
                        className='w-full h-8 text-sm mt-4'
                        type='button'
                        buttonNamePosthog={
                          isOutlookReply
                            ? ButtonNamePosthog.BatchQuoteAddReplyToCurrentDraft
                            : ButtonNamePosthog.BatchQuoteCreateDraftReply
                        }
                        logProperties={logProperties}
                        disabled={loadingDraftReply}
                        onClick={() => handleDraftResponse()}
                      >
                        {isOutlookReply ? (
                          ButtonText.AddReplyToCurrentDraft
                        ) : loadingDraftReply ? (
                          <ButtonLoader />
                        ) : (
                          ButtonText.CreateDraftReply
                        )}
                      </Button>
                    )}
                  </div>
                )}
              </>
            )}
          </Flex>
        </TooltipProvider>
      </form>
    </FormProvider>
  );
};

export default BatchQuoteForm;
