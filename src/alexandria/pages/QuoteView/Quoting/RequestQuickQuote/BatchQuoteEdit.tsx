import React, { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { Edit3 } from 'lucide-react';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { SelectedQuoteType } from 'pages/QuoteView/Quoting/RequestQuickQuote/types';

import { BatchEditValues, CarrierCostType, ProfitType } from './types';

type BatchQuoteEditProps = {
  selectedLanes: Set<number>;
  showBatchEdit: boolean;
  hasResults: boolean;
  transportTypeOptions: Array<{ value: string; label: string }>;
  lastBatchEditMargin: {
    margin: string;
    marginType: ProfitType;
  };
  onInitiateBatchEdit: () => void;
  onCancelBatchEdit: () => void;
  onApplyBatchEdit: (values: BatchEditValues) => void;
  isTMSLaneHistoryEnabled: boolean;
};

const BatchEditTextInput = React.memo(
  ({
    label,
    placeholder,
    ...fieldProps
  }: {
    label: string;
    placeholder: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: () => void;
    name: string;
  }) => (
    <div className='w-full'>
      <Label name={fieldProps.name} className='text-xs'>
        {label}
      </Label>
      <input
        {...fieldProps}
        type='text'
        placeholder={placeholder}
        className='w-full mt-1 px-3 py-1.5 text-xs tracking-tight bg-neutral-50 border border-neutral-400 hover:border-brand-main focus:border-brand-main transition-colors rounded-[4px] focus:outline-none focus:ring-2 focus:ring-brand-main/20'
      />
    </div>
  )
);

const BatchQuoteEdit: React.FC<BatchQuoteEditProps> = React.memo(
  ({
    selectedLanes,
    showBatchEdit,
    hasResults,
    transportTypeOptions,
    lastBatchEditMargin,
    onInitiateBatchEdit,
    onCancelBatchEdit,
    onApplyBatchEdit,
    isTMSLaneHistoryEnabled,
  }) => {
    const hasSelection = selectedLanes.size > 0;
    const [leaveMarginUnchanged, setLeaveMarginUnchanged] = useState(false);

    const batchEditForm = useForm<BatchEditValues>({
      defaultValues: {
        transportType: '',
        pickupLocation: '',
        dropoffLocation: '',
        margin: lastBatchEditMargin.margin,
        marginType: lastBatchEditMargin.marginType,
        buyRateSource: '',
        priceFormat: '',
      },
    });

    const handleApplyBatchEditInternal = useCallback(() => {
      const batchValues = batchEditForm.getValues();
      // Clear margin if user wants to leave it unchanged
      if (leaveMarginUnchanged) {
        batchValues.margin = '';
      }
      onApplyBatchEdit(batchValues);
      batchEditForm.reset();
      setLeaveMarginUnchanged(false);
    }, [batchEditForm, onApplyBatchEdit, leaveMarginUnchanged]);

    const handleCancelBatchEditInternal = useCallback(() => {
      onCancelBatchEdit();
      batchEditForm.reset();
      setLeaveMarginUnchanged(false);
    }, [batchEditForm, onCancelBatchEdit]);

    // Reset form when batch edit is initiated
    useEffect(() => {
      if (showBatchEdit) {
        batchEditForm.reset({
          transportType: '',
          pickupLocation: '',
          dropoffLocation: '',
          margin: lastBatchEditMargin.margin,
          marginType: lastBatchEditMargin.marginType,
          buyRateSource: '',
          priceFormat: '',
        });
        setLeaveMarginUnchanged(false);
      }
    }, [showBatchEdit, lastBatchEditMargin, batchEditForm]);

    const handleLeaveMarginUnchangedToggle = (checked: boolean) => {
      setLeaveMarginUnchanged(checked);
      if (checked) {
        // Clear the margin value when leaving unchanged
        batchEditForm.setValue('margin', '');
      } else {
        // Restore the last margin value when unchecked
        batchEditForm.setValue('margin', lastBatchEditMargin.margin);
      }
    };

    return (
      <>
        {!showBatchEdit && (
          <Flex gap='sm' className='mt-2'>
            <Button
              type='button'
              variant='outline'
              size='sm'
              onClick={onInitiateBatchEdit}
              className='text-xs w-full h-8'
              buttonNamePosthog={null}
              disabled={!hasSelection}
            >
              <Edit3 className='w-3 h-3 mr-1' />
              <Flex align='center' gap='xs'>
                Edit Selected
                {selectedLanes.size > 0 && <span>({selectedLanes.size})</span>}
              </Flex>
            </Button>
          </Flex>
        )}

        {showBatchEdit && hasSelection && (
          <div className='w-full mt-2 p-3 bg-neutral-50 border border-neutral-300 rounded-[4px]'>
            <Flex direction='col' className='mb-3'>
              <Typography
                variant='h6'
                weight='medium'
                className='text-neutral-800'
              >
                Edit {selectedLanes.size} {hasResults ? 'Quote' : 'Lane'}
                {selectedLanes.size > 1 ? 's' : ''}
              </Typography>
              <Typography variant='body-xs' className='text-neutral-500'>
                Make edits to all selected {hasResults ? 'quote' : 'lane'}
                {selectedLanes.size > 1 ? 's' : ''}
              </Typography>
            </Flex>

            <div className='space-y-3'>
              {!hasResults && (
                <Flex direction='col' gap='sm'>
                  <div className='w-full'>
                    <Label name='batchTransportType' className='text-xs'>
                      Transport Type
                    </Label>
                    <Controller
                      name='transportType'
                      control={batchEditForm.control}
                      render={({ field }) => (
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger className='mt-1 bg-neutral-50 border-neutral-400 hover:border-brand-main transition-colors text-xs'>
                            <SelectValue
                              placeholder='Leave unchanged'
                              className='text-xs'
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {transportTypeOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>

                  <Controller
                    name='pickupLocation'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <BatchEditTextInput
                        {...field}
                        label='Pickup Location'
                        placeholder='ZIP or City, State'
                      />
                    )}
                  />

                  <Controller
                    name='dropoffLocation'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <BatchEditTextInput
                        {...field}
                        label='Dropoff Location'
                        placeholder='ZIP or City, State'
                      />
                    )}
                  />
                </Flex>
              )}

              {hasResults && (
                <div className='w-full'>
                  <Flex justify='between' align='center' className='mb-2'>
                    <Label name='batchMargin'>Margin</Label>
                    <label className='flex items-center gap-2 text-xs text-neutral-600 cursor-pointer'>
                      <Checkbox
                        checked={leaveMarginUnchanged}
                        onCheckedChange={handleLeaveMarginUnchangedToggle}
                      />
                      <span>Leave unchanged</span>
                    </label>
                  </Flex>
                  <Flex align='center' gap='sm' className='mt-1'>
                    <Controller
                      name='margin'
                      control={batchEditForm.control}
                      render={({ field }) => (
                        <input
                          {...field}
                          onWheel={(e) => (e.target as HTMLElement).blur()}
                          type='number'
                          placeholder={
                            leaveMarginUnchanged ? 'Will not change' : 'e.g. 15'
                          }
                          disabled={leaveMarginUnchanged}
                          className={`px-3 py-2 text-xs tracking-tight border transition-colors rounded-[4px] focus:outline-none focus:ring-2 focus:ring-brand-main/20 ${
                            leaveMarginUnchanged
                              ? 'bg-neutral-100 border-neutral-200 text-neutral-500 cursor-not-allowed'
                              : 'bg-neutral-50 border-neutral-400 hover:border-brand-main focus:border-brand-main'
                          }`}
                        />
                      )}
                    />
                    <Controller
                      name='marginType'
                      control={batchEditForm.control}
                      render={({ field }) => (
                        <div
                          className={`flex rounded-[4px] overflow-hidden border text-xs ${
                            leaveMarginUnchanged
                              ? 'border-neutral-200'
                              : 'border-neutral-400'
                          }`}
                        >
                          <button
                            type='button'
                            onClick={() => field.onChange(ProfitType.Amount)}
                            disabled={leaveMarginUnchanged}
                            className={`px-2 py-1 transition-colors ${
                              leaveMarginUnchanged
                                ? 'bg-neutral-100 text-neutral-400 cursor-not-allowed'
                                : field.value === ProfitType.Amount
                                  ? 'bg-brand-main/10 text-brand-main'
                                  : 'bg-neutral-50 text-neutral-600 hover:bg-neutral-100'
                            }`}
                          >
                            $
                          </button>
                          <button
                            type='button'
                            onClick={() =>
                              field.onChange(ProfitType.Percentage)
                            }
                            disabled={leaveMarginUnchanged}
                            className={`px-2 py-1 transition-colors ${
                              leaveMarginUnchanged
                                ? 'bg-neutral-100 text-neutral-400 cursor-not-allowed'
                                : field.value === ProfitType.Percentage
                                  ? 'bg-brand-main/10 text-brand-main'
                                  : 'bg-neutral-50 text-neutral-600 hover:bg-neutral-100'
                            }`}
                          >
                            %
                          </button>
                        </div>
                      )}
                    />
                  </Flex>
                </div>
              )}

              {/* Buy Rate Source selector when results available */}
              {hasResults && (
                <div className='w-full'>
                  <Label name='buyRateSource'>Buy Rate Source</Label>
                  <Controller
                    name='buyRateSource'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='w-full mt-1 bg-neutral-50 border-neutral-400 hover:border-brand-main focus:border-brand-main transition-colors text-xs'>
                          <SelectValue
                            placeholder='Leave unchanged'
                            className='text-xs'
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={SelectedQuoteType.DAT_RATEVIEW}>
                            DAT RateView
                          </SelectItem>
                          <SelectItem
                            value={SelectedQuoteType.TRUCKSTOP_BOOKED}
                          >
                            Truckstop Booked
                          </SelectItem>
                          <SelectItem
                            value={SelectedQuoteType.TRUCKSTOP_POSTED}
                          >
                            Truckstop Posted
                          </SelectItem>
                          <SelectItem value={SelectedQuoteType.GS_BUYPOWER}>
                            Greenscreens Buy Power
                          </SelectItem>
                          <SelectItem value={SelectedQuoteType.GS_NETWORK}>
                            Greenscreens Network
                          </SelectItem>
                          {isTMSLaneHistoryEnabled && (
                            <SelectItem value={SelectedQuoteType.LANE_HISTORY}>
                              TMS Lane History
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              )}

              {/* Price Format selector when results available */}
              {hasResults && (
                <div className='w-full'>
                  <Label name='priceFormat'>Price Format</Label>
                  <Controller
                    name='priceFormat'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='w-full mt-1 bg-neutral-50 border-neutral-400 hover:border-brand-main focus:border-brand-main transition-colors text-xs'>
                          <SelectValue
                            placeholder='Leave unchanged'
                            className='text-xs'
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={CarrierCostType.Flat}>
                            Flat Rate
                          </SelectItem>
                          <SelectItem value={CarrierCostType.PerMile}>
                            Per Mile
                          </SelectItem>
                          <SelectItem value='Both'>Flat & Per Mile</SelectItem>
                          <SelectItem value={CarrierCostType.Linehaul}>
                            Linehaul
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              )}

              <div className='flex justify-end gap-2 pt-2'>
                <Button
                  type='button'
                  variant='ghost'
                  size='sm'
                  onClick={handleCancelBatchEditInternal}
                  className='text-xs h-8 hover:border-none hover:text-neutral-600 transition-all duration-200'
                  buttonNamePosthog={null}
                >
                  Cancel
                </Button>
                <Button
                  type='button'
                  size='sm'
                  onClick={handleApplyBatchEditInternal}
                  className='text-xs h-8'
                  buttonNamePosthog={null}
                >
                  Apply Changes
                </Button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  }
);

export default BatchQuoteEdit;
