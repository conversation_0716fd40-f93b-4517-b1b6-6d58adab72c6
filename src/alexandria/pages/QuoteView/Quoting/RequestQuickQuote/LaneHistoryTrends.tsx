import { useEffect, useState } from 'react';

import { Divider } from 'antd';
import { ChevronDownIcon, ChevronUpIcon, Info } from 'lucide-react';

import { Button } from 'components/Button';
import { GenericLineChart } from 'components/GenericLineChart';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { useServiceFeatures } from 'hooks/useServiceContext';
import {
  LaneHistoryResponse,
  SourceHistory,
  WeekLaneData,
} from 'lib/api/getLaneHistory';
import { QuickQuoteResponse } from 'lib/api/getQuickQuote';
import { TransportType } from 'types/QuoteRequest';
import { Maybe, Undef } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import { integrationNameMap } from 'types/enums/Integrations';
import { titleCase } from 'utils/formatStrings';
import {
  chartConfig,
  chartConfigPercentile,
} from 'utils/laneHistoryChartConfig';

import { LaneHistoryLoadsTable } from './LaneHistoryLoadsTable';
import { LaneTier } from './types';

interface LaneHistoryTrendsProps {
  isLoadingLaneHistory: boolean;
  laneHistory: Maybe<LaneHistoryResponse>;
  tmsLaneHistory: Maybe<SourceHistory[]>;
  tmsLaneHistorySelectedTierIndex: Maybe<number>;
  quoteResponse: Maybe<QuickQuoteResponse>;
  toggleLaneHistoryGraph: (option: string) => void;
}

export const LaneHistoryTrends = ({
  isLoadingLaneHistory,
  laneHistory,
  tmsLaneHistory,
  tmsLaneHistorySelectedTierIndex,
  quoteResponse,
  toggleLaneHistoryGraph,
}: LaneHistoryTrendsProps) => {
  const [hiddenLoadsTables, setHiddenLoadsTables] = useState<
    Record<string, boolean>
  >({});

  const { tmsIntegrations } = useServiceFeatures();

  // Initialize hidden state for each source when lane history changes
  useEffect(() => {
    if (laneHistory?.resultsBySource) {
      setHiddenLoadsTables(
        Object.keys(laneHistory.resultsBySource).reduce(
          (acc, source) => ({
            ...acc,
            [source]: true, // Start with all tables hidden
          }),
          {}
        )
      );
    }
  }, [laneHistory]);

  if (isLoadingLaneHistory) {
    return (
      <Flex justify='center' gap='xs'>
        <Typography variant='body-sm' className='text-neutral-500'>
          Fetching Lane History...
        </Typography>
      </Flex>
    );
  }

  if (!laneHistory || Object.values(laneHistory.resultsBySource).length === 0) {
    return (
      <Flex justify='center'>
        <Typography variant='body-sm' className='text-neutral-500'>
          No TMS lane history available
        </Typography>
      </Flex>
    );
  }

  return (
    <>
      {/* Generate a chart for each source */}
      {Object.entries(laneHistory.resultsBySource).map(
        ([source, history], index) => {
          let laneTierHistory = history[0];

          const isTMSChart =
            tmsLaneHistory &&
            tmsLaneHistory.length > 0 &&
            source === tmsLaneHistory[0].source;

          // If there's TMS lane history and multiple tiers, graph the selected tier
          if (
            tmsLaneHistory &&
            isTMSChart &&
            tmsLaneHistorySelectedTierIndex !== null
          ) {
            laneTierHistory = tmsLaneHistory[tmsLaneHistorySelectedTierIndex];
          }

          let laneTierOptions: Undef<LaneTier[]> = undefined;
          if (tmsLaneHistory && isTMSChart) {
            laneTierOptions = tmsLaneHistory
              .filter((history) => Boolean(history.laneTier))
              .map((history) => history.laneTier as LaneTier);
          }

          const yAxisMax = Math.max(
            ...history[0].weeks
              .filter((item) => Boolean(item.maxRate))
              .map((item) => item.maxRate)
          );

          const tooltipElt = (
            inputtedTransportType: TransportType,
            proxiedTransportType: TransportType
          ) => (
            <Flex justify='between'>
              <Flex align='baseline' gap='sm'>
                <Tooltip delayDuration={10}>
                  <TooltipTrigger className='border-b border-dashed border-neutral-900 text-sm font-normal'>
                    {`(${titleCase(proxiedTransportType)})`}
                  </TooltipTrigger>
                  <TooltipContent className='mr-1 font-normal max-w-60 whitespace-pre-wrap'>
                    <Typography>
                      {`Greenscreens does not support ${titleCase(inputtedTransportType)} quotes, showing equivalent ${titleCase(proxiedTransportType)} quoteResponse.`}
                    </Typography>
                  </TooltipContent>
                </Tooltip>
              </Flex>
            </Flex>
          );

          const isLoadsTableHidden = hiddenLoadsTables[laneTierHistory.source];

          const xLabels = laneTierHistory.weeks.map((w) => {
            const s = w.week ?? '';
            const i = s.indexOf(' - ');
            return i >= 0 ? s.slice(0, i) : s;
          });

          return (
            <>
              <GenericLineChart
                data={laneTierHistory.weeks}
                title={`${integrationNameMap[laneTierHistory.source]} Lane History`}
                subtitle={
                  laneTierHistory.inputtedTransportType !==
                  laneTierHistory.proxiedTransportType
                    ? tooltipElt(
                        laneTierHistory.inputtedTransportType,
                        laneTierHistory.proxiedTransportType
                      )
                    : titleCase(laneTierHistory.proxiedTransportType)
                }
                // Don't repeat description for each chart
                description={
                  index === 0
                    ? laneTierHistory.isPercentile
                      ? `${quoteResponse?.stops[0].state} to ${quoteResponse?.stops[quoteResponse.stops.length - 1].state} rates from the last 4 weeks`
                      : 'Rates from the last four weeks'
                    : ''
                }
                toggleOptions={laneTierOptions}
                selectedData={laneTierHistory.laneTier as string}
                toggleDataHandler={toggleLaneHistoryGraph}
                chartConfig={
                  laneTierHistory.isPercentile
                    ? chartConfigPercentile
                    : chartConfig
                }
                xAxisDomain={xLabels}
                yAxisDomainMax={yAxisMax}
                yAxisDomainMin={0}
                yAxisWidth={yAxisMax > 999 ? 45 : 40}
                thirdTooltipLabel='Loads'
                dataKeys={
                  [
                    'maxRate',
                    'averageRate',
                    'lowestRate',
                  ] as (keyof WeekLaneData)[]
                }
              />

              {laneHistory?.resultsBySource[laneTierHistory.source] && (
                <div className='flex flex-col'>
                  <Button
                    buttonNamePosthog={
                      isLoadsTableHidden
                        ? ButtonNamePosthog.QuickQuoteLaneHistoryShowTable
                        : ButtonNamePosthog.QuickQuoteLaneHistoryHideTable
                    }
                    type='button'
                    variant='ghost'
                    onClick={() =>
                      setHiddenLoadsTables((prev) => ({
                        ...prev,
                        [laneTierHistory.source]: !isLoadsTableHidden,
                      }))
                    }
                    className='w-[200px] h-8 mx-auto text-xs text-neutral-800 hover:border-neutral-700 hover:bg-neutral-100'
                  >
                    {isLoadsTableHidden ? (
                      <>
                        <ChevronDownIcon className='h-4 w-4 mr-1' />
                        Show Loads
                      </>
                    ) : (
                      <>
                        <ChevronUpIcon className='h-4 w-4 mr-1' />
                        Hide Loads
                      </>
                    )}
                  </Button>

                  {!isLoadsTableHidden && (
                    <>
                      <LaneHistoryLoadsTable
                        key={laneTierHistory.source}
                        loads={laneTierHistory.historyLoads}
                        source={laneTierHistory.source}
                        tenant={
                          tmsIntegrations?.find(
                            (i) => i.name === laneTierHistory.source
                          )?.tenant
                        }
                      />
                      <Divider className='border border-neutral-300 my-2' />
                    </>
                  )}
                </div>
              )}
            </>
          );
        }
      )}

      {/* Multi-stop lane history note */}
      {/* TODO: Multi-stop lane history ENG-3759 */}

      {/* Greenscreens natively supports multi-stop, but if there's lane history from TMS, let user know it's based on 2-stop data */}
      {quoteResponse &&
        quoteResponse.stops.length > 2 &&
        Object.keys(laneHistory.resultsBySource).some((key) =>
          Object.values(TMS).includes(key as TMS)
        ) && (
          <span className='flex flex-row bg-accent-100 rounded-lg space-x-1 py-1 mt-0'>
            <div>
              <Info className='h-4 w-4 pl-1' color='#969696' strokeWidth={2} />
            </div>
            <Typography variant='body-xs' className='text-neutral-600 italic'>
              TMS lane history is based on 2-stop data, from the first and last
              stop in the form. Multi-stop TMS lane history is coming soon.
            </Typography>
          </span>
        )}
    </>
  );
};
