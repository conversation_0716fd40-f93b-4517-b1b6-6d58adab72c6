import { useState } from 'react';

import { RefreshCw } from 'lucide-react';

import { Button } from 'components/Button';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { getLocations } from 'lib/api/getLocations';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';

export default function RefreshCarrierLocationsButton() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();
  const { tmsIntegrations } = useServiceFeatures();

  const handleRefresh = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent form submission/validation if used inside form
    setIsRefreshing(true);

    const res = await getLocations(tmsIntegrations[0]?.id ?? 0, true, true);
    if (res.isOk()) {
      toast({
        description: 'Successfully refreshed locations.',
        variant: 'success',
      });
    } else {
      toast({
        description: res.error.message || 'Failed to refresh locations.',
        variant: 'destructive',
      });
    }

    setIsRefreshing(false);
  };

  return (
    <Tooltip delayDuration={10}>
      <TooltipTrigger asChild>
        <Button
          onClick={handleRefresh}
          disabled={isRefreshing}
          type='button'
          variant='ghost'
          size='xs'
          buttonNamePosthog={ButtonNamePosthog.RefreshCarrierLocations}
        >
          {isRefreshing ? (
            <ButtonLoader />
          ) : (
            <RefreshCw className='h-4 w-4 text-brand-main hover:text-brand-600' />
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent className='mr-1'>
        <Typography className='text-sm text-neutral-700'>
          Refresh carrier locations
        </Typography>
      </TooltipContent>
    </Tooltip>
  );
}
