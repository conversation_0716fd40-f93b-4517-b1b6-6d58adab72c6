import { Checkbox } from 'components/Checkbox';
import { Flex } from 'components/layout';

interface SelectAllCarriersCheckboxProps {
  areAllCarriersSelected: boolean;
  onToggleSelectAllCarriers: () => void;
  disabled?: boolean;
}

export default function SelectAllCarriersCheckbox({
  areAllCarriersSelected,
  onToggleSelectAllCarriers,
  disabled = false,
}: SelectAllCarriersCheckboxProps) {
  const checkboxId = 'toggleAllCarriersInForm';

  return (
    <Flex align='center' className='hover:opacity-80'>
      <Checkbox
        id={checkboxId}
        checked={areAllCarriersSelected}
        onCheckedChange={onToggleSelectAllCarriers}
        aria-labelledby='toggleAllCarriersLabel'
        disabled={disabled}
      />
      <label
        htmlFor={checkboxId}
        id='toggleAllCarriersLabel'
        className='ml-2 text-xs font-medium text-neutral-700 cursor-pointer'
      >
        {areAllCarriersSelected
          ? 'Deselect All Available Carriers'
          : 'Select All Available Carriers'}
      </label>
    </Flex>
  );
}
