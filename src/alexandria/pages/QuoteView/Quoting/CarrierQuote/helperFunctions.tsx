import { Result } from 'neverthrow';

import { ValueType } from 'components/DebounceSelect';
import { toast } from 'hooks/useToaster';
import { CarrierGroup } from 'types/CarrierGroup';
import { Address as QuoteRequestAddress } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import {
  EmailCarriersError,
  EmailCarriersResponse,
} from 'types/api/EmailCarriersResult';

import { useHelperFunctions as QQHelperFunctions } from '../RequestQuickQuote/helperFunctions';

// Helper function to parse location input
export const parseLocation = (location: string) => {
  return QQHelperFunctions.parseLocation(location);
};

// Helper function to parse location string
export const parseLocationString = (
  locationString: Maybe<string>
): Partial<QuoteRequestAddress> => {
  if (!locationString) {
    return {};
  }

  const parts = locationString.split(',').map((part) => part.trim());
  let city: Maybe<string> = null;
  let state: Maybe<string> = null;
  let zip: Maybe<string> = null;

  // Regex for ZIP code (matches 5 digits or 5 digits-4 digits)
  const zipRegex = /^\d{5}(-\d{4})?$/;
  // Regex for state (matches 2 uppercase letters)
  const stateRegex = /^[A-Z]{2}$/;

  if (parts.length === 1) {
    const singlePart = parts[0];
    if (zipRegex.test(singlePart)) {
      zip = singlePart;
    } else {
      const cityStateParts = singlePart.split(' ');
      if (
        cityStateParts.length > 1 &&
        stateRegex.test(cityStateParts[cityStateParts.length - 1])
      ) {
        state = cityStateParts.pop() as string;
        city = cityStateParts.join(' ');
      } else {
        city = singlePart;
      }
    }
  } else if (parts.length >= 2) {
    city = parts[0];
    const stateAndZipPart = parts[1];
    const stateZipSplit = stateAndZipPart.split(' ');
    if (
      stateZipSplit.length > 1 &&
      zipRegex.test(stateZipSplit[stateZipSplit.length - 1])
    ) {
      zip = stateZipSplit.pop() as string;
      state = stateZipSplit.join(' ');
    } else if (stateRegex.test(stateAndZipPart)) {
      state = stateAndZipPart;
    } else if (zipRegex.test(stateAndZipPart)) {
      zip = stateAndZipPart;
    } else {
      state = stateAndZipPart;
    }
  }

  return { city: city ?? '', state: state ?? '', zip: zip ?? '' };
};

export const carrierGroupSearchHandler = ({
  value,
  carrierGroups,
}: {
  carrierGroups: Maybe<CarrierGroup[]>;
  value: string;
}): ValueType[] => {
  const allGroups = carrierGroups ?? [];
  if (!value) {
    return mapCarrierGroupsToAntdOptions(allGroups);
  }

  const filteredGroups = allGroups.filter((group) =>
    group.name?.toLowerCase().includes(value.toLowerCase())
  );
  return mapCarrierGroupsToAntdOptions(filteredGroups);
};

export const mapCarrierGroupsToAntdOptions = (
  groups: Maybe<CarrierGroup[]>
): ValueType[] => {
  if (!groups) return [];

  return groups.map((group: CarrierGroup) => {
    return {
      value: group.id,
      name: group.name,
      email: group.email ?? '',
      carriers: group.carriers || [],
      data: {
        ...group,
        carriers: group.carriers || [],
      },
      label: group.name,
    };
  });
};

export const handleEmailCarrierForQuoteResponse = (
  res: Result<EmailCarriersResponse, EmailCarriersError>,
  setLoading: (loading: boolean) => void
) => {
  if (res.isOk()) {
    toast({
      description: `Successfully emailed carriers 🎉  
      When carriers reply with a quote, the next time you open this email you will see their quotes here.`,
      variant: 'success',
      duration: 10000,
    });
    setLoading(false);
    return;
  } else {
    if (res.error.countSuccess == 0) {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
    } else {
      // Handle partial successes
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
    }
  }
};
