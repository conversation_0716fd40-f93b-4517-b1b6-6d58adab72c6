import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { ChevronDown, ChevronUp, Mail } from 'lucide-react';

import { Label } from 'components/Label';
import { TooltipProvider } from 'components/Tooltip';
import { Flex } from 'components/layout';
import { toast } from 'hooks/useToaster';
import { deleteLocation } from 'lib/api/deleteLocation';
import { TMSLocationWithDistance } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';

import CarrierSelectDropdown from './CarrierSelectDropdown';
import DeleteCarrierLocationModal from './DeleteCarrierLocationModal';
import SelectedCarrierTooltip from './SelectedCarrierTooltip';
import ShowMoreLessButton from './ShowMoreCarriers';
import { CarrierFormConfig, SelectedCarrierData } from './types';

interface CarrierSelectSectionProps {
  // TODO: refactor so that carrier groups don't need to be mapped to TMSLocationWithDistance
  carrierLocations: TMSLocationWithDistance[]; // list of carriers
  selectedCarriers: Record<string, boolean>;
  toggleSelection: (carrierId: string) => void;
  config: CarrierFormConfig;
}

const MAX_SELECTED_CARRIERS_DISPLAYED = 6;

export default function CarrierSelectSection({
  carrierLocations,
  selectedCarriers,
  toggleSelection,
  config,
}: CarrierSelectSectionProps) {
  const [showAllSelectedCarriers, setShowAllSelectedCarriers] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<TMSLocationWithDistance[]>(
    []
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [excludedCarrierIds, setExcludedCarrierIds] = useState<Set<string>>(
    new Set()
  );
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pendingDelete, setPendingDelete] = useState<
    Maybe<{
      id: number;
      externalTMSID: string;
      name?: string;
    }>
  >(null);

  const addExcludedCarrierId = useCallback((id: string) => {
    setExcludedCarrierIds((prev) => {
      const updatedExcludedCarrierIds = new Set(prev);
      updatedExcludedCarrierIds.add(id);
      return updatedExcludedCarrierIds;
    });
  }, []);

  const selectedCarriersData = useMemo(() => {
    const carriersData: SelectedCarrierData[] = [];
    Object.entries(selectedCarriers).forEach(([carrierId, isSelected]) => {
      if (isSelected) {
        const carrier = carrierLocations.find(
          (loc) => loc.externalTMSID === carrierId
        );
        if (carrier) {
          const carrierEmails: string[] = [];
          if (carrier.emails && carrier.emails.length > 0) {
            carrierEmails.push(
              ...carrier.emails.filter((e): e is string => !!e)
            );
          } else if (carrier.email) {
            carrierEmails.push(carrier.email);
          }

          if (carrierEmails.length > 0) {
            carriersData.push({
              id: carrierId,
              name:
                carrier.carrier?.name || carrier.name || `Carrier ${carrierId}`,
              city: carrier.city || null,
              state: carrier.state || null,
              emails: Array.from(new Set(carrierEmails)),
              notes: carrier.notes || null,
              milesDistance: carrier.milesDistance || null,
            });
          }
        }
      }
    });
    return carriersData;
  }, [selectedCarriers, carrierLocations]);

  const displayedSelectedCarriers = useMemo(() => {
    if (showAllSelectedCarriers) {
      return selectedCarriersData;
    }
    return selectedCarriersData.slice(0, MAX_SELECTED_CARRIERS_DISPLAYED);
  }, [selectedCarriersData, showAllSelectedCarriers]);

  const numberOfSelectedCarriers = useMemo(() => {
    return selectedCarriersData.length;
  }, [selectedCarriersData]);

  const availableCarriers = useMemo(() => {
    return carrierLocations.filter((carrier) => {
      const id = carrier.externalTMSID;
      if (id && selectedCarriers[id]) {
        return false;
      }
      if (id && excludedCarrierIds.has(id)) {
        return false;
      }
      return true;
    });
  }, [carrierLocations, selectedCarriers, excludedCarrierIds]);

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);

  useEffect(() => {
    const currentSearchTerm = searchTerm.trim().toLowerCase();
    if (currentSearchTerm === '') {
      setSearchResults(availableCarriers);
      return;
    }

    const filteredResults = availableCarriers.filter((carrier) => {
      const nameMatch =
        carrier.carrier?.name?.toLowerCase().includes(currentSearchTerm) ||
        carrier.name?.toLowerCase().includes(currentSearchTerm);
      const emailMatch =
        carrier.emails?.some((e) =>
          e.toLowerCase().includes(currentSearchTerm)
        ) || carrier.email?.toLowerCase().includes(currentSearchTerm);

      return !!(nameMatch || emailMatch);
    });
    setSearchResults(filteredResults);
  }, [searchTerm, availableCarriers]);

  const handleSearchTermChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const term = event.target.value;
      setSearchTerm(term);
      setIsDropdownOpen(true);
    },
    []
  );

  const handleSearchResultClick = useCallback(
    (carrierId: string) => {
      toggleSelection(carrierId);
      setSearchTerm('');
      setIsDropdownOpen(true);
    },
    [toggleSelection]
  );

  const handleDeleteSearchResult = useCallback(
    async (id: number, externalTMSID: string) => {
      const res = await deleteLocation(id);
      if (res.isOk()) {
        toast({ title: 'Location removed', variant: 'success' });
        addExcludedCarrierId(externalTMSID ?? '');
        return;
      }

      toast({
        title: 'Failed to remove location',
        description: res.error.message,
        variant: 'destructive',
      });
    },
    [addExcludedCarrierId]
  );

  const handleRequestDelete = useCallback(
    (id: number, externalTMSID: string) => {
      const loc = searchResults.find((l) => l.ID === id);
      setPendingDelete({
        id,
        externalTMSID,
        name: loc?.carrier?.name || loc?.name || undefined,
      });
      setIsConfirmDeleteOpen(true);
    },
    [searchResults]
  );

  const handleConfirmDelete = useCallback(async () => {
    if (!pendingDelete) {
      return;
    }

    setIsDeleting(true);
    await handleDeleteSearchResult(
      pendingDelete.id,
      pendingDelete.externalTMSID
    );
    setIsDeleting(false);
    setIsConfirmDeleteOpen(false);
    setPendingDelete(null);
  }, [pendingDelete, handleDeleteSearchResult]);

  const handleDropdownToggle = useCallback(() => {
    setIsDropdownOpen((prev) => !prev);
  }, []);

  const handleInputFocus = useCallback(() => {
    setIsDropdownOpen(true);
  }, []);

  const handleToggleShowAll = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();
      setShowAllSelectedCarriers((prev) => !prev);
    },
    []
  );

  return (
    <TooltipProvider>
      <Flex direction='col' gap='xs' className='w-full'>
        <Label name='to'>
          {numberOfSelectedCarriers === 0 ? (
            <EmptyDropdownLabel
              config={config}
              carrierLocationsLength={availableCarriers.length}
            />
          ) : (
            <SelectedCarriersDropdownLabel
              numberOfSelectedCarriers={numberOfSelectedCarriers}
            />
          )}
        </Label>
        <div
          ref={dropdownRef}
          className='w-full relative flex flex-wrap gap-1.5 border border-neutral-400 bg-neutral-input-bg rounded-[4px] p-1.5 text-xs items-center cursor-pointer'
        >
          {/* Selected carriers */}
          {displayedSelectedCarriers.map((carrier) => (
            <SelectedCarrierTooltip
              key={carrier.id}
              carrier={carrier}
              onRemove={toggleSelection}
            />
          ))}

          {/* Carrier search input */}
          <Flex align='center' gap='sm' justify='between' className='w-full'>
            <input
              type='text'
              value={searchTerm}
              onChange={handleSearchTermChange}
              onFocus={handleInputFocus}
              placeholder={
                displayedSelectedCarriers.length > 0
                  ? 'Add additional carriers to email'
                  : `Search carriers by name or email`
              }
              aria-label='Search for carriers to add to email list'
              className='grow p-1 outline-none bg-transparent hover:bg-neutral-input-bg rounded-[4px] text-xs placeholder-neutral-400 min-w-[150px] cursor-pointer'
            />
            {/* Carrier search dropdown toggle button */}
            <button
              type='button'
              aria-label={
                isDropdownOpen ? 'Close search results' : 'Open search results'
              }
              onClick={handleDropdownToggle}
              className={`p-0.5 text-neutral-800 ${
                isDropdownOpen
                  ? 'text-brand-main border hover:bg-neutral-100'
                  : 'hover:bg-neutral-100 border border-transparent'
              } focus:outline-none rounded-full`}
            >
              {isDropdownOpen ? (
                <ChevronUp size={16} />
              ) : (
                <ChevronDown size={16} />
              )}
            </button>
          </Flex>

          {/* Carrier search dropdown */}
          {isDropdownOpen && searchResults.length > 0 && (
            <CarrierSelectDropdown
              searchResults={searchResults}
              onCarrierSelect={handleSearchResultClick}
              isOpen={isDropdownOpen}
              onCarrierDelete={handleRequestDelete}
            />
          )}

          {selectedCarriersData.length > MAX_SELECTED_CARRIERS_DISPLAYED &&
            !searchTerm &&
            !isDropdownOpen && (
              <ShowMoreLessButton
                isShowingAll={showAllSelectedCarriers}
                totalCount={selectedCarriersData.length}
                displayedCount={MAX_SELECTED_CARRIERS_DISPLAYED}
                onToggle={handleToggleShowAll}
              />
            )}
        </div>

        <DeleteCarrierLocationModal
          open={isConfirmDeleteOpen}
          onOpenChange={setIsConfirmDeleteOpen}
          name={pendingDelete?.name}
          onConfirm={handleConfirmDelete}
          isLoading={isDeleting}
        />
      </Flex>
    </TooltipProvider>
  );
}

interface EmptyDropdownLabelProps {
  config: CarrierFormConfig;
  carrierLocationsLength: number;
}

const EmptyDropdownLabel: React.FC<EmptyDropdownLabelProps> = ({
  config,
  carrierLocationsLength,
}) => (
  <Flex align='center' gap='sm'>
    <span className='text-neutral-800 text-wrap'>
      {config.isFindCarrierByLocationEnabled
        ? `Contact up to ${carrierLocationsLength} carriers near pickup`
        : 'Select carriers to email'}
    </span>
    <span className='text-error-500'>*</span>
  </Flex>
);

interface SelectedCarriersDropdownLabelProps {
  numberOfSelectedCarriers: number;
}

const SelectedCarriersDropdownLabel: React.FC<
  SelectedCarriersDropdownLabelProps
> = ({ numberOfSelectedCarriers }) => (
  <Flex align='center' gap='xs'>
    Sending {numberOfSelectedCarriers === 1 ? 'email' : 'separate emails'} to{' '}
    <Flex align='center' gap='xs' className='text-brand-main'>
      {numberOfSelectedCarriers} <Mail size={14} />
    </Flex>
    {numberOfSelectedCarriers === 1 ? 'carrier' : 'carriers'}:
  </Flex>
);
