import React, { useEffect, useMemo, useState } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import { ProcessedAttachment } from 'components/AttachmentUpload';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { CarrierQuoteConfig } from 'contexts/serviceContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import {
  NewAttachment,
  emailCarriersForQuotes,
} from 'lib/api/emailCarriersForQuotes';
import fetchEmailTemplates from 'lib/api/fetchEmailTemplates';
import { getCustomers } from 'lib/api/getCustomers';
import { getQuoteRequest } from 'lib/api/getQuoteRequest';
import { searchLocations } from 'lib/api/searchLocations';
import FindCarrierForm from 'pages/QuoteView/Quoting/CarrierQuote/FindCarrierForm';
import MessageCarrierForm from 'pages/QuoteView/Quoting/CarrierQuote/MessageCarrierForm';
import {
  CarrierFormConfig,
  CarrierQuoteInputs,
} from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { Email } from 'types/Email';
import { EmailTemplate, TemplateType } from 'types/EmailTemplates';
import { TMSCustomer, TMSLocationWithDistance } from 'types/Load';
import { QuoteRequest } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import captureException from 'utils/captureException';
import getEmailAddress from 'utils/getEmailAddress';
import { nl2br } from 'utils/getJoditEditorHelpers';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
} from 'utils/loadInfoAndBuilding';

import {
  buildCQEmailTemplatesQuery,
  formatCQInlineVariable,
} from './emailTemplate';
import {
  handleEmailCarrierForQuoteResponse,
  parseLocation,
} from './helperFunctions';

interface RequestCarrierQuoteByLocationProps {
  email: Maybe<Email>;
  quoteRequest: Maybe<QuoteRequest>;
  setQuoteRequest: React.Dispatch<React.SetStateAction<Maybe<QuoteRequest>>>;
  // QuoteRequest may be null if there's a transient network error getting the parsed values. In those cases, still show the form
  carrierQuoteConfig: CarrierQuoteConfig;
}

export interface CarrierSelection {
  email: string;
  isSelected: boolean;
}

const hasDrumkitKeyword = (carrier: TMSLocationWithDistance): boolean => {
  if (!carrier) return false;

  const name = (carrier.carrier?.name || carrier.name || '').toLowerCase();
  const mainEmail = (carrier.email || '').toLowerCase();
  const otherEmails = (carrier.emails || [])
    .map((e) => (e || '').toLowerCase())
    .filter((e) => e);

  return (
    name.includes('drumkit') ||
    mainEmail.includes('drumkit') ||
    otherEmails.some((e) => e.includes('drumkit'))
  );
};

export default function RequestCarrierQuotesByLocation({
  email,
  quoteRequest,
  setQuoteRequest,
  carrierQuoteConfig,
}: RequestCarrierQuoteByLocationProps) {
  const { toast } = useToast();
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [initialCustomers, setInitialCustomers] =
    useState<Maybe<TMSCustomer[]>>(null);

  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);

  const [loading, setLoading] = useState(false);
  const [carrierLocations, setCarrierLocations] = useState<
    TMSLocationWithDistance[]
  >([]);
  const [showEmptyState, setShowEmptyState] = useState(false);
  const [showMessageCarrier, setShowMessageCarrier] = useState(false);
  const [selectedCarriers, setSelectedCarriers] = useState<
    Record<string, boolean>
  >({});
  const [newAttachments, setNewAttachments] = useState<ProcessedAttachment[]>(
    []
  );
  const [areAllCarriersSelected, setAreAllCarriersSelected] =
    useState<boolean>(false);
  const [lastSearchedLocation, setLastSearchedLocation] = useState<{
    city: string;
    state: string;
    zip: string;
  }>({ city: '', state: '', zip: '' });
  const { serviceID, tmsIntegrations } = useServiceFeatures();
  const [initialFromEmail, setInitialFromEmail] = useState<Maybe<string>>(
    carrierQuoteConfig.from ?? null
  );

  useEffect(() => {
    if (carrierLocations.length === 0) {
      setAreAllCarriersSelected(false);
      return;
    }

    const selectableCarriers = carrierLocations.filter(
      (carrier) => carrier.externalTMSID && !hasDrumkitKeyword(carrier)
    );

    if (selectableCarriers.length === 0) {
      setAreAllCarriersSelected(false);
      return;
    }

    const allActuallySelected = selectableCarriers.every(
      (carrier) => selectedCarriers[carrier.externalTMSID!] === true
    );

    setAreAllCarriersSelected(allActuallySelected);
  }, [carrierLocations, selectedCarriers]);

  // Early return if no TMS integrations are available
  useEffect(() => {
    if (!tmsIntegrations?.length) {
      return;
    }

    // Log warning if multiple TMS integrations are found in production
    if (tmsIntegrations.length > 1 && isProd()) {
      captureException(
        new Error(
          'Service with multiple TMS integrations trying to find carriers'
        ),
        { serviceID: serviceID, tmsIntegrations: tmsIntegrations }
      );
    }
  }, [tmsIntegrations, serviceID, toast]);

  const config: CarrierFormConfig = useMemo(() => {
    return {
      from: initialFromEmail || carrierQuoteConfig.from || '',
      cc: carrierQuoteConfig.cc ?? [],
      bcc: carrierQuoteConfig.bcc ?? [],
      bccCarriers: carrierQuoteConfig.bccCarriers,
      subject: carrierQuoteConfig.subject ?? '',
      emailBody: carrierQuoteConfig.emailBody ?? '',
      showCustomerSearch: carrierQuoteConfig.showCustomerSearch,
      showItemDescription: carrierQuoteConfig.showItemDescription,
      showDeliverySection: carrierQuoteConfig.showDeliverySection,
      requireDeliveryLocation: carrierQuoteConfig.requireDeliveryLocation,
      showPickupAddressLine1: carrierQuoteConfig.showPickupAddressLine1,
      showDeliveryAddressLine1: carrierQuoteConfig.showDeliveryAddressLine1,
      isFindCarrierByGroupEnabled:
        carrierQuoteConfig.isFindCarrierByGroupEnabled,
      isFindCarrierByLocationEnabled:
        carrierQuoteConfig.isFindCarrierByLocationEnabled,
    };
  }, [initialFromEmail, carrierQuoteConfig]);

  const handleRefreshCustomers = async () => {
    if (!tmsIntegrations?.length) {
      toast({
        description: 'No TMS integration available.',
        variant: 'destructive',
      });
      return;
    }
    const res = await getCustomers(tmsIntegrations[0].id, true);
    if (res.isOk()) {
      setInitialCustomers(res.value.customerList);
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
  };

  const handleResetCustomerSearch = () => {
    setCustomers(initialCustomers);
  };

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    if (!tmsIntegrations?.length) return [];
    return customerSearchHandler({
      tmsID: tmsIntegrations[0].id,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  const fetchUserEmail = async () => {
    try {
      const userEmail = await getEmailAddress();
      if (userEmail) {
        setInitialFromEmail(userEmail);
        // Update form defaults after fetching email
        findCarrierFormMethods.setValue('from', userEmail);
        messageCarrierFormMethods.setValue('from', userEmail);
      }
    } catch (error) {
      captureException(error, { context: 'fetchUserEmail' });
    }
  };

  useEffect(() => {
    if (tmsIntegrations?.length) {
      const fetchCustomers = async () => {
        const res = await getCustomers(tmsIntegrations[0].id);
        if (res.isOk()) {
          setInitialCustomers(res.value.customerList);
          setCustomers(res.value.customerList);
        }
      };
      fetchCustomers();
    }
  }, [tmsIntegrations]);

  const defaultValues = useMemo<CarrierQuoteInputs>(() => {
    const baseValues: CarrierQuoteInputs = {
      // Load details
      customerId: null,
      itemDescription: '',
      commodity: null,
      transportType: null,
      weightLbs: 0,
      weightUnit: 'lbs',

      // Pickup details
      pickupLocation: {
        name: '',
        addressLine1: '',
        addressLine2: '',
        zip: '',
        city: '',
        state: '',
        country: 'USA',
        location: '',
      },
      pickupStartDate: null,
      pickupEndDate: null,
      mileRadius: 50,

      // Delivery details
      deliveryLocation: {
        name: '',
        addressLine1: '',
        addressLine2: '',
        zip: '',
        city: '',
        state: '',
        country: 'USA',
        location: '',
      },
      deliveryStartDate: null,
      deliveryEndDate: null,

      // Email details
      from: config.from,
      cc: config.cc,
      bcc: config.bcc,
      subject: config.subject,
      emailBody: nl2br(config.emailBody), // Replace newlines with <br> tags

      // Carrier details
      carriers: [],
      carrierEmails: [],
      selectedExistingAttachments: [],
    };

    if (quoteRequest) {
      return {
        ...baseValues,
        transportType: quoteRequest.transportType || baseValues.transportType,
        commodity: quoteRequest.commodity || baseValues.commodity,
        weightLbs: quoteRequest.weightLbs || baseValues.weightLbs,
        weightUnit: quoteRequest.weightUnit || baseValues.weightUnit,
        pickupStartDate:
          quoteRequest.pickupStartDate || baseValues.pickupStartDate,
        pickupEndDate: quoteRequest.pickupEndDate || baseValues.pickupEndDate,
        deliveryStartDate:
          quoteRequest.deliveryStartDate || baseValues.deliveryStartDate,
        deliveryEndDate:
          quoteRequest.deliveryEndDate || baseValues.deliveryEndDate,
        pickupLocation: {
          name: quoteRequest.pickupLocation?.name || '',
          addressLine1: quoteRequest.pickupLocation?.addressLine1 || '',
          addressLine2: quoteRequest.pickupLocation?.addressLine2 || '',
          zip: quoteRequest.pickupLocation?.zip || '',
          city: quoteRequest.pickupLocation?.city || '',
          state: quoteRequest.pickupLocation?.state || '',
          country: quoteRequest.pickupLocation?.country || 'USA',
          location:
            quoteRequest.pickupLocation?.zip ||
            (quoteRequest.pickupLocation?.city &&
            quoteRequest.pickupLocation?.state
              ? `${quoteRequest.pickupLocation.city}, ${quoteRequest.pickupLocation.state}`
              : ''),
        },
        deliveryLocation: {
          name: quoteRequest.deliveryLocation?.name || '',
          addressLine1: quoteRequest.deliveryLocation?.addressLine1 || '',
          addressLine2: quoteRequest.deliveryLocation?.addressLine2 || '',
          zip: quoteRequest.deliveryLocation?.zip || '',
          city: quoteRequest.deliveryLocation?.city || '',
          state: quoteRequest.deliveryLocation?.state || '',
          country: quoteRequest.deliveryLocation?.country || 'USA',
          location:
            quoteRequest.deliveryLocation?.zip ||
            (quoteRequest.deliveryLocation?.city &&
            quoteRequest.deliveryLocation?.state
              ? `${quoteRequest.deliveryLocation.city}, ${quoteRequest.deliveryLocation.state}`
              : ''),
        },
        carriers: [],
      };
    }

    return baseValues;
  }, [quoteRequest, config]);

  // Define forms and state for both search methods
  // Form for finding carriers by location
  const findCarrierFormMethods = useForm<CarrierQuoteInputs>({
    defaultValues,
    mode: 'onChange',
    resolver: async (values) => {
      const errors: Record<string, any> = {};

      // Validate pickup location
      if (!values.pickupLocation?.location) {
        errors.pickupLocation = {
          location: {
            type: 'required',
            message: 'Pickup location is required',
          },
        };
      }

      // Validate mile radius
      if (!values.mileRadius) {
        errors.mileRadius = {
          type: 'required',
          message: 'Search radius is required',
        };
      } else if (values.mileRadius < 1) {
        errors.mileRadius = {
          type: 'min',
          message: 'Search radius must be at least 1 mile',
        };
      }

      if (
        config.requireDeliveryLocation &&
        !values.deliveryLocation?.location
      ) {
        errors.deliveryLocation = {
          location: {
            type: 'required',
            message: 'Delivery location is required',
          },
        };
      }

      return {
        values: values as any, // Preserve user input for correction
        errors: Object.keys(errors).length > 0 ? errors : {},
      };
    },
  });

  // Form for messaging carriers
  const messageCarrierFormMethods = useForm<CarrierQuoteInputs>({
    defaultValues,
    mode: 'onChange',
  });

  // Fetch user's email if not provided in config
  useEffect(() => {
    if (!initialFromEmail) {
      fetchUserEmail();
    }
  }, [initialFromEmail]);

  // Find carriers handler for FindCarrierForm button
  const onSubmitFindCarriers = async (data: CarrierQuoteInputs) => {
    if (!tmsIntegrations?.length) {
      toast({
        description: 'No TMS integration available.',
        variant: 'destructive',
      });
      return;
    }
    setLoading(true);
    // Reset carrier selections
    setSelectedCarriers({});
    messageCarrierFormMethods.setValue('carrierEmails', []);

    if (data.customerId) {
      messageCarrierFormMethods.setValue('customerId', data.customerId);
    }

    // Parse the pickup location input to extract city, state, and zip
    const pickupLocationInput = data.pickupLocation.location || '';
    const parsedPickupLocation = parseLocation(pickupLocationInput);

    // Store the last searched location
    if (!parsedPickupLocation) {
      toast({
        description: 'Invalid pickup location',
        variant: 'destructive',
      });
      return;
    }
    setLastSearchedLocation(parsedPickupLocation);

    // Update both the form values and the data object with the parsed pickup location data
    messageCarrierFormMethods.setValue(
      'pickupLocation.city',
      parsedPickupLocation.city
    );
    messageCarrierFormMethods.setValue(
      'pickupLocation.state',
      parsedPickupLocation.state
    );
    messageCarrierFormMethods.setValue(
      'pickupLocation.zip',
      parsedPickupLocation.zip
    );
    data.pickupLocation.city = parsedPickupLocation.city;
    data.pickupLocation.state = parsedPickupLocation.state;
    data.pickupLocation.zip = parsedPickupLocation.zip;

    // Parse the delivery location input to extract city, state, and zip
    const deliveryLocationInput = data.deliveryLocation?.location || '';
    const parsedDeliveryLocation = parseLocation(deliveryLocationInput);

    // Delivery is optional
    if (parsedDeliveryLocation) {
      // Update both the form values and the data object with the parsed delivery location data
      messageCarrierFormMethods.setValue(
        'deliveryLocation.city',
        parsedDeliveryLocation.city
      );
      messageCarrierFormMethods.setValue(
        'deliveryLocation.state',
        parsedDeliveryLocation.state
      );
      messageCarrierFormMethods.setValue(
        'deliveryLocation.zip',
        parsedDeliveryLocation.zip
      );
      if (data.deliveryLocation) {
        data.deliveryLocation.city = parsedDeliveryLocation.city;
        data.deliveryLocation.state = parsedDeliveryLocation.state;
        data.deliveryLocation.zip = parsedDeliveryLocation.zip;
      }
    }

    const searchRes = await searchLocations(
      tmsIntegrations[0].id,
      undefined, // key required as part of interface (for name and addressLine1 searches)
      undefined, // value also required to maintain backwards compatibility with existing searchLocations API
      data.mileRadius ?? 0,
      parsedPickupLocation.city,
      parsedPickupLocation.state,
      parsedPickupLocation.zip
    );

    let searchedLocations: TMSLocationWithDistance[] = [];
    if (searchRes.isOk()) {
      searchedLocations = searchRes.value.locationList;
      setCarrierLocations(searchedLocations);
      messageCarrierFormMethods.setValue('carriers', searchedLocations);

      // Initialize selectedCarriers: do NOT select any by default
      const initialSelected: Record<string, boolean> = {};

      // Mark all as false (unselected)
      searchedLocations.forEach((carrier) => {
        if (carrier.externalTMSID) {
          initialSelected[carrier.externalTMSID] = false;
        }
      });

      setSelectedCarriers(initialSelected);
      messageCarrierFormMethods.setValue(
        'carrierEmails',
        [] // No emails selected by default
      );

      // If we searched with ZIP code, update city and state from the API response
      if (parsedPickupLocation.zip && searchRes.value.location) {
        const resolvedCity = searchRes.value.location.city;
        const resolvedState = searchRes.value.location.state;

        messageCarrierFormMethods.setValue('pickupLocation.city', resolvedCity);
        messageCarrierFormMethods.setValue(
          'pickupLocation.state',
          resolvedState
        );

        // Update the pickup location with resolved values for email subject
        data.pickupLocation.city = resolvedCity;
        data.pickupLocation.state = resolvedState;
      }

      if (searchedLocations.length > 0) {
        toast({
          description: `Found ${searchedLocations.length}
          carrier${searchedLocations.length === 1 ? '' : 's'} near
          ${messageCarrierFormMethods.getValues('pickupLocation.city') || lastSearchedLocation.zip}, ${messageCarrierFormMethods.getValues('pickupLocation.state')}`,
          variant: 'success',
        });
        setShowEmptyState(false);
        setShowMessageCarrier(true);
      } else {
        setShowEmptyState(true);
        toast({
          description: `No carriers found near ${messageCarrierFormMethods.getValues('pickupLocation.city') || lastSearchedLocation.zip}, ${messageCarrierFormMethods.getValues('pickupLocation.state')}`,
          variant: 'destructive',
        });
      }
    }

    if (searchRes.isErr()) {
      toast({
        description: searchRes.error.message,
        variant: 'destructive',
      });
      setLoading(false);
      return;
    }

    // Construct email after carriers are found and location is resolved
    const carrierQuoteEmailTemplateQuery = buildCQEmailTemplatesQuery({
      carrierFormConfig: config,
      carrierProcessingData: {
        ...findCarrierFormMethods.getValues(),
        pickupLocation: {
          ...findCarrierFormMethods.getValues('pickupLocation'),
          city: messageCarrierFormMethods.getValues('pickupLocation.city'),
          state: messageCarrierFormMethods.getValues('pickupLocation.state'),
          zip: messageCarrierFormMethods.getValues('pickupLocation.zip'),
        },
        itemDescription: formatCQInlineVariable(
          findCarrierFormMethods.getValues('itemDescription')
        ),
      },
    });

    const emailTemplatesResponse =
      await fetchEmailTemplates<TemplateType.CARRIER_QUOTE_BY_LOCATION>({
        templateType: TemplateType.CARRIER_QUOTE_BY_LOCATION,
        carrierQuoteQuery: carrierQuoteEmailTemplateQuery,
      });
    if (emailTemplatesResponse.isOk()) {
      setEmailTemplates(
        emailTemplatesResponse.value.carrierQuoteEmailTemplates
      );
    }

    setLoading(false);
  };

  const toggleSelection = (carrierId: string) => {
    setSelectedCarriers((prevSelectedCarriers) => {
      const newSelectedCarriers = {
        ...prevSelectedCarriers,
        [carrierId]: !prevSelectedCarriers[carrierId],
      };

      const selectedEmailsArray = carrierLocations
        .filter(
          (carrier) =>
            carrier.externalTMSID && newSelectedCarriers[carrier.externalTMSID]
        )
        .flatMap(
          (carrier) => carrier.emails || (carrier.email ? [carrier.email] : [])
        )
        .filter((email): email is string => !!email)
        .map((email) => email.trim());
      messageCarrierFormMethods.setValue(
        'carrierEmails',
        Array.from(new Set(selectedEmailsArray))
      );
      // areAllCarriersSelected will be updated by its useEffect
      return newSelectedCarriers;
    });
  };

  const handleToggleSelectAllCarriers = () => {
    const newGlobalSelectedState = !areAllCarriersSelected;
    const newSelectedCarriersMap: Record<string, boolean> = {};
    const newCarrierEmailsList: string[] = [];

    carrierLocations.forEach((carrier) => {
      if (carrier.externalTMSID) {
        if (!hasDrumkitKeyword(carrier)) {
          // Only affect non-drumkit carriers for selection
          newSelectedCarriersMap[carrier.externalTMSID] =
            newGlobalSelectedState;

          if (newGlobalSelectedState) {
            const emails =
              carrier.emails || (carrier.email ? [carrier.email] : []);
            emails.forEach((email) => {
              if (email) newCarrierEmailsList.push(email.trim());
            });
          }
        } else {
          // Ensure drumkit carriers are explicitly deselected by this action
          newSelectedCarriersMap[carrier.externalTMSID] = false;
        }
      }
    });

    setSelectedCarriers(newSelectedCarriersMap);
    messageCarrierFormMethods.setValue(
      'carrierEmails',
      Array.from(new Set(newCarrierEmailsList)) // Emails are derived from newCarrierEmailsList populated above
    );
    // areAllCarriersSelected will be updated by its useEffect
  };

  // onSubmitMessageCarriers calls backend endpoint that sends emails to selected carriers
  const onSubmitMessageCarriers: SubmitHandler<CarrierQuoteInputs> = async (
    data
  ) => {
    setLoading(true);

    const formattedAttachments: NewAttachment[] = newAttachments.map(
      (attachment) => ({
        data: attachment.data,
        fileName: attachment.fileName,
        mimeType: attachment.mimeType,
      })
    );

    // Process cc emails - convert from comma-separated string to array of strings if needed
    const processedCcEmails = Array.isArray(data.cc)
      ? data.cc
      : typeof data.cc === 'string'
        ? (data.cc as string)
            .split(',')
            .map((email: string) => email.trim())
            .filter((email: string) => email !== '')
        : [];

    const response = await emailCarriersForQuotes(
      {
        ...data,
        id: quoteRequest?.id ?? 0,
        threadID: email?.threadID ?? '',
        emailID: email?.id ?? 0,
        pallets: quoteRequest?.pallets ?? [],
        carrierNetworkEmails: null,
        carriers: data.carriers,
        from: data.from,
        carrierGroupId: undefined,
      },
      data.carrierEmails,
      processedCcEmails,
      data.subject,
      data.emailBody,
      data.selectedExistingAttachments,
      formattedAttachments
    );

    handleEmailCarrierForQuoteResponse(response, setLoading);

    // Refresh the Quote Request so the UI immediately shows the "Review Responses" section
    if (response.isOk() && email?.threadID) {
      const res = await getQuoteRequest(email.threadID);
      setQuoteRequest(res.isOk() ? res.value : null);
    }

    setLoading(false);
  };

  return (
    <Grid gap='xl' className='mx-0 w-full'>
      <ExtendedFormProvider aiDefaultValues={true}>
        {/* Form for finding carriers */}
        <FormProvider {...findCarrierFormMethods}>
          <form
            onSubmit={findCarrierFormMethods.handleSubmit(onSubmitFindCarriers)}
            className='grid grid-cols-1 mx-0 w-full'
          >
            <FindCarrierForm
              loading={loading}
              config={config}
              customers={customers}
              handleRefreshCustomers={handleRefreshCustomers}
              handleResetCustomerSearch={handleResetCustomerSearch}
              handleCustomerSearch={handleCustomerSearch}
              tmsIntegrations={tmsIntegrations}
            />
          </form>
        </FormProvider>

        {/* Empty state message when no carriers found */}
        {!loading && carrierLocations.length === 0 && showEmptyState && (
          <Flex
            direction='col'
            align='center'
            justify='center'
            className='mt-4 p-3 rounded-lg border border-brand-main'
          >
            <div className='text-md text-neutral-800 font-semibold mb-2 text-center'>
              No carriers found
            </div>
            <Typography variant='body-sm' className='text-neutral-600'>
              We couldn't find any carrier locations near{' '}
              {messageCarrierFormMethods.getValues('pickupLocation.city') ||
                lastSearchedLocation.zip}
              , {messageCarrierFormMethods.getValues('pickupLocation.state')}.
              Try adjusting your search radius or location.
            </Typography>
            <Typography
              variant='body-xs'
              className='text-neutral-600 italic mt-2'
            >
              If you recently updated locations in Turvo you may need to refresh
              carrier locations.
            </Typography>
          </Flex>
        )}

        {/* Form for messaging carriers, only shown after carriers are found */}
        {showMessageCarrier && carrierLocations.length > 0 && (
          <FormProvider {...messageCarrierFormMethods}>
            <form
              onSubmit={messageCarrierFormMethods.handleSubmit(
                onSubmitMessageCarriers
              )}
              className='grid grid-cols-1 mx-0 w-full'
            >
              <MessageCarrierForm
                email={email}
                emailTemplates={emailTemplates}
                loading={loading}
                carrierLocations={carrierLocations}
                selectedCarriers={selectedCarriers}
                toggleSelection={toggleSelection}
                setNewAttachments={setNewAttachments}
                config={config}
                areAllCarriersSelected={areAllCarriersSelected}
                onToggleSelectAllCarriers={handleToggleSelectAllCarriers}
              />
            </form>
          </FormProvider>
        )}
      </ExtendedFormProvider>
    </Grid>
  );
}
