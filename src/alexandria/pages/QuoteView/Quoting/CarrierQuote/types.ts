import { ProcessedAttachment } from 'components/AttachmentUpload';
import { Attachment } from 'types/EmailAttachment';
import { TMSLocationWithDistance } from 'types/Load';
import { Address, TransportType } from 'types/QuoteRequest';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';

export interface CarrierFormConfig {
  from: string;
  cc: string[];
  bcc: string[];
  bccCarriers: boolean;
  subject: string;
  emailBody: string;
  isFindCarrierByGroupEnabled: boolean;
  isFindCarrierByLocationEnabled: boolean;
  showCustomerSearch: boolean;
  showItemDescription: boolean;
  showDeliverySection: boolean;
  requireDeliveryLocation: boolean;
  showPickupAddressLine1: boolean;
  showDeliveryAddressLine1: boolean;
}

export interface CarrierQuoteInputs {
  // Load details
  customerId: Maybe<string>;
  itemDescription: Maybe<string>;
  commodity: Maybe<string>;
  transportType: Maybe<TransportType>;
  weightLbs: Maybe<number>;
  weightUnit: Maybe<string>;

  // Pickup details
  pickupLocation: Address & { location: Maybe<string> };
  pickupStartDate: Maybe<Date>;
  pickupEndDate: Maybe<Date>;
  mileRadius: Maybe<number>;

  // Delivery details
  deliveryLocation: Address & { location: Maybe<string> };
  deliveryStartDate: Maybe<Date>;
  deliveryEndDate: Maybe<Date>;

  // Email details
  from: string;
  cc: string[] | string;
  bcc: string[];
  subject: string;
  emailBody: string;
  emailTemplateId?: Maybe<string>;
  // Attachments user selects to include from original customer's (i.e. shipper's) email
  selectedExistingAttachments: Attachment[];

  // Carrier details
  carriers: TMSLocationWithDistance[];
  carrierEmails: string[];

  // New processed attachments
  processedAttachments?: ProcessedAttachment[];

  carrierGroupId?: Maybe<string | number>;
}

export interface CarrierSelection {
  email: string;
  isSelected: boolean;
}

export interface MappedQuote {
  carrierEmails: string[];
  threadID: string;
  bounced: boolean;
  responded: boolean;
  available: MaybeUndef<boolean>;
  price: MaybeUndef<number>;
  notes: MaybeUndef<string>;
  url: string; // URL-encoded
}

export interface SelectedCarrierData {
  id: string;
  name: string;
  city: Maybe<string>;
  state: Maybe<string>;
  emails: string[];
  notes: Maybe<string>;
  milesDistance: Maybe<number>;
}
