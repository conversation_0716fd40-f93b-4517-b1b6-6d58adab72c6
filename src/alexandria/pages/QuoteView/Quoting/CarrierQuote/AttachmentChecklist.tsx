import { useFormContext } from 'react-hook-form';

import { Checkbox } from 'components/Checkbox';
import { Label } from 'components/Label';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import useFetchEmailAttachments from 'hooks/useFetchEmailAttachments';
import { CarrierQuoteInputs } from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { Email } from 'types/Email';
import { Attachment } from 'types/EmailAttachment';
import { Maybe } from 'types/UtilityTypes';
import { cn } from 'utils/shadcn';

type AttachmentChecklistProps = {
  email: Maybe<Email>;
};

const AttachmentChecklist: React.FC<AttachmentChecklistProps> = ({ email }) => {
  const { response, isLoading } = useFetchEmailAttachments(email?.externalID);

  if (isLoading) {
    return (
      <Flex justify='center' gap='xs'>
        <Typography variant='body-sm' className='text-neutral-500'>
          Fetching attachments...
        </Typography>
        <ButtonLoader />
      </Flex>
    );
  }

  if (!response?.attachments?.length) {
    return (
      <Typography variant='body-xs' className='text-neutral-600 italic mb-1'>
        No attachments available from customer email.
      </Typography>
    );
  }

  const { watch, setValue } = useFormContext<CarrierQuoteInputs>();
  const selectedAttachments = new Set(
    watch('selectedExistingAttachments', []).map((att) => att.externalId)
  );

  const attachments = response.attachments;

  // Group attachments by MIME type
  const groupedAttachments = attachments.reduce<Record<string, Attachment[]>>(
    (acc, attachment) => {
      if (!acc[attachment.mimeType]) {
        acc[attachment.mimeType] = [];
      }
      acc[attachment.mimeType].push(attachment);
      return acc;
    },
    {}
  );

  const allAttachmentIds = new Set(
    attachments.map((attachment) => attachment.externalId)
  );
  const allSelected =
    allAttachmentIds.size > 0 &&
    [...allAttachmentIds].every((id) => selectedAttachments.has(id));

  const handleToggleAll = () => {
    setValue('selectedExistingAttachments', allSelected ? [] : attachments);
  };

  const handleToggleMimeType = (mimeType: string) => {
    const mimeTypeAttachments = groupedAttachments[mimeType];
    const isSelected = mimeTypeAttachments.every((att) =>
      selectedAttachments.has(att.externalId)
    );

    const newSelections = new Set(selectedAttachments);
    if (isSelected) {
      mimeTypeAttachments.forEach((att) =>
        newSelections.delete(att.externalId)
      );
    } else {
      mimeTypeAttachments.forEach((att) => newSelections.add(att.externalId));
    }

    setValue(
      'selectedExistingAttachments',
      attachments.filter((att) => newSelections.has(att.externalId))
    );
  };

  const handleToggleAttachment = (attachment: Attachment) => {
    const newSelections = new Set(selectedAttachments);
    if (newSelections.has(attachment.externalId)) {
      newSelections.delete(attachment.externalId);
    } else {
      newSelections.add(attachment.externalId);
    }

    setValue(
      'selectedExistingAttachments',
      attachments.filter((att) => newSelections.has(att.externalId))
    );
  };

  return (
    <>
      <Flex direction='col' gap='sm' className='mt-2'>
        <Typography variant='h5' className='text-neutral-800'>
          Include attachments from customer email
        </Typography>
        <Typography variant='body-xs' className='text-neutral-800 italic mb-1'>
          Selected items will be attached to email to carriers, not embedded
          inline.
        </Typography>
      </Flex>

      <div className='w-full p-4 border rounded-lg shadow-md'>
        <Flex align='center' className='mb-4'>
          <Checkbox
            id='select-all'
            checked={allSelected}
            onCheckedChange={handleToggleAll}
          />
          <Label
            name='selectAll'
            htmlFor='select-all'
            className='ml-2 font-semibold text-xs'
          >
            Select All
          </Label>
        </Flex>

        {Object.entries(groupedAttachments).map(([mimeType, files]) => {
          const allMimeSelected = files.every((file) =>
            selectedAttachments.has(file.externalId)
          );

          return (
            <div key={mimeType} className='mb-3 border-t pt-2'>
              <Flex align='center'>
                <Checkbox
                  id={`select-all-${mimeType}`}
                  checked={allMimeSelected}
                  onCheckedChange={() => handleToggleMimeType(mimeType)}
                />
                <Label
                  name='allMimeTypes'
                  htmlFor={`select-all-${mimeType}`}
                  className='ml-2 font-semibold text-sm'
                >
                  {`All ${mimeType.split('/')?.[1].toUpperCase()}s`}
                </Label>
              </Flex>

              <ul className='ml-6 mt-2'>
                {files.map((attachment) => (
                  <li
                    key={attachment.externalId}
                    className='flex items-center my-1'
                  >
                    <Checkbox
                      id={attachment.externalId}
                      checked={selectedAttachments.has(attachment.externalId)}
                      onCheckedChange={() => handleToggleAttachment(attachment)}
                    />
                    <Flex
                      align='center'
                      className='mx-2 text-xs space-x-1 w-full'
                    >
                      {/* Truncated filename with tooltip */}
                      <Typography
                        className={cn(
                          'truncate inline-block cursor-help text-xs',
                          attachment.isInline ? 'w-3/5' : 'w-4/5'
                        )}
                        title={attachment.originalFileName} // Tooltip on hover displays full name
                      >
                        {attachment.originalFileName}
                      </Typography>

                      {/* Inline indicator */}
                      {attachment.isInline && (
                        <Typography className='text-neutral-500 text-xs'>
                          (Inline)
                        </Typography>
                      )}
                    </Flex>
                  </li>
                ))}
              </ul>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default AttachmentChecklist;
