import { Button } from 'components/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from 'components/Dialog';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';

interface DeleteCarrierLocationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  name?: string;
  onConfirm: () => void | Promise<void>;
  isLoading?: boolean;
}

export default function DeleteCarrierLocationModal({
  open,
  onOpenChange,
  name,
  onConfirm,
  isLoading,
}: DeleteCarrierLocationModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <Flex direction='col' gap='sm' className='p-4'>
          <DialogTitle>Delete Carrier?</DialogTitle>
          <DialogDescription>
            <Typography variant='body-sm'>
              Are you sure you want to remove{' '}
              <Typography variant='body-xs' weight='bold'>
                {name || 'this location'}
              </Typography>
              <Typography variant='body-sm'>from Drumkit?</Typography>
              <Typography variant='body-sm' className='mt-2' weight='medium'>
                This will NOT delete from your TMS.
              </Typography>
              <Typography
                variant='body-xs'
                textColor='muted'
                className='italic'
              >
                This action cannot be undone.
              </Typography>
            </Typography>
          </DialogDescription>
          <Flex direction='row' gap='sm' className='w-full mt-4' justify='end'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onOpenChange(false)}
              buttonNamePosthog={null}
              className='border-black'
            >
              Cancel
            </Button>
            <Button
              variant='destructive'
              size='sm'
              onClick={onConfirm}
              disabled={!!isLoading}
              buttonNamePosthog={null}
            >
              {isLoading ? 'Deleting…' : 'Delete'}
            </Button>
          </Flex>
        </Flex>
      </DialogContent>
    </Dialog>
  );
}
