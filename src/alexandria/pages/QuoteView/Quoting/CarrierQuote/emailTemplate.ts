import { Address } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import { titleCase } from 'utils/formatStrings';

import { CarrierFormConfig, CarrierQuoteInputs } from './types';

type CQEmailTemplateProps = {
  carrierFormConfig: CarrierFormConfig;
  carrierProcessingData: CarrierQuoteInputs;
};

// Builds the query string for carrier quote template requests.
export const buildCQEmailTemplatesQuery = ({
  carrierFormConfig,
  carrierProcessingData,
}: CQEmailTemplateProps): string => {
  const {
    pickupLocation,
    pickupStartDate,
    pickupEndDate,
    deliveryLocation,
    deliveryStartDate,
    deliveryEndDate,
    transportType,
    itemDescription,
  } = carrierProcessingData;

  const params = new URLSearchParams();

  // Locations
  params.append(
    'pickupLocation',
    formatAddress(pickupLocation, carrierFormConfig.showPickupAddressLine1)
  );
  params.append(
    'deliveryLocation',
    formatAddress(deliveryLocation, carrierFormConfig.showDeliveryAddressLine1)
  );

  // Time ranges
  if (pickupStartDate) {
    params.append(
      'pickupStartTime',
      new Date(pickupStartDate).toLocaleDateString('en-US', timeOptions)
    );
  }
  if (pickupEndDate) {
    params.append(
      'pickupEndTime',
      new Date(pickupEndDate).toLocaleDateString('en-US', timeOptions)
    );
  }
  if (deliveryStartDate) {
    params.append(
      'deliveryStartTime',
      new Date(deliveryStartDate).toLocaleDateString('en-US', timeOptions)
    );
  }
  if (deliveryEndDate) {
    params.append(
      'deliveryEndTime',
      new Date(deliveryEndDate).toLocaleDateString('en-US', timeOptions)
    );
  }

  // Transport type
  if (transportType) {
    params.append('transportType', transportType.toLowerCase());
  }

  // Item description
  if (itemDescription) {
    params.append('itemDescription', formatCQInlineVariable(itemDescription));
  }

  return params.toString();
};

export const timeOptions: Intl.DateTimeFormatOptions = {
  hour: 'numeric',
  minute: 'numeric',
  hour12: true,
};

export const formatCQInlineVariable = (text: Maybe<string>): string => {
  if (!text) return '';

  return text.includes('\n') ? text.replace(/\n/g, '<br>') : text;
};

export const formatAddress = (
  address: Maybe<Address>,
  showAddressLine1: boolean = false
): string => {
  if (!address) return '';

  const { addressLine1, city, state, zip } = address;
  const addressOutputParts: string[] = [];

  if (showAddressLine1 && addressLine1) {
    addressOutputParts.push(addressLine1);
  }

  const cityStr = city ? titleCase(city) : null;
  const stateStr = state || null;
  const zipStr = zip || null;

  let locationString = '';
  if (cityStr && stateStr) {
    locationString = `${cityStr}, ${stateStr}`;
    if (zipStr) {
      locationString += ` ${zipStr}`;
    }
  } else if (cityStr && zipStr) {
    locationString = `${cityStr} ${zipStr}`;
  } else if (stateStr && zipStr) {
    locationString = `${stateStr} ${zipStr}`;
  } else if (cityStr) {
    locationString = cityStr;
  } else if (stateStr) {
    locationString = stateStr;
  } else if (zipStr) {
    locationString = zipStr;
  }

  if (locationString) {
    addressOutputParts.push(locationString);
  }

  return addressOutputParts.join(', ');
};
