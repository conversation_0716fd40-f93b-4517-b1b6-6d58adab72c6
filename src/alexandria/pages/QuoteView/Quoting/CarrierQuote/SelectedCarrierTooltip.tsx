import { Mail, X } from 'lucide-react';

import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';

import { SelectedCarrierData } from './types';

interface SelectedCarrierTooltipProps {
  carrier: SelectedCarrierData;
  onRemove: (carrierId: string) => void;
}

export default function SelectedCarrierTooltip({
  carrier,
  onRemove,
}: SelectedCarrierTooltipProps) {
  const handleRemove = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    onRemove(carrier.id);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      e.stopPropagation();
      onRemove(carrier.id);
    }
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Flex
          align='center'
          gap='sm'
          tabIndex={0}
          aria-label={`Selected carrier: ${carrier.name}. Click to see details or press X to remove.`}
          className='max-w-full border border-neutral-400 hover:bg-neutral-100 text-neutral-800 px-2 py-1 rounded-md cursor-default'
        >
          <Typography variant='body-xs' className='truncate'>
            {carrier.name}
          </Typography>
          <button
            type='button'
            aria-label={`Remove ${carrier.name}`}
            onClick={handleRemove}
            onKeyDown={handleKeyDown}
            className='text-neutral-600 hover:text-neutral-900 focus:outline-none hover:cursor-pointer'
          >
            <X size={14} />
          </button>
        </Flex>
      </TooltipTrigger>
      <TooltipContent>
        <div className='max-w-[280px]'>
          <Typography
            variant='body-sm'
            weight='semibold'
            className='text-neutral-800 truncate'
          >
            {carrier.name}
          </Typography>

          {carrier.city && carrier.state && (
            <Flex justify='between' align='center' gap='lg' className='mb-1'>
              <Typography variant='body-xs' className='text-neutral-400'>
                {carrier.city}, {carrier.state}
              </Typography>
              {carrier.milesDistance && carrier.milesDistance > 0 && (
                <Typography variant='body-xs' className='text-neutral-800'>
                  ~{carrier.milesDistance.toFixed(1)} mi from pickup
                </Typography>
              )}
            </Flex>
          )}

          {carrier.emails.length > 0 && (
            <Flex
              direction='col'
              gap='xs'
              className='py-1 mt-1 border-t border-neutral-300'
            >
              {carrier.emails.map((email, index) => (
                <Typography
                  variant='body-xs'
                  key={index}
                  className='flex items-center gap-1'
                >
                  <Mail size={10} />
                  {email}
                </Typography>
              ))}
            </Flex>
          )}

          {carrier.notes && (
            <div className='py-1 border-t border-neutral-200'>
              <Typography
                variant='body-xs'
                weight='semibold'
                className='text-neutral-600 mb-0.5'
              >
                Note:
              </Typography>
              <Typography variant='body-xs' className='text-neutral-500 italic'>
                {carrier.notes}
              </Typography>
            </div>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  );
}
