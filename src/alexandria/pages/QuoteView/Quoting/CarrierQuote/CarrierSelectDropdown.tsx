import { Info, Trash2 } from 'lucide-react';

import { But<PERSON> } from 'components/Button';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { TMSLocationWithDistance } from 'types/Load';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';

interface CarrierSelectDropdownProps {
  searchResults: TMSLocationWithDistance[];
  onCarrierSelect: (carrierId: string) => void;
  isOpen: boolean;
  onCarrierDelete?: (id: number, externalTMSID: string) => void;
}

export default function CarrierSelectDropdown({
  searchResults,
  onCarrierSelect,
  isOpen,
  onCarrierDelete,
}: CarrierSelectDropdownProps) {
  if (!isOpen || searchResults.length === 0) {
    return null;
  }

  const handleCarrierClick = (carrierId: string) => {
    onCarrierSelect(carrierId);
  };

  return (
    <div
      aria-label='Carrier search results'
      className='absolute top-full left-0 right-0 mt-1 z-10 bg-neutral-50 border border-neutral-400 rounded-[4px] shadow-lg max-h-80 overflow-y-auto w-full'
    >
      {searchResults.map((searchResult, index) => {
        const isFirst = index === 0;
        const isLast = index === searchResults.length - 1;
        let classNames =
          'p-2 hover:bg-brand-50 border-y border-neutral-200 hover:border-brand-main cursor-pointer text-xs';
        if (isFirst) {
          classNames += ' border-t-0';
        }
        if (isLast) {
          classNames += ' border-b-0';
        }

        return (
          <div key={searchResult.ID} className={classNames}>
            <Flex justify='between' align='stretch' gap='sm'>
              {/* Select carrier location button */}
              <button
                type='button'
                aria-label={`Select ${searchResult.carrier?.name || searchResult.name || 'carrier'}`}
                className='w-[80%] pr-1 text-left rounded-[4px] focus:outline-none focus:ring-1 focus:ring-brand-main cursor-pointer'
                onClick={() => {
                  if (searchResult.externalTMSID) {
                    handleCarrierClick(searchResult.externalTMSID);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    if (searchResult.externalTMSID) {
                      handleCarrierClick(searchResult.externalTMSID);
                    }
                  }
                }}
              >
                <Flex justify='between' align='start'>
                  <div className='grow'>
                    <Typography variant='body-sm' weight='medium'>
                      {searchResult.carrier?.name || searchResult.name}
                    </Typography>
                    {(searchResult.city || searchResult.state) && (
                      <Typography
                        variant='body-xs'
                        className='text-neutral-400'
                      >
                        {searchResult.city}
                        {searchResult.city && searchResult.state ? ', ' : ''}
                        {searchResult.state}
                      </Typography>
                    )}
                  </div>

                  {typeof searchResult.milesDistance === 'number' &&
                    searchResult.milesDistance > 0 && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Flex align='center' className='ml-2 shrink-0'>
                            <Typography variant='body-xs'>
                              {searchResult.milesDistance.toFixed(1)} mi
                            </Typography>
                            <Info
                              size={12}
                              className='ml-1 text-neutral-500 group-hover:text-neutral-800'
                            />
                          </Flex>
                        </TooltipTrigger>
                        <TooltipContent side='top' align='end'>
                          <Typography variant='body-xs'>
                            Approx. distance from pickup
                          </Typography>
                        </TooltipContent>
                      </Tooltip>
                    )}
                </Flex>

                <Typography
                  variant='body-sm'
                  className='mt-0.5 text-neutral-600'
                >
                  {searchResult.emails?.join(', ') || searchResult.email}
                </Typography>
                {searchResult.notes && (
                  <Typography
                    variant='body-xs'
                    textColor='muted'
                    className='mt-1 italic'
                    title={searchResult.notes}
                  >
                    Note: {searchResult.notes}
                  </Typography>
                )}
              </button>

              {/* Delete carrier location button */}
              <Button
                buttonNamePosthog={ButtonNamePosthog.DeleteCarrierLocation}
                type='button'
                variant='destructive'
                size='sm'
                aria-label={`Remove ${searchResult.carrier?.name || searchResult.name || 'carrier'} from suggestions`}
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  if (searchResult.ID && onCarrierDelete) {
                    onCarrierDelete(
                      searchResult.ID,
                      searchResult.externalTMSID
                    );
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    e.stopPropagation();
                    if (searchResult.ID && onCarrierDelete) {
                      onCarrierDelete(
                        searchResult.ID,
                        searchResult.externalTMSID
                      );
                    }
                  }
                }}
                className='shrink-0 rounded-[4px] text-neutral-500 hover:bg-error-300 hover:text-error-700 focus:outline-none focus:ring-1 focus:ring-error-300 flex items-center justify-center h-full min-h-[24px]'
              >
                <Trash2 size={14} />
              </Button>
            </Flex>
          </div>
        );
      })}
    </div>
  );
}
