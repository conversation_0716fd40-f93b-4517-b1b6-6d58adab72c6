import { Controller, useFormContext } from 'react-hook-form';

import { DebounceSelect, ValueType } from 'components/DebounceSelect';
import { Label } from 'components/Label';
import { Flex } from 'components/layout/Flex';
import { Typography } from 'components/typography/Typography';
import { EmailTemplate, EmailTemplateAccessTier } from 'types/EmailTemplates';
import { Maybe } from 'types/UtilityTypes';

type CarrierQuoteEmailTemplateSelectProps = {
  emailTemplates: EmailTemplate[];
  handleEmailTemplateSelection: (selectedTemplate: ValueType) => void;
};

export default function CarrierQuoteEmailTemplateSelect({
  emailTemplates,
  handleEmailTemplateSelection,
}: CarrierQuoteEmailTemplateSelectProps) {
  const { control } = useFormContext();

  return (
    <Flex direction='col' gap='xs' className='w-full'>
      <Label name='emailTemplateId'>Email Template</Label>
      <Controller
        name='emailTemplateId'
        control={control}
        render={({ field }) => (
          <DebounceSelect
            showSearch
            className='w-full max-w-full h-10 text-sm border-neutral-300'
            placeholder='Select an email template'
            optionFilterProp='children'
            fetchOptions={() => Promise.resolve([])}
            onChange={(value) => {
              field.onChange(value.value);
              handleEmailTemplateSelection(value);
            }}
            value={field.value}
            options={mapEmailTemplatesToAntdOptions(emailTemplates)}
            optionRender={(option) => {
              const emailTemplate = option.data as EmailTemplate;
              return (
                <Flex direction='col'>
                  <Typography variant='body-sm' weight='semibold'>
                    {emailTemplate.name}
                  </Typography>
                  {emailTemplate.subject && (
                    <Typography variant='body-xs' className='text-neutral-500'>
                      {emailTemplate.subject}
                    </Typography>
                  )}
                </Flex>
              );
            }}
            notFoundContent={
              <Typography variant='body-sm' className='text-neutral-500'>
                No templates found
              </Typography>
            }
          />
        )}
      />
    </Flex>
  );
}

export const mapEmailTemplatesToAntdOptions = (
  emailTemplates: Maybe<EmailTemplate[]>
): ValueType[] => {
  if (!emailTemplates) return [];

  const tierLabelMap = {
    [EmailTemplateAccessTier.GENERIC]: 'Generic',
    [EmailTemplateAccessTier.USER]: 'Personal',
    [EmailTemplateAccessTier.GROUP]: 'User Group',
    [EmailTemplateAccessTier.SERVICE]: 'Company',
  };

  // Group templates by access tier
  const groupedTemplates = emailTemplates.reduce(
    (groups, template, index) => {
      const accessTier =
        template.templateAccessTier || EmailTemplateAccessTier.GENERIC;
      const tierLabel = tierLabelMap[accessTier];

      if (!groups[accessTier]) {
        groups[accessTier] = {
          label: tierLabel,
          options: [],
        };
      }

      groups[accessTier].options.push({
        value: `template-${index}`,
        label: template.name || 'Unnamed Template',
        ...template,
      });

      return groups;
    },
    {} as Record<string, { label: string; options: ValueType[] }>
  );

  return Object.values(groupedTemplates);
};
