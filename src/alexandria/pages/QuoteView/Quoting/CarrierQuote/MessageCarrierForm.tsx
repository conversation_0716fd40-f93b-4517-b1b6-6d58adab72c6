import { useEffect, useRef } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore react-quill is in the parent dir
import JoditEditor from 'jodit-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { IJodit } from 'jodit/esm/types';

import AttachmentUpload, {
  ProcessedAttachment,
} from 'components/AttachmentUpload';
import { Button } from 'components/Button';
import { ValueType } from 'components/DebounceSelect';
import { Label } from 'components/Label';
import { Textarea } from 'components/Textarea';
import { Input } from 'components/input/Input';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import AttachmentChecklist from 'pages/QuoteView/Quoting/CarrierQuote/AttachmentChecklist';
import {
  CarrierFormConfig,
  CarrierQuoteInputs,
} from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { Email } from 'types/Email';
import { EmailTemplate, EmailTemplateAccessTier } from 'types/EmailTemplates';
import { TMSLocationWithDistance } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { getJoditEditorConfig, nl2br } from 'utils/getJoditEditorHelpers';

import CarrierQuoteEmailTemplateSelect, {
  mapEmailTemplatesToAntdOptions,
} from './CarrierQuoteEmailTemplateSelect';
import CarrierSelectSection from './CarrierSelectSection';
import SelectAllCarriersCheckbox from './SelectAllCarriersCheckbox';

// import EligibleCarrierList from './EligibleCarrierList';

interface MessageCarrierFormProps {
  email: Maybe<Email>;
  emailTemplates: EmailTemplate[];
  loading: boolean;
  carrierLocations: TMSLocationWithDistance[];
  selectedCarriers: Record<string, boolean>;
  toggleSelection: (carrierId: string) => void;
  setNewAttachments: (files: ProcessedAttachment[]) => void;
  config: CarrierFormConfig;
  areAllCarriersSelected: boolean;
  onToggleSelectAllCarriers: () => void;
}

export default function MessageCarrierForm({
  email,
  emailTemplates,
  loading,
  carrierLocations,
  selectedCarriers,
  toggleSelection,
  setNewAttachments,
  config,
  areAllCarriersSelected,
  onToggleSelectAllCarriers,
}: MessageCarrierFormProps) {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext<CarrierQuoteInputs>();

  const joditRef = useRef<Maybe<IJodit>>(null);

  // Handle email template selection and populate form fields
  const handleEmailTemplateSelection = (selectedTemplate: ValueType) => {
    if (!selectedTemplate) {
      return;
    }

    const selectedIsGeneric =
      selectedTemplate.templateAccessTier === EmailTemplateAccessTier.GENERIC;

    const emailTemplate = selectedIsGeneric
      ? emailTemplates?.find(
          (t) => t.templateAccessTier === EmailTemplateAccessTier.GENERIC
        )
      : emailTemplates?.find((t) => t.name === selectedTemplate.label);

    if (emailTemplate) {
      setValue('subject', nl2br(emailTemplate.subject));
      setValue('emailBody', nl2br(emailTemplate.body));

      if (emailTemplate.cc?.length) {
        setValue('cc', emailTemplate.cc?.join(', ') || '');
      }
    }
  };

  useEffect(() => {
    if (emailTemplates.length > 0) {
      const templateGroups = mapEmailTemplatesToAntdOptions(emailTemplates);
      const initialTemplate = templateGroups.find((t) => t.options.length > 0)
        ?.options[0];

      if (initialTemplate) {
        handleEmailTemplateSelection(initialTemplate);
        setValue('emailTemplateId', initialTemplate.value);
      }
    }
  }, [emailTemplates, setValue]);

  return (
    <Grid gap='md' className='w-full mx-0 mt-2'>
      <Flex direction='col' gap='xs' className='mt-1'>
        <Typography
          variant='h6'
          className='text-neutral-800 whitespace-nowrap'
          weight='semibold'
        >
          Email Carriers
        </Typography>
        <Typography variant='body-xs' className='text-neutral-800 italic mb-1'>
          Sending as separate emails to each carrier.
        </Typography>

        <Flex direction='col' gap='md' className='max-w-full'>
          <Flex direction='col' gap='xs' className='w-full'>
            <Label name='from'>From:</Label>
            <Controller
              name='from'
              control={control}
              rules={{
                required: { value: true, message: 'Required' },
                minLength: {
                  value: 5,
                  message: 'Minimum length is 5 characters',
                },
              }}
              render={({ field: { ref, onChange, value } }) => (
                <Input
                  type='text'
                  value={value}
                  onChange={onChange}
                  ref={ref}
                  placeholder='<EMAIL>'
                />
              )}
            />
            <ErrorMessage
              errors={errors}
              name='from'
              render={({ message }: { message: string }) => (
                <Typography variant='body-xs' className='text-error-500'>
                  {message}
                </Typography>
              )}
            />
          </Flex>

          {/* Sending to */}
          <Flex direction='col' gap='xs' className='w-full'>
            {carrierLocations.length > 0 && (
              <SelectAllCarriersCheckbox
                areAllCarriersSelected={areAllCarriersSelected}
                onToggleSelectAllCarriers={onToggleSelectAllCarriers}
                disabled={carrierLocations.length === 0}
              />
            )}
            <CarrierSelectSection
              carrierLocations={carrierLocations}
              selectedCarriers={selectedCarriers}
              toggleSelection={toggleSelection}
              config={config}
            />
          </Flex>

          {emailTemplates?.length > 1 && (
            <CarrierQuoteEmailTemplateSelect
              emailTemplates={emailTemplates}
              handleEmailTemplateSelection={handleEmailTemplateSelection}
            />
          )}

          <Flex direction='col' gap='xs' className='w-full'>
            <Label name='subject'>Subject</Label>
            <Controller
              name='subject'
              control={control}
              rules={{
                required: { value: true, message: 'Required' },
                minLength: {
                  value: 5,
                  message: 'Minimum length is 5 characters',
                },
              }}
              render={({ field: { ref, onChange, value } }) => (
                <Textarea
                  className='min-h-[20px]'
                  value={value}
                  onChange={onChange}
                  ref={ref}
                  rows={Math.max(2, value.split('\n').length || 1)}
                />
              )}
            />
            <ErrorMessage
              errors={errors}
              name='subject'
              render={({ message }: { message: string }) => (
                <Typography variant='body-xs' className='text-error-500'>
                  {message}
                </Typography>
              )}
            />
          </Flex>

          <Flex direction='col' gap='xs' className='w-full'>
            <Label name='cc'>Cc:</Label>
            <Controller
              name='cc'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type='text'
                  placeholder='Comma-separated emails (optional)'
                />
              )}
            />
          </Flex>

          <Flex direction='col' gap='xs'>
            <Label name='emailBody'>Body</Label>
            <Controller
              name='emailBody'
              control={control}
              rules={{
                required: { value: true, message: 'Required' },
                minLength: {
                  value: 20,
                  message: 'Minimum length is 20 characters',
                },
              }}
              render={({ field: { onChange, value } }) => {
                return (
                  <JoditEditor
                    className='min-h-80 mb-6'
                    config={getJoditEditorConfig({
                      refToUpdateAfterInit: joditRef,
                    })}
                    value={value}
                    onBlur={(e: any) => onChange(e)}
                  />
                );
              }}
            />
            <ErrorMessage
              errors={errors}
              name='emailBody'
              render={({ message }: { message: string }) => (
                <Typography variant='body-xs' className='text-error-500'>
                  {message}
                </Typography>
              )}
            />
          </Flex>
        </Flex>
      </Flex>

      <Flex direction='col' gap='xs' className='my-3'>
        <Typography variant='h6' weight='semibold'>
          Attachments
        </Typography>
        <AttachmentChecklist email={email} />

        <Flex
          direction='col'
          gap='sm'
          className='border-t border-neutral-400 pt-2'
        >
          <Typography
            variant='body-xs'
            className='text-neutral-800 font-medium'
          >
            Upload new files
          </Typography>
          <AttachmentUpload onFilesChange={setNewAttachments} />
        </Flex>
      </Flex>

      <Button
        buttonNamePosthog={ButtonNamePosthog.EmailCarriers}
        type='submit'
        className='w-full'
        disabled={loading}
      >
        {loading ? <ButtonLoader /> : ButtonText.EmailCarriers}
      </Button>
    </Grid>
  );
}
