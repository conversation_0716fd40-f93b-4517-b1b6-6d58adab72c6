import { useState } from 'react';

import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { CarrierGroup } from 'types/CarrierGroup';
import { TMSCarrier } from 'types/Load';

type DisplayCarrierGroupProps = {
  group: CarrierGroup;
  carriers: TMSCarrier[];
};

const MAX_CARRIERS_DISPLAYED = 6;

export default function DisplayCarrierGroup({
  group,
  carriers,
}: DisplayCarrierGroupProps) {
  const [showAllCarriers, setShowAllCarriers] = useState(false);

  const handleToggleShowAllCarriers = () => {
    setShowAllCarriers((prev) => !prev);
  };

  const carriersToDisplay = showAllCarriers
    ? carriers
    : carriers.slice(0, MAX_CARRIERS_DISPLAYED);

  return (
    <div className='w-full mt-4 p-3 border border-brand-main rounded bg-neutral-50'>
      <Typography variant='h5' weight='semibold' className='mb-0.5'>
        {group.name || 'Unnamed Group'}
      </Typography>

      {group.email && (
        <div className='mb-2 text-sm'>
          <span className='text-xs text-neutral-600'>Group Email:</span>{' '}
          {group.email}
        </div>
      )}

      {carriers.length > 0 ? (
        <>
          <Typography variant='body-xs'>Contacts: {carriers.length}</Typography>
          <ul className='list-disc pl-3.5 space-y-1'>
            {carriersToDisplay.map((carrier: TMSCarrier, index: number) => (
              <li
                key={
                  carrier.externalTMSID || carrier.email || `carrier-${index}`
                }
                className='text-xs'
              >
                <Flex direction='col'>
                  <span className='text-neutral-900'>
                    {carrier.name || 'Unknown Carrier'}
                  </span>
                  {carrier.email && (
                    <span className='text-neutral-600'>{carrier.email}</span>
                  )}
                </Flex>
              </li>
            ))}
          </ul>
          {carriers.length > MAX_CARRIERS_DISPLAYED && (
            <button
              onClick={handleToggleShowAllCarriers}
              className='mt-2 text-xs text-brand-main hover:underline focus:outline-none'
              aria-label={
                showAllCarriers ? 'Hide extra carriers' : 'Show all carriers'
              }
              type='button'
            >
              {showAllCarriers
                ? 'Hide'
                : `Show more (${carriers.length - MAX_CARRIERS_DISPLAYED} more)`}
            </button>
          )}
        </>
      ) : (
        <div className='text-sm text-neutral-500 mb-4'>
          No carriers found in this group.
        </div>
      )}
    </div>
  );
}
