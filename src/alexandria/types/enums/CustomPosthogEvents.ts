enum ButtonNamePosthog {
  // AI Suggestion Clicks
  ApptConfirmationSuggestionClick = 'appointment_confirmation_suggestion_click',
  CarrierInfoSuggestionClick = 'carrier_info_suggestion_click',
  CheckCallSuggestionClick = 'check_call_suggestion_click',
  CycleCheckCallSuggestion = 'cycle_check_call_suggestion',
  QuickQuoteSuggestionClick = 'quick_quote_suggestion_click',

  // AI Suggestion Skips
  // SkipApptSchedulingSuggestion = 'skip_appt_scheduling_suggestion', // TODO: Implement PostHog event
  // SkipCarrierInfoSuggestion = 'skip_carrier_info_suggestion',       // TODO: Implement PostHog event
  SkipCheckCallSuggestion = 'skip_check_call_suggestion',

  // Appointment Scheduling
  ConfirmSlotApptScheduling = 'confirm_slot_appt_scheduling',
  FindOpenApptSlots = 'find_open_appt_slots',
  ShowSmartReply = 'show_smart_reply',
  SearchE2openCompany = 'search_e2open_company',
  SearchE2openOperations = 'search_e2open_operations',
  ValidateE2openPRONumber = 'validate_e2open_pro_number',
  ValidateCostcoPRONumber = 'validate_costco_pro_number',
  ValidateManhattanPONumber = 'validate_manhattan_po_number',
  ValidateRetalixPONumbers = 'validate_retalix_po_numbers',
  SubmitE2openAppt = 'submit_e2open_appointment',
  SubmitManhattanAppt = 'submit_manhattan_appointment',
  SubmitRetalixAppt = 'submit_retalix_appointment',
  RetalixAddPO = 'retalix_add_po',
  SwitchApptType = 'switch_appt_type',
  ApptSchedulingCopyEmailDraft = 'appt_scheduling_copy_email_draft',
  UpdateTMSLoadWithAppointment = 'update_tms_load_with_appointment',
  EmailSchedulingCopyEmailDraft = 'email_scheduling_copy_email_draft',

  // Carrier Quote
  AddCarrierNetworkQuoteDraft = 'add_carrier_network_quote_draft',
  FindCarriers = 'find_carriers',
  EmailCarriers = 'email_carriers',
  DraftEmailForCarrierGroup = 'draft_email_for_carrier_group',
  CreateDraftReplyToCustomer = 'create_draft_reply_to_customer',
  AddReplyToCurrentDraftToCustomer = 'add_reply_to_current_draft_to_customer',
  CarrierQuoteCopySellPrice = 'carrier_quote_copy_sell_price',
  CarrierQuoteCopyLinehaulPrice = 'carrier_quote_copy_linehaul_price',
  CarrierQuoteDeleteAttachment = 'carrier_quote_delete_attachment',
  CarrierQuoteToggleDetails = 'carrier_quote_toggle_details',
  RemoveAttachment = 'carrier_quote_remove_attachment',
  CopyDraftResponse = 'carrier_quote_copy_draft_response',
  DeleteCarrierLocation = 'delete_carrier_location',

  // Load Building
  BuildLoad = 'build_load',
  ClearForm = 'clear_form',
  LoadBuildingDateInput = 'load_building_date_input',
  LoadBuildingLocationNameVsStreet = 'load_building_location_name_vs_street',
  LoadBuildingSuggestionClick = 'load_building_suggestion_click',
  LoadBuildingTimeInput = 'load_building_time_input',
  LoadBuildingAddMcleodReferenceNumber = 'load_building_mcleod_add_reference_number',
  LoadBuildingEditMcleodReferenceNumber = 'load_building_mcleod_edit_reference_number',
  LoadBuildingCreateMcleodLocation = 'load_building_mcleod_create_location',
  LoadBuildingEditMcleodLocation = 'load_building_mcleod_edit_location',
  LoadBuildingCopyLoadID = 'load_building_copy_load_id',

  // Load Info
  SearchLoad = 'search_load',
  UpdateTMS = 'update_tms',
  ShowAllRouteStops = 'show_all_route_stops',

  // Miscellaneous
  // RefreshCustomers = 'refresh_customers', // TODO: Implement PostHog event
  RefreshCarrierLocations = 'refresh_locations',
  RefreshE2openCache = 'refresh_e2open_cache',
  // RefreshOperators = 'refresh_operators', // TODO: Implement PostHog event
  ToggleExpandableContent = 'toggle_expandable_content',
  ToggleLaneHistoryTier = 'toggle_lane_history_tier',
  ToggleDatePicker = 'toggle_date_picker',
  CarrierVerificationInvite = 'carrier_verification_invite',

  // Outbox
  DeleteCarrierEmail = 'delete_carrier_email',
  // GoToOutboxTab = 'go_to_outbox', // TODO: Implement PostHog event
  UndoDeleteCarrierEmail = 'undo_delete_carrier_email',
  UpdateCarrierEmail = 'update_carrier_email',

  // Quick Quote
  // AddQuickQuoteDraft = 'add_quick_quote_draft', // TODO: Implement PostHog event
  GetQuickQuote = 'get_quick_quote',
  ToggleQuoteDetails = 'toggle_quote_details',
  AddQuoteDateDetails = 'add_quote_date_details',
  CopyQuoteToClipboard = 'copy_quote_to_clipboard',
  CreateDraftReply = 'create_draft_reply',
  AddReplyToCurrentDraft = 'add_reply_to_current_draft',
  SubmitQuoteToTMS = 'submit_quote_to_tms',
  SubmitQuoteViaURL = 'submit_quote_via_url',
  SubmitLineHaulToPortal = 'submit_line_haul_to_portal',
  SubmitTotalToPortal = 'submit_total_to_portal',
  QuickQuoteCopySellPrice = 'quick_quote_copy_sell_price',
  QuickQuoteCopyLinehaulPrice = 'quick_quote_copy_linehaul_price',
  EnableDATForUser = 'enable_dat_for_user',
  UpdateDefaultProfit = 'update_default_profit',
  UpdateDefaultStopFee = 'update_default_stop_fee',
  ExpandRouteDetails = 'expand_route_details',
  QuickQuoteAddStop = 'quick_quote_add_stop',
  QuickQuoteRemoveStop = 'quick_quote_remove_stop',
  QuickQuoteLaneHistoryShowTable = 'quick_quote_lane_history_show_table',
  QuickQuoteLaneHistoryHideTable = 'quick_quote_lane_history_hide_table',
  QuickQuotePullDATLaneHistory = 'quick_quote_pull_dat_lane_history',
  QuickQuoteUpdateRateView = 'quick_quote_update_rateview',

  // Batch Quote
  GetBatchQuote = 'get_batch_quote',
  BatchQuoteCopyQuoteToClipboard = 'batch_quote_copy_quote_to_clipboard',
  BatchQuoteCreateDraftReply = 'batch_quote_create_draft_reply',
  BatchQuoteAddReplyToCurrentDraft = 'batch_quote_add_reply_to_current_draft',
  BatchQuoteAddLane = 'batch_quote_add_lane',
  BatchQuoteAddLanesUsingAI = 'batch_quote_add_lanes_using_ai',

  // Track & Trace
  GoToTrackAndTraceTab = 'go_to_track_and_trace',
  ScheduleCarrierEmails = 'schedule_carrier_emails',
  ScheduleShipperEmails = 'schedule_shipper_emails',
  SubmitCheckCall = 'submit_check_call',
  SubmitException = 'submit_exception',
  SubmitNote = 'submit_note',
  CarrierQuoteCopyEmailDraft = 'carrier_quote_copy_email_draft',

  // Truck Lists
  CreatePickupGroupForTruckList = 'create_pickup_group_for_truck_list',
  CreateTruckForTruckList = 'create_truck_for_truck_list',
  CreateTruckList = 'create_truck_list',
  DeleteTruckFromTruckList = 'delete_truck_from_truck_list',
  IngestEmail = 'ingest_email',
  MoveTruckToExistingPickupGroup = 'move_truck_to_existing_pickup_group',
  MoveTruckToNewPickupGroup = 'move_truck_to_new_pickup_group',
  SubmitTruckList = 'submit_truck_list',
  ValidateTruckListCarrier = 'validate_truck_list_carrier',
  TruckListToggleOverrideDropoff = 'truck_list_toggle_override_dropoff',
  TruckListToggleTruckDetails = 'truck_list_toggle_truck_details',

  // Titlebar
  SwitchToLoadsView = 'switch_to_loads_view',
  SwitchToQuoteView = 'switch_to_quote_view',
  HideDrumkit = 'hide_drumkit',
  ProfileDropdown = 'profile_dropdown',
  HelpCenterDialog = 'help_center_dialog',

  // Error Boundary
  RefreshPage = 'refresh_page',
}

enum StatusResponseEventsPosthog {
  SubmitSuccess = 'submit_success',
  SubmitError = 'submit_error',
}

export { ButtonNamePosthog, StatusResponseEventsPosthog };
