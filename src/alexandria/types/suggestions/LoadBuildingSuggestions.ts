import { NormalizedLoad } from 'types/Load';

import {
  GenericSuggestionCore,
  SuggestionCategories,
  SuggestionPipelines,
} from './CoreSuggestions';

export type SuggestedLoad = Pick<
  NormalizedLoad,
  | 'mode'
  | 'moreThanTwoStops'
  | 'poNums'
  | 'notes'
  | 'customer'
  | 'pickup'
  | 'consignee'
  | 'rateData'
  | 'specifications'
  | 'additionalReferences'
  | 'commodities'
>;

export type AppliedLoad = Pick<
  NormalizedLoad,
  | 'mode'
  | 'moreThanTwoStops'
  | 'poNums'
  | 'notes'
  | 'customer'
  | 'pickup'
  | 'consignee'
  | 'rateData'
  | 'specifications'
  | 'additionalReferences'
  | 'commodities'
>;

export type LoadBuildingSuggestions = GenericSuggestionCore & {
  pipeline: SuggestionPipelines.LoadBuilding;
  category: SuggestionCategories.LoadBuilding;
  suggested: SuggestedLoad;
  applied: AppliedLoad;
};

export type LoadBuildingChanges = {
  'consignee.addressLine1': string;
  'consignee.addressLine2': string;
  'consignee.apptEndTime': string;
  'consignee.apptNote': string;
  'consignee.apptStartTime': string;
  'consignee.apptType': string;
  'consignee.city': string;
  'consignee.contact': string;
  'consignee.country': string;
  'consignee.email': string;
  'consignee.mustDeliver': string;
  'consignee.name': string;
  'consignee.phone': string;
  'consignee.refNumber': string;
  'consignee.state': string;
  'consignee.zipCode': string;
  'customer.addressLine1': string;
  'customer.addressLine2': string;
  'customer.city': string;
  'customer.contact': string;
  'customer.country': string;
  'customer.email': string;
  // 'customer.externalTMSID': string;
  'customer.name': string;
  'customer.phone': string;
  'customer.refNumber': string;
  'customer.state': string;
  'customer.zipCode': string;
  mode: string;
  // notes: string;
  'pickup.addressLine1': string;
  'pickup.addressLine2': string;
  'pickup.apptEndTime': string;
  'pickup.apptNote': string;
  'pickup.apptStartTime': string;
  'pickup.apptType': string;
  'pickup.city': string;
  'pickup.contact': string;
  'pickup.country': string;
  'pickup.email': string;
  'pickup.name': string;
  'pickup.phone': string;
  'pickup.readyTime': string;
  'pickup.refNumber': string;
  'pickup.state': string;
  'pickup.zipCode': string;
  'rateData.carrierRateNumUnits': string;
  'rateData.carrierLineHaulRate': string;
  'rateData.carrierLineHaulCharge': string;
  'rateData.carrierRateType': string;
  'rateData.customerRateNumUnits': string;
  'rateData.customerLineHaulRate': string;
  'rateData.customerLineHaulCharge': string;
  'rateData.customerRateType': string;
  'specifications.commodities': string;
  'specifications.totalOutPalletCount': number;
  'specifications.totalPieces.value': number;
  'specifications.totalWeight.value': number;
  'specifications.planningComment': string;
  transportType: string;
  additionalReferences: string;
  commoditiesArray: string;
};
