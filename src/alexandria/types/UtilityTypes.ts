// common helpful utility types
export type Maybe<T> = T | null;
export type Undef<T> = T | undefined;
export type MaybeUndef<T> = T | null | undefined;
export type ObjectData = { [key: string]: any };

// Reference: https://www.raygesualdo.com/posts/flattening-object-keys-with-typescript-types/
export type FlattenObjectKeys<
  T extends Record<string, unknown>,
  Key = keyof T,
> = Key extends string
  ? T[Key] extends Record<string, unknown>
    ? `${Key}.${FlattenObjectKeys<T[Key]>}`
    : `${Key}`
  : never;
