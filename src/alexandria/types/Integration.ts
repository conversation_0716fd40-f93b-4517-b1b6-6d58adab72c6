import { SchedulingPortals } from 'types/Appointment';
import { CarrierVerification, Quoting, TMS } from 'types/enums/Integrations';
import { IntegrationType } from 'types/enums/Integrations';

export type IntegrationCore = {
  id: number;
  name: TMS | Quoting | SchedulingPortals | CarrierVerification;
  type: IntegrationType;
  username?: string;
  tenant: string;
  note?: string;
  featureFlags: IntegrationFeatureFlags;
};

export type IntegrationFeatureFlags = {
  // Some services want to restrict access to write to certain TMS fields,
  // e.g. Mcleod integration supports assigning carrier but org doesn't want to allow users to do so via Drumkit
  isCarrierAssignmentDisabled: boolean;
  // This is a McLeod Enterprise feature flag to only require city and state for pickup and consignee.
  // For example, Fetch Freight only requires city and state for pickup and consignee
  // but Trident requires the entire address/location profile
  isOnlyCityStateRequired: boolean;
  // Some services require revenue code to be filled out on the load, e.g. Fetch Freight does but Tumalo doesn't
  isRevenueCodeRequired: boolean;
};
