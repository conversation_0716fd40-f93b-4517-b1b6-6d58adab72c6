import { CreateQuoteRequestSuggestionRequest } from 'lib/api/createQuoteRequestSuggestion';
import { Maybe } from 'types/UtilityTypes';

export type PortalActionResult = {
  success: boolean;
  partialSuccess?: boolean;
  error?: any;
};

export type PortalParseQuoteRequestResult = {
  data: Maybe<CreateQuoteRequestSuggestionRequest>;
  error?: any;
};

export type SidepanelMessage = {
  /*
   * Because each tab can have its own Drumkit sidepanel, we need to specify which tab should receive the message.
   * This prevents the background script from triggering behaviors on the wrong tab.
   */
  targetTabId: number;
  action: string;
  data?: any;
};
