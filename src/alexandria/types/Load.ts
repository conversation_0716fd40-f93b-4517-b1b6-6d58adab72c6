import { toUpper } from 'lodash';

import CheckCallSource from 'types/enums/CheckCallSource';
import { TMS } from 'types/enums/Integrations';
import { titleCase } from 'utils/formatStrings';
import { normalizeTimezone } from 'utils/normalizeTimezone';
import { normalizeDatesForTMSForm } from 'utils/parseDatesForTMSForm';

import { Order } from './Order';
import { Maybe, MaybeUndef } from './UtilityTypes';

export type Load = {
  // Capitalized only because of underlying gorm.Model
  ID: MaybeUndef<number>;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: MaybeUndef<string>;

  serviceID: number;
  tmsID: number;
  freightTrackingID: string;
  isPlaceholder: boolean;
  externalTMSID: string;
  status: string;
  mode: string;
  moreThanTwoStops: boolean;
  poNums: MaybeUndef<string>;
  operator: string;
  additionalReferences: AdditionalReference[];
  rateData: RateData;
  customer: Customer;
  billTo: CompanyCoreInfo;
  pickup: Pickup;
  consignee: Dropoff;
  carrier: LoadCarrier;
  specifications: Specifications;
  notes: Note[];
  commodities: Commodity[];
  stops?: LoadStopsItem[];
  orders?: Order[];
};

export enum LoadStopsItemTypes {
  Pickup = 'pickup',
  Delivery = 'delivery',
  Dropoff = 'dropoff',
}

export type LoadStopsItemAddress = {
  name: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zip: string;
  timezone: string;
  country: string;
  isVerified: boolean;
};

export type LoadStopsItem = {
  id?: string | number;
  order?: number;
  loadID: number;
  stopType: LoadStopsItemTypes;
  stopNumber: number;
  address: LoadStopsItemAddress;
  externalTMSID: string;
  contact: string;
  phone: string;
  email: string;
  businessHours: string;
  refNumber: string;
  readyTime: string;
  readyEndTime: string;
  mustDeliver: string;
  apptRequired: boolean;
  apptType: string;
  apptStartTime: string;
  apptEndTime: string;
  actualStartTime: string;
  actualEndTime: string;
  expectedStartTime: string;
  apptNote: string;
  timezone: string;
  additionalReferences: Maybe<AdditionalReference[]>;
};

export type CompanyCoreInfo = {
  externalTMSID: string;
  name: string;
  nameAddress?: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  country?: string;
  contact: string;
  phone: string;
  email: string;
};

export const formatAddress = (address: CompanyCoreInfo): string => {
  if (!address) {
    return '';
  }

  const parts: string[] = [];
  if (address.addressLine1) parts.push(titleCase(address.addressLine1));
  if (address.addressLine2) parts.push(titleCase(address.addressLine2));
  if (address.city) parts.push(titleCase(address.city));
  if (address.state) parts.push(address.state);
  if (address.zipCode) parts.push(toUpper(address.zipCode));

  return parts.join(', ');
};

export const formatAddressCityStateZip = (address: CompanyCoreInfo): string => {
  if (!address) {
    return '';
  }

  const parts: string[] = [];
  if (address.city) parts.push(titleCase(address.city));
  if (address.state) parts.push(address.state);
  if (address.zipCode) parts.push(toUpper(address.zipCode));

  return parts.join(', ');
};

export type Customer = CompanyCoreInfo & {
  refNumber: string;
  refNumberCandidates?: string[];
};

export type Note = {
  updatedBy: string;
  createdAt: string;
  note: string;
  isException: Maybe<boolean>;
  isOnTime: Maybe<boolean>;
  source: string; // Primarily for Relay, e.g. dispatcher, driver

  // GlobalTranz TMS specific fields
  userName?: string;
  text?: string;
  noteType?: string;
  timeStamp?: string;
  isDeleted?: boolean;
  isResolved?: boolean;
};

export type LoadCarrier = {
  externalTMSID: string;
  name: string;
  mcNumber: string;
  dotNumber: string;
  phone: string;
  dispatcher: string;
  notes: string;
  sealNumber: string;
  scac: string;
  firstDriverName: string;
  firstDriverPhone: string;
  secondDriverName: string;
  secondDriverPhone: string;
  email: string;
  dispatchCity: string;
  dispatchState: string;
  dispatchSource: CheckCallSource;
  truckNumber: string;
  trailerNumber: string;
  rateConfirmationSent: boolean;
  confirmationSentTime: MaybeUndef<string>;
  confirmationReceivedTime: MaybeUndef<string>;
  dispatchedTime: MaybeUndef<string>;
  expectedPickupTime: MaybeUndef<string>;
  pickupStart: MaybeUndef<string>;
  pickupEnd: MaybeUndef<string>;
  expectedDeliveryTime: MaybeUndef<string>;
  deliveryStart: MaybeUndef<string>;
  deliveryEnd: MaybeUndef<string>;
  signedBy: string;
  equipmentName: string;
};

export type Pickup = LoadStop & {
  readyTime: MaybeUndef<string>;
};

export type Dropoff = LoadStop & {
  mustDeliver: string; // Must deliver by datetime
};

export type LoadStop = CompanyCoreInfo & {
  businessHours: string;
  refNumber: string;
  refNumberCandidates?: string[];
  apptRequired: Maybe<boolean>;
  apptStartTime: MaybeUndef<string>;
  apptEndTime: MaybeUndef<string>;
  apptNum: string;
  apptNote: string;
  timezone: string; // IANA timezone e.g. America/New_York
};

export type Commodity = {
  ID?: number;
  CreatedAt?: string;
  UpdatedAt?: string;
  DeletedAt?: MaybeUndef<string>;
  handlingQuantity: number;
  packagingType: string;
  length: number;
  width: number;
  height: number;
  dimensionUnit: string; // e.g. ft, cm
  weightTotal: number;
  hazardousMaterial: boolean;
  quantity: number;
  freightClass: string;
  nmfc: string;
  description: string;
  additionalMarkings: string;
  unNumber: string;
  packagingGroup: string;
  referenceNumber: string;
  hazmatCustomClassDescription: string;
  hazmatPieceDescription: string;
  harmonizedCode: string;
  hazardClasses: MaybeUndef<string[]>;
  loadID: number;
  // Turvo-specific fields
  totalPieces?: ValueUnit;
  grossWeight?: ValueUnit;
  netWeight?: ValueUnit;
};

export const createNewCommodity = (): Commodity => ({
  ID: undefined,
  CreatedAt: undefined,
  UpdatedAt: undefined,
  DeletedAt: undefined,
  handlingQuantity: 0,
  packagingType: '',
  length: 0,
  width: 0,
  height: 0,
  dimensionUnit: '',
  weightTotal: 0,
  hazardousMaterial: false,
  quantity: 0,
  freightClass: '',
  nmfc: '',
  description: '',
  additionalMarkings: '',
  unNumber: '',
  packagingGroup: '',
  referenceNumber: '',
  hazmatCustomClassDescription: '',
  hazmatPieceDescription: '',
  harmonizedCode: '',
  hazardClasses: undefined,
  loadID: 0,
  // Turvo-specific fields
  totalPieces: { val: 0, unit: null },
  grossWeight: { val: 0, unit: null },
  netWeight: { val: 0, unit: null },
});

export type ValueUnit = {
  val: number;
  unit: Maybe<Unit>;
};

export enum Unit {
  // Currency
  //---------------
  USD = 'USD',
  CAD = 'CAD',

  // Weight
  //---------------
  Kg = 'kg',
  Pounds = 'lbs',
  // Turvo specific
  Tonne = 't',
  Ounce = 'oz',
  Ton = 'ton',
  Gram = 'g',
  Pound = 'lb',

  // Pieces
  //---------------
  Boxes = 'boxes',
  Crates = 'crates',
  Pallets = 'pallets',
  // Turvo specific
  MetricTons = 'Metric tons',
  Bottles = 'Bottles',
  Packages = 'Packages',
  Racks = 'Racks',
  Bin = 'Bin',
  Piece = 'Piece',
  Ream = 'Ream',
  Spack = 'Spack',
  Spool = 'Spool',
  Bushels = 'Bushels',
  Containers = 'Containers',
  Gallons = 'Gallons',
  Tons = 'Tons',
  Kegs = 'Kegs',
  Cases = 'Cases',
  Truckload = 'Truckload',
  Blocks = 'Blocks',
  Bulk = 'Bulk',
  Barrels = 'Barrels',
  Units = 'Units',
  Each = 'Each',
  TurvoPallets = 'Pallets',
  BaseUnits = 'Base units',
  TurvoBoxes = 'Boxes',
  Loose = 'Loose',
  Bags = 'Bags',
  Bales = 'Bales',
  Bundles = 'Bundles',
  Cans = 'Cans',
  Carboys = 'Carboys',
  Carpets = 'Carpets',
  Cartons = 'Cartons',
  Coils = 'Coils',
  TurvoCrates = 'Crates',
  Cylinders = 'Cylinders',
  Drums = 'Drums',
  Pails = 'Pails',
  Reels = 'Reels',
  Lbs = 'Lbs',
  Rolls = 'Rolls',
  Kgs = 'Kgs',
  Skids = 'Skids',
  Liters = 'Liters',
  Totes = 'Totes',
  TubesPipes = 'Tubes/pipes',
  Vehicles = 'Vehicles',
  Other = 'Other',
  Kit = 'Kit',
  Pack = 'Pack',
  Pair = 'Pair',
  Feet = 'Feet',
  InnerPack = 'Inner Pack',
  Grams = 'Grams',
  Layer = 'Layer',
  Items = 'Items',
  Buckets = 'Buckets',
  Combinations = 'Combinations',
  HundredweightOnNet = 'Hundredweight on Net',
  Pouches = 'Pouches',
  Trays = 'Trays',
  Tubs = 'Tubs',

  // Distance
  //---------------
  Miles = 'miles',
  Km = 'km',
}

export type Specifications = {
  // Different from Mode which is the type of load (e.g. TL, LTL)
  // OrderType is primarily for McleodEnterprise and enums vary by tenant
  orderType: string;
  planningComment: string;
  totalInPalletCount: MaybeUndef<number>;
  totalOutPalletCount: MaybeUndef<number>;
  palletsRequired: boolean;
  totalPieces: Maybe<ValueUnit>;
  totalPiecesType: string;
  commodities: string;
  numCommodities: MaybeUndef<number>;
  transportType: string; // Van, Flatbed, Reefer, etc
  serviceType: string;
  transportSize: string;
  totalWeight: Maybe<ValueUnit>;
  totalVolume: Maybe<ValueUnit>;
  netWeight: Maybe<ValueUnit>;
  billableWeight: Maybe<ValueUnit>;
  totalDistance: MaybeUndef<ValueUnit>;
  minTempFahrenheit: MaybeUndef<number>;
  maxTempFahrenheit: MaybeUndef<number>;
};

export type CommodityItem = {
  description: string;
  totalPieces: ValueUnit;
  grossWeight: ValueUnit;
  netWeight: ValueUnit;
};

export type CostLineItem = {
  label: string; // Required
  unitBasis: string; // e.g. flat, hour, mile, etc.
  quantity: number; // if unit basis is flat, then quantity is 1, otherwise quantity is the number of units (miles, hours, weight, etc)
  ratePerUnitUSD: number; // e.g. $100 / hour, $15 / mile, etc.
  totalChargeUSD: number; // i.e. Quantity * RatePerUnitUSD
  note: string; // optional
};

export type RateData = {
  salesperson1: string;
  salesperson1Percent: number;

  collectionMethod: string; // e.g. Prepaid, Collect, Third-Party, etc
  revenueCode: string; // Department/team in brokerage receiving the revenue
  customerRateType: string; // e.g. FlatRate, All In, Hourly, Mileage

  // Customer rate info
  customerLineHaulCharge: Maybe<ValueUnit>; // ValueUnit with currency (e.g. USD)
  customerRateNumUnits: Maybe<number>; // Number of units for rate type (e.g. miles, hours, etc.)
  customerLineHaulRate: Maybe<number>; // Per unit rate (e.g. $/mile or $/hour)
  customerLineHaulUnit: string; // Unit for the rate (e.g. miles, hours, etc.)
  customerTotalCharge: Maybe<ValueUnit>; // ValueUnit.Unit is currency (e.g. USD).
  customerLineItems: CostLineItem[]; // e.g. detention fee, drayage fee, etc

  // Carrier rate info
  carrierRateType: string; // e.g. FlatRate, All In, Hourly, Mileage
  carrierLineHaulCharge: Maybe<ValueUnit>; // ValueUnit with currency (e.g. USD)
  carrierRateNumUnits: Maybe<number>; // Number of units for rate type (e.g. miles, hours, etc.)
  carrierLineHaulRate: Maybe<number>; // Per unit rate (e.g. $/mile or $/hour)
  carrierLineHaulUnit: string; // Unit for the rate (e.g. miles, hours, etc.)
  carrierTotalCost: Maybe<ValueUnit>; // ValueUnit.Unit is currency (e.g. USD).
  carrierLineItems: CostLineItem[]; // e.g. detention fee, drayage fee, etc
  carrierMaxRate: Maybe<number>;

  fscPercent: Maybe<number>; // 0 - 100
  fscPerMile: Maybe<number>;
  fscFlatRate: Maybe<number>;

  netProfitUSD: Maybe<number>;
  profitPercent: Maybe<number>; // 0 - 100
};

// For Aljex loads whose timestamps are timezone-agnostic, normalizes all timestamps to be in UTC,
// regardless of the user's locale
export type NormalizedLoad = {
  // Capitalized only because of underlying gorm.Model
  ID: MaybeUndef<number>;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: MaybeUndef<string>;

  serviceID: number;
  tmsID: number;
  freightTrackingID: string;
  isPlaceholder: boolean;
  externalTMSID: string;
  status: string;
  mode: string;
  moreThanTwoStops: boolean;
  poNums: MaybeUndef<string>;
  operator: string;
  additionalReferences: AdditionalReference[];
  rateData: RateData;
  customer: Customer;
  billTo: CompanyCoreInfo;
  pickup: NormalizedPickup;
  consignee: NormalizedDropoff;
  carrier: NormalizedLoadCarrier;
  specifications: Specifications;
  notes: Note[];
  commodities: Commodity[];
  stops?: LoadStopsItem[];
};

export type NormalizedLoadCarrier = {
  name: string;
  externalTMSID: string;
  mcNumber: string;
  dotNumber: string;
  phone: string;
  dispatcher: string;
  notes: string;
  sealNumber: string;
  scac: string;
  firstDriverName: string;
  firstDriverPhone: string;
  secondDriverName: string;
  secondDriverPhone: string;
  email: string;
  dispatchSource: CheckCallSource;
  dispatchCity: string;
  dispatchState: string;
  truckNumber: string;
  trailerNumber: string;
  rateConfirmationSent: boolean;
  confirmationSentTime: Maybe<Date>;
  confirmationReceivedTime: Maybe<Date>;
  dispatchedTime: Maybe<Date>;
  expectedPickupTime: Maybe<Date>;
  pickupStart: Maybe<Date>;
  pickupEnd: Maybe<Date>;
  expectedDeliveryTime: Maybe<Date>;
  deliveryStart: Maybe<Date>;
  deliveryEnd: Maybe<Date>;
  signedBy: string;
  equipmentName: string;
};

export type NormalizedPickup = NormalizedLoadStop & {
  // Capitalized only because of underlying gorm.Model
  ID: MaybeUndef<number>;
  apptConfirmed: Maybe<boolean>; // happens in load information tab
  readyTime: Maybe<Date>;
};

export type NormalizedDropoff = NormalizedLoadStop & {
  // Capitalized only because of underlying gorm.Model
  ID: MaybeUndef<number>;
  apptConfirmed: Maybe<boolean>; // happens in load information tab
  mustDeliver: Maybe<Date>;
};

export type AdditionalReference = {
  qualifier: string;
  number: string;
  weight: number;
  pieces: number;
  shouldSendToDriver: boolean;
};

export type NormalizedLoadStop = CompanyCoreInfo & {
  businessHours: string;
  refNumber: string;
  refNumberCandidates?: string[];
  additionalReferences: AdditionalReference[];
  apptRequired: Maybe<boolean>;
  apptStartTime: Maybe<Date>;
  apptEndTime: Maybe<Date>;
  apptType: Maybe<string>;
  apptNum: string;
  apptNote: string;
  timezone: string; // IANA timezone e.g. America/New_York
  instructions: string;
  referenceNumber: string;
  locationType: string;
  openTime: Maybe<Date>;
  closeTime: Maybe<Date>;
  tsaCompliant: Maybe<boolean>;
};

export function normalizeLoad(tmsName: string, load: Load) {
  // For Revenova TMS, map commodities array to specifications
  const isRevenovaTMS = tmsName === TMS.Revenova;
  const firstCommodity = load.commodities?.[0];

  return {
    ...load,
    mode: load.mode || 'TL', // Use API value (TL, LTL, Refrigerated) or default to 'TL'
    poNums: load.poNums,
    customer: normalizeDatesForTMSForm(tmsName, load.customer),
    billTo: normalizeDatesForTMSForm(tmsName, load.billTo),
    pickup: {
      ...normalizeDatesForTMSForm(tmsName, load.pickup),
      timezone: normalizeTimezone(load.pickup?.timezone) || '',
    },
    consignee: {
      ...normalizeDatesForTMSForm(tmsName, load.consignee),
      timezone: normalizeTimezone(load.consignee?.timezone) || '',
    },
    carrier: normalizeDatesForTMSForm(tmsName, load.carrier),
    specifications: {
      ...load.specifications,
      transportType: load.specifications.transportType || 'Truckload', // Use API value or default to 'Truckload'
      // Map commodities data to specifications for Revenova
      ...(isRevenovaTMS &&
        firstCommodity && {
          commodities:
            firstCommodity.description || load.specifications.commodities || '',
          totalPieces: {
            val:
              firstCommodity.handlingQuantity ||
              load.specifications.totalPieces?.val ||
              0,
            unit:
              firstCommodity.packagingType ||
              load.specifications.totalPieces?.unit ||
              '',
          },
        }),
    },
    stops: (load.stops || []).map((s) => {
      const normalizedStopDates = normalizeDatesForTMSForm(tmsName, {
        readyTime: s.readyTime,
        readyEndTime: s.readyEndTime,
        mustDeliver: s.mustDeliver,
        apptStartTime: s.apptStartTime,
        apptEndTime: s.apptEndTime,
        actualStartTime: s.actualStartTime,
        actualEndTime: s.actualEndTime,
        expectedStartTime: s.expectedStartTime,
      });

      return {
        ...s,
        ...normalizedStopDates,
      };
    }),
    rateData: load.rateData,
    additionalReferences: load.additionalReferences || [],
    commodities: load.commodities || [],
  } as NormalizedLoad;
}

export function mapUnit(unitStr: string): Maybe<Unit> {
  switch (unitStr.toLowerCase()) {
    case 'kg':
      return Unit.Kg;
    case 'lbs':
      return Unit.Pounds;
    case 'boxes':
      return Unit.Boxes;
    case 'miles':
      return Unit.Miles;
    case 'km':
      return Unit.Km;
    case 'pallets':
      return Unit.Pallets;
    case 'crates':
      return Unit.Crates;
    default:
      return null;
  }
}

export type TMSCustomer = CompanyCoreInfo & {
  refNumber: string;
};

export type TMSLocation = CompanyCoreInfo & {
  // Capitalized only because of underlying gorm.Model
  ID: MaybeUndef<number>;
  apptRequired: Maybe<boolean>;
  isShipper?: boolean;
  isConsignee?: boolean;
  driverLoadingResponsibility?: string; // e.g. No (un)loading, driver load, drop & hook trailer. Primarily for Mcleod
  carrier?: TMSCarrier;
  notes?: string;
  instructions?: string;
  note?: string;
  refNumber?: string;
  locationType?: string;
  openTime?: Maybe<Date>;
  closeTime?: Maybe<Date>;
  isTSACompliant?: boolean;
};

export type TMSLocationWithDistance = TMSLocation & {
  milesDistance?: number;
  emails?: Maybe<string[]>;
};

export type TMSCarrier = CompanyCoreInfo & {
  // Capitalized only because of underlying gorm.Model
  ID: MaybeUndef<number>;
  dotNumber: string;
};

export function createInitCompanyCoreInfo(): CompanyCoreInfo {
  return {
    externalTMSID: '',
    name: '',
    nameAddress: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    contact: '',
    phone: '',
    email: '',
  };
}

// Use factory functions to avoid mutating const object (alternative is using Object.freeze(),
// but this made intent more explicit)
export function createInitRateData(): RateData {
  return {
    salesperson1: '',
    salesperson1Percent: 0,

    revenueCode: '',
    collectionMethod: '',

    customerRateType: '',
    customerLineHaulCharge: null,
    customerRateNumUnits: null,
    customerLineHaulRate: null,
    customerLineHaulUnit: '',
    customerTotalCharge: null,

    fscPercent: null,
    fscPerMile: null,
    fscFlatRate: null,

    customerLineItems: [],

    carrierRateType: '',
    carrierLineHaulCharge: null,
    carrierRateNumUnits: null,
    carrierLineHaulRate: null,
    carrierLineHaulUnit: '',
    carrierTotalCost: null,
    carrierLineItems: [],

    carrierMaxRate: null,

    netProfitUSD: null,
    profitPercent: null,
  };
}

export function createInitSpecs(): Specifications {
  return {
    orderType: '',
    planningComment: '',
    totalInPalletCount: 0,
    totalOutPalletCount: 0,
    palletsRequired: false,
    totalPieces: null,
    totalPiecesType: '',
    commodities: '',
    numCommodities: 0,
    totalWeight: null,
    totalVolume: null,
    billableWeight: null,
    netWeight: null,
    totalDistance: null,
    transportType: '',
    serviceType: '',
    transportSize: '',
    minTempFahrenheit: null,
    maxTempFahrenheit: null,
  };
}

export function createInitLoad(): Load {
  return {
    ID: null,
    CreatedAt: '',
    UpdatedAt: '',
    DeletedAt: null,
    serviceID: 0,
    tmsID: 0,
    freightTrackingID: '',
    isPlaceholder: false,
    externalTMSID: '',
    status: '',
    mode: '',
    moreThanTwoStops: false,
    poNums: '',
    operator: '',
    additionalReferences: [],
    rateData: {
      salesperson1: '',
      salesperson1Percent: 0,

      revenueCode: '',
      collectionMethod: '',

      customerRateType: '',
      customerLineHaulCharge: null,
      customerRateNumUnits: null,
      customerLineHaulRate: null,
      customerLineHaulUnit: '',
      customerTotalCharge: null,
      customerLineItems: [],

      fscPercent: null,
      fscPerMile: null,
      fscFlatRate: null,
      carrierRateType: '',
      carrierLineHaulCharge: null,
      carrierRateNumUnits: null,
      carrierLineHaulRate: null,
      carrierLineHaulUnit: '',
      carrierTotalCost: null,
      carrierLineItems: [],

      carrierMaxRate: null,

      netProfitUSD: null,
      profitPercent: null,
    },
    customer: {
      externalTMSID: '',
      name: '',
      nameAddress: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
      contact: '',
      phone: '',
      email: '',
      refNumber: '',
    },
    billTo: {
      externalTMSID: '',
      name: '',
      nameAddress: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      contact: '',
      phone: '',
      email: '',
    },
    pickup: {
      externalTMSID: '',
      name: '',
      nameAddress: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
      contact: '',
      phone: '',
      email: '',
      businessHours: '',
      refNumber: '',
      readyTime: null,
      apptRequired: null,
      apptStartTime: null,
      apptEndTime: null,
      apptNum: '',
      apptNote: '',
      timezone: '',
    },
    consignee: {
      externalTMSID: '',
      name: '',
      nameAddress: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
      contact: '',
      phone: '',
      email: '',
      businessHours: '',
      refNumber: '',
      apptNum: '',
      mustDeliver: '',
      apptRequired: null,
      apptStartTime: null,
      apptEndTime: null,
      apptNote: '',
      timezone: '',
    },
    carrier: {
      externalTMSID: '',
      mcNumber: '',
      dotNumber: '',
      name: '',
      phone: '',
      dispatcher: '',
      notes: '',
      sealNumber: '',
      scac: '',
      firstDriverName: '',
      firstDriverPhone: '',
      secondDriverName: '',
      secondDriverPhone: '',
      email: '',
      dispatchSource: CheckCallSource.Dispatcher,
      dispatchCity: '',
      dispatchState: '',
      truckNumber: '',
      trailerNumber: '',
      rateConfirmationSent: false,
      confirmationSentTime: null,
      confirmationReceivedTime: null,
      dispatchedTime: null,
      expectedPickupTime: null,
      pickupStart: null,
      pickupEnd: null,
      expectedDeliveryTime: null,
      deliveryStart: null,
      deliveryEnd: null,
      signedBy: '',
      equipmentName: '',
    },
    specifications: {
      orderType: '',
      planningComment: '',
      totalInPalletCount: 0,
      totalOutPalletCount: 0,
      palletsRequired: false,
      totalPieces: null,
      totalPiecesType: '',
      commodities: '',
      numCommodities: 0,
      totalWeight: null,
      totalVolume: null,
      billableWeight: null,
      netWeight: null,
      totalDistance: null,
      transportType: '',
      serviceType: '',
      transportSize: '',
      minTempFahrenheit: 0,
      maxTempFahrenheit: 0,
    },
    notes: [],
    commodities: [],
  };
}
