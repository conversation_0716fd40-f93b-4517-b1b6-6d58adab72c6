import { Email } from 'types/Email';
import { Load } from 'types/Load';
import { LoadAttributes } from 'types/LoadAttributes';
import { TruckListErrors } from 'types/Truck';
import { MaybeUndef } from 'types/UtilityTypes';

export type ApiError = {
  message: string;
  status?: number;
  response?: unknown;
  email?: Email;
  load?: Load;
  loadAttributes?: LoadAttributes;
  truckListErrors?: MaybeUndef<TruckListErrors>;
};
