import {
  QuickQuoteResponse,
  QuickQuoteResponseQuote,
  QuickQuoteResponseStop,
} from 'lib/api/getQuickQuote';
import {
  QuoteFormStop,
  SelectedQuoteType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { Stop, TransportType } from 'types/QuoteRequest';

export enum BatchQuoteStatus {
  Success = 'success',
  Error = 'error',
}

export interface BatchQuotePrivateBody {
  loadId: string;
  transportType: TransportType;
  stops: QuoteFormStop[];
  newStops: Stop[];
  pickupDate: string;
  deliveryDate: string;
  customerName: string;
  emailID?: number;
  threadID?: string;
  quoteRequestId?: number;
  selectedQuickQuoteId?: number;
}

export interface BatchQuickQuoteBody {
  quickQuotes: BatchQuotePrivateBody[];
  testFailLastQuote?: boolean;
}

export interface BatchQuoteResult {
  quoteRequestId: number;
  status: BatchQuoteStatus;
  error?: string;
  quotes?: QuickQuoteResponseQuote[];
  stops?: QuickQuoteResponseStop[];
  selectedRateName?: SelectedQuoteType;
  isZipCodeLookup?: boolean;
  inputtedTransportType?: TransportType;
  submittedTransportType?: TransportType;
  configuration?: QuickQuoteResponse['configuration'];
  quoteReplyDraftTemplate?: QuickQuoteResponse['quoteReplyDraftTemplate'];
}

export interface BatchQuoteResponse {
  batchQuoteId: number;
  results: BatchQuoteResult[];
  processedCount: number;
  errorCount: number;
  totalQuotes: number;
  errors: Record<string, string[]>;
}
