import { CarrierVerification as CarrierVerificationEnum } from 'types/enums/Integrations';

export type CarrierVerification = {
  name: string;
  dba: string;
  dot_number: string;
  docket_number: string;
  risk_rating: string;
  incident_reports: number;
  incident_reports_with_fraud: number;
  completed_packet: boolean;
  integration_name: CarrierVerificationEnum;
  is_authorized_user: boolean;
};
