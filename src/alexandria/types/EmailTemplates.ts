import EmailTemplateType from './enums/EmailTemplateType';

export enum TemplateType {
  CARRIER = 'carrier',
  APPOINTMENT = 'appointment',
  CARRIER_QUOTE_BY_GROUP = 'carrier_quote_by_group',
  CARRIER_QUOTE_BY_LOCATION = 'carrier_quote_by_location',
}

export enum EmailTemplateAccessTier {
  USER = 'user',
  GROUP = 'group',
  SERVICE = 'service',
  GENERIC = 'generic',
}

export type EmailTemplate = {
  type: EmailTemplateType;
  cc: string[];
  subject: string;
  body: string;
  isGeneric: boolean;
  id?: number;
  templateType?: EmailTemplateType;
  templateAccessTier?: EmailTemplateAccessTier;
  name?: string;
};
