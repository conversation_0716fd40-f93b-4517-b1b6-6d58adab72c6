import { TruckType } from 'pages/QuoteView/TruckList/TruckListTab';

import { CoreAddress } from './Address';
import { MaybeUndef } from './UtilityTypes';

export interface Truck {
  id: number;
  pickupLocation: CoreAddress;
  pickupDate: string;
  dropoffLocation: CoreAddress;
  dropoffDate: string;
  type: TruckType;
  length: number;
  dropoffIsCarrierDomicile: boolean;
}

export interface FormTruck {
  id: number;
  pickupLocation: CoreAddress;
  pickupDate: string;
  dropoffLocation: CoreAddress;
  dropoffDate: string;
  type: TruckType;
  length: string;
  dropoffIsCarrierDomicile: boolean;
  isEditing: boolean;
  // Assigned by RHF to entries in useFieldArray, preventing conflicts with the Truck's actual ID.
  rhfId?: string;
  errors?: TruckError;
}

export type TruckError = {
  pickupDateErrors: string[];
  pickupLocationErrors: string[];
  dropoffLocationErrors: string[];
  equipmentErrors: string[];
  postingErrors: string[];
};

export type TruckListErrors = {
  carrierErrors: MaybeUndef<{ [error: string]: CarrierInformation[] }>;
  carrierContactErrors: MaybeUndef<string[]>;
  postedByErrors: MaybeUndef<string[]>;
  truckErrors: MaybeUndef<{ [truckId: number]: TruckError }>;
};

export type CarrierInformation = {
  name: string;
  mc: string;
  dot: string;
  contactEmail: string;
  contactName: string;
  externalTMSID: string;
};
