import eslint from '@eslint/js';
import tseslintParser from '@typescript-eslint/parser';
import prettierConfig from 'eslint-config-prettier';
import react from 'eslint-plugin-react';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import tseslint from 'typescript-eslint';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const customRules = {
  'no-grid-without-explicit-margin-and-width': {
    meta: {
      type: 'warn',
      docs: {
        description:
          'Enforce explicit margin and width classes with grid layouts to override TMS website defaults',
        category: 'Best Practices',
        recommended: false,
      },
      schema: [], // no options
    },
    create(context) {
      return {
        JSXAttribute(node) {
          if (
            node.name.name === 'className' &&
            typeof node.value.value === 'string'
          ) {
            const classNameValue = node.value.value;
            const hasGrid = /\bgrid\b/.test(classNameValue);
            const hasGridColsOrRows = /\b(grid-cols-\d+|grid-rows-\d+)\b/.test(
              classNameValue
            );
            const hasMx = /\bm[xy]?-\d+\b/.test(classNameValue);
            const hasWidth = /\bw-(?:full|\d+\/\d+|\d+)\b/.test(classNameValue);

            if (hasGrid && hasGridColsOrRows && (!hasMx || !hasWidth)) {
              context.report({
                node,
                message:
                  'Using `grid` with `grid-cols-*` or `grid-rows-*` without explicit margin and/or width class. Consider adding `mx-0 w-full` or appropriate classes.',
              });
            }
          }
        },
      };
    },
  },
  'no-direct-axios-import': {
    meta: {
      type: 'error',
      docs: {
        description:
          'Prevent direct imports of axios. Use the configured axios instance from @utils/axios instead.',
        category: 'Best Practices',
        recommended: false,
      },
      schema: [],
    },
    create(context) {
      const filename = context.getFilename();
      // Skip the axios.ts file itself to avoid self-referencing issues
      const isAxiosUtilFile = filename.includes('/utils/axios.ts');

      if (isAxiosUtilFile) {
        return {};
      }

      return {
        ImportDeclaration(node) {
          if (node.source.value === 'axios') {
            // Check if this is importing the default axios export
            const hasDefaultImport = node.specifiers.some(
              (spec) => spec.type === 'ImportDefaultSpecifier'
            );

            if (hasDefaultImport) {
              context.report({
                node,
                message:
                  'Direct import of axios is not allowed. Use the configured axios instance from "@utils/axios" instead.',
              });
            }
          }
        },
      };
    },
  },
};

export default [
  eslint.configs.recommended,
  ...tseslint.configs.recommended,
  {
    files: ['**/*.{js,mjs,cjs,jsx,mjsx,ts,tsx,mtsx}'],
    plugins: {
      react,
      custom: { rules: customRules },
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    rules: {
      'react/button-has-type': 'off',
      'react/react-in-jsx-scope': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': [
        'warn',
        { argsIgnorePattern: '^_', varsIgnorePattern: '^_' },
      ],
      '@typescript-eslint/no-unused-expressions': [
        'error',
        {
          allowShortCircuit: true,
          allowTernary: true,
          allowTaggedTemplates: true,
        },
      ],
      '@typescript-eslint/no-empty-object-type': [
        'error',
        { allowInterfaces: 'with-single-extends' },
      ],
      'custom/no-grid-without-explicit-margin-and-width': 'warn',
    },
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parser: tseslintParser,
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
        tsconfigRootDir: __dirname,
      },
      globals: {
        chrome: 'readonly',
      },
    },
  },
  {
    ignores: ['watch.js', 'dist/**'],
  },
  prettierConfig,
];
