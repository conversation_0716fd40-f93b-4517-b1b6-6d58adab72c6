import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import putViewedLoadHistory from '../lib/api/putViewedLoadHistory';

export type TabType = 'load' | 'order';

export interface TabItem {
  id: string;
  type: TabType;
}

export const getTabKey = (tab: TabItem): string => `${tab.id}-${tab.type}`;

export type AddTabFn = (
  id: string,
  type: TabType,
  clearExistingTabs?: boolean
) => void;

type LoadSearchContextType = {
  proNumberInput: string;
  setProNumberInput: (value: string) => void;
  addTab: AddTabFn;
  tabs: TabItem[];
  setTabs: (tabs: TabItem[] | ((prev: TabItem[]) => TabItem[])) => void;
  activeTabKey: string;
  setActiveTabKey: (key: string) => void;
};

const noop: AddTabFn = () => {};

export const LoadSearchContext = createContext<LoadSearchContextType>({
  proNumberInput: '',
  setProNumberInput: () => {},
  addTab: noop,
  tabs: [],
  setTabs: () => {},
  activeTabKey: '',
  setActiveTabKey: () => {},
});

type LoadSearchProviderProps = {
  children: React.ReactNode;
  initialFreightTrackingIDs?: string[];
};

export function LoadSearchProvider({
  children,
  initialFreightTrackingIDs,
}: LoadSearchProviderProps) {
  const [proNumberInput, setProNumberInput] = useState('');
  const [tabs, setTabs] = useState<TabItem[]>([]);
  const [activeTabKey, setActiveTabKey] = useState<string>('');

  // Memoized function so it doesn't get recreated every render, preventing stale closures
  const processFreightTrackingIDs = useCallback(
    (freightTrackingIDs: TabItem[]) => {
      freightTrackingIDs.forEach(async (tab) => {
        const trimmedId = tab.id.trim();
        if (!trimmedId) {
          return;
        }

        createTabForFreightTrackingID(tab, setTabs, setActiveTabKey);
      });
    },
    [setTabs, setActiveTabKey]
  );

  // Automatically add tabs when initialFreightTrackingIDs changes
  useEffect(() => {
    // Clear all existing tabs first
    setTabs([]);
    setActiveTabKey('');

    if (!initialFreightTrackingIDs || initialFreightTrackingIDs.length === 0) {
      return;
    }

    processFreightTrackingIDs(
      initialFreightTrackingIDs.map((id) => ({ id, type: 'load' }))
    );
  }, [initialFreightTrackingIDs]);

  // Memoized function other components can use to add to tab list
  const addTab = useCallback(
    (id: string, type: TabType, clearExistingTabs: boolean = false) => {
      if (clearExistingTabs) {
        setTabs([]);
        setActiveTabKey('');
      }

      processFreightTrackingIDs([{ id, type }]);
      setProNumberInput('');
    },
    [processFreightTrackingIDs, setProNumberInput]
  );

  return (
    <LoadSearchContext.Provider
      value={{
        proNumberInput,
        setProNumberInput,
        addTab,
        tabs,
        setTabs,
        activeTabKey,
        setActiveTabKey,
      }}
    >
      {children}
    </LoadSearchContext.Provider>
  );
}

export function useLoadSearch() {
  const context = useContext(LoadSearchContext);

  if (context === undefined) {
    throw new Error('useLoadSearch must be used within a LoadSearchProvider');
  }
  return context;
}

const createTabForFreightTrackingID = async (
  tab: TabItem,
  setTabs: React.Dispatch<React.SetStateAction<TabItem[]>>,
  setActiveTabKey: React.Dispatch<React.SetStateAction<string>>
) => {
  setTabs((prevTabs: TabItem[]) => {
    const existingTab = prevTabs.find(
      (t: TabItem) => t.id === tab.id && t.type === tab.type
    );

    // If the tab exists, set the active tab key and return the existing tab list
    if (existingTab) {
      setActiveTabKey(getTabKey(existingTab));
      return prevTabs;
    }

    // If the tab doesn't exist, set the active tab key and add it to the tab list
    setActiveTabKey(getTabKey(tab));
    return [...prevTabs, tab];
  });

  // Track viewed history for loads
  if (tab.type === 'load') {
    try {
      const res = await putViewedLoadHistory(tab.id);
      if (!res.isOk()) {
        console.error('putViewedLoadHistory response not OK:', res);
      }
    } catch (error) {
      console.error('Error updating viewed loads:', error);
    }
  }
};
