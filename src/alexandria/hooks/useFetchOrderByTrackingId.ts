import { Order } from 'types/Order';
import { useThrowableSWR } from 'utils/fetcher';

type OrderFetchResult = {
  order: Order | null;
  isLoading: boolean;
  error: Error | null;
};

export default function useFetchOrderByTrackingId(
  trackingId?: string
): OrderFetchResult {
  const { data, isLoading, error } = useThrowableSWR<Order>(
    trackingId ? `orders/tracking/${trackingId}` : null,
    false // Don't retry on 404s
  );

  return {
    order: data || null,
    isLoading,
    error,
  };
}
