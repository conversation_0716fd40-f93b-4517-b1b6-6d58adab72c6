import { KeyedMutator } from 'swr';

import { Attachment } from 'types/EmailAttachment';
import { Maybe } from 'types/UtilityTypes';
import { useThrowableSWR } from 'utils/fetcher';

export type EmailAttachmentsResponse = {
  emailID: number;
  emailExternalID: string;
  emailThreadID: string;
  attachments: Maybe<Attachment[]>;
};

export type EmailAttachmentFetchResult = {
  response: Maybe<EmailAttachmentsResponse>;
  invalidate: KeyedMutator<EmailAttachmentsResponse>;
  isLoading: boolean;
  error: Error;
};

export default function useFetchEmailAttachment(
  externalId?: string
): EmailAttachmentFetchResult {
  const { data, isLoading, mutate, error } =
    useThrowableSWR<EmailAttachmentsResponse>(
      externalId ? `email/${externalId}/attachments` : null
    );

  return {
    response: data || null,
    invalidate: mutate,
    isLoading: isLoading,
    error: error,
  };
}
