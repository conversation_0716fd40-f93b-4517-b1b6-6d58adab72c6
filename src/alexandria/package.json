{"name": "alexandria", "version": "0.0.1", "repository": {"type": "git", "url": "https://github.com/drumkitai/alexandria.git"}, "license": "MIT", "scripts": {"lint": "eslint --max-warnings=0 '**/*.{ts,tsx,js,jsx}'", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "test": "jest"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "devDependencies": {"@hookform/error-message": "^2.0.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.1", "@testing-library/dom": "^10.4.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/chrome": "^0.1.27", "@types/eslint": "^9", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/office-js": "^1.0.555", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.2", "@types/testing-library__react": "^10.2.0", "@types/testing-library__user-event": "^4.2.0", "@types/uuid": "^11", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "antd": "^5.27.6", "class-variance-authority": "^0.7.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "eslint": "^9.39.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.0", "jest": "^30.2.0", "jest-environment-jsdom": "^30.2.0", "neverthrow": "^8.0.0", "prettier": "^3.6.2", "react-content-loader": "^7.1.1", "react-router-dom": "^7.9.5", "ts-jest": "^29.4.5", "ts-node": "^10.9.2", "typescript": "^5.9.3"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toggle-group": "^1.1.10", "@sentry/browser": "^10.22.0", "@types/node": "^24.9.2", "axios": "^1.13.1", "clsx": "^2.1.1", "dayjs": "^1.11.19", "framer-motion": "^12.23.24", "fuse.js": "^7.0.0", "lodash": "^4.17.21", "lucide-react": "^0.552.0", "react": "^19.2.0", "react-day-picker": "9.11.1", "react-dom": "^19.2.0", "react-hook-form": "^7.66.0", "recharts": "^3.3.0", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "typescript-eslint": "^8.46.2", "uuid": "^13.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}