/* eslint-disable no-undef */
import { execSync } from 'child_process';
import fs from 'fs';

function runCommand(command) {
  try {
    console.log(`Running command: ${command}`);
    const output = execSync(command, { stdio: 'pipe' }).toString().trim();
    console.log(`Output: ${output}`);
    return output;
  } catch (error) {
    console.error(`<PERSON>rror executing command: ${command}`);
    console.error(error.message);
    return null;
  }
}

function compareVersions(a, b) {
  const parseVersion = (v) => {
    const match = v.match(/^v?(\d+)\.(\d+)\.(\d+)$/);
    if (!match) return [0, 0, 0];
    return [
      parseInt(match[1], 10),
      parseInt(match[2], 10),
      parseInt(match[3], 10),
    ];
  };

  const [majorA, minorA, patchA] = parseVersion(a);
  const [majorB, minorB, patchB] = parseVersion(b);

  if (majorA !== majorB) return majorB - majorA;
  if (minorA !== minorB) return minorB - minorA;
  return patchB - patchA;
}

function getLatestTag() {
  // Fetch all tags
  runCommand('git fetch --tags');

  // Get all tags
  const tags = runCommand('git tag -l "v*"')?.split('\n').filter(Boolean) || [];

  if (tags.length === 0) return 'v0.0.0';

  // Sort tags by semantic version
  return tags.sort(compareVersions)[0];
}

function getPRAuthor(commitHash) {
  try {
    // Get PR number from commit message
    const prNumber = runCommand(`git log -1 --pretty=%B ${commitHash}`)?.match(
      /#(\d+)/
    )?.[1];

    if (!prNumber) return null;

    // Get PR details using GitHub CLI
    const prDetails = JSON.parse(
      runCommand(`gh pr view ${prNumber} --json author,reviews,mergedBy`)
    );

    // If PR was created by a bot, try to get the merger or last reviewer
    if (prDetails.author.login === 'AxleGithubBot') {
      // First try to get the person who merged
      if (prDetails.mergedBy) {
        return prDetails.mergedBy.login;
      }

      // Then try to get the last reviewer who approved
      const approvers = prDetails.reviews
        ?.filter((review) => review.state === 'APPROVED')
        .map((review) => review.author.login);

      if (approvers?.length > 0) {
        return approvers[approvers.length - 1];
      }
    }

    return prDetails.author.login;
  } catch (error) {
    console.error('Error getting PR author:', error);
    return null;
  }
}

async function getPRAuthorWithRetry(commitHash, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const author = getPRAuthor(commitHash);
      if (author) return author;

      console.log(`Attempt ${i + 1}: No author found, retrying...`);
    } catch (error) {
      console.error(`Attempt ${i + 1} failed:`, error);
      if (i === retries - 1) {
        console.error('All retry attempts failed');
        return null;
      }

      await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
    }
  }

  return null;
}

async function main() {
  try {
    // Get the latest tag
    const currentTag = getLatestTag();
    console.log(`Current tag: ${currentTag}`);

    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const version = packageJson.version;
    console.log(`Package.json version: ${version}`);

    // Get previous tag
    const tags =
      runCommand('git tag -l "v*"')?.split('\n').filter(Boolean) || [];
    const sortedTags = tags
      .filter((tag) => tag !== `v${version}`)
      .sort(compareVersions);

    const previousTag = sortedTags[0] || 'v0.0.0';
    console.log('Previous tag:', previousTag);

    // Get the changelog content
    const changelogContent = fs.readFileSync('CHANGELOG.md', 'utf8');
    const versionHeader = `## ${version}`;
    const startIndex =
      changelogContent.indexOf(versionHeader) + versionHeader.length;
    let endIndex = changelogContent.indexOf('\n## ', startIndex);
    endIndex = endIndex === -1 ? changelogContent.length : endIndex;

    // Process changelog to update author information
    let newChangelog =
      startIndex !== -1
        ? changelogContent.substring(startIndex, endIndex).trim()
        : '';

    if (!newChangelog) {
      newChangelog =
        'Auto-released. Please update the description with the changes mentioned in `CHANGELOG.md`.';
    } else {
      // Update author information in the changelog
      const commits = runCommand(
        `git log ${previousTag}..HEAD --pretty=format:"%H"`
      ).split('\n');

      for (const commit of commits) {
        const author = await getPRAuthorWithRetry(commit);
        if (author) {
          // Replace bot usernames with actual PR author/merger/reviewer
          newChangelog = newChangelog.replace(/@[\w-]+/g, (match) =>
            match.includes('bot') ? `@${author}` : match
          );
        }
      }
    }

    console.log(`Processed changelog content: ${newChangelog}`);
    fs.writeFileSync('NEW_CHANGELOG.md', newChangelog);

    const fullChangelogLink = `\n\n**Full Changelog:** [${previousTag}...v${version}](https://github.com/axleapi/vulcan/compare/${previousTag}...v${version})\n`;
    console.log(`Full changelog link: ${fullChangelogLink}`);
    fs.appendFileSync('NEW_CHANGELOG.md', fullChangelogLink);

    const envVariables = [
      `VITE_DRUMKIT_API_URL=${process.env.DRUMKIT_API_URL}`,
      `VITE_DRUMKIT_AUTH_URL=${process.env.DRUMKIT_AUTH_URL}`,
      `VITE_ENVIRONMENT=${process.env.ENVIRONMENT}`,
      `VITE_SENTRY_DSN=${process.env.SENTRY_DSN}`,
      `VITE_SENTRY_AUTH_TOKEN=${process.env.SENTRY_AUTH_TOKEN}`,
      `VITE_POSTHOG_API_KEY=${process.env.POSTHOG_API_KEY}`,
    ];
    fs.writeFileSync('.env', envVariables.join('\n'));

    runCommand('yarn run build');
    runCommand('zip -r dist.zip dist/');

    const releaseTitle = `v${version}`;
    runCommand(
      `gh release create "${releaseTitle}" --title "${releaseTitle}" --notes-file NEW_CHANGELOG.md --repo ${process.env.GITHUB_REPOSITORY} --target ${process.env.GITHUB_SHA} ./dist.zip`
    );
  } catch (error) {
    console.error('Error in release process: ', error);
    process.exit(1);
  }
}

main();
