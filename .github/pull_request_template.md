## Background
<!-- Why are you making this change? What context does the reviewer need to know? -->

## Changes
<!-- What changes did you make? Describe any tradeoffs you considered. -->

## Testing (Screenshot or Video)
<!-- How did you test the change? How can the reviewer verify it independently? -->

---

## Notes
* Please remember to add a changeset to every vulcan PR to `main` that is not "Release". If you forget, then please create a new PR that includes a changeset and merge it in. This is a necessary step as part of the release process in `vulcan`
* In the description of your changeset, for PRs created by AxleGithubBot, please add "By **{engineer name}** - " at the beginning of your changeset description, so it's clear when reviewing the "Release" PR who is the engineer responsible for a change (since AxleGithubBot is getting the attribution)

### Release process
1. PRs are merged into `main` with changesets
2. A new "Release" PR is created
3. "Release" PR is tested and approved
   * Tip: Given Github rules, you may need to locally download `changeset-release/main` to your computer locally and then run `git commit --allow-empty -m "trigger checks"` and `git push` in order to trigger the linter and testing CI/CD process on the "Release" PR
   * Tip: In order to test the PR, please download it locally and build the production-ready version by running `yarn run build`
4. Once "Release" PR is approved and merged, go download the latest .zip from Releases on Github
5. Log into "<EMAIL>" and go to the Chrome Extension Store account at https://chrome.google.com/webstore/devconsole/5d51f8fc-a86b-4ef9-afea-af544c69323b/ilafmdbednnamihpnofibohjpfidgjjc/edit/package and upload the .zip under "Package" and click "Submit"

