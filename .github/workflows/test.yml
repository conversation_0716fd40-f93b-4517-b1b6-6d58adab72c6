name: Test

on:
  push:
    branches: [ main ]
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.BOT_RW_PAT }}
          submodules: 'true'

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          cache: 'yarn'

      - uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.OS }}-build-${{ hashFiles('**/package-lock.json') }}

      - run: yarn install

      - run: yarn test
