name: Create Release

on:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
          
      - name: Clone Alexandria submodule
        run: |
          git config submodule.src/alexandria.url https://${{ secrets.BOT_RW_PAT }}@github.com/axleapi/alexandria.git
          git submodule update --init
          git config submodule.src/alexandria.url '**************:axleapi/alexandria.git'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          cache: 'yarn'
          node-version: '21.x'

      - name: Install dependencies
        run: yarn install

      - name: Create release pull request or create GitHub release
        uses: changesets/action@v1
        with:
          version: yarn changeset version
          publish: yarn run release
          title: Release
        env:
          GITHUB_TOKEN: ${{ secrets.BOT_RW_PAT }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_SHA: ${{ github.sha }}
          DRUMKIT_API_URL: ${{ secrets.DRUMKIT_API_URL }}
          DRUMKIT_AUTH_URL: ${{ secrets.DRUMKIT_AUTH_URL }}
          ENVIRONMENT: ${{ secrets.ENVIRONMENT }}
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          POSTHOG_API_KEY: ${{ secrets.POSTHOG_API_KEY }}
          NODE_OPTIONS: "--max_old_space_size=4096"

      - uses: ravsamhq/notify-slack-action@v2
        if: always()
        with:
          status: ${{ job.status }}
          notify_when: "failure"
          notification_title: "{workflow} is failing"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GH_ACTION_STATUS_SLACK_WH_URL }}
