name: Create Manual Release

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g. 0.0.X)'
        required: true
      release_notes:
        description: 'Release notes (optional)'
        required: false

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.BOT_RW_PAT }}
          submodules: 'true'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          cache: 'yarn'
          node-version: '21.x'

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.OS }}-build-${{ hashFiles('**/package-lock.json') }}


      - name: Inject .env file
        run: |
          echo "VITE_DRUMKIT_API_URL=${{ secrets.DRUMKIT_API_URL }}" > .env
          echo "VITE_DRUMKIT_AUTH_URL=${{ secrets.DRUMKIT_AUTH_URL }}" >> .env
          echo "VITE_ENVIRONMENT=${{ secrets.ENVIRONMENT }}" >> .env
          echo "VITE_SENTRY_DSN=${{ secrets.SENTRY_DSN }}" >> .env
          echo "VITE_SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" >> .env
          echo "VITE_POSTHOG_API_KEY=${{ secrets.POSTHOG_API_KEY }}" >> .env

      - run: yarn install

      - run: yarn run build

      - name: Zip dist folder
        run: zip -r dist.zip dist/

      - name: Create Release using GitHub CLI
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release create ${{ github.event.inputs.version }} \
            --title "v${{ github.event.inputs.version }}" \
            --notes "${{ github.event.inputs.release_notes || 'Auto-generated release' }}" \
            --repo ${{ github.repository }} \
            --target ${{ github.sha }} \
            ./dist.zip

      - uses: ravsamhq/notify-slack-action@v2
        if: always()
        with:
          status: ${{ job.status }}
          notify_when: "failure"
          notification_title: "{workflow} is failing"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GH_ACTION_STATUS_SLACK_WH_URL }}
